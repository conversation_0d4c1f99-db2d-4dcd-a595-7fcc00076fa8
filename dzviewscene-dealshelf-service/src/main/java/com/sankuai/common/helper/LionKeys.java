package com.sankuai.common.helper;

/**
 * <AUTHOR>
 * @create 2023/8/31 16:31
 */
public class LionKeys {
    public static final String APP_KEY = "com.sankuai.dzviewscene.dealshelf";
    public static final String APP_KEY_MAPI_DZTG_WEB = "mapi-dztg-web";

//    public static final String HIDE_OPTIONAL_PRICE_OR_MARKET_PRICE = "com.sankuai.dzviewscene.dealshelf.hide.price.or.marketPrice";
    /**
     *  关闭团购详情中的 团购价&门市价
     */
    public static final String HIDE_OPTIONAL_PRICE_OR_MARKET_PRICE = "mapi-dztg-web.hide.group.detail.hide.price.or.marketPrice";

    /**
     * 是否开启无效代码下线
     */
    public static final String OPEN_GRAY_CODE_DOWNLINE = "com.sankuai.dzviewscene.dealshelf.open.gray.code.downline";

    /**
     * 无效代码下线默认灰度比例
     */
    public static final String GRAY_CODE_DOWNLINE_PERCENT = "com.sankuai.dzviewscene.dealshelf.gray.code.downline.percent";

    /**
     * 具体到OPT无效代码下线默认灰度比例
     */
    public static final String GRAY_CODE_DOWNLINE_PERCENT_OPT = "com.sankuai.dzviewscene.dealshelf.gray.code.downline.percent.opt";

    /**
     * poiid加密开关
     */
    public static final String SIG_DEGRADE_SWITCH = "sig.sdk.degrade";
    /**
     * 团详RCF缓存开关
     */
    public static final String DEAL_DETAIL_FLASH_CACHE_SWITCH = "deal.detail.flash.cache.switch";

    /**
     * 类目和planId的映射关系，通过planId查销售渠道
     */
    public static final String SALE_CHANNEL_CATE_TO_PLAN_ID_CONFIG = "deal.detail.similarity.sale.channel.category.to.planid";

    /**
     * 团购次卡c端表达优化控制开关
     */
    public static final String TIMES_DEAL_OPTIMIZE_SWITCH = "times_deal_optimize_switch";

}
