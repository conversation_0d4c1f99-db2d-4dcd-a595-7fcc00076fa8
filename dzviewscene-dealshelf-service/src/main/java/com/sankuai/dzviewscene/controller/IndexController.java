package com.sankuai.dzviewscene.controller;

import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 监控检查使用
 */
@RestController
@InterfaceDoc(
    type = "restful",
    displayName = "服务健康检查",
    description = "服务健康检查，提供服务健康检查",
    scenarios = "服务健康检查,场景用于服务的健康检查",
    host = "https://m.dianping.com"
)
public class IndexController {
    @RequestMapping(value = "/",method = {RequestMethod.GET,RequestMethod.POST})
    @MethodDoc(
        displayName = "服务健康检查",
        description = "服务健康检查",
        parameters = {},
        restExampleUrl = "https://m.51ping.com",
        restExampleResponseData = ""

    )
    public String start(){
        return "hello world! web app is started! ";
    }

    @RequestMapping(value = "index",method = {RequestMethod.GET,RequestMethod.POST})
    @MethodDoc(
            displayName = "服务健康检查",
            description = "服务健康检查",
            parameters = {},
            restExampleUrl = "https://m.51ping.com/index",
            restExampleResponseData = ""

    )
    public String index(){
        return "hello world! web app is started!";
    }
}
