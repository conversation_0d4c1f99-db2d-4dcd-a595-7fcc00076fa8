package com.sankuai.dzviewscene.productshelf.vu.vo.edudetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 活动标签
 * @auther: liweilong06
 * @date: 2021/3/4 下午3:12
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ActivityTagVO implements Serializable {

    /**
     * 文字标签
     */
    private String label;

    /**
     * 图片地址，与label互斥
     */
    private String imgUrl;

    /**
     * 位置：1左上单个，2右上单个，3右下单个，4左下单个，5底部平铺
     */
    private int position;

}
