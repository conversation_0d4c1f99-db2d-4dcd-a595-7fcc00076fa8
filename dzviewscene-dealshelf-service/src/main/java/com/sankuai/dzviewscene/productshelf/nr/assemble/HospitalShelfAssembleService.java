package com.sankuai.dzviewscene.productshelf.nr.assemble;

import com.sankuai.dzviewscene.productshelf.nr.assemble.req.HospitalDealShelfReq;
import com.sankuai.dzviewscene.productshelf.nr.assemble.res.HospitalDealShelfRes;

import java.util.concurrent.CompletableFuture;

public interface HospitalShelfAssembleService {
    /**
     * 获取医疗团单货架内容
     * @param req 货架请求
     * @return 团单货架内容
     */
    CompletableFuture<HospitalDealShelfRes> getDealShelfRes(HospitalDealShelfReq req);

}
