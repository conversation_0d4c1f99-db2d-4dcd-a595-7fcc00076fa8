package com.sankuai.dzviewscene.productshelf.vu.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 试听课货架内容
 * @auther: liweilong06
 * @date: 2023/6/18 下午12:38
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@MobileDo(id = 0xebaf)
public class DzEduTrialClassShelf implements Serializable {
    /**
     * 试听课横幅
     */
    @MobileField(key = 0x98ab)
    private DzEduTrialClassBar trialClassBar;

    /**
     * 货架弹窗
     */
    @MobileField(key = 0x1e1c)
    private DzEduTrialClassPopWindow popWindow;
}
