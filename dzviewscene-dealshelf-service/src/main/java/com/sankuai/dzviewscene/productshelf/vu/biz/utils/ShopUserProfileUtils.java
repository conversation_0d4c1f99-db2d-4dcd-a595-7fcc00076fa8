package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzcard.navigation.api.enums.CardTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.enums.CardHoldStatusEnums;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

public class ShopUserProfileUtils {

    public static int buildProfile(List<Integer> cardTypeList) {
        if (CollectionUtils.isEmpty(cardTypeList)) {
            return CardHoldStatusEnums.OFFLINE.getStatus();
        }
        if (cardTypeList.containsAll(Lists.newArrayList(CardTypeEnum.DISCOUNT_CARD.getCode(), CardTypeEnum.JOY_CARD.getCode()))) {
            return CardHoldStatusEnums.DISCOUNT__JOY_CARD.getStatus();
        }
        if (cardTypeList.contains(CardTypeEnum.DISCOUNT_CARD.getCode())) {
            return CardHoldStatusEnums.DISCOUNT_CARD.getStatus();
        }
        if (cardTypeList.contains(CardTypeEnum.JOY_CARD.getCode())) {
            return CardHoldStatusEnums.JOY_CARD.getStatus();
        }
        return CardHoldStatusEnums.OFFLINE.getStatus();
    }
}

