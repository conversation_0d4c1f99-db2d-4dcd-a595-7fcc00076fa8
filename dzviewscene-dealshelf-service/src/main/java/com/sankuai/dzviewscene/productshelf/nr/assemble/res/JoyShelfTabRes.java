package com.sankuai.dzviewscene.productshelf.nr.assemble.res;

import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.dzcard.joycard.navigation.api.dto.ProductShelfJoyCardDTO;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dztheme.deal.res.DealThemeDTO;
import lombok.Data;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Created by zhangsuping on 2019-08-03.
 */
@Data
public class JoyShelfTabRes {

    /**
     * 主题列表
     */
    private List<DealThemeDTO> dealThemes;

    /**
     * 导航标签
     */
    private List<NavTabRes> navTabResList;

    /**
     * 筛选导航ID
     */
    private long navIdSelected;

    /**
     * 商户信息
     */
    private ShopRes shopRes;

    /**
     * 货架tab
     */
    private ShelfTabRes shelfTabRes;

    /**
     * 卡的持有状态
     */
    private CardHoldStatusDTO cardHoldStatus;

    /**
     * 销量斗斛数据
     */
    private DouHuResponse saleDouhuResponse;

    /**
     * 购买按钮斗斛数据
     */
    private DouHuResponse buyDouhuResponse;

    /**
     * 推荐排序
     */
    private DouHuResponse sortDouHuResponse;

    /**
     * 副标题斗斛
     */
    private DouHuResponse subTitleDouHuResponse;

    /**
     * 无头图斗斛
     */
    private DouHuResponse noPicDouHuResponse;
}
