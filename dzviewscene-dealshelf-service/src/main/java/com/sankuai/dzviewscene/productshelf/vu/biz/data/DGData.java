package com.sankuai.dzviewscene.productshelf.vu.biz.data;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 团购数据
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019-08-03.
 */
@Data
public class DGData {
    /**
     * 点评团单ID
     */
    private int dpDGId;
    /**
     * 标题
     */
    private String title;
    /**
     * 是否为代金券
     */
    private boolean isUniversalDG;
    /**
     * 代金券类型
     * eg：足疗类目：充值套餐、单次代金券
     */
    private String cashType;
    /**
     * 服务类型
     */
    private List<String> projectTypes;

    /**
     * 子分类属性
     * get 方法废弃，请使用 querySubCategoryAdapt
     */
    @Deprecated
    private List<String> subCategories;

    /**
     * 从结构化数据中取到的子分类
     */
    private List<String> subCategoryFromStruct;

    /**
     * 兼容获取子分类，优先从结构化数据中取
     * @return
     */
    public List<String> querySubCategoryAdapt() {
        if (CollectionUtils.isNotEmpty(this.subCategoryFromStruct)) {
            return this.subCategoryFromStruct;
        }
        return subCategories;
    }
}
