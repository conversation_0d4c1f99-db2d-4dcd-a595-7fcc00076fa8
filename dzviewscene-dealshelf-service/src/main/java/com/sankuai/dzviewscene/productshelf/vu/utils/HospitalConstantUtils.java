package com.sankuai.dzviewscene.productshelf.vu.utils;

public interface HospitalConstantUtils {

    /**
     * 默认起始页
     */
    int DEFAULT_PAGE_NO = 1;

    /**
     * 默认从主题层调取的所有团单数目
     */
    int DEFAULT_PAGE_SIZE = 50;

    /**
     * 每页展示的团单数目
     */
    int PER_PAGE_SIZE = 5;

    /**
     * 医疗plan id
     */
    String DEAL_PLAN_ID = "10002163";

    /**
     * 医疗中含有子标签时Plan_id为DEAL_PLAN_ID_A的子标签名称
     */
    String SUB_TAB_WITH_DEAL_PLAN_ID_A = "全部";

    /**
     * 医疗默认团单展示类型：1：不带图，2：小图，3：大图
     */
    int DEAL_DEFAULT_SHOW_TYPE = 2;

    /**
     * 医疗中优惠信息列表放置位置：1:原价后，2：现价后：3：会员价后
     */
    int DZPROMOVO_POSITION = 1;

    /**
     * 美团侧医疗主标题图片url  团
     */
    String TITLE_PIC_URL_MT = "https://p0.meituan.net/dprainbow/5ddffebb9869b304da7a6bcb99c39a3e834.png";

    /**
     * 点评侧医疗主标题图片url  团
     */
    String TITLE_PIC_URL_DP = "https://p0.meituan.net/dprainbow/0ba09e1e1091096479c7417510efefe31359.png";

    /**
     * 美团侧选中"男科"时医疗主标题图片url  优
     */
    String TITLE_PIC_URL_MAN_MT = "https://p0.meituan.net/dprainbow/fa81aa2a5cc2b3fbf45f09b5d3dd8ac7929.png";
    /**
     * 点评侧选中"男科"时医疗主标题图片url  优
     */
    String TITLE_PIC_URL_MAN_DP = "https://p1.meituan.net/dprainbow/bd39abe991dd9d8aaebe86385a786a1d984.png";

    /**
     * 医疗主标题icon图片url 小对勾
     */
    String TITLE_ICON_PIC_URL = "https://p0.meituan.net/dprainbow/eb0dd38ce2ef531801c7282e7995bfe71262.png";

    /**
     * 男科医疗主标题icon图片url 绿色小对勾
     */
    String TITLE_ICON_PIC_URL_MAN = "https://p0.meituan.net/dprainbow/eb0dd38ce2ef531801c7282e7995bfe71262.png";

    /**
     * 医疗商品头图宽高比
     */
    double PRODUCT_PIC_ASPECT_RADIO = 1.0;

    /**
     * 图片宽度
     */
    int PIC_WIDTH_300 = 300;

    /**
     * 图片高度
     */
    int PIC_HEIGHT_300 = 300;

    /**
     * 医疗筛选项最少展示数量
     */
    int DEAl_FILTER_MIN_NUM = 2;

    /**
     * 医疗导航标签子标签最少展示数目
     */
    int DEAL_SUBTAB_MIN_NUM = 1;

    /**
     * 医疗导航标签主标题
     */
    String DEAL_NAV_TAB_TITLE = "团购";

    /**
     * 选中标签为"男科"时医疗导航标签主标题
     */
    String DEAL_NAV_TAB_TITLE_MAN = "优选服务";

    /**
     * 医疗导航标签icon标题1
     */
    String DEAL_NAV_TAB_ICON1_TITLE = "过期退";

    /**
     * 男科医疗导航标签icon标题1
     */
    String DEAL_NAV_TAB_ICON1_TITLE_MAN = "隐私保护";

    /**
     * 男科医疗导航标签icon大小
     */
    int DEAL_NAV_TAB_TEXT_SIZE = 1;

    /**
     * 医疗导航标签icon标题2
     */
    String DEAL_NAV_TAB_ICON2_TITLE = "随时退";

    /**
     * 医疗更多模块首部信息
     */
    String DEAL_MORE_HEAD ="查看更多";

    /**
     * 医疗更多模块尾部信息
     */
    String DEAL_MORE_TAIL = "个团购";

    /**
     * 医疗侧需特殊展示标签名称
     */
    String DEAL_SPECIAL_TAB_NAME = "男科";

}
