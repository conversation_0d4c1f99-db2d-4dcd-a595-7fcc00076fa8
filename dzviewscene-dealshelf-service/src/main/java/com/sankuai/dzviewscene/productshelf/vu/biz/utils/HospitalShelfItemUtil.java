package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import com.google.common.collect.Lists;
import com.meituan.carnation.corepath.enums.VCClientTypeEnum;
import com.sankuai.dzviewscene.productshelf.nr.assemble.res.FilterTabsRes;
import com.sankuai.dzviewscene.productshelf.nr.assemble.res.HospitalDealShelfRes;
import com.sankuai.dzviewscene.productshelf.vu.enums.FilterShowTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.RichLabelFrontWeightEnums;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.HospitalConstantUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.ap.internal.util.Collections;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * 医疗货架展示工具类
 */
public class HospitalShelfItemUtil {

    /**
     * 构造筛选标签和商品区信息
     * @param hospitalDealShelfRes 团单货架内容
     * @param platform
     * @param searchKeyword
     * @param sceneCode
     * @return 筛选标签和商品区信息
     */
    public static FilterBtnIdAndProAreasVO buildFilterBtnIdAndProAreasVO(HospitalDealShelfRes hospitalDealShelfRes, int platform, String searchKeyword, String sceneCode, List<DouHuM> douHuList) {
        FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO = new FilterBtnIdAndProAreasVO();
        long filterBtnId = getFilterBtnId(hospitalDealShelfRes.getFilterTabsResList());
        filterBtnIdAndProAreasVO.setFilterBtnId(filterBtnId);
        filterBtnIdAndProAreasVO.setProductAreas(Lists.newArrayList(buildProductAreaComponentVO(hospitalDealShelfRes, platform, searchKeyword,filterBtnId, douHuList)));
        filterBtnIdAndProAreasVO.setSceneCode(sceneCode);
        return filterBtnIdAndProAreasVO;
    }

    /**
     * 构造商品区信息
     * @param hospitalDealShelfRes 团单货架内容
     * @param searchKeyword
     * @return 商品区信息
     */
    public static ProductAreaComponentVO buildProductAreaComponentVO(HospitalDealShelfRes hospitalDealShelfRes, int platform, String searchKeyword, long filterId, List<DouHuM> douHuList) {
        //构造商品信息
        DzItemAreaComponentVO dzItemAreaComponentVO = new DzItemAreaComponentVO();
        List<DzItemVO> dzItemVOS = HospitalProductItemUtils.buildDzItemVOs(hospitalDealShelfRes.getProducts(), platform, searchKeyword, filterId, douHuList);
        dzItemAreaComponentVO.setDefaultShowNum(HospitalConstantUtils.PER_PAGE_SIZE);
        dzItemAreaComponentVO.setShowType(HospitalConstantUtils.DEAL_DEFAULT_SHOW_TYPE);
        dzItemAreaComponentVO.setProductItems(dzItemVOS);
        //构造more
        DzMoreComponentVO dzMoreComponentVO = buildDzMoreComponentVO(hospitalDealShelfRes);
        //构造标题
        TitleComponentVO titleComponentVO = buildTitle(hospitalDealShelfRes);
        //填充
        ProductAreaComponentVO productAreaComponentVO = new ProductAreaComponentVO();
        productAreaComponentVO.setItemArea(dzItemAreaComponentVO);
        productAreaComponentVO.setMore(dzMoreComponentVO);
        productAreaComponentVO.setTitle(titleComponentVO);
        return productAreaComponentVO;
    }

    /**
     * 构造标题模块
     *@param hospitalDealShelfRes 货架团单信息
     *@return 标题模块
     */
    public static TitleComponentVO buildTitle(HospitalDealShelfRes hospitalDealShelfRes) {
        TitleComponentVO titleComponentVO = new TitleComponentVO();
        long filterTabId = HospitalShelfItemUtil.getFilterBtnId(hospitalDealShelfRes.getFilterTabsResList());
        if(isSelectedFilterTabsResOfMan(hospitalDealShelfRes.getFilterTabsResList(),filterTabId)) {
            //男科时填充标题模块
            titleComponentVO = fillTitleComponentVO(hospitalDealShelfRes.getPlatform(), HospitalConstantUtils.DEAL_NAV_TAB_TITLE_MAN, HospitalConstantUtils.TITLE_PIC_URL_MT, HospitalConstantUtils.TITLE_PIC_URL_DP, HospitalConstantUtils.DEAL_NAV_TAB_ICON1_TITLE_MAN, HospitalConstantUtils.DEAL_NAV_TAB_ICON2_TITLE, HospitalConstantUtils.TITLE_ICON_PIC_URL_MAN);
        } else {
            //非男科时填充标题模块
            titleComponentVO = fillTitleComponentVO(hospitalDealShelfRes.getPlatform(), HospitalConstantUtils.DEAL_NAV_TAB_TITLE, HospitalConstantUtils.TITLE_PIC_URL_MT, HospitalConstantUtils.TITLE_PIC_URL_DP, HospitalConstantUtils.DEAL_NAV_TAB_ICON2_TITLE, HospitalConstantUtils.DEAL_NAV_TAB_ICON1_TITLE, HospitalConstantUtils.TITLE_ICON_PIC_URL);
        }
        return titleComponentVO;
    }

    /**
     * 判断选中的标签是不是"男科"
     * @param filterTabsResList 标签列表
     * @param filterTabId 选中的标签id
     * @return 选中的标签是否为"男科"
     */
    private static boolean isSelectedFilterTabsResOfMan(List<FilterTabsRes> filterTabsResList,long filterTabId) {
        if(filterTabsResList == null || filterTabsResList.size() == 0) {
            return false;
        }
        for(FilterTabsRes filterTabsRes : filterTabsResList) {
            if(filterTabsRes.getId() == filterTabId && filterTabsRes.getName() == HospitalConstantUtils.DEAL_SPECIAL_TAB_NAME) {
                return true;
            }
            if(!CollectionUtils.isEmpty(filterTabsRes.getChildTabs()) && isSelectedFilterTabsResOfMan(filterTabsRes.getChildTabs(),filterTabId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 填充标题模块
     *@param platform 货架所属平台
     *@param title 团单名称
     *@param titleUrlMT 美团侧团单图片url
     *@param iconName1 第一个icon名称
     *@param iconUrl icon图片对于url
     *@return 标题模块
     */
    public static TitleComponentVO fillTitleComponentVO(int platform, String title, String titleUrlMT, String titleUrlDP, String iconName1, String iconName2, String iconUrl ) {
        TitleComponentVO titleComponentVO = new TitleComponentVO();
        titleComponentVO.setTitle(title);
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            titleComponentVO.setIcon(titleUrlMT);
        } else {
            titleComponentVO.setIcon(titleUrlDP);
        }
        List<IconRichLabelVO> iconRichLabelVOS = new ArrayList<>();
        IconRichLabelVO iconRichLabelVO1 = new IconRichLabelVO();
        iconRichLabelVO1.setText(new RichLabelVO(HospitalConstantUtils.DEAL_NAV_TAB_TEXT_SIZE,ColorUtils.color777777,iconName1));//随时退
        iconRichLabelVO1.setIcon(HospitalConstantUtils.TITLE_ICON_PIC_URL);//对勾url
        iconRichLabelVOS.add(iconRichLabelVO1);
        IconRichLabelVO iconRichLabelVO2 = new IconRichLabelVO();
        iconRichLabelVO2.setText(new RichLabelVO(HospitalConstantUtils.DEAL_NAV_TAB_TEXT_SIZE,ColorUtils.color777777,iconName2));//过期退
        iconRichLabelVO2.setIcon(iconUrl);//对勾url
        iconRichLabelVOS.add(iconRichLabelVO2);
        titleComponentVO.setTags(iconRichLabelVOS);
        return titleComponentVO;
    }

    /**
     * 构造more模块
     *@param hospitalDealShelfRes 团单货架内容
     *@return more模块
     */
    public static DzMoreComponentVO buildDzMoreComponentVO(HospitalDealShelfRes hospitalDealShelfRes) {
        DzMoreComponentVO dzMoreComponentVO = new DzMoreComponentVO();
        int totalCount = getSelectedTabTotalCountWithDefault(hospitalDealShelfRes.getFilterTabsResList());
        if(totalCount > HospitalConstantUtils.PER_PAGE_SIZE) {
            dzMoreComponentVO.setText(buildMoreText(getSelectedTabTotalCountWithDefault(hospitalDealShelfRes.getFilterTabsResList())));
        }
        return dzMoreComponentVO;
    }

    /**
     *获取tab列表中展示的tab下的团单数目
     *@param filterTabs 导航标签列表
     *@return 被展示的标签下的商品总数
     */
    public static int getSelectedTabTotalCountWithDefault(List<FilterTabsRes> filterTabs){
        if(getSelectedTabTotalCountRecursively(filterTabs) >= 0){
            return getSelectedTabTotalCountRecursively(filterTabs);
        }
        return getFirstTabTotalCountRecursively(filterTabs);
    }

    private static int getFirstTabTotalCountRecursively(List<FilterTabsRes> filterTabs) {
        if(CollectionUtils.isEmpty(filterTabs)) {
            return -1;
        }
        if (getFirstTabTotalCountRecursively(Collections.first(filterTabs).getChildTabs()) >= 0 ){
            return getFirstTabTotalCountRecursively(Collections.first(filterTabs).getChildTabs());
        }
        return Collections.first(filterTabs).getTotalCount();
    }

    private static int getSelectedTabTotalCountRecursively(List<FilterTabsRes> filterTabs){
        if(CollectionUtils.isEmpty(filterTabs)){
            return -1;
        }
        for(FilterTabsRes filterTab : filterTabs) {
            if(getSelectedTabTotalCountRecursively(filterTab.getChildTabs()) >= 0) {
                return getSelectedTabTotalCountRecursively(filterTab.getChildTabs());
            }
            if(filterTab.isSelected()) {
                return filterTab.getTotalCount();
            }
        }
        return -1;
    }

    private static String buildMoreText(int productsNum) {
        return HospitalConstantUtils.DEAL_MORE_HEAD + (productsNum - HospitalConstantUtils.PER_PAGE_SIZE) + HospitalConstantUtils.DEAL_MORE_TAIL;
    }

    /**
     * 构造导航标签信息
     * @param filterTabsResList 团单货架内容
     * @return 导航标签信息
     */
    public static FilterComponentVO buildFilterComponent(List<FilterTabsRes> filterTabsResList, int platform) {
        FilterComponentVO filterComponentVO = new FilterComponentVO();
        filterComponentVO.setShowType(FilterShowTypeEnums.FIRST_UNDERLINE_SECONDE_CIRCULAR.getType());
        filterComponentVO.setMinShowNum(HospitalConstantUtils.DEAl_FILTER_MIN_NUM);
        filterComponentVO.setFilterBtns(buildFilterBtns(filterTabsResList, platform));
        return filterComponentVO;
    }

    private static List<FilterButtonVO> buildFilterBtns(List<FilterTabsRes> filterTabsResList, int platform) {
        if (CollectionUtils.isEmpty(filterTabsResList)) {
            return null;
        }
        List<FilterButtonVO> filterButtonVOS = new ArrayList<>();
        filterTabsResList.forEach(new Consumer<FilterTabsRes>() {
            @Override
            public void accept(FilterTabsRes filterTabsRes) {
                FilterButtonVO filterButtonVO = new FilterButtonVO();
                filterButtonVO.setTitle(buildTabTitle(filterTabsRes.getName()));
                filterButtonVO.setSelectedTitle(buildSelectedTabTitle(filterTabsRes.getName()));
                filterButtonVO.setFilterBtnId(filterTabsRes.getId());
                filterButtonVO.setSelected(filterTabsRes.isSelected());
                filterButtonVO.setChildrenMinShowNum(HospitalConstantUtils.DEAL_SUBTAB_MIN_NUM);
                if(filterTabsRes.getChildTabs()!=null){
                    filterButtonVO.setChildren(buildChildrenFilterComponent(filterTabsRes.getChildTabs(), platform));
                }
                filterButtonVOS.add(filterButtonVO);
            }
        });
        return filterButtonVOS;
    }

    private static RichLabelVO buildSelectedTabTitle(String name) {
        if(StringUtils.isNotEmpty(name) && name.equals(PerfectActivityBuildUtils.getActivityName())) {
            return new RichLabelVO(StringUtils.EMPTY, PerfectActivityBuildUtils.getSelectedFilterSize(), PerfectActivityBuildUtils.getSelectedFilterColor(), name);
        }
        return new RichLabelVO(StringUtils.EMPTY, 0, StringUtils.EMPTY, name);
    }

    private static RichLabelVO buildTabTitle(String name) {
        if(StringUtils.isNotEmpty(name) && name.equals(PerfectActivityBuildUtils.getActivityName())) {
            return new RichLabelVO(StringUtils.EMPTY, PerfectActivityBuildUtils.getFilterSize(), PerfectActivityBuildUtils.getFilterColor(), name);
        }
        return new RichLabelVO(StringUtils.EMPTY, 0, StringUtils.EMPTY, name);
    }

    /**
     * 构造子导航标签
     * @param filterTabsResList 子导航标签内容
     * @return 子导航标签
     */
    public static List<FilterButtonVO> buildChildrenFilterComponent(List<FilterTabsRes> filterTabsResList, int platform) {

        List<FilterButtonVO> childrenFilterButtonVOS = new ArrayList<>();
        filterTabsResList.forEach(new Consumer<FilterTabsRes>() {
            @Override
            public void accept(FilterTabsRes filterTabsRes) {
                FilterButtonVO filterButtonVO = new FilterButtonVO();
                filterButtonVO.setTitle(buildChildTabTitle(filterTabsRes.getName(), filterTabsRes.isSelected(), platform));
                filterButtonVO.setFilterBtnId(filterTabsRes.getId());
                filterButtonVO.setSelected(filterTabsRes.isSelected());
                childrenFilterButtonVOS.add(filterButtonVO);
            }
        });
        return childrenFilterButtonVOS;
    }

    private static RichLabelVO buildChildTabTitle(String name, boolean isSelected, int platform) {
        if(name.equals("玩美季")) {
            return new RichLabelVO(RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front14, ColorUtils.colorFF4100, name);
        }
        if(VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            if (isSelected) {
                return new RichLabelVO(RichLabelFrontWeightEnums.BOLD.getType(), FrontSizeUtils.front14, ColorUtils.color222222, name);
            }
            return new RichLabelVO(RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front14, ColorUtils.color646464, name);
        }
        if (isSelected) {
            return new RichLabelVO(RichLabelFrontWeightEnums.BOLD.getType(), FrontSizeUtils.front14, ColorUtils.colorFF6633, name);
        }
        return new RichLabelVO(RichLabelFrontWeightEnums.NORMAL.getType(), FrontSizeUtils.front14, ColorUtils.color111111, name);
    }

    /**
     * 设置打点
     * @return 打点信息
     */
    public static ShelfOceanVO buildOcean(){
        ShelfOceanVO oceanVO = new ShelfOceanVO();
        DzShelfOceanEntryVO firstTabOceanEntryVO = new DzShelfOceanEntryVO();
        firstTabOceanEntryVO.setBidClick("b_22jpfeg0");
        firstTabOceanEntryVO.setBidView("b_tifz7k5h");
        firstTabOceanEntryVO.setCategory("cosmetology");
        DzShelfOceanEntryVO childTabOceanEntryVO = new DzShelfOceanEntryVO();
        childTabOceanEntryVO.setBidClick("b_gv8tgk3k");
        childTabOceanEntryVO.setBidView("b_ny4s5ld0");
        childTabOceanEntryVO.setCategory("cosmetology");
        DzShelfOceanEntryVO moreOceanEntryVO = new DzShelfOceanEntryVO();
        moreOceanEntryVO.setBidClick("b_uvm18ncb");
        moreOceanEntryVO.setBidView("b_lbjnl50o");
        moreOceanEntryVO.setCategory("cosmetology");
        DzShelfOceanEntryVO productOceanEntryVO = new DzShelfOceanEntryVO();
        productOceanEntryVO.setBidClick("b_9a31vccu");
        productOceanEntryVO.setBidView("b_rjhc51m5");
        productOceanEntryVO.setCategory("cosmetology");

        DzShelfOceanEntryVO activityPromoPopViewButtonOceanEntryVO = new DzShelfOceanEntryVO();
        activityPromoPopViewButtonOceanEntryVO.setBidClick("b_gc_f9o3r1fb_mc");
        activityPromoPopViewButtonOceanEntryVO.setCategory("gc");

        DzShelfOceanEntryVO activityPromoPopViewOceanEntryVO = new DzShelfOceanEntryVO();
        activityPromoPopViewOceanEntryVO.setBidView("b_gc_b2zp3hkj_mv");
        activityPromoPopViewOceanEntryVO.setCategory("gc");

        DzShelfOceanEntryVO activityPromoPopViewCloseOceanEntryVO = new DzShelfOceanEntryVO();
        activityPromoPopViewCloseOceanEntryVO.setBidClick("b_gc_gf3tzzka_mc");
        activityPromoPopViewCloseOceanEntryVO.setCategory("gc");

        DzShelfOceanEntryVO promoPopView = new DzShelfOceanEntryVO();
        promoPopView.setBidView("b_gc_8bd2y70k_mv");
        promoPopView.setCategory("gc");

        DzShelfOceanEntryVO promoPopViewCloseOceanEntryVO = new DzShelfOceanEntryVO();
        promoPopViewCloseOceanEntryVO.setBidClick("b_gc_y42m8ed2_mc");
        promoPopViewCloseOceanEntryVO.setCategory("gc");

        oceanVO.setActivityPromoPopViewButton(activityPromoPopViewButtonOceanEntryVO);
        oceanVO.setActivityPromoPopView(activityPromoPopViewOceanEntryVO);
        oceanVO.setActivityPromoPopViewClose(activityPromoPopViewCloseOceanEntryVO);
        oceanVO.setPromoPopView(promoPopView);
        oceanVO.setPromoPopViewClose(promoPopViewCloseOceanEntryVO);
        oceanVO.setFilterBar(firstTabOceanEntryVO);
        oceanVO.setFilterBar(firstTabOceanEntryVO);
        oceanVO.setMore(moreOceanEntryVO);
        oceanVO.setProductItem(productOceanEntryVO);
        oceanVO.setChildrenFilterBar(childTabOceanEntryVO);
        return oceanVO;
    }

    /**
     * 获取选中的标签id：当有选中标签时返回相应id，无选中时返回第一个标签及第一个子（存在时）标签id
     * @param filterTabsResList 标签列表
     * @return 选中的标签id
     */
    public static long getFilterBtnId(List<FilterTabsRes> filterTabsResList) {
        if(CollectionUtils.isEmpty(filterTabsResList)) {
            return -1;
        }
        FilterTabsRes selectedFirstLevelTab = getSelectedTab(filterTabsResList);
        if(selectedFirstLevelTab == null) {
            return getDefaultTabId(getFirstTab(filterTabsResList));
        }
        if(CollectionUtils.isEmpty(selectedFirstLevelTab.getChildTabs())) {
            return selectedFirstLevelTab.getId();
        }
        FilterTabsRes selectedSecondLevelTab = getSelectedTab(selectedFirstLevelTab.getChildTabs());
        if(selectedSecondLevelTab == null) {
            getFirstTab(selectedFirstLevelTab.getChildTabs()).setSelected(true);
            return getFirstTab(selectedFirstLevelTab.getChildTabs()).getId();
        }
        return selectedSecondLevelTab.getId();
    }

    private static long getDefaultTabId(FilterTabsRes filterTabsRes) {
        if(CollectionUtils.isEmpty(filterTabsRes.getChildTabs())) {
            filterTabsRes.setSelected(true);
            return filterTabsRes.getId();
        }
        filterTabsRes.setSelected(true);
        getFirstTab(filterTabsRes.getChildTabs()).setSelected(true);
        return getFirstTab(filterTabsRes.getChildTabs()).getId();
    }

    private static FilterTabsRes getFirstTab(List<FilterTabsRes> filterTabsResList) {
        return filterTabsResList.get(0);
    }

    private static FilterTabsRes getSelectedTab(List<FilterTabsRes> filterTabsResList) {
        for(FilterTabsRes filterTabsRes : filterTabsResList) {
            if(filterTabsRes.isSelected()) {
                return filterTabsRes;
            }
        }
        return null;
    }
}
