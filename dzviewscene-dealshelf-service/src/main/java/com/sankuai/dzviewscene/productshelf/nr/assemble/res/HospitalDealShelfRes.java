package com.sankuai.dzviewscene.productshelf.nr.assemble.res;

import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.List;

@Data
public class HospitalDealShelfRes {
    //导航标签信息
    private List<FilterTabsRes> filterTabsResList;
    //导航标签栏被选中的标签下所有商品的信息，包括商品id和对应扽例如头图等信息
    private List<ProductM> products;
    private int shopId;
    private long shopIdL;
    private int platform;
    private List<DouHuM> douHuMList;
}
