package com.sankuai.dzviewscene.productshelf.vu.vo.edudetail;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 货架落地页
 * @auther: liweilong06
 * @date: 2020/7/31 11:35 上午
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DzShelfComponentVO implements Serializable {

    /**
     * 筛选栏，不带商品分类
     */
    private List<FilterButtonVO> filterBtns;

    /**
     * 当前商品类型下的商品列表
     */
    private List<ProductItemVO> productItems;

    /**
     * 商品聚合属性，例如适合年龄：0-18月
     */
    private List<AttrVO> attrs;

    /**
     * 当前筛选项下所有团购数量
     */
    private int currentFilterTotalDealNum;

    /**
     * 当前筛选项下所有课程数量
     */
    private int currentFilterTotalClassNum;

    /**
     * 该商户下所有商品数量（课程+团购）
     */
    private int totalProductNum;

    private boolean hasNextPage;

}
