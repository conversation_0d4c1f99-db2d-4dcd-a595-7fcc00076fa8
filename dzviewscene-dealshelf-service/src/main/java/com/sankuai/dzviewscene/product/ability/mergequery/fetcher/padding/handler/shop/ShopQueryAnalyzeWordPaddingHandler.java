package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.intention.service.enums.ClientFeatureEnum;
import com.sankuai.dzviewscene.intention.service.req.ClientEnv;
import com.sankuai.dzviewscene.intention.service.req.IntentionRecognizeRequest;
import com.sankuai.dzviewscene.intention.service.res.IntentionEntityDTO;
import com.sankuai.dzviewscene.intention.service.res.IntentionRecognizeResult;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.medicalbeauty.ProductPlatformUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class ShopQueryAnalyzeWordPaddingHandler implements ContextPaddingHandler {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ContextHandlerResult> padding(ActivityCxt ctx, ContextHandlerResult contextHandlerResult, Map<String, Object> params) {
        String searchKeyword = ParamsUtil.getStringSafely(ctx, ShelfActivityConstants.Params.searchKeyword);
        if (StringUtils.isEmpty(searchKeyword)) {
            return CompletableFuture.completedFuture(null);
        }
        IntentionRecognizeRequest request = buildIntentionRecognizeRequest(ctx);
        CompletableFuture<IntentionRecognizeResult> recognizeFuture = compositeAtomService.recognize(request);
        return recognizeFuture.thenApply(recognize -> {
            if (recognize == null || CollectionUtils.isEmpty(recognize.getIntentions())) {
                return contextHandlerResult;
            }
            List<IntentionEntityDTO> entities = recognize.getIntentions().get(0).getEntities();
            if (CollectionUtils.isEmpty(entities)) {
                return contextHandlerResult;
            }
            List<String> recognizeList = entities.stream().filter(Objects::nonNull).map(IntentionEntityDTO::getEntityId).collect(Collectors.toList());
            fillContextHandlerResult(recognizeList, contextHandlerResult);
            return contextHandlerResult;
        });
    }

    private IntentionRecognizeRequest buildIntentionRecognizeRequest(ActivityCxt activityContext) {
        double lat = ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lat);
        double lng = ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lng);
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        long userId = PlatformUtil.isMT(platform) ? ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtUserId) : ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpUserId);
        int cityId = PlatformUtil.isMT(platform) ? ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtCityId) : ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId);
        int locationCityId = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.locationCityId);
        String deviceId = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.deviceId);
        String unionId = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.unionId);
        int clientType = ProductPlatformUtils.getClientType(activityContext.getParam(ShelfActivityConstants.Params.userAgent), activityContext.getParam(ShelfActivityConstants.Params.clientType));
        String appVersion = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.appVersion);
        int clientIOS = buildClientIOS(activityContext.getParam(ShelfActivityConstants.Params.clientType));
        String searchKeyword = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.searchKeyword);
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setCityId(cityId);
        clientEnv.setLocatedCityId(locationCityId);
        clientEnv.setPlatform(platform);
        clientEnv.setLng(lng);
        clientEnv.setLat(lat);
        clientEnv.setDeviceId(deviceId);
        clientEnv.setUnionId(unionId);
        clientEnv.setUserId(userId);
        clientEnv.setClientVersion(appVersion);
        clientEnv.setClientType(clientType);
        clientEnv.setClientOS(clientIOS);

        Map<String, Object> clientFeatures = Maps.newHashMap();
        clientFeatures.put(ClientFeatureEnum.searchKeyword.code, searchKeyword);
        IntentionRecognizeRequest request = new IntentionRecognizeRequest();
        request.setClientEnv(clientEnv);
        request.setClientFeatures(clientFeatures);
        request.setBizCode("keyword_segment_intention");

        return request;
    }

    private int buildClientIOS(String client) {
        if ("ios".equals(client)) {
            return 1;
        }
        if ("android".equals(client)) {
            return 2;
        }
        return 0;
    }

    public void fillContextHandlerResult(List<String> recognizeList, ContextHandlerResult contextHandlerResult) {
        if (CollectionUtils.isEmpty(recognizeList)) {
            return;
        }
        contextHandlerResult.setRecognizeList(recognizeList);
    }

}
