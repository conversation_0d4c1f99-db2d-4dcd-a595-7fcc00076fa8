package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.ocean;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemOceanLabsVP;

@VPointOption(name = "空变化点-返回 null", description = "", code = "UnifiedShelfItemNullOceanLabsOpt")
public class UnifiedShelfItemNullOceanLabsOpt extends UnifiedShelfItemOceanLabsVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
