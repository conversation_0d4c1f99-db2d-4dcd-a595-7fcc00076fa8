package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.AbstractPriceBottomTagBuildStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.springframework.stereotype.Component;

@Component
public class PriceProtectionTagBuildStrategy extends AbstractPriceBottomTagBuildStrategy {

    @Override
    public String getName() {
        return "价保";
    }

    @Override
    public DzTagVO buildTag(PriceBottomTagBuildReq req) {
        int clientType = ParamsUtil.getIntSafely(req.getContext(), ShelfActivityConstants.Params.userAgent);
        //非app不展示
        if(!PlatformUtil.isApp(clientType)){
            return null;
        }
        DzTagVO dzTagVO = PriceDisplayUtils.buildPriceProtectionTag(req.getProductM(), req.getPlatform());
        addComparePriceTagAttr(req.getProductM(), dzTagVO);
        return dzTagVO;
    }

    public boolean simplifyRemoveTag(){
        return true;
    }
}