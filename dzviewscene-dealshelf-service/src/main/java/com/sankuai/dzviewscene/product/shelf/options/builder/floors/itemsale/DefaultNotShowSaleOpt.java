package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSaleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/08/16
 */
@VPointOption(
        name = "默认实现-不展示销量",
        description = "默认实现-不展示销量",
        code = "DefaultNotShowSaleOpt")
public class DefaultNotShowSaleOpt extends ItemSaleVP<DefaultNotShowSaleOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        return StringUtils.EMPTY;
    }

    @VPointCfg
    @Data
    public static class Config {

    }
}
