package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.extra;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfExtraVP;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;


@VPointOption(name = "筛选项-配置上下文扩展信息",
        description = "筛选项扩展信息，附加上下文参数信息，默认opt",
        code = "UnifiedShelfConfigFilterExtraOpt",
        isDefault = true)
public class UnifiedShelfConfigFilterExtraOpt extends UnifiedShelfExtraVP<UnifiedShelfConfigFilterExtraOpt.Config> {

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        JSONObject extra = new JSONObject();
        extractMustParameters(context, extra);
        if (CollectionUtils.isNotEmpty(config.getContextKeys())) {
            for (String contextKey : config.getContextKeys()) {
                Object contextValue = context.getParam(contextKey);
                if (contextValue != null) {
                    extra.put(contextKey, contextValue);
                }
            }
        }
        return !extra.isEmpty() ? extra.toString() : null;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 上下文参数key
         */
        private List<String> contextKeys;
    }
}
