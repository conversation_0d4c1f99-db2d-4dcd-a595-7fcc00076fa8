package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: wuwen<PERSON><PERSON>
 * @create: 2024-07-04
 * @description:
 */
@VPointOption(name = "可配置商品标签",
        description = "可配置商品标签",
        code = ConfigurableProductTagsOpt.CODE)
public class ConfigurableProductTagsOpt extends ProductTagsVP<ConfigurableProductTagsOpt.Config> {

    public static final String CODE = "ConfigurableProductTagsOpt";

    @Override
    public List<String> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getAttrKeys())) {
            return Lists.newArrayList();
        }
        // 如果设定模板，则返回模板化的属性
        // 没有设定模板则直接返回当前属性
        return config.getAttrKeys().stream().map(attrKey -> buildTemplateAttrVal(param.getProductM(), attrKey, config))
                .filter(attrVal -> StringUtils.isNotBlank(attrVal)).collect(Collectors.toList());
    }

    private String getAttrVal(ProductM productM, String attrKey) {
        if (productM == null || StringUtils.isEmpty(attrKey)) {
            return null;
        }
        String attrVal = productM.getAttr(attrKey);
        if (StringUtils.isNotBlank(attrVal)) {
            return attrVal;
        }
        return productM.getObjAttr(attrKey);
    }

    private String buildTemplateAttrVal(ProductM productM, String attrKey, Config config) {
        String attrVal = getAttrVal(productM, attrKey);
        if (StringUtils.isBlank(attrVal) || StringUtils.isBlank(attrKey)
                || MapUtils.isEmpty(config.getAttrsTemplate())
                || !config.getAttrsTemplate().containsKey(attrKey)) {
            return attrVal;
        }
        String template = config.getAttrsTemplate().get(attrKey);
        if (StringUtils.isBlank(template)) {
            return attrVal;
        }
        return String.format(template, attrVal);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 需要展示属性列表
         */
        private List<String> attrKeys;

        /**
         * 属性展示模板
         */
        private Map<String, String> attrsTemplate;

    }
}
