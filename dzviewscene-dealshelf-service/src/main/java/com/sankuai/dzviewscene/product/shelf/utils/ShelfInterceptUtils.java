package com.sankuai.dzviewscene.product.shelf.utils;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;

public class ShelfInterceptUtils {

    public static void setInterceptContext(ActivityCxt ctx) {
        ctx.addParam(ShelfActivityConstants.Params.interceptShelf, true);
    }

    public static boolean checkIntercept(ActivityCxt ctx) {
        return ParamsUtil.getBooleanSafely(ctx.getParameters(), ShelfActivityConstants.Params.interceptShelf);
    }

    public static boolean checkSkipFilter(ActivityCxt ctx) {
        return ParamsUtil.getBooleanSafely(ctx.getParameters(), ShelfActivityConstants.Params.skipFilter);
    }

}


