package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemextra;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemExtraVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.utils.ItemExtraUtil;
import com.sankuai.dzviewscene.product.shelf.utils.DealShanKaiAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/2/14
 */
@VPointOption(name = "配置驱动填充 Json 类型的Extra",
        description = "",
        code = "PaddingJsonItemExtraOpt")
public class PaddingJsonItemExtraOpt extends ItemExtraVP<PaddingJsonItemExtraOpt.Config> {

    public static final String DEAL_CATEGORY_ID_KEY = "dealCategoryId";
    public static final String DEAL_CATEGORY_NAME_KEY = "dealCategoryName";
    public static final String DEAL_SALE_NUM_KEY = "dealSaleNum";

    @Override
    public Map<String, Object> buildExtraMap(ActivityCxt activityCxt, Param param, Config config) {
        Map<String, Object> extraMap = new HashMap<>();
        if (config.isPaddingDealCategoryId()) {
            extraMap.put(DEAL_CATEGORY_ID_KEY, param.getProductM().getCategoryId());
        }
        if (config.isPaddingDealCategoryName() && StringUtils.isNotBlank(param.getProductM().getCategoryName())) {
            extraMap.put(DEAL_CATEGORY_NAME_KEY, param.getProductM().getCategoryName());
        }
        addAttrs(extraMap, config, param.getProductM());
        addSaleNum(extraMap, config, param.getProductM());
        ItemExtraUtil.addUseResUrlTag(param.getProductM(), extraMap);

        if(StringUtils.isNotEmpty(config.getExtra())){
            // 目前只有一个场景在用，并且只返回跳链标记
            return JSON.parseObject(config.getExtra());
        }
        if (MapUtils.isNotEmpty(config.getAppendMap())) {
            extraMap.putAll(config.getAppendMap());
        }
        return extraMap;
    }

    private void addSaleNum(Map<String, Object> extraMap, Config config, ProductM productM) {
        if (!config.isPaddingSaleNum() || productM.getSale() == null) {
            return;
        }
        extraMap.put(DEAL_SALE_NUM_KEY, productM.getSale().getSale());
    }

    private void addAttrs(Map<String, Object> extraMap, Config config, ProductM productM) {
        if (CollectionUtils.isEmpty(config.getProductAttrKeys())) {
            return;
        }
        for (String attrKey : config.getProductAttrKeys()) {
            String attrValue = productM.getAttr(attrKey);
            if (StringUtils.isBlank(attrValue)) {
                continue;
            }
            extraMap.put(attrKey, attrValue);
        }
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 填充团单类目id
         */
        private boolean paddingDealCategoryId;

        /**
         * 填充团单类目名称
         */
        private boolean paddingDealCategoryName;

        private boolean paddingSaleNum;

        private List<String> productAttrKeys;

        /**
         * 旧版配置化的extra属性，如果配置了该属性，优先返回
         */
        @Deprecated
        private String extra;

        /**
         * 配置化的extra属性，如果配置了该属性，优先返回
         */
        private Map<String, String> appendMap;
    }
}
