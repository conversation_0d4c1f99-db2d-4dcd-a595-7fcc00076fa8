package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import com.sankuai.haima.admin.common.util.GsonUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/11
 */
@VPointOption(name = "医疗-副标题构造",
        description = "医疗副标题构造的是富文本，内部通过 SceneCode 路由",
        code = "MedicalItemProductTagsOpt")
public class MedicalItemProductTagsOpt extends ItemProductTagsVP<MedicalItemProductTagsOpt.Config> {
    private static final String DEFAULT_COLOR = "#777777";
    private static final String RESV_TAG_COLOR = "#FF6633";

    private static final int DEFAULT_FONT = 11;

    private static final String DEAL_STRUCT_CONTENTT_ATTR_NAME = "dealStructContent";
    private static final String PRODUCT_TYPE = "productType";
    public static final String VOUCHER_USING_OF_LIMIT = "voucher_limit_of_using_2";
    private static final String MEDICAL_BOOKINGTAG = "medicalBookingTag";
    private static final String MEDICAL_BOOKABLE = "medicalBookable";
    private static final int VACCINE_CATEGORY_ID = 1611;

    private static final String CHECK_TAG = "100208251";

    private static final String CHECK_TAG_ATTR_KEY = "dentistryPlantCheckTag";

    private static final String DENTAL_PRODUCT_TAGS_ATTR = "dentalProductTagsAttr";


    @Override
    public List<String> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        //齿科
        List<String> medicalShowcaseDentals = Optional.ofNullable(config)
                .flatMap(c -> Optional.ofNullable(c.getMedicalShowcaseDental()))
                .orElse(Lists.newArrayList());
        if (!ObjectUtils.isEmpty(context.getSceneCode()) && medicalShowcaseDentals.contains(context.getSceneCode())) {
            // 超级团购处理
            if(ProductMAttrUtils.superSpu(productM)){
                List<String> superDealProductTags = getSuperDealProductTags(productM);
                if (CollectionUtils.isNotEmpty(superDealProductTags)) {
                    return superDealProductTags;
                }
            }
            //标品-不展示productTag
            if (productM.getProductType() == 4) {
                return Lists.newArrayList();
            }
            //种植牙-检查单
            if (isPlantCheck(productM)) {
                return buildImplantCheckTag(productM);
            }
            // 种植牙副标题逻辑
            List<String> encodeTags = new ArrayList<>();
            if (Objects.equals(productM.getAttr("serviceType"), "种植牙")) {
                addTagIfNoNull(encodeTags, encodeTag(productM.getAttr("dentistryPlantTag")));
                if (CollectionUtils.isNotEmpty(encodeTags)) {
                    return encodeTags;
                }
            }
            return getNewDentalTags(productM);
        }
        //眼科-标准化团单(翻单)
        //属于1604且server_type在范围内且包括服务流程字段
        //同时属于灰度范围内
        DealDetailDtoModel model = getDealDetailModel(productM);
        if (isUpgradeOphDeal(productM, model, param, context, config)) {
            return buildOphTags(productM, model, param, context, config);
        }
        if (isUpgradeGlassesDeal(productM, model, config)) {
            return buildTags(productM, model);
        }
        // 医疗默认
        return buildDefaultTag(productM, context, config);
    }

    public List<String> getSuperDealProductTags(ProductM productM) {
        if(Objects.isNull(productM) || Objects.isNull(productM.getSpuM())){
            return Lists.newArrayList();
        }
        return productM.getSpuM().getSpuKeyInformation();
    }

    private List<String> buildImplantCheckTag(ProductM productM) {
        return Optional.ofNullable(productM.getAttr(CHECK_TAG_ATTR_KEY)).map(attr -> JsonCodec.decode(attr, new TypeReference<List<String>>() {
                }))
                .orElse(Lists.newArrayList()).stream().filter(v -> !ObjectUtils.isEmpty(v))
                .map(this::encodeTag).collect(Collectors.toList());
    }

    private boolean isPlantCheck(ProductM productM) {
        TagM tag = Optional.ofNullable(productM.getProductTagList()).orElse(Lists.newArrayList()).stream()
                .filter(tagM -> !ObjectUtils.isEmpty(tagM) && Objects.equals(CHECK_TAG, tagM.getId())).findFirst().orElse(null);
        return !ObjectUtils.isEmpty(tag);
    }

    private boolean isUpgradeGlassesDeal(ProductM productM, DealDetailDtoModel model, Config config) {
        if (Objects.isNull(config) || Objects.isNull(config.getGlassConfig()) || Objects.isNull(config.getGlassConfig().ophMatchCondition)) {
            return false;
        }
        String serviceType = productM.getAttr("service_type");
        MedicalDealMatchCondition condition = config.getGlassConfig().ophMatchCondition;
        boolean matchC = CollectionUtils.isEmpty(condition.getCategoryIds()) || condition.getCategoryIds().contains(productM.getCategoryId());
        boolean matchServiceType = CollectionUtils.isEmpty(condition.getServiceTypes()) || condition.getServiceTypes().contains(serviceType);
        boolean hasAttr = CollectionUtils.isEmpty(condition.getMustHasAttr()) ||
                condition.getMustHasAttr().stream().allMatch(r -> productM.getAttr(r) != null);
        boolean hasSkuAttr = false;
        if (CollectionUtils.isNotEmpty(condition.getMustHasSkuAttr())) {
            if (model != null && model.getSkuUniStructuredDto() != null
                    && CollectionUtils.isNotEmpty(model.getSkuUniStructuredDto().getMustGroups())
                    && CollectionUtils.isNotEmpty(model.getSkuUniStructuredDto().getMustGroups().get(0).getSkuItems())
            ) {
                List<String> skuAttrList = model.getSkuUniStructuredDto().getMustGroups().get(0).getSkuItems().get(0).getAttrItems()
                        .stream().map(SkuAttrItemDto::getAttrName).collect(Collectors.toList());
                hasSkuAttr = skuAttrList.containsAll(condition.getMustHasSkuAttr());
            }
        } else {
            hasSkuAttr = true;
        }
        boolean matchAttrValue = MapUtils.isEmpty(condition.getMatchAttrValue())
                || condition.matchAttrValue.entrySet().stream().allMatch(en -> CollectionUtils.isEmpty(en.getValue())
                || en.getValue().contains(productM.getAttr(en.getKey()))
        );
        return matchC && matchServiceType && hasAttr && hasSkuAttr && matchAttrValue;
    }

    public List<String> buildTags(ProductM productM, DealDetailDtoModel model) {
        if (model == null || model.getSkuUniStructuredDto() == null) {
            return null;
        }
        //获取所有N选1服务项目
        List<SkuItemDto> optionalSkuList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(model.getSkuUniStructuredDto().getOptionalGroups())) {
            model.getSkuUniStructuredDto().getOptionalGroups().forEach(group -> {
                if (CollectionUtils.isEmpty(group.getSkuItems())) {
                    return;
                }
                optionalSkuList.addAll(group.getSkuItems());
            });

        }
        List<SkuItemDto> mustSkuList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(model.getSkuUniStructuredDto().getMustGroups())) {
            model.getSkuUniStructuredDto().getMustGroups().forEach(group -> {
                if (CollectionUtils.isEmpty(group.getSkuItems())) {
                    return;
                }
                mustSkuList.addAll(group.getSkuItems());
            });

        }
        if (CollectionUtils.isEmpty(optionalSkuList) && CollectionUtils.isEmpty(mustSkuList)) {
            return null;
        }
        //镜片服务项目
        SkuItemDto lensSkuItemDto = optionalSkuList.stream()
                .filter(skuItemDto -> skuItemDto.getProductCategory() == 2104926L || skuItemDto.getProductCategory() == 2104930L)
                .findFirst().orElse(null);
        if (Objects.isNull(lensSkuItemDto)) {
            lensSkuItemDto = mustSkuList.stream()
                    .filter(skuItemDto -> skuItemDto.getProductCategory() == 2104926L || skuItemDto.getProductCategory() == 2104930L)
                    .findFirst().orElse(null);
        }
        //镜框服务项目
        SkuItemDto frameSkuItemDto = optionalSkuList.stream()
                .filter(skuItemDto -> skuItemDto.getProductCategory() == 2104927L || skuItemDto.getProductCategory() == 2104931L)
                .findFirst().orElse(null);
        if (Objects.isNull(frameSkuItemDto)) {
            frameSkuItemDto = mustSkuList.stream()
                    .filter(skuItemDto -> skuItemDto.getProductCategory() == 2104927L || skuItemDto.getProductCategory() == 2104931L)
                    .findFirst().orElse(null);
        }
        List<String> result = new ArrayList<>();
        String lenTagStr = buildLensTag(lensSkuItemDto);
        String frameTagStr = buildFrameTag(frameSkuItemDto);
        if (org.apache.commons.lang.StringUtils.isNotEmpty(lenTagStr)) {
            result.add(lenTagStr);
        }
        if (org.apache.commons.lang.StringUtils.isNotEmpty(frameTagStr)) {
            result.add(frameTagStr);
        }
        return convertRichText(result, DEFAULT_COLOR, DEFAULT_FONT);
    }

    private String buildLensTag(SkuItemDto lensSkuItemDto) {
        //可用范围
        String useScope = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(lensSkuItemDto, "useScope");
        //服务项目-镜片-可用范围=指定镜片、自定义，且aa、bb、cc、dd值不都为空时展示
        //展示为：aabbcc（dd）
        //拼接逻辑：
        //aa取值服务项目-镜片-品牌
        //bb取值服务项目-镜片-折射率
        //cc取值服务项目-镜片-镜面设计
        //dd取值服务项目-镜片-是否进口
        if (org.apache.commons.lang.StringUtils.isNotEmpty(useScope) && "指定镜片".equals(useScope)) {
            return null;
        } else if (org.apache.commons.lang.StringUtils.isNotEmpty(useScope) && "特定范围内任选".equals(useScope)) {
            return DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(lensSkuItemDto, "availablePriceRange") + "元内镜片任选";
        }
        return null;
    }

    private String buildFrameTag(SkuItemDto frameSkuItemDto) {
        String useScope = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(frameSkuItemDto, "useScope");
        //服务项目-镜框-可用范围=指定镜框、自定义，且aa、bb值不都为空时展示
        //展示为：aabbcc
        //拼接逻辑：
        //aa取值服务项目-镜框-品牌
        //bb取值服务项目-镜框-材质
        //cc写死为“镜框”
        if (org.apache.commons.lang.StringUtils.isNotEmpty(useScope) && "指定镜框".equals(useScope)) {
            //品牌
            String brandname = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(frameSkuItemDto, "brandname");
            //材质
            String refractivity = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(frameSkuItemDto, "materialTexture");
            if (org.apache.commons.lang.StringUtils.isNotEmpty(brandname) || org.apache.commons.lang.StringUtils.isNotEmpty(refractivity)) {
                return brandname + refractivity + "镜框";
            }
        } else if (org.apache.commons.lang.StringUtils.isNotEmpty(useScope) && "特定范围内任选".equals(useScope)) {
            //可用价格段
            return DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(frameSkuItemDto, "availablePriceRange") + "元内镜框任选";
        }
        return null;
    }


    private boolean isUpgradeOphDeal(ProductM productM, DealDetailDtoModel model, Param param, ActivityCxt context, Config config) {
        if (Objects.isNull(config) || Objects.isNull(config.getOphTagConfig()) || Objects.isNull(config.ophTagConfig.ophMatchCondition)) {
            return false;
        }
        String serviceType = productM.getAttr("service_type");
        MedicalDealMatchCondition condition = config.getOphTagConfig().ophMatchCondition;
        boolean matchC = CollectionUtils.isEmpty(condition.getCategoryIds()) || condition.getCategoryIds().contains(productM.getCategoryId());
        boolean matchServiceType = CollectionUtils.isEmpty(condition.getServiceTypes()) || condition.getServiceTypes().contains(serviceType);
        boolean hasAttr = CollectionUtils.isEmpty(condition.getMustHasAttr()) ||
                condition.getMustHasAttr().stream().allMatch(r -> productM.getAttr(r) != null);
        boolean hasSkuAttr = false;
        if (CollectionUtils.isNotEmpty(condition.getMustHasSkuAttr())) {
            if (model != null && model.getSkuUniStructuredDto() != null
                    && CollectionUtils.isNotEmpty(model.getSkuUniStructuredDto().getMustGroups())
                    && CollectionUtils.isNotEmpty(model.getSkuUniStructuredDto().getMustGroups().get(0).getSkuItems())
            ) {
                List<String> skuAttrList = model.getSkuUniStructuredDto().getMustGroups().get(0).getSkuItems().get(0).getAttrItems()
                        .stream().map(r -> r.getAttrName()).collect(Collectors.toList());
                hasSkuAttr = skuAttrList.containsAll(condition.getMustHasSkuAttr());
            }
        } else {
            hasSkuAttr = true;
        }
        boolean matchAttrValue = MapUtils.isEmpty(condition.getMatchAttrValue())
                || condition.matchAttrValue.entrySet().stream().allMatch(en -> CollectionUtils.isEmpty(en.getValue())
                || en.getValue().contains(productM.getAttr(en.getKey()))
        );
        return matchC && matchServiceType && hasAttr && hasSkuAttr && matchAttrValue;
    }

    @Data
    private static class MedicalOphthalmologyGrayConfig {
        //true时需要灰度，false不需要灰度即开全量
        private boolean graySwitch;
        private List<Long> grayDpShopIds;
        private List<Long> grayMtShopIds;
        private List<Integer> grayDpCityIds;
        private List<Integer> grayMtCityIds;

    }

    private List<String> buildOphTags(ProductM productM, DealDetailDtoModel model, Param param, ActivityCxt context, Config config) {
        List<String> resultTags = new ArrayList<>();
        if (model == null || model.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(model.getSkuUniStructuredDto().getMustGroups())) {
            return resultTags;
        }
        MustSkuItemsGroupDto groupDto = model.getSkuUniStructuredDto().getMustGroups().get(0);
        if (CollectionUtils.isEmpty(groupDto.getSkuItems())) {
            return resultTags;
        }
        SkuItemDto itemDto = groupDto.getSkuItems().get(0);
        String serviceType = productM.getAttr("service_type");
        //检查项目
        String inspectionItemsArray = filterSkuAttr(itemDto, "inspectionItemsArray");
        //适用人群
        String applicableCrowds = filterSkuAttr(itemDto, "applicableCrowds");
        //最小适用年龄
        String minApplicableAge = filterSkuAttr(itemDto, "minApplicableAge");
        //最大适用年龄
        String maxApplicableAge = filterSkuAttr(itemDto, "maxApplicableAge");

        //角膜接触类型
        String homePkgCategory = filterSkuAttr(itemDto, "homePkgCategory");
        //术式类型
        String applyType = filterSkuAttr(itemDto, "applyType");
        //验光师级别
        String optometristLevel = filterSkuAttr(itemDto, "optometristLevel");
        List<InspectionItems> inspectionItemsList = buildInspectionItems(inspectionItemsArray);
        resultTags.add(inspectionItemsList.size() + "项检查");
        if ("医学验光".equals(serviceType) && StringUtils.isNotBlank(applicableCrowds)) {
            resultTags.add(applicableCrowds);
        }
        if ("儿童验光".equals(serviceType) && StringUtils.isNotBlank(minApplicableAge) && StringUtils.isNotEmpty(maxApplicableAge)) {
            resultTags.add(minApplicableAge + "-" + maxApplicableAge + "岁");
        }
        if ("角膜接触镜配镜检查".equals(serviceType)) {
            if ("OK镜".equals(homePkgCategory)) {
                resultTags.add("8-18岁");
            } else if ("RGP镜".equals(homePkgCategory)) {
                resultTags.add("无年龄限制");
            } else if ("离焦软镜".equals(homePkgCategory)) {
                resultTags.add("6周岁以上");
            }
        }
        if ("近视术前检查".equals(serviceType)) {
            if ("激光近视手术".equals(applyType)) {
                resultTags.add("18周岁以上");
            } else if ("晶体植入近视手术".equals(applyType)) {
                resultTags.add("18周岁以上");
            } else if ("激光/晶体植入通用".equals(applyType)) {
                resultTags.add("18周岁以上");
            }
        }
        if (StringUtils.isNotBlank(optometristLevel)) {
            resultTags.add(StringUtils.substringBefore(optometristLevel, "（"));
        }
        Map<String, String> inspectionItemsMap = inspectionItemsList.stream().filter(Objects::nonNull).collect(Collectors.toMap(InspectionItems::getSubjectName, r -> StringUtils.defaultIfEmpty(r.getPostscript(), ""), (a, b) -> a));
        if (inspectionItemsMap.containsKey("角膜生物力学检查")) {
            resultTags.add("含角膜生物力学");
        } else if (inspectionItemsMap.containsKey("超生物显微镜检查（UBM）")) {
            resultTags.add("含UBM检查");
        }
        return convertRichText(resultTags.subList(0, Math.min(resultTags.size(), 3)), DEFAULT_COLOR, DEFAULT_FONT);
    }

    private String filterSkuAttr(SkuItemDto itemDto, String key) {
        if (CollectionUtils.isEmpty(itemDto.getAttrItems())) return "";
        return itemDto.getAttrItems().stream().filter(r -> r.getAttrName().equals(key)).findFirst().map(r -> r.getAttrValue()).orElse("");
    }

    //todo
    private List<InspectionItems> buildInspectionItems(String inspectionItemsArray) {
        if (StringUtils.isEmpty(inspectionItemsArray)) return Lists.newArrayList();
        return JsonCodec.converseList(inspectionItemsArray, InspectionItems.class);
    }

    @Data
    static class InspectionItems {

        String subjectName;

        String postscript;
    }

    @Data
    static class OphTagConfig {
        private MedicalDealMatchCondition ophMatchCondition;
    }

    @Data
    static class MedicalDealMatchCondition {
        private List<Integer> categoryIds;
        private List<String> serviceTypes;
        private List<String> mustHasAttr;
        //仅支持必选一组的服务项目
        private List<String> mustHasSkuAttr;
        private Map<String, List<String>> matchAttrValue;
    }

    private List<ProductTagCfg> getProductTagCfg(ProductM productM, Config config) {
        if (config == null || CollectionUtils.isEmpty(config.getProductTagRules())) {
            return null;
        }
        List<SkuItemDto> mustSkuList = DealStructUtils.getMustSkuList(productM.getAttr(ProductMAttrUtils.DEAL_STRUCT_DETAIL));
        if (CollectionUtils.isEmpty(mustSkuList)) {
            return null;
        }
        for (ProductTagRuleCfg productTagRuleCfg : config.getProductTagRules()) {
            boolean hitRouteCondition = hitRouteCondition(mustSkuList.get(0), productM.getExtAttrs(), productTagRuleCfg.getRouteCondition());
            if (hitRouteCondition) {
                return productTagRuleCfg.getProductTags();
            }
        }
        return null;
    }

    private boolean hitRouteCondition(SkuItemDto skuItemDto, List<AttrM> dealAttrs, RouteConditionCfg routeCondition) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.MedicalItemProductTagsOpt.hitRouteCondition(SkuItemDto,List,MedicalItemProductTagsOpt$RouteConditionCfg)");
        if (routeCondition == null) {
            return false;
        }
        boolean satisfyAllAttrKeyValue = ProductMAttrUtils.satisfyAllAttrKeyValue(dealAttrs, routeCondition.getSatisfyAllAttrKeyValuesMap());
        boolean satisfyServiceItemCategory = satisfyServiceItemCategory(skuItemDto, routeCondition.getServiceItemCategoryIds());
        return satisfyAllAttrKeyValue && satisfyServiceItemCategory;
    }

    private boolean satisfyServiceItemCategory(SkuItemDto skuItemDto, List<Long> serviceItemCategoryIds) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.MedicalItemProductTagsOpt.satisfyServiceItemCategory(SkuItemDto,List)");
        //配置无效说明不做校验，返回true
        if (CollectionUtils.isEmpty(serviceItemCategoryIds)) {
            return true;
        }
        if (skuItemDto == null) {
            return false;
        }
        return serviceItemCategoryIds.contains(skuItemDto.getProductCategory());
    }

    /**
     * @param productM
     * @return 齿科团单标签
     */
    private List<String> getNewDentalTags(ProductM productM) {
        List<String> encodeTags = new ArrayList<>();
        //团单属性中有治疗医师资质放在最前面
        addTagIfNoNull(encodeTags, encodeTag(productM.getAttr("dentistryQualification")));
        //如果团单是代金券，则展示适用范围，并返回
        if (Objects.equals(productM.getAttr("productType"), "voucher")) {
            addTagIfNoNull(encodeTags, encodeTag(productM.getAttr("dentalVoucherUsingOfLimit")));
            return encodeTags;
        }
        // 齿科副标题新取数逻辑
        String dentalProductTagsAttr = productM.getAttr(DENTAL_PRODUCT_TAGS_ATTR);
        List<String> dentalProductTags = GsonUtils.fromJsonArray(dentalProductTagsAttr, String.class);
        encodeTags.addAll(convertRichText(dentalProductTags, DEFAULT_COLOR, DEFAULT_FONT));
        return encodeTags;
    }

    /**
     * 转换标签为富文本形式标签，当不传入颜色和字体时，使用默认
     *
     * @param tags
     * @param color
     * @param font
     * @return
     */
    private List<String> convertRichText(List<String> tags, String color, int font) {
        List<String> resultTags = new ArrayList<>();
        if (CollectionUtils.isEmpty(tags)) {
            return resultTags;
        }
        for (String tag : tags) {
            addTagIfNoNull(resultTags, encodeTag(tag, color, font));
        }
        return resultTags;
    }

    /**
     * 转换标签为富文本形式标签
     *
     * @param tag
     * @param color
     * @param font
     * @return
     */
    private String encodeTag(String tag, String color, int font) {
        if (StringUtils.isEmpty(tag)) {
            return tag;
        }
        return JsonCodec.encode(new AbstractFloorsBuilderExtAdapter.RichLabel(tag, color, font));
    }

    /**
     * @param tag
     * @return 默认样式
     */
    private String encodeTag(String tag) {
        if (StringUtils.isEmpty(tag)) {
            return tag;
        }
        return JsonCodec.encode(new AbstractFloorsBuilderExtAdapter.RichLabel(tag, DEFAULT_COLOR, DEFAULT_FONT));
    }

    private DealDetailDtoModel getDealDetailModel(ProductM productM) {
        if (productM == null || org.apache.commons.lang.StringUtils.isEmpty(productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME))) {
            return null;
        }
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME);
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || org.apache.commons.lang.StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        DealDetailDtoModel dealDetailDtoModel = JsonCodec.decode(dealStructModel.getStract(), DealDetailDtoModel.class);
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailDtoModel;
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (org.apache.commons.lang.StringUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || org.apache.commons.lang.StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    private List<String> buildDefaultTag(ProductM productM, ActivityCxt context, Config config) {
        // 如果团单是代金券，则展示适用范围
        String productType = productM.getAttr(PRODUCT_TYPE);
        if (StringUtils.isNotEmpty(productType) && "voucher".equals(productType)) {
            String usingOfLimit = productM.getAttr(VOUCHER_USING_OF_LIMIT);
            if (StringUtils.isNotEmpty(usingOfLimit)) {
                return getEncodeStrsOfProductTags(Lists.newArrayList(usingOfLimit));
            }
        }
        // 默认标签
        List<String> productTags = Lists.newArrayList();
        //如果品类是疫苗并且可预约，则展示
        if (productM.getCategoryId() == VACCINE_CATEGORY_ID && StringUtils.isNotEmpty(productM.getAttr(MEDICAL_BOOKABLE))) {
            String bookInfo = getEncodeStrsOfProductAttr(productM.getAttr(MEDICAL_BOOKINGTAG));
            productTags.add(bookInfo);
        }
        if (CollectionUtils.isNotEmpty(productM.getProductTags())) {
            productTags.addAll(getEncodeStrsOfProductTags(productM.getProductTags()));
        }
        return productTags;
    }

    private String getEncodeStrsOfProductAttr(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        AbstractFloorsBuilderExtAdapter.RichLabel richLabel = new AbstractFloorsBuilderExtAdapter.RichLabel(str, RESV_TAG_COLOR, DEFAULT_FONT);
        return JsonCodec.encode(richLabel);
    }

    private List<String> getEncodeStrsOfProductTags(List<String> productTags) {
        return productTags.stream().filter(tag -> StringUtils.isNotEmpty(tag)).map(tag -> getEncodeStrOfSingleProductTag(tag)).collect(Collectors.toList());
    }

    private String getEncodeStrOfSingleProductTag(String str) {
        AbstractFloorsBuilderExtAdapter.RichLabel richLabel = new AbstractFloorsBuilderExtAdapter.RichLabel(str, DEFAULT_COLOR, DEFAULT_FONT);
        return JsonCodec.encode(richLabel);
    }

    @Data
    @VPointCfg
    public static class Config {
        //齿科斗斛实验
        private Map<String, String> dentalExpSks;
        //商品标签规则
        private List<ProductTagRuleCfg> productTagRules;
        //新眼科团单标签规则
        private OphTagConfig ophTagConfig;
        //配镜团单标签
        private OphTagConfig glassConfig;

        private List<String> medicalShowcaseDental;

    }

    @Data
    private static class ProductTagRuleCfg {
        //路由条件
        private RouteConditionCfg routeCondition;
        //标签规则列表
        private List<ProductTagCfg> productTags;

    }

    @Data
    private static class RouteConditionCfg {
        //满足所有属性key及属性值的映射
        private Map<String, List<String>> satisfyAllAttrKeyValuesMap;
        //满足服务项目分类配置，即如果配置了该字段，商品的服务项目分类要包含在该配置中
        private List<Long> serviceItemCategoryIds;
    }

    @Data
    private static class ProductTagCfg {
        //标签ID列表
        private String tagId;
        //标签别名
        private String tagAlias;
    }
}
