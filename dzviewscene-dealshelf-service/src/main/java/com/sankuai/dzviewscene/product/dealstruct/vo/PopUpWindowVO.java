package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/28 3:55 下午
 */
@MobileDo(id = 0xa1cc)
public class PopUpWindowVO implements Serializable {

    /**
     * 弹窗详细信息
     */
    @MobileDo.MobileField(key = 0x347e)
    private List<CommonAttrVO> infos;

    /**
     * 弹窗图标
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 弹窗内容
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    /**
     * 弹窗标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<CommonAttrVO> getInfos() {
        return infos;
    }

    public void setInfos(List<CommonAttrVO> infos) {
        this.infos = infos;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
