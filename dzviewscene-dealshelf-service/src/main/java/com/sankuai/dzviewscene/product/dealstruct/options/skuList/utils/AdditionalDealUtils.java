package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.sankuai.dzviewscene.product.dealstruct.vo.*;


import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealAdditionalProjectM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2024/5/21 11:07
 */
public class AdditionalDealUtils {

    /**
     * 加项的项目数
     */
    public static final String ADDITIONAL_PROJECT_COUNT = "additional_project_count";
    public static final String GROUP_NAME = "自选服务模块";
    public static final String GROUP_SUBTITLE = "下单时可自选加购";

    /**
     * 解析加项的项目
     *
     * @param additionalProjectList 加项列表
     * @param tagFormatMap          加项标签解析map
     * @param titleReplaceList      加项标题replace值列表
     * @return 加项结果
     */
    public static DealDetailSkuListModuleGroupModel parseAdditionalProject(List<DealAdditionalProjectM> additionalProjectList, Map<String, String> tagFormatMap, List<String> titleReplaceList) {
        if (CollectionUtils.isEmpty(additionalProjectList)) {
            return null;
        }
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        groupModel.setGroupName(GROUP_NAME);
        groupModel.setGroupSubtitle(GROUP_SUBTITLE);
        groupModel.setDealSkuGroupModuleVOS(buildSkuModules(additionalProjectList, tagFormatMap, titleReplaceList));
        return groupModel;
    }

    public static boolean containsAdditionalDeal(List<ProductM> products) {
        if (CollectionUtils.isEmpty(products)) {
            return false;
        }
        return products.stream().filter(Objects::nonNull).anyMatch(ProductM::isAdditionalDeal);
    }

    private static List<DealSkuGroupModuleVO> buildSkuModules(List<DealAdditionalProjectM> additionalProjectList, Map<String, String> tagFormatMap, List<String> titleReplaceList) {
        Map<String, List<DealAdditionalProjectM>> map = additionalProjectList.stream().filter(additionalProject -> StringUtils.isNotBlank(additionalProject.getClassification()))
                .collect(Collectors.groupingBy(DealAdditionalProjectM::getClassification));
        return map.entrySet().stream().map(entry -> {
            DealSkuGroupModuleVO skuModule = new DealSkuGroupModuleVO();
            skuModule.setTitle(buildTitle(entry.getKey(), titleReplaceList));
            skuModule.setTitleStyle(1);
            skuModule.setDealSkuList(buildDealSkuList(entry.getValue(), tagFormatMap));
            return skuModule;
        }).collect(Collectors.toList());
    }

    private static String buildTitle(String title, List<String> titleReplaceList) {
        if (CollectionUtils.isEmpty(titleReplaceList)) {
            return title;
        }
        for (String replaceChar : titleReplaceList) {
            title = title.replaceAll(replaceChar, "");
        }
        return title;
    }

    private static List<DealSkuVO> buildDealSkuList(List<DealAdditionalProjectM> additionalList, Map<String, String> tagFormatMap) {
        return additionalList.stream().filter(Objects::nonNull).filter(additionalO -> Objects.nonNull(additionalO.getSalesCnt()))
                .sorted(Comparator.comparingInt(DealAdditionalProjectM::getSalesCnt).reversed())
                .map(additionalItem -> {
                    DealSkuVO dealSkuVO = new DealSkuVO();
                    dealSkuVO.setTitle(additionalItem.getItemName());
                    dealSkuVO.setPrice(String.format("￥%s", additionalItem.getSalePrice().stripTrailingZeros().toPlainString()));
                    dealSkuVO.setItems(buildSkuItems(additionalItem.getProductTagMap(), tagFormatMap));
                    return dealSkuVO;
                }).collect(Collectors.toList());
    }

    private static List<DealSkuItemVO> buildSkuItems(Map<String, String> productTagMap, Map<String, String> tagFormatMap) {
        if (MapUtils.isEmpty(productTagMap)) {
            return Lists.newArrayList();
        }
        return productTagMap.entrySet().stream().map(entry -> {
            DealSkuItemVO skuItem = new DealSkuItemVO();
            if (MapUtils.isNotEmpty(tagFormatMap) && tagFormatMap.containsKey(entry.getKey())) {
                skuItem.setValue(String.format(tagFormatMap.get(entry.getKey()), entry.getValue()));
            } else {
                skuItem.setValue(entry.getValue());
            }
            return skuItem;
        }).collect(Collectors.toList());
    }

}
