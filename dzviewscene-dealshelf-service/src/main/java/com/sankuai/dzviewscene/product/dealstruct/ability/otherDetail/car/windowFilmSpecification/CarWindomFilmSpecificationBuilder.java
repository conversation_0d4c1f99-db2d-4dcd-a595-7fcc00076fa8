package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.car.windowFilmSpecification;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.tpfun.product.api.sku.platform.resource.request.BatchQueryResourceInfoRequest;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailConstants;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrsVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.PicItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.car.*;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/6 7:41 下午
 */
@Ability(code = CarWindomFilmSpecificationBuilder.CODE,
        name = "车窗玻璃贴膜规格系数说明模块构造能力",
        description = "车窗玻璃贴膜规格系数说明模块构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class CarWindomFilmSpecificationBuilder extends PmfAbility<CarDetailModuleVO, CarWindomFilmSpecificationParam, CarWindomFilmSpecificationCfg> {

    public static final String CODE = "CarWindomFilmSpecificationBuilder";

    private static final int CAR_WINDOW_FILM_RESOURCE_TYPE = 54;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<CarDetailModuleVO> build(ActivityCxt ctx, CarWindomFilmSpecificationParam carWindomFilmSpecificationParam, CarWindomFilmSpecificationCfg cfg) {
        //1.提取用于构造结果的窗膜规格参数FilmSpecificationModel列表
        CompletableFuture<List<FilmSpecificationModel>> filmSpecificationModelsFuture = extractFilmSpecificationModelList(ctx, cfg);
        return filmSpecificationModelsFuture.thenApply(filmSpecificationModels -> {
            //2.获取展示的参数信息来自品牌库的提示文案
            String specificationDesc = getDescDocIndicatingThatParamsAreFromSpecificationDataBase(filmSpecificationModels, cfg);
            //3.利用窗膜规格参数FilmSpecificationModel列表构造窗膜信息
            List<CarItemVO> carItemVOS = buildCarItemVOListByFilmSpecificationModelList(filmSpecificationModels, cfg, ParamsUtil.getIntSafely(ctx, DealDetailConstants.Params.platform));
            //4.利用窗膜规格参数FilmSpecificationModel列表构造弹窗部分的商品详细参数
            CarDetailPopupItemVO carDetailPopupItemVO = buildCarDetailPopupItemVOByFilmSpecificationModelList(filmSpecificationModels, cfg, specificationDesc);
            //5.构造CarDetailModuleVO
            return buildCarDetailModuleVO(carItemVOS, carDetailPopupItemVO, specificationDesc, cfg);
        });
    }

    private CarDetailModuleVO buildCarDetailModuleVO(List<CarItemVO> carItemVOS, CarDetailPopupItemVO carDetailPopupItemVO, String specificationDesc, CarWindomFilmSpecificationCfg cfg) {
        if (CollectionUtils.isEmpty(carItemVOS) && carDetailPopupItemVO == null) {
            return null;
        }
        CarDetailModuleVO carDetailModuleVO = new CarDetailModuleVO();
        carDetailModuleVO.setItemList(carItemVOS);
        carDetailModuleVO.setPopup(carDetailPopupItemVO);
        carDetailModuleVO.setTitle(cfg.getCarWindowFilmSpecificationModuleTitle());
        carDetailModuleVO.setDesc(specificationDesc);
        return carDetailModuleVO;
    }

    /**
     * 构造整个弹窗组件
     *@param
     *@return
     */
    private CarDetailPopupItemVO buildCarDetailPopupItemVOByFilmSpecificationModelList(List<FilmSpecificationModel> filmSpecificationModels, CarWindomFilmSpecificationCfg cfg, String specificationDesc) {
        CarPopupDetailVO carPopupDetailVO = buildCarPopupDetailVO(filmSpecificationModels, cfg, specificationDesc);
        if (carPopupDetailVO == null) {
            return null;
        }
        CarDetailPopupItemVO carDetailPopupItemVO = new CarDetailPopupItemVO();
        carDetailPopupItemVO.setName(cfg.getCarWindowFilmSpecificationModulePopupDoc());
        carDetailPopupItemVO.setPopupDetail(carPopupDetailVO);
        return carDetailPopupItemVO;
    }

    /**
     * 构造弹窗页面
     *@param
     *@return
     */
    private CarPopupDetailVO buildCarPopupDetailVO(List<FilmSpecificationModel> filmSpecificationModels, CarWindomFilmSpecificationCfg cfg, String specificationDesc) {
        if (CollectionUtils.isEmpty(filmSpecificationModels) || MapUtils.isEmpty(cfg.getParamsInCarDetailPopupItemVOMap())) {
            return null;
        }
        List<CarParamVO> carParamVOS = buildCarParamVOList(filmSpecificationModels, cfg);
        if (CollectionUtils.isEmpty(carParamVOS)) {
            return null;
        }
        CarPopupDetailVO carPopupDetailVO = new CarPopupDetailVO();
        carPopupDetailVO.setTitle(cfg.getCarWindowFilmSpecificationModulePopupTitle());
        carPopupDetailVO.setDesc(specificationDesc);
        carPopupDetailVO.setParamInfo(carParamVOS);
        return carPopupDetailVO;
    }

    /**
     * 构造弹窗页面上规格参数信息列表
     *@param
     *@return
     */
    private List<CarParamVO> buildCarParamVOList(List<FilmSpecificationModel> filmSpecificationModels, CarWindomFilmSpecificationCfg cfg) {
        if (CollectionUtils.isEmpty(filmSpecificationModels) || MapUtils.isEmpty(cfg.getParamsInCarDetailPopupItemVOMap())) {
            return null;
        }
        return filmSpecificationModels.stream().map(specification -> {
            CarParamVO carParamVO = new CarParamVO();
            String title = getCarParamVOTitle(cfg.getPartNameMap().get(specification.getPart()), specification.getSpecificationItems().size());
            List<CommonAttrsVO> commonAttrsVOS = getCommonAttrsVOList(specification.getSpecificationItems(), cfg.getParamsInCarDetailPopupItemVOMap());
            if (StringUtils.isEmpty(title) && CollectionUtils.isEmpty(commonAttrsVOS)) {
                return null;
            }
            carParamVO.setTitle(title);
            carParamVO.setParams(commonAttrsVOS);
            return carParamVO;
        }).filter(vo -> vo != null).collect(Collectors.toList());
    }

    /**
     * 获取描述文案，该文案像用户表明展示的参数信息是来自信息库的，该文案会被展示在窗膜信息组件和弹窗组件
     *@param
     *@return
     */
    private String getDescDocIndicatingThatParamsAreFromSpecificationDataBase(List<FilmSpecificationModel> filmSpecificationModels, CarWindomFilmSpecificationCfg carWindomFilmSpecificationCfg) {
        if (CollectionUtils.isEmpty(filmSpecificationModels)) {
            return null;
        }
        //获取任何一个手动输入的参数信息
        FilmSpecificationModel munualInputSpecification = filmSpecificationModels.stream().filter(model -> {
            if (model == null || CollectionUtils.isEmpty(model.getSpecificationItems())) {
                return false;
            }
            FilmSpecificationItem specificationItemitem = model.getSpecificationItems().stream().filter(item -> item.isManualInputParams()).findFirst().orElse(null);
            return specificationItemitem != null;
        }).findFirst().orElse(null);
        //当不存在任何一个手动输入的参数信息时，则返回"以下参数信息来源于品牌官网，仅供参考"
        return munualInputSpecification == null ? carWindomFilmSpecificationCfg.getNormalizedSpecificationDescDoc() : null;
    }

    /**
     * 构造弹窗页面上规格参数字段列表
     *@param
     *@return
     */
    private List<CommonAttrsVO> getCommonAttrsVOList(List<FilmSpecificationItem> specificationItems, Map<String, String> paramsInCarDetailPopupItemVOMap) {
        if (CollectionUtils.isEmpty(specificationItems) || MapUtils.isEmpty(paramsInCarDetailPopupItemVOMap)) {
            return null;
        }
        return specificationItems.stream().map(item -> {
            if (MapUtils.isEmpty(item.getParamsMap())) {
                return null;
            }
            CommonAttrsVO commonAttrsVO = new CommonAttrsVO();
            commonAttrsVO.setName(getBrandAndType(item.getParamsMap()));
            List<String> values = paramsInCarDetailPopupItemVOMap.entrySet().stream().map(entry -> getCommonAttrsValue(entry.getKey(), entry.getValue(), item.isManualInputParams(), item.getParamsMap())).filter(doc -> StringUtils.isNotEmpty(doc)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(values)) {
                return commonAttrsVO;
            }
            commonAttrsVO.setValues(values);
            return commonAttrsVO;
        }).collect(Collectors.toList());
    }

    /**
     * 构造弹窗页面上规格参数值
     *@param
     *@return
     */
    private String getCommonAttrsValue(String paramName, String paramTitle, boolean isManualInputParams, Map<String, String> paramsMap) {
        if (MapUtils.isEmpty(paramsMap) || StringUtils.isEmpty(paramsMap.get(paramName))) {
            return null;
        }
        //如果参数是质保类型，则只有质保类型是"终身质保"是才展示
        if (ObjectUtils.equals(paramName, "keepyears")) {
            return ObjectUtils.equals(paramsMap.get("keepyears"), "终身质保") ? "终身质保" : null;
        }
        //如果参数是质保年限，则需要添加单位：年
        if (ObjectUtils.equals(paramName, "numberOfYear")) {
            return String.format("%s：%s年", paramTitle, paramsMap.get(paramName));
        }
        //如果参数是质保范畴，则会有多个值，按照 标题：范畴1、范畴2、范畴3 展示
        if (ObjectUtils.equals(paramName, "warrantyScope")) {
            //自定义品牌和信息可以品牌下质保范畴的解析方式不一样
            List<String> scopes = isManualInputParams ? Lists.newArrayList(paramsMap.get(paramName).split("、")) : JsonCodec.converseList(paramsMap.get(paramName), String.class);
            return String.format("%s：%s", paramTitle, StringUtils.join(scopes, "、"));
        }
        //如果参数是厚度，则需要添加单位：mil
        if (ObjectUtils.equals(paramName, "thickness")) {
            return String.format("%s：%smil", paramTitle, paramsMap.get(paramName));
        }
        //如果展示的是比率，则按照 标题：值% 进行展示
        if (paramTitle.contains("率")) {
            return String.format("%s：%s%%", paramTitle, paramsMap.get(paramName));
        }
        //剩余情况按照  标题：值 进行展示
        return String.format("%s：%s", paramTitle, paramsMap.get(paramName));
    }

    private String getCarParamVOTitle(String part, int brandSize) {
        if (StringUtils.isEmpty(part)) {
            return null;
        }
        if (brandSize <= 1) {
            return part;
        }
        return String.format("%s（以下型号%s选1）", part, brandSize);
    }

    /**
     * 构造作用部位和规格参数信息的CarItemVO列表
     *@param filmSpecificationModels 初始规格参数信息，所有的规格参数信息都在这里面
     *@param cfg 配置信息，根据这个配置判断页面上应该展示哪些规格参数信息
     *@return
     */
    private List<CarItemVO> buildCarItemVOListByFilmSpecificationModelList(List<FilmSpecificationModel> filmSpecificationModels, CarWindomFilmSpecificationCfg cfg, int platform) {
        if (CollectionUtils.isEmpty(filmSpecificationModels)) {
            return null;
        }
        return filmSpecificationModels.stream().map(specification -> {
            if (CollectionUtils.isEmpty(specification.getSpecificationItems())) {
                return null;
            }
            CarItemVO carItemVO = new CarItemVO();
            //获取作用部位
            String part = cfg.getPartNameMap().get(specification.getPart());
            carItemVO.setTitle(getTitle(part, cfg, platform));
            carItemVO.setValue(getValueOfCarItemVO(specification.getSpecificationItems(), cfg));
            carItemVO.setValueDesc(getValueDescOfCarItemVO(specification.getSpecificationItems(), cfg));
            return carItemVO;
        }).filter(vo -> vo != null).collect(Collectors.toList());
    }

    private PicItemVO getTitle(String part, CarWindomFilmSpecificationCfg cfg, int platform) {
        PicItemVO title = new PicItemVO();
        title.setTitle(part);
        title.setUrl(PlatformUtil.isMT(platform) ? cfg.getMtPartDescUrl() : cfg.getDpPartDescUrl());
        return title;
    }

    /**
     * 获取CarItemVO value
     *@param
     *@return
     */
    private String getValueOfCarItemVO(List<FilmSpecificationItem> specificationItems, CarWindomFilmSpecificationCfg cfg) {
        if (CollectionUtils.isNotEmpty(specificationItems) && specificationItems.size() > 1) {
            return String.format("以下型号%s选1", specificationItems.size());
        }
        FilmSpecificationItem filmSpecificationItem = CollectUtils.firstValue(specificationItems);
        if (filmSpecificationItem == null) {
            return null;
        }
        return getBrandAndSpecificationParamsInfo(filmSpecificationItem.getParamsMap(), cfg.getParamsInCarItemVOMap());
    }

    /**
     * 获取CarItemVO value desc
     *@param
     *@return
     */
    private String getValueDescOfCarItemVO(List<FilmSpecificationItem> specificationItems, CarWindomFilmSpecificationCfg cfg) {
        if (CollectionUtils.isEmpty(specificationItems) || specificationItems.size() <= 1) {
            return null;
        }
        List<String> brandAndSpecificationParamsInfo = specificationItems.stream().map(item -> getBrandAndSpecificationParamsInfo(item.getParamsMap(), cfg.getParamsInCarItemVOMap())).filter(doc -> StringUtils.isNotEmpty(doc)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(brandAndSpecificationParamsInfo)) {
            return null;
        }
        return StringUtils.join(brandAndSpecificationParamsInfo, "\n");
    }

    /**
     * 获取品牌参数信息和规格参数信息拼接文案
     *@param paramsMap 所有参数信息的Map集合
     *@param paramsConfigMap 配置，给出C端页面上展示哪些规格参数
     *@return
     */
    private String getBrandAndSpecificationParamsInfo(Map<String, String> paramsMap, Map<String, String> paramsConfigMap) {
        if (MapUtils.isEmpty(paramsConfigMap) || MapUtils.isEmpty(paramsMap)) {
            return null;
        }
        //获取品牌信息
        String brandAndSize = getBrandAndType(paramsMap);
        //获取规格参数信息
        List<String> paramValues = paramsConfigMap.entrySet().stream().map(entry -> getSpecificationParam(entry.getKey(), entry.getValue(), paramsMap)).filter(v -> StringUtils.isNotEmpty(v)).collect(Collectors.toList());
        String specificationParams =  StringUtils.join(paramValues, "；");
        //拼接 品牌信息 和 规格参数信息
        if (StringUtils.isEmpty(specificationParams)) {
            return brandAndSize;
        }
        return StringUtils.isEmpty(brandAndSize) ? specificationParams : String.format("%s（%s）", brandAndSize, specificationParams);
    }

    /**
     * 获取品牌型号
     *@param
     *@return
     */
    private String getBrandAndType(Map<String, String> paramsMap) {
        if (MapUtils.isEmpty(paramsMap)) {
            return null;
        }
        if (StringUtils.isNotEmpty(paramsMap.get("brandAndType"))) {
            return paramsMap.get("brandAndType");
        }
        if (StringUtils.isEmpty(paramsMap.get("modelno"))) {
            return paramsMap.get("chineseName");
        }
        if (StringUtils.isEmpty(paramsMap.get("chineseName"))) {
            return paramsMap.get("modelno");
        }
        return paramsMap.get("chineseName") + paramsMap.get("modelno");
    }

    /**
     * 获取规格参数信息文案
     *@param
     *@return
     */
    private String getSpecificationParam(String paramName, String paramTitle, Map<String, String> paramsMap) {
        if (StringUtils.isEmpty(paramsMap.get(paramName))) {
            return null;
        }
        //如果参数是质保类型，则只有质保类型是"终身质保"是才展示
        if (ObjectUtils.equals(paramName, "keepyears")) {
            return ObjectUtils.equals(paramsMap.get("keepyears"), "终身质保") ? "终身质保" : null;
        }
        //如果参数是质保年限，则按照 标题：值年 进行展示
        if (ObjectUtils.equals(paramName, "numberOfYear")) {
            return String.format("质保%s年", paramsMap.get(paramName));
        }
        return paramTitle + paramsMap.get(paramName) + "%";
    }

    /**
     * 从第一个must sku中提取窗膜规格参数对应的FilmSpecificationModel列表，该列表包含构造结果VO所需的所有的参数信息
     *@param
     *@return
     */
    private CompletableFuture<List<FilmSpecificationModel>> extractFilmSpecificationModelList(ActivityCxt ctx, CarWindomFilmSpecificationCfg cfg) {
        //获取sku属性列表
        List<SkuAttrItemDto> skuAttrItemDtos = getSkuAttrItemDtosRelatedCarWindowFilmSpecification(ctx);
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return CompletableFuture.completedFuture(null);
        }
        List<CompletableFuture<FilmSpecificationModel>> futures = skuAttrItemDtos.stream()
                //过滤非窗膜参数信息的属性
                .filter(skuAttr -> cfg.getCarWindomFilmSpecificationRelatedSkuAttrs().contains(skuAttr.getAttrName()))
                //从窗膜参数sku属性中提取FilmSpecificationModel
                .map(skuAttr -> extractFilmSpecificationModelFromSkuAttrItemDto(skuAttr))
                .collect(Collectors.toList());
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(Void -> futures.stream().map(future -> future.join()).filter(model -> model != null).collect(Collectors.toList()));
    }

    /**
     * 获取第一个must组的第一个sku的属性列表
     *@param
     *@return
     */
    private List<SkuAttrItemDto> getSkuAttrItemDtosRelatedCarWindowFilmSpecification(ActivityCxt ctx) {
        List<DealDetailInfoModel> dealDetailInfoModels = ctx.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel =  CollectUtils.firstValue(dealDetailInfoModels);
        return DealDetailUtils.extractFirstMustSkuAttrListFromDealDetailInfoModel(dealDetailInfoModel);
    }

    /**
     * 从第一个SkuAttrItemDto中提取窗膜规格参数对应的FilmSpecificationModel
     *@param
     *@return
     */
    private CompletableFuture<FilmSpecificationModel> extractFilmSpecificationModelFromSkuAttrItemDto(SkuAttrItemDto skuAttrItemDto) {
        List<Map<String, String>> specificationParamsMapList = JsonCodec.decode(skuAttrItemDto.getAttrValue(), new TypeReference<List<Map<String, String>>>() {} );
        if (CollectionUtils.isEmpty(specificationParamsMapList)) {
            return CompletableFuture.completedFuture(null);
        }
        List<CompletableFuture<FilmSpecificationItem>> futures = specificationParamsMapList.stream().map(paramsMap -> getFilmSpecificationItem(paramsMap)).collect(Collectors.toList());
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(Void -> futures.stream().map(future -> future.join()).filter(item -> item != null).collect(Collectors.toList()))
                .thenApply(itemList -> {
                    FilmSpecificationModel filmSpecificationModel = new FilmSpecificationModel();
                    filmSpecificationModel.setPart(skuAttrItemDto.getChnName());
                    filmSpecificationModel.setSpecificationItems(itemList);
                    return filmSpecificationModel;
                });
    }

    /**
     * 通过上单传过来的服务项目属性信息获取车窗玻璃膜规格参数信息
     *@param
     *@return
     */
    private CompletableFuture<FilmSpecificationItem> getFilmSpecificationItem(Map<String, String> preparamsMap) {
        if (MapUtils.isEmpty(preparamsMap)) {
            return CompletableFuture.completedFuture(null);
        }
        //判断上单页面窗膜规格参数信息是否是商家自填
        if (isManualInputSpecificationParams(preparamsMap.get("customEnable"))) {
            //如果上单页面窗膜规格参数信息是商家自填，则返回上单页面填写的参数信息
            return CompletableFuture.completedFuture(buildFilmSpecificationItem(preparamsMap, true));
        }
        //否则从按照资源id从信息库中查询参数信息
        long resourceId = NumberUtils.objToLong(preparamsMap.get("resourceId"));
        return getParamsFromSpecificationDatabase(resourceId).thenApply(params -> buildFilmSpecificationItem(params, false));
    }

    private FilmSpecificationItem buildFilmSpecificationItem(Map<String, String> paramsMap, boolean isManualInputParams) {
        if (MapUtils.isEmpty(paramsMap)) {
            return null;
        }
        FilmSpecificationItem filmSpecificationItem = new FilmSpecificationItem();
        filmSpecificationItem.setParamsMap(paramsMap);
        filmSpecificationItem.setManualInputParams(isManualInputParams);
        return filmSpecificationItem;
    }

    /**
     * 按照资源id从信息库获取产品规格参数信息
     *@param
     *@return
     */
    private CompletableFuture<Map<String, String>> getParamsFromSpecificationDatabase(long resourceId) {
        return compositeAtomService.batchQueryResourceInfo(buildBatchQueryResourceInfoRequest(resourceId)).thenApply(resourceInfos -> {
            if (CollectionUtils.isEmpty(resourceInfos)) {
                return new HashMap<>();
            }
            return resourceInfos.stream().filter(resourceInfo -> resourceInfo != null && MapUtils.isNotEmpty(resourceInfo.getExtInfoMap())).collect(HashMap::new, (map, resourceInfo) -> map.putAll(resourceInfo.getExtInfoMap()), HashMap::putAll);
        });
    }

    /**
     * 构造查询信息库型号参数request
     *@param
     *@return
     */
    private BatchQueryResourceInfoRequest buildBatchQueryResourceInfoRequest(long resourceId) {
        BatchQueryResourceInfoRequest batchQueryResourceInfoRequest = new BatchQueryResourceInfoRequest();
        batchQueryResourceInfoRequest.setResourceIds(Lists.newArrayList(resourceId));
        batchQueryResourceInfoRequest.setResourceType(CAR_WINDOW_FILM_RESOURCE_TYPE);
        return batchQueryResourceInfoRequest;
    }

    /**
     * 判断上单页面窗膜规格参数信息是否是商家自填
     *@param
     *@return
     */
    private boolean isManualInputSpecificationParams(String fromSpecificationDatabasesFlag) {
        return ObjectUtils.equals(fromSpecificationDatabasesFlag, "是");
    }

    @Data
    private static class FilmSpecificationModel {
        //窗膜规格item列表
        private List<FilmSpecificationItem> specificationItems;
        //窗膜规格参数对应的部位，例如前挡、侧后档、侧挡、天窗、侧后档及天窗
        private String part;
    }

    @Data
    private static class FilmSpecificationItem {
        //窗膜规格参数map
        private Map<String, String> paramsMap;
        //窗膜规格参数信息是否商家自填
        private boolean isManualInputParams;
    }

    @Data
    private static class FilmSpecificationParam {
        //窗膜规格参数名
        private String paramName;
        //窗膜规格参数值
        private String paramValue;
    }
}
