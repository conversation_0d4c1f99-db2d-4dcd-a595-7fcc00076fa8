package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.utils.DealAttrUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@VPointOption(name = "结婚通用构造DealAttrVO列表变化点", description = "结婚通用构造DealAttrVO列表变化点", code = CommonWeddingDealAttrVOListOpt.CODE)
public class CommonWeddingDealAttrVOListOpt extends DealAttrVOListVP<CommonWeddingDealAttrVOListOpt.Config> {

    public static final String CODE = "CommonWeddingDealAttrVOListOpt";

    private static final String SERVICE_STAFF_KEY = "serviceStaff";
    private static final String CLOTHING_STYLES_KEY = "clothingStyles";

    private static final String ATTR_VALUE_CONNECTOR = "×";
    private static final String LEFT_SQUARE_BRACKETS = "[";
    private static final String RIGHT_SQUARE_BRACKETS = "]";

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        if (config == null || param == null || CollectionUtils.isEmpty(config.getAttrListGroupModels())) {
            return null;
        }
        List<AttrM> attrs = new ArrayList<>();

        // 1.获取第一个sku属性 服务项目
        List<AttrM> firstSkuAttrs = DealAttrUtils.extractFirstSkuAttrFromDealDetailDtoModel(param.getDealDetailDtoModel());
        if (CollectionUtils.isNotEmpty(firstSkuAttrs)) {
            attrs.addAll(firstSkuAttrs);
        }
        // 2.获取团单属性 行业属性
        List<AttrM> dealAttrs = param.getDealAttrs();
        if (CollectionUtils.isNotEmpty(dealAttrs)) {
            attrs.addAll(dealAttrs);
        }
        // 3. 团单属性信息 嵌套解析
        List<AttrM> dealDetailInfoAttrs = DealAttrUtils.getDealDetailInfoAttrsFromAttrs(dealAttrs);
        if (CollectionUtils.isNotEmpty(dealDetailInfoAttrs)) {
            attrs.addAll(dealDetailInfoAttrs);
        }

        // 3.构造DealDetailStructAttrModuleVO列表
        return config.getAttrListGroupModels().stream()
                .map(model -> createDealDetailStructAttrModuleGroupModel(model, attrs))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DealDetailStructAttrModuleGroupModel createDealDetailStructAttrModuleGroupModel(AttrListGroupModel attrListGroupModel, List<AttrM> attrs) {
        if (attrListGroupModel == null || CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        if (CollectionUtils.isEmpty(attrListGroupModel.getAttrModelList())
                && CollectionUtils.isEmpty(attrListGroupModel.getAttrListGroupModels2())) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = processAttrModelList(attrListGroupModel.getAttrModelList(), attrs);
        List<DealDetailStructAttrModuleVO> subtitleItems = processAttrModelList(attrListGroupModel.getGroupSubtitleAttrModelList(), attrs);
        List<DealDetailStructAttrModuleGroupModel> dealDetailModuleList2 = attrListGroupModel.getAttrListGroupModels2() != null
                    ? attrListGroupModel.getAttrListGroupModels2().stream()
                            .map(model -> createDealDetailStructAttrModuleGroupModel(model, attrs))
                            .filter(Objects::nonNull).collect(Collectors.toList())
                    : null;
        return buildDealDetailStructAttrModuleGroupModel(attrListGroupModel.getGroupName(),
                dealDetailStructAttrModuleVOS, dealDetailModuleList2, subtitleItems,
                attrListGroupModel.getVideoModuleVO());
    }

    private List<DealDetailStructAttrModuleVO> processAttrModelList(List<MergeSortMapJoinFilterAttrModel> attrModelList, List<AttrM> attrs) {
        return Optional.ofNullable(attrModelList)
                .map(list -> list.stream().map(model -> createDealDetailStructAttrModuleVO(model, attrs))
                        .filter(Objects::nonNull).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    private DealDetailStructAttrModuleVO createDealDetailStructAttrModuleVO(MergeSortMapJoinFilterAttrModel attrModel, List<AttrM> attrs) {
        if (CollectionUtils.isEmpty(attrs) || attrModel == null
                || CollectionUtils.isEmpty(attrModel.getAttrNameList())) {
            return null;
        }
        // 属性值获取 如果有format则属性值会进行format转换
        List<String> attrValues = getAttrValues(attrModel, attrs);
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(attrModel.getAttrValueMapModels())) {
            attrValues = sortAndMapAttrValues(attrValues, attrModel.getAttrValueMapModels());
        }
        // 属性值拼接
        attrValues = joinAttrValue(attrValues, attrModel.getSeperator());
        return buildDealDetailStructAttrModuleVO(attrModel.getDisplayName(), attrValues, attrModel.getIcon());
    }

    private List<String> getAttrValues(MergeSortMapJoinFilterAttrModel model, List<AttrM> dealAttrs) {
        return model.getAttrNameList().stream()
                .flatMap(name -> getValueFromFormat(dealAttrs, name, model.getAttrFormatModels()).stream())
                .filter(value -> StringUtils.isNotEmpty(value))
                .collect(Collectors.toList());
    }

    private List<String> getValueFromFormat(List<AttrM> attrs, String attrName, List<AttrFormatModel> attrFormatModels) {
        if (SERVICE_STAFF_KEY.equals(attrName)) {
            return getServiceStaffValues(attrs);
        } else if (CLOTHING_STYLES_KEY.equals(attrName)) {
            return getClothingStyleValues(attrs);
        }
        List<String> attrValues = DealDetailUtils.getAttrValueByAttrName(attrs, attrName);
        if (CollectionUtils.isEmpty(attrValues)) {
            return Lists.newArrayList();
        }
        return formatAttrValues(attrValues, attrFormatModels, attrName);
    }

    private List<String> getServiceStaffValues(List<AttrM> attrs) {
        List<ServiceStaff> serviceStaffList = getServiceStaffList(attrs);
        if (CollectionUtils.isEmpty(serviceStaffList)) {
            return Lists.newArrayList();
        }
        return getValuesFromServiceStaffList(serviceStaffList);
    }

    private List<ServiceStaff> getServiceStaffList(List<AttrM> attrs) {
        AttrM attrM = attrs.stream().filter(attr -> SERVICE_STAFF_KEY.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null || StringUtils.isEmpty(attrM.getValue())) {
            return Lists.newArrayList();
        }
        String jsonValue = attrM.getValue();
        if (invalidJsonStart(jsonValue) && invalidJsonEnd(jsonValue)) {
            jsonValue = LEFT_SQUARE_BRACKETS + jsonValue + RIGHT_SQUARE_BRACKETS;
        }
        try {
            return JSONObject.parseArray(jsonValue, ServiceStaff.class);
        } catch (Exception e) {
            log.error("CommonWeddingDealAttrVOListOpt#getServiceStaffList error:{}", JSON.toJSON(attrM), e);
            return Lists.newArrayList();
        }
    }

    private List<String> getValuesFromServiceStaffList(List<ServiceStaff> serviceStaffList) {
        return serviceStaffList.stream().filter(this::isValidServiceStaff)
                .map(serviceStaff -> serviceStaff.getStaffType() + ATTR_VALUE_CONNECTOR + serviceStaff.getStaffCount())
                .collect(Collectors.toList());
    }

    private boolean isValidServiceStaff(ServiceStaff staff) {
        return staff != null
                && StringUtils.isNotEmpty(staff.getStaffType())
                && StringUtils.isNotEmpty(staff.getStaffCount());
    }

    private List<String> getClothingStyleValues(List<AttrM> attrs) {
        List<ClothingStyle> clothingStyleList = getClothingStyleList(attrs);
        if (CollectionUtils.isEmpty(clothingStyleList)) {
            return Lists.newArrayList();
        }
        return getValuesFromClothingStyleList(clothingStyleList);
    }

    private List<ClothingStyle> getClothingStyleList(List<AttrM> attrs) {
        AttrM attrM = attrs.stream().filter(attr -> CLOTHING_STYLES_KEY.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null || StringUtils.isEmpty(attrM.getValue())) {
            return Lists.newArrayList();
        }
        String jsonValue = attrM.getValue();
        if (invalidJsonStart(jsonValue) && invalidJsonEnd(jsonValue)) {
            jsonValue = LEFT_SQUARE_BRACKETS + jsonValue + RIGHT_SQUARE_BRACKETS;
        }
        try {
            return JSONObject.parseArray(jsonValue, ClothingStyle.class);
        } catch (Exception e) {
            log.error("CommonWeddingDealAttrVOListOpt#getClothingStyleList error:{}", JSON.toJSON(attrM), e);
            return Lists.newArrayList();
        }
    }

    private boolean invalidJsonStart(String jsonValue) {
        return !jsonValue.startsWith(LEFT_SQUARE_BRACKETS);
    }

    private boolean invalidJsonEnd(String jsonValue) {
        return !jsonValue.endsWith(RIGHT_SQUARE_BRACKETS);
    }

    private List<String> getValuesFromClothingStyleList(List<ClothingStyle> clothingStyleList) {
        return clothingStyleList.stream()
                .filter(this::isValidClothingStyle)
                .map(clothingStyle -> clothingStyle.getClothingTypes() + ATTR_VALUE_CONNECTOR + clothingStyle.getClothingCount())
                .collect(Collectors.toList());
    }

    private boolean isValidClothingStyle(ClothingStyle style) {
        return style != null
                && StringUtils.isNotEmpty(style.getClothingTypes())
                && StringUtils.isNotEmpty(style.getClothingCount());
    }

    private List<String> formatAttrValues(List<String> attrValues, List<AttrFormatModel> attrFormatModels, String attrName) {
        AttrFormatModel attrFormatModel = getAttrFormatMapModelByAttrValue(attrFormatModels, attrName, attrValues);
        if (attrFormatModel == null) {
            return attrValues;
        }
        return attrValues.stream()
                .map(value -> formatAttrValue(value, attrFormatModel))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    private AttrFormatModel getAttrFormatMapModelByAttrValue(List<AttrFormatModel> attrFormatModels, String attrName, List<String> attrValues) {
        if (CollectionUtils.isEmpty(attrFormatModels) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        return attrFormatModels.stream().filter(model -> isValidFormatModel(model, attrName, attrValues)).findFirst()
                .orElse(null);
    }

    private boolean isValidFormatModel(AttrFormatModel model, String attrName, List<String> attrValues) {
        if (!attrName.equals(model.getAttrName())) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(model.getFilterAttrValues())) {
            return model.getFilterAttrValues().stream().anyMatch(attrValues::contains);
        }
        return true;
    }

    private String formatAttrValue(String value, AttrFormatModel attrFormatModel) {
        if (value == null) {
            return null;
        }
        if (StringUtils.isNotEmpty(attrFormatModel.getDisplayFormat())) {
            return String.format(attrFormatModel.getDisplayFormat(), value);
        }
        return value;
    }

    private List<String> sortAndMapAttrValues(List<String> attrValues, List<AttrValueMapModel> attrValueMapModels) {
        return attrValues.stream()
                .sorted(Comparator.comparingInt(value -> getAttrPriority(attrValueMapModels, value)))
                .map(value -> getAttrDisplayValueByAttrValue(attrValueMapModels, value))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    private int getAttrPriority(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null || attrValueMapModel.getPriority() == 0) {
            return Integer.MAX_VALUE;
        }
        return attrValueMapModel.getPriority();
    }

    private String getAttrDisplayValueByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        AttrValueMapModel attrValueMapModel = getAttrValueMapModelByAttrValue(attrValueMapModels, attrValue);
        if (attrValueMapModel == null) {
            return attrValue;
        }
        return attrValueMapModel.getDisplayValue();
    }

    private List<String> joinAttrValue(List<String> attrValues, String separator) {
        if (StringUtils.isEmpty(separator) || attrValues.size() <= 1) {
            return attrValues;
        }
        attrValues = attrValues.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return Lists.newArrayList(StringUtils.join(attrValues, separator));
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(String displayName, List<String> attrValues, String icon) {
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(displayName);
        dealDetailStructAttrModuleVO.setAttrValues(attrValues);
        dealDetailStructAttrModuleVO.setIcon(icon);
        return dealDetailStructAttrModuleVO;
    }

    private DealDetailStructAttrModuleGroupModel buildDealDetailStructAttrModuleGroupModel(String groupName,
            List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS,
            List<DealDetailStructAttrModuleGroupModel> dealDetailStructAttrModuleGroupModels,
            List<DealDetailStructAttrModuleVO> subtitleItems, VideoModuleVO videoModuleVO) {
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS) && Objects.isNull(videoModuleVO)
                && CollectionUtils.isEmpty(dealDetailStructAttrModuleGroupModels)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailStructAttrModuleGroupModel = new DealDetailStructAttrModuleGroupModel();
        dealDetailStructAttrModuleGroupModel.setGroupName(groupName);
        dealDetailStructAttrModuleGroupModel.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        dealDetailStructAttrModuleGroupModel.setDealDetailModuleList2(dealDetailStructAttrModuleGroupModels);
        dealDetailStructAttrModuleGroupModel.setSubTitleItems(subtitleItems);
        dealDetailStructAttrModuleGroupModel.setVideoModuleVO(videoModuleVO);
        return dealDetailStructAttrModuleGroupModel;
    }

    private AttrValueMapModel getAttrValueMapModelByAttrValue(List<AttrValueMapModel> attrValueMapModels, String attrValue) {
        return attrValueMapModels.stream()
                .filter(model -> StringUtils.equals(model.getAttrValue(), attrValue))
                .findFirst()
                .orElse(null);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<AttrListGroupModel> attrListGroupModels;
    }

    @Data
    public static class AttrListGroupModel {
        private List<MergeSortMapJoinFilterAttrModel> attrModelList;
        private String groupName;
        private List<AttrListGroupModel> attrListGroupModels2;      // 多层嵌套配置
        private List<MergeSortMapJoinFilterAttrModel> groupSubtitleAttrModelList;      // 标题附加字段配置
        private VideoModuleVO videoModuleVO;    // 同一模块嵌入视频组件
    }

    /**
     * 融合命名/排序/映射/拼接/过滤功能的属性配置模型
     */
    @Data
    public static class MergeSortMapJoinFilterAttrModel {
        // 属性展示名称
        private String displayName;
        // 属性名列表
        private List<String> attrNameList;
        // 属性值 - 展示值映射 ； 展示顺序按照map中的展示顺序
        private List<AttrValueMapModel> attrValueMapModels;
        // 属性名 - 展示format映射
        private List<AttrFormatModel> attrFormatModels;
        // 图标
        private String icon;

        // 多个属性之间的拼接符
        private String seperator;
    }

    @Data
    public static class AttrValueMapModel {
        private String attrValue;
        private String displayValue;
        // 优先级从1开始
        private int priority;
    }

    @Data
    public static class AttrFormatModel {
        private String attrName;
        private String displayFormat;
        // 如果displayAttrValue不为空数组，则只有相应属性值应用该format规则
        private List<String> filterAttrValues;
    }

    @Data
    private static class ServiceStaff {
        private String staffType;
        private String staffCount;

        public String getStaffCount() {
            if (staffCount != null) {
                try {
                    return String.valueOf((int) NumberUtils.toDouble(staffCount));
                } catch (NumberFormatException e) {
                    log.error("CommonWeddingDealAttrVO.ServiceStaff#getstaffCount error:{}", staffCount, e);
                    return null;
                }
            }
            return null;
        }
    }

    @Data
    private static class ClothingStyle {
        private String clothingTypes;
        private String clothingCount;

        public String getClothingCount() {
            if (clothingCount != null) {
                try {
                    return String.valueOf((int) NumberUtils.toDouble(clothingCount));
                } catch (NumberFormatException e) {
                    log.error("CommonWeddingDealAttrVO.ClothingStyle#getClothingCount error:{}", clothingCount, e);
                    return null;
                }
            }
            return null;
        }
    }
}