package com.sankuai.dzviewscene.product.shelf.utils;

import org.apache.commons.lang3.StringUtils;

public class FitnessCrossSkipUrlUtil {

    /**
     * 点评货架
     */
    public static final String DP_SHELF_URL = "dianping://shopinfo?shopid=%s&shopuuid=%s";

    /**
     * 美团货架
     */
    public static final String MT_SHELF_URL = "imeituan://www.meituan.com/gc/poi/detail?id=%s";

    /**
     * 点评团详
     */
    public static final String DP_BASE_URL = "dianping://tuandeal?shopid=%s&id=%s";

    /**
     * 美团团详
     */
    public static final String MT_BASE_URL = "imeituan://www.meituan.com/gc/deal/detail?did=%s&poiid=%s";

    /**
     * 点评充值页
     */
    public static final String DP_RECHARGE_URL = "dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-play-fitness-recharge&mrn_component=fitness-recharge&entry_source=1&shop_id=%s&shop_uuid=%s";

    /**
     * 美团充值页
     */
    public static final String MT_RECHARGE_URL = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-play-fitness-recharge&mrn_component=fitness-recharge&entry_source=1&shop_id=%s&shop_uuid=%s";

    /**
     * 获取货架url
     */
    public static String getDealShelfUrl(boolean isDp, long shopId, String shopUuid) {
        return isDp ? String.format(DP_SHELF_URL, shopId, StringUtils.isEmpty(shopUuid) ? "" : shopUuid) : String.format(MT_SHELF_URL, shopId);
    }

    /**
     * 获取团详url
     */
    public static String getDealBaseUrl(boolean isDp, long shopId, long dealId) {
        return isDp ? String.format(DP_BASE_URL, shopId, dealId) : String.format(MT_BASE_URL, dealId, shopId);
    }

    /**
     * 获取充值页url
     */
    public static String getRechargeUrl(boolean isDp, long shopId, String shopUuid) {
        String urlFormat = isDp ? DP_RECHARGE_URL : MT_RECHARGE_URL;
        return String.format(urlFormat, shopId, StringUtils.isEmpty(shopUuid) ? "" : shopUuid);
    }

    /**
     * 获取充值页url
     */
    public static String getRechargeUrl(String urlFormat, long shopId, String shopUuid) {
        return String.format(urlFormat, shopId, StringUtils.isEmpty(shopUuid) ? "" : shopUuid);
    }

}
