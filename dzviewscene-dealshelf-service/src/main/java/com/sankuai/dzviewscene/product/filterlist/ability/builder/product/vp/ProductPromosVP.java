package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoDetailVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoItemVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/10
 */
@VPoint(name = "商品优惠信息", description = "商品-promo", code = ProductPromosVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductPromosVP<T> extends PmfVPoint<List<DzPromoVO>, ProductPromosVP.Param, T> {
    public static final String CODE = "ProductPromosVP";

    /**
     * 优惠弹窗的标题
     */
    private static final String PROMO_POP_TITLE = "优惠明细";

    /**
     * 优惠弹窗的总优惠标签
     */
    private static final String PROMO_POP_TOTAL_LAB = "共优惠：";

    protected List<DzPromoVO> getDefPromo(Param param) {
        // 多次卡不展示
        if (param.getProductM().isTimesDeal()) {
            return null;
        }
        ProductM productM = param.getProductM();
        if (CollectionUtils.isEmpty(productM.getPromoPrices())) {
            return Collections.emptyList();
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return Collections.emptyList();
        }
        return buildDzPromoVOS(productPromoPriceM);
    }

    protected List<DzPromoVO> buildDzPromoVOS(ProductPromoPriceM productPromoPriceM) {
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(productPromoPriceM.getPromoTag());
        dzPromoVO.setPromo(productPromoPriceM.getPromoTag());
        dzPromoVO.setPromoDetail(buildPromoDetail(productPromoPriceM));
        return Lists.newArrayList(dzPromoVO);
    }

    private DzPromoDetailVO buildPromoDetail(ProductPromoPriceM productPromoPriceM) {
        if (CollectionUtils.isEmpty(productPromoPriceM.getPromoItemList())) {
            return null;
        }
        DzPromoDetailVO detailVO = new DzPromoDetailVO();
        detailVO.setTitle(PROMO_POP_TITLE);
        detailVO.setTotalPromoLab(PROMO_POP_TOTAL_LAB);
        detailVO.setTotalPromoPrice(productPromoPriceM.getTotalPromoPriceTag());
        detailVO.setPromoItems(buildPromoItems(productPromoPriceM.getPromoItemList()));
        return detailVO;
    }

    private List<DzPromoItemVO> buildPromoItems(List<PromoItemM> promoItemList) {
        return promoItemList.stream().map(promoItemM -> {
            DzPromoItemVO promoPerItemVO = new DzPromoItemVO();
            promoPerItemVO.setPromoId(promoItemM.getPromoId());
            promoPerItemVO.setPromoType(promoItemM.getPromoTypeCode());
            promoPerItemVO.setTitle(promoItemM.getPromoType());
            promoPerItemVO.setDesc(promoItemM.getDesc());
            promoPerItemVO.setPromoPrice(promoItemM.getPromoTag());
            return promoPerItemVO;
        }).collect(Collectors.toList());
    }

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private String salePrice;
    }
}