package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date :2023/7/15
 */
@VPoint(name = "商品区-展示样式", description = "商品区提示信息", code = ItemAreaShowTypeVP.CODE, ability = FloorsBuilder.CODE)
public abstract class ItemAreaShowTypeVP <T> extends PmfVPoint<Integer, ItemAreaShowTypeVP.Param, T> {

    public static final String CODE = "ItemAreaShowTypeVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 商品组名
         */
        private String groupName;

    }
}
