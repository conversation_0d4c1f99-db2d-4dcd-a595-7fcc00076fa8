package com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.product.utils.LionObjectManagerUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@VPoint(name = "筛选-扩展信息", description = "筛选-extra", code = FilterExtraVP.CODE, ability = FilterBuilder.CODE)
public abstract class FilterExtraVP<T> extends PmfVPoint<String, FilterExtraVP.Param, T> {
    public static final String CODE = "FilterExtraVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private List<DouHuM> douHuList;

        private ProductGroupM productGroupM;

        private FilterBtnM filterBtnM;
    }

    public void extractMustParameters(ActivityCxt context, Map<String, Object> extra) {
        LionObjectManagerUtils.getFilterExtraRequired().forEach(key -> {
            String value = ContextParamBuildUtils.getParamFromExtraMap(context, key, "");
            if (StringUtils.isNotBlank(value)) {
                extra.put(key, value);
            }
        });
    }
}