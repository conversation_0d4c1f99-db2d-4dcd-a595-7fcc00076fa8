package com.sankuai.dzviewscene.product.shelf.options.assembler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.cat.util.StringUtils;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DataUtils;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfmodel.vp.ShelfGroupAssemblerVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.mpcontent.feeds.thrift.dto.request.QueryIsLivingReqDTO;
import com.sankuai.mpcontent.feeds.thrift.dto.response.QueryIsLivingRespDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;


/**
 * <AUTHOR>
 * @date :2023/8/3
 */
@VPointOption(name = "货架爆品堆头商品分组",
        description = "根据商品爆品标记，拆分堆头分组",
        code = "HotSaleTopDisplayProductShelfGroupOpt"
)
public class HotSaleTopDisplayProductShelfGroupOpt extends ShelfGroupAssemblerVP<HotSaleTopDisplayProductShelfGroupOpt.Config> {

    @Resource
    private CompositeAtomService compositeAtomService;


    // 查直播状态接口，主体类型 1=门店（私域场景必传）
    private static final int SUBJECT_TYPE = 1;
    @Override
    public Map<String, ProductGroupM> compute(ActivityCxt context, Param param, Config config){
        if (!isHitDouhu(context, config)) {
            return Maps.newHashMap();
        }
        ProductGroupM productGroupM = param.getProductGroupMs().get(config.getGroupName());
        if(productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())){
            return Maps.newHashMap();
        }
        // 商户是否有直播模块
        boolean isLiveShowExist = isLiveShowExist(context, productGroupM);
        context.addParam(ShelfActivityConstants.Params.isLiveShowExist, isLiveShowExist);
        return isLiveShowExist ? Maps.newHashMap() : param.getProductGroupMs();
    }

    private boolean isHitDouhu(ActivityCxt context, Config config) {
        List<DouHuM> douHuMList = context.getSource(ShelfDouHuFetcher.CODE);
        if (CollectionUtils.isEmpty(douHuMList)) {
            return false;
        }
        List<String> douHuMSkList = DouHuUtils.getDouHuMSkList(douHuMList);
        return CollectionUtils.containsAny(config.getDouHuConfig(), douHuMSkList);
    }

    private boolean isLiveShowExist(ActivityCxt context, ProductGroupM productGroupM) {
        try {
            CompletableFuture<QueryIsLivingRespDTO> queryIsLivingFuture = compositeAtomService.queryIsLiving(buildQueryIsLivingReq(context, productGroupM));
            return Objects.nonNull(queryIsLivingFuture.join()) && Objects.nonNull(queryIsLivingFuture.join().getIsHaveLive()) && queryIsLivingFuture.join().getIsHaveLive();
        } catch (TException e) {
            Cat.logError(e);
            return false;
        }
    }

    private QueryIsLivingReqDTO buildQueryIsLivingReq(ActivityCxt context, ProductGroupM productGroupM) {
        List<ProductM> productList = productGroupM.getProducts();
        if (CollectionUtils.isEmpty(productList)) {
            return new QueryIsLivingReqDTO();
        }
        List<ShopM> shopMs = productList.get(0).getShopMs();
        if (CollectionUtils.isEmpty(shopMs)) {
            return new QueryIsLivingReqDTO();
        }
        QueryIsLivingReqDTO queryIsLivingReq = new QueryIsLivingReqDTO();
        queryIsLivingReq.setSubjectType(SUBJECT_TYPE);
        queryIsLivingReq.setPlatform(context.getParam(ShelfActivityConstants.Params.platform));
        queryIsLivingReq.setVersion(context.getParam(ShelfActivityConstants.Params.appVersion));
        queryIsLivingReq.setSubjectId((long)shopMs.get(0).getShopId());

        return queryIsLivingReq;
    }
    @VPointCfg
    @Data
    public static class Config {
        List<String> douHuConfig;
        String groupName;
    }
}

