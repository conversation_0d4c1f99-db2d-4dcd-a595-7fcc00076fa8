package com.sankuai.dzviewscene.product.shelf.options.builder.shelfshowtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.shelfshowtype.ShelfShowTypeVP;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;

@VPointOption(name = "根据version和实验来控制showType",
        description = "根据version和实验来控制showType",
        code = "ShelfVersionExpShelfShowTypeOpt")
public class ShelfVersionExpShelfShowTypeOpt extends ShelfShowTypeVP<ShelfVersionExpShelfShowTypeOpt.Config> {

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        // 新货架走实验逻辑
        if (param.getShelfVersion() >= config.getShelfVersion()) {
            int showTypeByExpDefaultEmpty = getShowTypeByExpDefaultEmpty(config, param.getDouHuList());
            if (showTypeByExpDefaultEmpty > 0) {
                return showTypeByExpDefaultEmpty;
            }
        }
        return doGetShowTypeByShelfVersion(config, param.getShelfVersion());
    }

    @VPointCfg
    @Data
    public static class Config {
        private int shelfVersion;
        private int showType;

        /**
         * 低于某个版本时的 showType
         */
        private int showTypeOfLessShelfVersion;

        /**
         * 低版本号，小于该版本返回 showTypeOfLessShelfVersion
         */
        private int lessThanShelfVersion;

        private Map<String, Integer> douHuSk2ShowType;
    }

    public static int doGetShowTypeByShelfVersion(Config config, int reqShelfVersion) {
        if (config.getShowTypeOfLessShelfVersion() > 0 && config.getLessThanShelfVersion() > 0 && reqShelfVersion < config.getLessThanShelfVersion()) {
            // 声明小于某个版本，返回指定showType
            return config.getShowTypeOfLessShelfVersion();
        }
        return config.getShowType();
    }

    public static int getShowTypeByExpDefaultEmpty(Config config, List<DouHuM> douHuMList) {
        if (MapUtils.isNotEmpty(config.getDouHuSk2ShowType())) {
            List<String> douHuMSkList = DouHuUtils.getDouHuMSkList(douHuMList);
            if (CollectionUtils.isNotEmpty(douHuMSkList)) {
                for (String douhuSK : douHuMSkList) {
                    Integer showType = config.getDouHuSk2ShowType().get(douhuSK);
                    if (showType != null && showType > 0) {
                        return showType;
                    }
                }
            }
        }
        return 0;
    }
}
