package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempurchase;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPurchaseVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/2/6
 */
@VPointOption(name = "通用商品购买信息，通过配置来启动某些通用逻辑",
        description = "",
        code = "CommonItemPurchaseOpt",
        isDefault = true)
public class CommonItemPurchaseOpt extends ItemPurchaseVP<CommonItemPurchaseOpt.Config> {

    @Override
    public RichLabelVO compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (config.isEnableRecycleModification() && StringUtils.isNotBlank(productM.getPurchase())) {
            String purchase = productM.getPurchase().replace("购买", "预约");
            RichLabelVO richLabelVO = new RichLabelVO();
            richLabelVO.setText(purchase);
            return richLabelVO;
        }
        if (config.isEnablePreSaleStock() && PreSaleUtils.isPreSaleDealAndStockLess(productM)) {
            //预售库存紧张信息 优于 X小时购买信息展示
            return PreSaleUtils.getPreSaleStockPurchase(productM);
        }
        if (config.isEnablePerfectPreheatHide() && PerfectActivityBuildUtils.isDuringPerfectActivityPreheatTime(productM)) {
            return null;
        }
        if (config.isEnableSecKillHide() && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(productM)) {
            //秒杀团单展示秒杀倒计时
            return null;
        }
        // 有「全网低价」标签，则不展示购买信息
        if (config.isEnableLowestPriceHide() && PriceDisplayUtils.containsLowestPriceTag(productM)) {
            return null;
        }
        if (StringUtils.isNotEmpty(productM.getPurchase())) {
            RichLabelVO richLabelVO = new RichLabelVO();
            richLabelVO.setText(productM.getPurchase());
            return richLabelVO;
        }
        return null;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 预售库存不足展示
         */
        private boolean enablePreSaleStock = false;

        /**
         * 秒杀隐藏购买信息
         */
        private boolean enableSecKillHide = false;

        /**
         * 玩美季预售隐藏购买信息
         */
        private boolean enablePerfectPreheatHide = true;

        /**
         * 商品有全网低价标签，则隐藏购买信息
         */
        private boolean enableLowestPriceHide = false;

        /**
         * 是否允许回收修改购买信息文案
         */
        private boolean enableRecycleModification = false;
    }
}
