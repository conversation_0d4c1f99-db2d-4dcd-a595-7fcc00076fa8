package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@VPointOption(name = "配置化attr输出",
        description = "配置化attr输出",
        code = "ConfigExtAttrOpt")
public class ConfigExtAttrOpt extends ProductExtAttrVP<ConfigExtAttrOpt.Config> {
    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        Map<String, Object> extAttrMap = new HashMap<>();
        if (MapUtils.isNotEmpty(config.getFixAttrAndValueMap())) {
            config.getFixAttrAndValueMap().forEach((key, value) -> addAttr(extAttrMap, key, value));
        }
        if (CollectionUtils.isNotEmpty(config.getDealAttrKeys())) {
            config.getDealAttrKeys().forEach(attrKey -> addAttr(extAttrMap, attrKey, param.getProductM().getAttr(attrKey)));
        }
        if (MapUtils.isEmpty(extAttrMap)) {
            return null;
        }
        return JsonCodec.encodeWithUTF8(extAttrMap);
    }

    private void addAttr(Map<String, Object> extAttrMap, String attrKey, String attrValue) {
        if (StringUtils.isAnyBlank(attrKey, attrValue)) {
            return;
        }
        extAttrMap.put(attrKey, attrValue);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 从团单中读取的attr
         */
        private List<String> dealAttrKeys;
        /**
         * 固定添加的key和value
         */
        private Map<String, String> fixAttrAndValueMap;
    }
}
