package com.sankuai.dzviewscene.product.filterlist.option.builder.product.bottomtag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductBottomTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/4/12 20:40
 */
@VPointOption(name = "默认团单底部标签",
        description = "默认团单底部标签",
        code = "DefaultBottomTagOpt",
        isDefault = true
)
public class DefaultBottomTagOpt extends ProductBottomTagsVP<Void> {
    @Override
    public List<RichLabelVO> compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
