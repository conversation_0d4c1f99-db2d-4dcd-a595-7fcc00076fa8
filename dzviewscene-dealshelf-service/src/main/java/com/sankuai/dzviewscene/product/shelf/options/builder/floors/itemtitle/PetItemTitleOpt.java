package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemtitle;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemTitleVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/10
 */
@VPointOption(name = "宠物货架团单标题",
        description = "宠物货架团单标题",
        code = "PetItemTitleOpt")
public class PetItemTitleOpt extends ItemTitleVP<PetItemTitleOpt.Config> {

    private static final String PET_INTERACTION_DEAL_TITLE_ATTR = "petInteractionDealTitleAttr";

    private static final String PET_DEAL_TITLE_WITH_TAG = "petDealWithTagTitle";

    /**
     * 标准化团单属性Key，用于判断是否为标准团单
     */
    private static final String STANDARD_DEAL_GROUP_KEY = "standardDealGroupKey";

    /**
     * 适用宠物对应的属性值
     */
    private static final List<String> PET_TYPE_NAMES = Lists.newArrayList("apply_pets_in_wash_beauty", "apply_pets", "apply_pet", "pet_type");

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        //标准化团单展示原标题
        if(StringUtils.isNotEmpty(productM.getAttr(STANDARD_DEAL_GROUP_KEY))){
            return productM.getTitle();
        }
        //萌宠互动由主题层控制逻辑
        if (StringUtils.isNotEmpty(productM.getAttr(PET_INTERACTION_DEAL_TITLE_ATTR))) {
            return productM.getAttr(PET_INTERACTION_DEAL_TITLE_ATTR);
        }
        String rawServiceType = ProductMAttrUtils.getServiceType(productM);
        if (StringUtils.isEmpty(rawServiceType)) {
            return productM.getTitle();
        }
        //判断是否不展示团单前标签
        if (isIncludedInNoPreFixFilterIds(config, param.getFilterId())){
            return productM.getTitle();
        }
        //不展示团单标签的筛选id 如在美容Tab下，只展示标题，其他Tab展示[xx]标题
        if(isNoServiceTypeFilterIds(config, param.getFilterId())){
            return buildNoServiceTypeTitle(productM);
        }
        if(StringUtils.isNotEmpty(productM.getAttr(PET_DEAL_TITLE_WITH_TAG))){
            return productM.getAttr(PET_DEAL_TITLE_WITH_TAG);
        }
        return productM.getTitle();
    }

    /**
     * 获取适用宠物
     *
     * @param productM
     * @return
     */
    private List<String> getApplyPetTypes(ProductM productM) {
        for (String petTypeName : PET_TYPE_NAMES) {
            String attrRawValue = productM.getAttr(petTypeName);
            if (StringUtils.isEmpty(attrRawValue)) {
                continue;
            }
            return Arrays.asList(StringUtils.split(attrRawValue, ","));
        }
        return new ArrayList<>();
    }

    private boolean isNoServiceTypeFilterIds(Config config, long filterId){
        return config != null && CollectionUtils.isNotEmpty(config.getNoServiceTypeFilterIds()) && config.getNoServiceTypeFilterIds().contains(filterId);
    }

    private String buildNoServiceTypeTitle(ProductM productM){
        List<String> applyPetTypes = getApplyPetTypes(productM);
        //犬猫 + serviceType + 标题
        if (onlyContainsCatAndDog(applyPetTypes)) {
            return String.format("[犬猫]%s", productM.getTitle());
        }
        //猫/狗/兔 + serviceType + 标题
        if (onlyCatOrDogOrRabbit(applyPetTypes)) {
            return String.format("[%s]%s", applyPetTypes.get(0), productM.getTitle());
        }
        //兜底用服务分类拼接
        return productM.getTitle();
    }

    /**
     * 仅犬猫
     *
     * @param petTypes
     * @return
     */
    private boolean onlyContainsCatAndDog(List<String> petTypes) {
        if (CollectionUtils.isEmpty(petTypes)) {
            return false;
        }
        return petTypes.contains("猫咪") && petTypes.contains("狗狗") && petTypes.size() == 2;
    }

    /**
     * 值为猫、狗、兔
     *
     * @param petTypes
     * @return
     */
    private boolean onlyCatOrDogOrRabbit(List<String> petTypes) {
        if (CollectionUtils.isEmpty(petTypes) || CollectionUtils.size(petTypes) > 1) {
            return false;
        }
        return petTypes.contains("猫咪") || petTypes.contains("狗狗") || petTypes.contains("兔子");
    }

    /**
     * 判断是否需要展示标题前的团单标签
     */
    private boolean isIncludedInNoPreFixFilterIds(Config config, long filterId) {
        return (config != null && CollectionUtils.isNotEmpty(config.getNoPreFixFilterIds()) && config.getNoPreFixFilterIds().contains(filterId));
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 不展示服务类型的筛选id
         * 如在美容Tab下，标题就不用展示 [XX美容] 了 仅展示 [XX]
         */
        private List<Long> noServiceTypeFilterIds;

        /**
         * 不展示团单标签的筛选id 如在美容Tab下，只展示标题，其他Tab展示[xx]标题
         */
        private List<Long> noPreFixFilterIds;
    }
}
