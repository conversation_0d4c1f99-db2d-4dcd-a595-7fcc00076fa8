package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@VPointOption(name = "医疗-副标题构造",
        description = "医疗副标题构造的是富文本，数据在主题层ProductTags处理完，个性化放在attr中",
        code = "MedicalItemProductTagsV2Opt")
public class MedicalItemProductTagsV2Opt extends ItemProductTagsVP<MedicalItemProductTagsV2Opt.Config> {

    private static final String DEFAULT_COLOR = "#777777";
    private static final int DEFAULT_FONT = 11;

    private static final String EXAMINE_PLANT_TAG = "examinePlantTag";


    @Override
    public List<String> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (productM == null) {
            return null;
        }

        return buildProductTags(productM, context, config);
    }

    private List<String> buildProductTags(ProductM productM, ActivityCxt context, Config config) {
        // 默认标签
        List<String> productTags = Lists.newArrayList();

        //根据行业不同，从attr中取数据（展示不同颜色）
        getProductTagsByAttr(productM, config, productTags);

        if (CollectionUtils.isNotEmpty(productM.getProductTags())) {
            productTags.addAll(getEncodeStrsOfProductTags(productM.getProductTags()));
        }

        return productTags;
    }

    private void getProductTagsByAttr(ProductM productM, Config config, List<String> productTags) {
        if(productM.getCategoryId() == 401){
            List<String> examinePlantTag = getExaminePlantTag(productM, config);
            productTags.addAll(examinePlantTag);
        }
    }

    private List<String> getExaminePlantTag(ProductM productM, Config config) {
        List<String> productTags = Lists.newArrayList();
        String examinePlantTag = productM.getAttr(EXAMINE_PLANT_TAG);
        FontCfg fontCfg = config.displayCfg.stream()
                .filter(cfg -> cfg.attr.equals(EXAMINE_PLANT_TAG))
                .findFirst().get();
        if (StringUtils.isNotEmpty(examinePlantTag)){
            int checkNums = Integer.parseInt(examinePlantTag);
            String checkNumsProductTag = String.format("%s项检查", checkNums);
            productTags.add(getEncodeStrOfSingleProductTagByFontCfg(checkNumsProductTag,fontCfg));
        }
        return productTags;
    }


    private List<String> getEncodeStrsOfProductTags(List<String> productTags) {
        return productTags.stream().filter(tag -> StringUtils.isNotEmpty(tag)).map(tag -> getEncodeStrOfSingleProductTag(tag)).collect(Collectors.toList());
    }

    private String getEncodeStrOfSingleProductTag(String str) {
        AbstractFloorsBuilderExtAdapter.RichLabel richLabel = new AbstractFloorsBuilderExtAdapter.RichLabel(str, DEFAULT_COLOR, DEFAULT_FONT);
        return JsonCodec.encode(richLabel);
    }

    private String getEncodeStrOfSingleProductTagByFontCfg(String str, FontCfg fontCfg) {
        if (fontCfg == null) {
            return getEncodeStrOfSingleProductTag(str);
        }
        String fontColor = fontCfg.getFontColor() == null ? DEFAULT_COLOR : fontCfg.getFontColor();
        Integer fontSize = fontCfg.getFontSize() == null ? DEFAULT_FONT : fontCfg.getFontSize();
        AbstractFloorsBuilderExtAdapter.RichLabel richLabel = new AbstractFloorsBuilderExtAdapter.RichLabel(str, fontColor, fontSize);
        return JsonCodec.encode(richLabel);
    }

    @Data
    @VPointCfg
    public static class Config {
        //根据serviceType拿对应需要增加的attr的副标题
        List<FontCfg> displayCfg;
    }

    @Data
    private static class FontCfg {
        String attr;
        String fontColor;
        Integer fontSize;
    }

}
