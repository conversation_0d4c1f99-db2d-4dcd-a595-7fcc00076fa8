package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductRichTagsVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.UnifiedShelfItemLaundryDeliverySubTitleOpt;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @see UnifiedShelfItemLaundryDeliverySubTitleOpt
 */
@VPointOption(name = "洗涤取送-RichTags形式的副标题构造",
        description = "",
        code = "LaundryDeliveryItemProductRichTagsOpt")
public class LaundryDeliveryItemProductRichTagsOpt extends ItemProductRichTagsVP<LaundryDeliveryItemProductRichTagsOpt.Config> {

    private static final String ATTR_NAME = "lifeServiceLaundrySupportDelivery";

    private static final String ON_SITE_FETCH_TAG = "免费取送";

    @Override
    public List<RichLabelVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        List<RichLabelVO> richLabelVOS = new ArrayList<>();
        RichLabelVO laundryDeliveryTag = buildLaundryDeliveryTag(productM);
        if (Objects.nonNull(laundryDeliveryTag)) {
            richLabelVOS.add(laundryDeliveryTag);
        }
        richLabelVOS.addAll(buildNormalTags(productM));
        return richLabelVOS;
    }

    private List<RichLabelVO> buildNormalTags(ProductM productM) {
        List<String> productTags = productM.getProductTags();
        if (CollectionUtils.isEmpty(productTags)) {
            return Lists.newArrayList();
        }
        return productTags.stream().filter(StringUtils::isNotEmpty)
                .map(tag -> new RichLabelVO(FrontSizeUtils.front12, ColorUtils.color777777, tag))
                .collect(Collectors.toList());
    }

    private RichLabelVO buildLaundryDeliveryTag(ProductM productM) {
        String attr = productM.getAttr(ATTR_NAME);
        if (StringUtils.isEmpty(attr)) {
            return null;
        }
        if (attr.equals(ON_SITE_FETCH_TAG)) {
            RichLabelVO richLabelVO = new RichLabelVO(FrontSizeUtils.front10, attr, "#FF4B10",  "#FFF1EC");
            richLabelVO.setCssPadding("0,3,0,3");
            richLabelVO.setCssBorderRadius("3,3,3,3");
            return richLabelVO;
        } else {
            RichLabelVO richLabelVO = new RichLabelVO(FrontSizeUtils.front10, attr, "#666666",  "#F4F4F4");
            richLabelVO.setCssPadding("0,3,0,3");
            richLabelVO.setCssBorderRadius("3,3,3,3");
            return richLabelVO;
        }
    }

    @VPointCfg
    @Data
    public static class Config {
    }
}
