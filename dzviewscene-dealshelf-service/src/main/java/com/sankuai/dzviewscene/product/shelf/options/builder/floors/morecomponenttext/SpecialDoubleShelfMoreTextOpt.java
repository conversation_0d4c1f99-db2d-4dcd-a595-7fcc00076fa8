package com.sankuai.dzviewscene.product.shelf.options.builder.floors.morecomponenttext;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.MoreComponentTextVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/3/10
 */
@VPointOption(name = "双列团购货架落地页-查看更多-展示文案",
        description = "货架跟落地页复用，如酒吧",
        code = "SpecialDealShelfMoreTextOpt")
public class SpecialDoubleShelfMoreTextOpt extends MoreComponentTextVP<SpecialDoubleShelfMoreTextOpt.Config> {

    /**
     * 默认文案
     */
    private static final String DEFAULT_TEXT_FORMAT = "更多%d个团购";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        //如果是落地页，有分页，展示固定“加载中”
        if (com.sankuai.dzviewscene.product.utils.ParamsUtil.judgeDealShelfHasPage(context)) {
            return config.getFloorFormat();
        }
        //货架文案
        int totalNum = config.isNeedCurrentFilterAllProductNum()
                ? ContextParamBuildUtils.getTotalProductNum(context, param.getGroupName(), true)
                : param.getItemAreaItemCnt();
        String shelfFormat = StringUtils.isNotEmpty(config.getShelfFormat()) ? config.getShelfFormat() : DEFAULT_TEXT_FORMAT;
        if (totalNum <= param.getDefaultShowNum()) {
            return StringUtils.EMPTY;
        }
        return String.format(shelfFormat, totalNum - param.getDefaultShowNum());
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 货架文案
         */
        private String shelfFormat;

        /**
         * 落地页分页文案
         */
        private String floorFormat;

        /**
         * 是否使用当前Filter的全部商品数
         */
        private boolean needCurrentFilterAllProductNum = false;
    }
}
