package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import cn.hutool.core.util.NumberUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.CarouselMsgTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.util.Objects;

/**
 * 聚合累计销量
 */
@Component
public class AggregateSaleMsgStrategy implements ButtonCarouselMsgStrategy{

    private final static String  STRATEGY_NAME = "AggregateSaleMsgStrategy";

    @Override
    public String getName() {
        return AggregateSaleMsgStrategy.STRATEGY_NAME;
    }

    @Override
    public RichLabelVO build(CarouselBuilderContext context) {
        return null;
    }

    @Override
    public CarouselMsg unifiedBuild(CarouselBuilderContext context) {
        if(context.getProductM() != null && context.getProductM().getSale() != null){
            return buildSaleMsg(context.getProductM().getSale().getSaleTag());
        }
        if(CollectionUtils.isNotEmpty(context.getChildrenProductM())){
            int sales = context.getChildrenProductM().stream().map(ProductM::getSale).filter(Objects::nonNull).mapToInt(ProductSaleM::getSale).sum();
            return buildSaleMsg(sectionSales(sales));
        }
        return null;
    }

    private CarouselMsg buildSaleMsg(String sale){
        if(StringUtils.isEmpty(sale)){
            return null;
        }
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setStyle(TextStyleEnum.TEXT_GRAY.getType());
        styleTextModel.setText(sale);
        CarouselMsg carouselMsg = new CarouselMsg();
        carouselMsg.setText(styleTextModel);
        carouselMsg.setType(CarouselMsgTypeEnum.SALE_MSG.getType());
        return carouselMsg;
    }

    private String sectionSales(int saleNum) {
        if (saleNum <= 0) {
            return null;
        }
        if (saleNum < 50) {
            return String.format("年售%s", saleNum);
        }
        if (saleNum < 100) {
            return String.format("年售%s+", saleNum / 10 * 10);
        }
        if (saleNum < 1000) {
            return String.format("年售%s+", saleNum / 100 * 100);
        }
        if (saleNum < 10000) {
            return String.format("年售%s+", saleNum / 1000 * 1000);
        }
        return String.format("年售%s万+", NumberUtil.div(saleNum, 10000, 1, RoundingMode.DOWN));
    }
}
