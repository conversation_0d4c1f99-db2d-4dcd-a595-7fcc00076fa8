package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import lombok.Builder;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/11/26 6:13 下午
 */

@VPoint(name = "团购详情sku价格变化点", description = "团购详情sku价格变化点",code = SkuPriceVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuPriceVP<T> extends PmfVPoint<String, SkuPriceVP.Param, T> {

    public static final String CODE = "SkuPriceVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private BigDecimal price;
    }

}
