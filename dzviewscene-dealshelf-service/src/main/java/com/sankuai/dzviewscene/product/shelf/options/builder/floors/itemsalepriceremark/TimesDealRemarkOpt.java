package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceremark;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceRemarkVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.stream.Stream;

/**
 * @author: created by hang.yu on 2024/1/5 16:00
 */
@VPointOption(name = "次数",
        description = "返回 /3次",
        code = "TimesDealRemarkOpt")
public class TimesDealRemarkOpt extends ItemSalePriceRemarkVP<TimesDealRemarkOpt.Config> {
    @Override
    public String compute(ActivityCxt context, Param param, Config config) {

        String time = buildTimeRemark(param, config);

        String campaginPriceDesc = buildCampaginPriceDesc(param, config);

        return Stream.of(time, campaginPriceDesc).filter(StringUtils::isNotEmpty).findFirst().orElse(null);

    }

    private String buildTimeRemark(Param param, Config config){
        // 判断交易类型是团购次卡
        if (!param.getProductM().isTimesDeal()) {
            return param.getProductM().isAdditionalDeal() ? "起" : null;
        }
        if (!TimesDealUtil.hitTimesStyle1(param.getProductM(), param.getTimesDealSk())){
            return null;
        }
        if (StringUtils.isBlank(config.getTimesKey()) || StringUtils.isBlank(config.getRemarkTemplate())) {
            return null;
        }
        String attr = param.getProductM().getAttr(config.getTimesKey());
        if (StringUtils.isBlank(attr) || !NumberUtils.isDigits(attr)) {
            return null;
        }
        return String.format(config.getRemarkTemplate(), attr);
    }

    private String buildCampaginPriceDesc(Param param, Config config){
        // 判断交易类型是团建商品卡
        String campaginPriceDesc = param.getProductM().getAttr(config.getCampaginPriceDesc());
        if(!StringUtils.isEmpty(campaginPriceDesc)){
            return campaginPriceDesc;
        }
        return null;

    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 获取次数的key
         */
        private String timesKey = "sys_multi_sale_number";

        /**
         * 返回描述的模板
         */
        private String remarkTemplate = "/%s次";

        /**
         * 返回团建商品卡售价后描述
         */
        private String campaginPriceDesc = "campaginPriceDesc";

    }

}
