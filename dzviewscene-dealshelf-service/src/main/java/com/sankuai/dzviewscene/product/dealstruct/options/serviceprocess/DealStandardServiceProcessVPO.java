package com.sankuai.dzviewscene.product.dealstruct.options.serviceprocess;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess.vcpoints.DealStandardServiceProcessVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceProcessVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-07
 * @desc
 */
@VPointOption(name = "团购详情服务流程模块变化点", description = "团购详情服务流程模块变化点-根据属性判断是否显示",
        code = DealStandardServiceProcessVPO.CODE, isDefault = true)
public class DealStandardServiceProcessVPO extends DealStandardServiceProcessVP<DealStandardServiceProcessVPO.Config> {
    public static final String CODE = "dealStandardServiceProcessVPO";

    @Override
    public List<StandardServiceProcessVO> compute(ActivityCxt activityCxt, Param param, Config config) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return null;
        }
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        List<AttrM> dealAttrs = dealDetailInfoModel.getDealAttrs();
        return getWearableNailDealStandardServiceProcess(dealAttrs, config);
    }

    public List<StandardServiceProcessVO> getWearableNailDealStandardServiceProcess(List<AttrM> dealAttrs, DealStandardServiceProcessVPO.Config config) {
        // 判断甲片类型是否是穿戴甲
        boolean isWearableNail = DealDetailUtils.isWearableNail(dealAttrs);
        if (!isWearableNail) {
            return null;
        }
        // 判断是否可到店佩戴
        boolean isCanWearAtStore = DealDetailUtils.isCanWearAtStore(dealAttrs);
        Map<String, List<DealStandardServiceProcessVPO.WearableNailServiceProcess>> wearableNailServiceProcessMap = config.getWearableNailServiceProcessMap();
        List<DealStandardServiceProcessVPO.WearableNailServiceProcess> wearableNailServiceProcesses = Lists.newArrayList();
        if (isCanWearAtStore) {
            wearableNailServiceProcesses = wearableNailServiceProcessMap.getOrDefault("freeWearingAtStore", Lists.newArrayList());
        } else {
            wearableNailServiceProcesses = wearableNailServiceProcessMap.getOrDefault("notFreeWearingAtStore", Lists.newArrayList());
        }
        return wearableNailServiceProcesses.stream().filter(Objects::nonNull).map(this::convertToSkuItemVO).collect(Collectors.toList());
    }

    private StandardServiceProcessVO convertToSkuItemVO(DealStandardServiceProcessVPO.WearableNailServiceProcess freeWearingAtStore) {
        if (Objects.isNull(freeWearingAtStore)) {
            return null;
        }
        StandardServiceProcessVO standardServiceProcessVO = new StandardServiceProcessVO();
        standardServiceProcessVO.setStepName(freeWearingAtStore.getStepName());
        standardServiceProcessVO.setStepIcon(freeWearingAtStore.getIcon());
        standardServiceProcessVO.setArrowIcon(freeWearingAtStore.getRightArrowIcon());
        return standardServiceProcessVO;
    }

    @Data
    @VPointCfg
    public static class Config {
        // 穿戴甲服务流程配置
        private Map<String, List<DealStandardServiceProcessVPO.WearableNailServiceProcess>> wearableNailServiceProcessMap;
    }

    @Data
    public static class WearableNailServiceProcess {
        private String icon;
        private String stepName;
        private String rightArrowIcon;
    }
}
