/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-04-22
 * Project        :
 * File Name      : ButtonCarouselMsgStrategy.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import com.sankuai.dzviewscene.dealshelf.shelfvo.CarouselMsg;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-04-22
 * @since dzviewscene-dealshelf-home 1.0
 */
public interface ButtonCarouselMsgStrategy {

    String getName();

    RichLabelVO build(CarouselBuilderContext context);

    CarouselMsg unifiedBuild(CarouselBuilderContext context);
}
