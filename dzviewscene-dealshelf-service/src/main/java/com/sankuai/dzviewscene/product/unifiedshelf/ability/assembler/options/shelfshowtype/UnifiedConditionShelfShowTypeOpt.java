package com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.options.shelfshowtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.vp.UnifiedShelfShowTypeVP;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@VPointOption(name = "根据条件配置来控制整个货架样式",
        description = "根据条件配置来控制整个货架样式",
        code = UnifiedConditionShelfShowTypeOpt.CODE)
public class UnifiedConditionShelfShowTypeOpt extends UnifiedShelfShowTypeVP<UnifiedConditionShelfShowTypeOpt.Config> {

    public static final String CODE = "UnifiedConditionShelfShowTypeOpt";

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getConditionConfigs())) {
            return config.getDefaultShowType();
        }
        return getShowTypeByCondition(context, param, config);
    }

    private Integer getShowTypeByCondition(ActivityCxt context, Param param, Config config) {
        List<ConditionValueConfig> sortedConditionValueConfigs = config.getConditionConfigs().stream()
                .sorted(Comparator.comparing(ConditionValueConfig::getPriority).reversed())
                .collect(Collectors.toList());
        return sortedConditionValueConfigs.stream()
                .filter(condition -> hitCondition(context, param, condition.getCondition()))
                .findFirst()
                .map(ConditionValueConfig::getHitShowType)
                .orElse(config.getDefaultShowType());
    }

    @Data
    public static class ConditionValue {

        private Integer priority;

        public static ConditionValue getInstance(Integer priority){
            ConditionValue config = new ConditionValue();
            config.setPriority(priority);
            return config;
        }
    }

    private boolean hitCondition(ActivityCxt context, Param param, Condition condition) {
        return Stream.of(
                hitShelfLessVersion(context, param, condition),
                hitDoubleColumn(context, param, condition),
                hitDouHuExp(context, param, condition),
                hitClientTypeLimit(context, param, condition),
                hitBackCategoryTypeLimit(context, param, condition)
        ).allMatch(bool -> bool);
    }

    /**
     * 双列货架判断
     */
    private boolean hitDoubleColumn(ActivityCxt context, Param param, Condition condition) {
        if (Objects.isNull(condition.getDoubleShelf())) {
            return true;
        }
        int doubleColumnShelf = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.doubleColumnShelf);
        return doubleColumnShelf == 1;
    }

    /**
     * 不配置或者当前版本号大于等配置版本号都视为命中
     */
    private boolean hitShelfLessVersion(ActivityCxt context, Param param, Condition condition) {
        if (Objects.isNull(condition.getLargeMinShelfVersion())) {
            return true;
        }
        return param.getShelfVersion() >= condition.getLargeMinShelfVersion();
    }

    /**
     * 斗斛实验判断
     */
    private boolean hitDouHuExp(ActivityCxt context, Param param, Condition condition) {
        if (CollectionUtils.isEmpty(condition.getDouHuExps())) {
            return true;
        }
        return DouHuUtils.hitAnySk(param.getDouHuList(), condition.getDouHuExps());
    }

    /**
     * 端限制
     * @param context
     * @param param
     * @param condition
     * @return
     */
    private boolean hitClientTypeLimit(ActivityCxt context, Param param, Condition condition) {
        if (CollectionUtils.isEmpty(condition.getClientTypeLimit())) {
            return true;
        }
        int clientType = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.userAgent);
        return condition.getClientTypeLimit().contains(clientType);
    }

    /**
     * 后台类目限制判断, 条件为空时直接命中，
     * 无店铺信息则视为不命中,类目层级不足则视为不命中
     * 粒度为2级类目,命中才返回true
     */
    private boolean hitBackCategoryTypeLimit(ActivityCxt context, Param param, Condition condition) {
        if (CollectionUtils.isEmpty(condition.getBackCategory())) {
            return true;
        }
        ShopM shopM = context.getParam(PmfConstants.Ctx.ctxShop);
        if (shopM == null || CollectionUtils.isEmpty(shopM.getBackCategory())) {
            return false;
        }
        List<Integer> backCategory = shopM.getBackCategory();
        if (backCategory.size() < 2) {
            return false;
        }
        return condition.getBackCategory().contains(backCategory.get(1));
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 默认展示样式
         */
        private int defaultShowType;

        /**
         * 条件判断样式
         */
        private List<ConditionValueConfig> conditionConfigs;
    }

    @Data
    public static class ConditionValueConfig {

        /**
         * 命中值
         */
        private Integer hitShowType;

        /**
         * 判断条件
         */
        private Condition condition;

        /**
         * 条件优先级，先取哪个优先级条件做判断
         * 值越大优先级越高
         */
        private int priority;
    }


    @Data
    public static class Condition {

        /**
         * 双列货架
         */
        private Boolean doubleShelf;

        /**
         * 高版本样式最低版本号
         */
        private Integer largeMinShelfVersion;

        /**
         * 斗槲实验结果
         */
        private List<String> douHuExps;

        /**
         * 端限制
         */
        private List<Integer> clientTypeLimit;

        /**
         * 后台类目限制
         */
        private List<Integer> backCategory;
    }
}
