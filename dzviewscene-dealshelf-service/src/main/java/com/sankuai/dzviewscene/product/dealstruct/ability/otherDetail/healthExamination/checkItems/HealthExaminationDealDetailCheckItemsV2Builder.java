package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.DealAttrVOListModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.vpoints.HealthExaminationCheckItemListV2VP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.HealthExaminationDealDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.HealthExaminationItemsGroupVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Ability(code = HealthExaminationDealDetailCheckItemsV2Builder.CODE,
        name = "体检团购详情模块检查项列表构造能力V2",
        description = "体检团购详情模块检查项列表构造能力V2",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealAttrVOListModuleBuilder.CODE
        }
)
public class HealthExaminationDealDetailCheckItemsV2Builder extends PmfAbility<DealModuleDetailVO, HealthExaminationDealDetailCheckItemsV2Param, HealthExaminationDealDetailCheckItemsV2Cfg> {

    public static final String CODE = "healthExaminationDealDetailCheckItemsV2Builder";

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt ctx, HealthExaminationDealDetailCheckItemsV2Param param, HealthExaminationDealDetailCheckItemsV2Cfg cfg) {
        //1.获取构造团详所需的团单基本信息
        DealDetailInfoModel dealDetailBasicInfo = getDealDetailBasicInfo(ctx);
        if (dealDetailBasicInfo == null) {
            CompletableFuture.completedFuture(null);
        }
        //3.获取不含落地页数据的检查项列表（用在新的前端包上展示）
        CompletableFuture<HealthExaminationItemsGroupVO> checkItemListGroupFuture = getCheckItemListGroup(ctx, dealDetailBasicInfo);
        //4.组装结果
        return checkItemListGroupFuture.thenApply(checkItemListGroup -> buildDealModuleDetailVO(checkItemListGroup, dealDetailBasicInfo.getDesc(), dealDetailBasicInfo.getDealAttrs()));
    }

    /**
     * 获取不含检查意义的检查项列表
     *@param
     *@return
     */
    private CompletableFuture<HealthExaminationItemsGroupVO> getCheckItemListGroup(ActivityCxt ctx, DealDetailInfoModel dealDetailBasicInfo) {
        HealthExaminationCheckItemListV2VP<?> healthExaminationCheckItemListVP = findVPoint(ctx, HealthExaminationCheckItemListV2VP.CODE);
        HealthExaminationItemsGroupVO healthExaminationItemsGroupVO = healthExaminationCheckItemListVP.execute(ctx, HealthExaminationCheckItemListV2VP.Param.builder().dealDetailBasicInfo(dealDetailBasicInfo).build());
        return CompletableFuture.completedFuture(healthExaminationItemsGroupVO);
    }


    private DealDetailInfoModel getDealDetailBasicInfo(ActivityCxt activityCxt) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        return CollectUtils.firstValue(dealDetailInfoModels);
    }

    private DealModuleDetailVO buildDealModuleDetailVO(HealthExaminationItemsGroupVO healthExaminationItemsGroupVO, String desc, List<AttrM> dealAttrs) {
        if (healthExaminationItemsGroupVO == null) {
            return null;
        }
        HealthExaminationDealDetailVO healthExaminationDealDetailVO = new HealthExaminationDealDetailVO();
        //添加落地页筛选检查项列表
        healthExaminationDealDetailVO.setFilterItemListVOs(Lists.newArrayList());//走兼容
        //添加检查项列表
        healthExaminationDealDetailVO.setExaminationItemsGroup(healthExaminationItemsGroupVO);
        //添加商家描述信息
        healthExaminationDealDetailVO.setDesc(desc);
        //添加团单属性信息
        healthExaminationDealDetailVO.setDealAttrs(buildDealAttributes(dealAttrs));
        //构造结果
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        dealModuleDetailVO.setHealthExaminationDealDetailVO(healthExaminationDealDetailVO);
        return dealModuleDetailVO;
    }

    /**
     * 构造团单属性模块
     *@param
     *@return
     */
    private List<DealDetailStructAttrModuleVO> buildDealAttributes(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> attrModuleVOS = new ArrayList<>();
        //添加适用人群
        addSuitablePeople(attrModuleVOS, dealAttrs);
        //添加服务亮点
        addServiceHighlights(attrModuleVOS, dealAttrs);
        if (CollectionUtils.isEmpty(attrModuleVOS)) {
            return null;
        }
        return attrModuleVOS;
    }

    /**
     * 添加服务亮点属性
     *@param
     *@return
     */
    private void addServiceHighlights(List<DealDetailStructAttrModuleVO> attrModuleVOS, List<AttrM> dealAttrs) {
        List<String> highlights = new ArrayList<>();
        //添加出报告时效
        addReportTime(highlights, dealAttrs);
        //添加亮点属性
        addHighlights(highlights, dealAttrs);
        if (CollectionUtils.isEmpty(highlights)) {
            return;
        }
        attrModuleVOS.add(buildDealDetailStructAttrModuleVO("服务亮点", StringUtils.join(highlights, "、")));
    }

    /**
     * 添加出报告时效
     *@param
     *@return
     */
    private void addReportTime(List<String> highlights, List<AttrM> dealAttrs) {
        if (!ObjectUtils.equals(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "service_type"), "入职体检")) {
            return;
        }
        double reportDayNum = getReportDayNum(dealAttrs);
        if (reportDayNum < 0) {
            return;
        }
        if (reportDayNum < 1) {
            highlights.add("当日出报告");
            return;
        }
        if (reportDayNum < 2) {
            highlights.add("次日出报告");
            return;
        }
        if (reportDayNum < 3) {
            highlights.add("3日内出报告");
            return;
        }
        if (reportDayNum < 5) {
            highlights.add("3-5个工作日出报告");
            return;
        }
        if (reportDayNum < 7) {
            highlights.add("5-7个工作日出报告");
            return;
        }
        if (reportDayNum < 7.000001) {
            highlights.add("一周出报告");
            return;
        }
    }

    /**
     * 添加亮点属性
     *@param
     *@return
     */
    private void addHighlights(List<String> highlights, List<AttrM> dealAttrs) {
        List<String> highlightList = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "px_additional_service");
        if (CollectionUtils.isEmpty(highlightList)) {
            return;
        }
        highlights.addAll(highlightList);
    }

    /**
     * 获取出报告所需的天数
     *@param
     *@return
     */
    private double getReportDayNum(List<AttrM> dealAttrs) {
        String reportTimeAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "physical_examination_get_result_time");
        if (StringUtils.isEmpty(reportTimeAttrValue)) {
            return -1;
        }
        reportTimeAttrValue = reportTimeAttrValue.replaceAll("体检报告可于体检结束后", StringUtils.EMPTY).replaceAll("后自取", StringUtils.EMPTY);
        if (reportTimeAttrValue.contains("周")) {
            return  NumberUtils.objToDouble(reportTimeAttrValue.replaceAll("周", StringUtils.EMPTY)) * 7;
        }
        if (reportTimeAttrValue.contains("个工作日")) {
            return  NumberUtils.objToDouble(reportTimeAttrValue.replaceAll("个工作日", StringUtils.EMPTY));
        }
        return -1;
    }

    /**
     * 添加适用人群属性
     *@param
     *@return
     */
    private void addSuitablePeople(List<DealDetailStructAttrModuleVO> attrModuleVOS, List<AttrM> dealAttrs) {
        List<String> suitablePeople = new ArrayList<>();
        addSuitableAge(suitablePeople, dealAttrs);
        addSuitableGender(suitablePeople, dealAttrs);
        addSuitableCrowd(suitablePeople, dealAttrs);
        if (CollectionUtils.isEmpty(suitablePeople)) {
            return;
        }
        attrModuleVOS.add(buildDealDetailStructAttrModuleVO("适用人群", StringUtils.join(suitablePeople, "、")));
    }

    private void addSuitableCrowd(List<String> suitablePeople, List<AttrM> dealAttrs) {
        List<String> suitableCrowd = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "suitable_crowd");
        if (CollectionUtils.isEmpty(suitableCrowd)) {
            return;
        }
        suitableCrowd = suitableCrowd.stream()
                .map(crowd -> StringUtils.isNotEmpty(crowd) ? crowd + "人群" : null)
                .filter(crowd -> StringUtils.isNotEmpty(crowd))
                .collect(Collectors.toList());
        suitablePeople.addAll(suitableCrowd);
    }

    private void addSuitableGender(List<String> suitablePeople, List<AttrM> dealAttrs) {
        String suitableGenderAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "checkup_sex");
        String suitableGender = ObjectUtils.equals(suitableGenderAttrValue, "通用") ? "男女通用" : suitableGenderAttrValue;
        if (StringUtils.isEmpty(suitableGender)) {
            return;
        }
        String maritalStatus = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "marital_status");
        if (StringUtils.isNotEmpty(maritalStatus)) {
            suitableGender = suitableGender + maritalStatus;
        }
        suitablePeople.add(suitableGender);
    }

    private void addSuitableAge(List<String> suitablePeople, List<AttrM> dealAttrs) {
        String suitableAgeAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "px_suitable_age");
        if (StringUtils.isEmpty(suitableAgeAttrValue)) {
            return;
        }
        String suitableAge = Lists.newArrayList("青年", "中年", "老年").stream()
                .filter(age -> suitableAgeAttrValue.contains(age))
                .map(age -> age.replaceAll("年", StringUtils.EMPTY))
                .reduce(String::concat)
                .map(age -> "青中老".equals(age) ? "全年龄（18岁以上）" : age + "年")
                .orElse(null);
        if (StringUtils.isEmpty(suitableAge)) {
            return;
        }
        suitablePeople.add(suitableAge);
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(String attrName, String attrValue) {
        if (StringUtils.isEmpty(attrName) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(attrName);
        dealDetailStructAttrModuleVO.setAttrValues(Lists.newArrayList(attrValue));
        return dealDetailStructAttrModuleVO;
    }

}

