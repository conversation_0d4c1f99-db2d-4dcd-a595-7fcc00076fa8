package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/4/27
 */
@VPoint(name = "商品区-查看更多-跳转链接", description = "DzMoreComponentVO.jumpUrl", code = MoreComponentUrlVP.CODE, ability = FloorsBuilder.CODE)
public abstract class MoreComponentUrlVP<T> extends PmfVPoint<String, MoreComponentUrlVP.Param, T> {
    public static final String CODE = "MoreComponentUrlVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * {@link ShelfActivityConstants.Params#dpPoiId}
         */
        private int dpPoiId;
        private long dpPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#mtPoiId}
         */
        private int mtPoiId;
        private long mtPoiIdL;

        /**
         * {@link ShelfActivityConstants.Params#platform}
         */
        private int platform;

        /**
         * 筛选id
         */
        private long filterId;

        /**
         * {@link ShelfActivityConstants.Params#shopUuid}
         */
        private String shopUuid;

        /**
         * {@link ShelfActivityConstants.Params#userAgent}
         */
        private int userAgent;

        private String groupName;

        private Integer cityId;

        private Integer locationCityId;

        private Double lng;

        private Double lat;

    }
}
