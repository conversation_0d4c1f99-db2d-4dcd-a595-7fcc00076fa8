package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemJumpUrlVP;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "跳转链接拼接渠道来源",
        description = "跳转链接拼接渠道来源",
        code = UnifiedShelfItemSourceJumpUrlOpt.CODE)
public class UnifiedShelfItemSourceJumpUrlOpt extends UnifiedShelfItemJumpUrlVP<Void> {

    public static final String CODE = "UnifiedShelfItemSourceJumpUrlOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Void aVoid) {
        ProductM productM = param.getProductM();
        if(StringUtils.isEmpty(productM.getJumpUrl())){
            return null;
        }
        //拼接货架渠道
        String jumpUrl = productM.getJumpUrl();
        // 检查是否已经包含 source= 参数
        if (!containsSourceParam(jumpUrl)) {
            return jumpUrl + "&source=poi_page";
        }
        return jumpUrl;
    }

    private boolean containsSourceParam(String url) {
        // 确保参数前有&或?，避免部分匹配的情况
        return url.matches(".*[?&]source=.*");
    }

}
