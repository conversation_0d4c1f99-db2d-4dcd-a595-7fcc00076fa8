package com.sankuai.dzviewscene.product.shelf.options.filter.anchor;

import com.dianping.product.shelf.common.enums.NavRouterTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ProductAnchorVP;
import com.sankuai.dzviewscene.product.shelf.common.ProductAnchorInfo;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

@VPointOption(name = "意图识别锚定导航",
        description = "可配置导航锚定路由类型，用于bp侧路由锚定召回商品",
        code = "IntentionKeyAnalyzeOpt"
)
public class IntentionKeyAnalyzeOpt extends ProductAnchorVP<IntentionKeyAnalyzeOpt.Config> {

    @Override
    public ProductAnchorInfo compute(ActivityCxt context, Param param, Config config) {
        String keyword = context.getParam(ShelfActivityConstants.Params.keyword);
        //搜索词为空，返回首tab
        if (StringUtils.isBlank(keyword) || Objects.isNull(config) || config.getRouterType()<=0) {
            return getFirstNavAnchor();
        }
        ProductAnchorInfo productAnchorInfo = new ProductAnchorInfo();
        productAnchorInfo.setRouterType(config.getRouterType());
        productAnchorInfo.setMatchKey(keyword);
        return productAnchorInfo;
    }

    @VPointCfg
    @Data
    public static class Config {
        private int routerType;
    }
}
