package com.sankuai.dzviewscene.product.dealstruct.ability.videoModule;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints.DealDetailVideoModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 11:43 上午
 */
@Ability(code = DealDetailVideoModuleBuilder.CODE,
        name = "团购详情模块视频组件构造能力",
        description = "团购详情模块视频组件构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealDetailDouhuFetcher.CODE
        }
)
public class DealDetailVideoModuleBuilder extends PmfAbility<List<VideoModuleVO>, DealDetailVideoModuleParam, DealDetailVideoModuleCfg> implements ModuleStrategy {

    public static final String CODE = "dealDetailVideoModuleBuilder";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        String abilityCode = DealDetailVideoModuleBuilder.CODE;
        List<VideoModuleVO> videoModuleVOS = activityCxt.getSource(abilityCode);
        if (CollectionUtils.isEmpty(videoModuleVOS)) {
            return null;
        }
        VideoModuleVO videoModuleVO = CollectUtils.firstValue(videoModuleVOS);
        return buildDealDetailModuleVO(videoModuleVO);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(VideoModuleVO videoModuleVO) {
        if (videoModuleVO == null) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setVideoModuleVO(videoModuleVO);
        return dealDetailModuleVO;
    }

    @Override
    public CompletableFuture<List<VideoModuleVO>> build(ActivityCxt activityCxt, DealDetailVideoModuleParam dealDetailVideoModuleParam, DealDetailVideoModuleCfg dealDetailVideoModuleCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        List<DouhuResultModel> douhuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(null);
        }
        List<VideoModuleVO> detailStructAttrModels = dealDetailInfoModels.stream().map(detailModel -> {
            if(detailModel == null) {
                return null;
            }
            List<AttrM> dealAttrs = detailModel.getDealAttrs();
            DealDetailDtoModel dealDetailDtoModel = detailModel.getDealDetailDtoModel();
            List<ProductSkuCategoryModel> productCategories = detailModel.getProductCategories();
            DealDetailVideoModuleVP<?> dealDetailVideoModuleVP = findVPoint(activityCxt, DealDetailVideoModuleVP.CODE);
            return dealDetailVideoModuleVP.execute(activityCxt,
                    DealDetailVideoModuleVP.Param.builder().dealAttrs(dealAttrs).dealDetailDtoModel(dealDetailDtoModel).productCategories(productCategories).douhuResultModels(douhuResultModels).build());
        }).collect(Collectors.toList());
        return CompletableFuture.completedFuture(detailStructAttrModels);
    }
}
