package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/4/6 00:30
 */
@VPointOption(name = "折扣标签",
        description = "",
        code = "DiscountProductPromoOpt")
public class DiscountProductPromoOpt extends ProductPromosVP<DiscountProductPromoOpt.Config> {

    private static final BigDecimal TOP_LIMIT = new BigDecimal("9.9");

    private static final BigDecimal BOTTOM_LIMIT = new BigDecimal("0.1");

    @Override
    public List<DzPromoVO> compute(ActivityCxt context, Param param, Config config) {
        // 多次卡标签
        if (config.isUseTimeDealTag() && param.getProductM() != null && param.getProductM().isTimesDeal()) {
            return buildTimesCardTag(param, config);
        }
        BigDecimal salePrice = getSalePrice(param);
        BigDecimal marketOrBasicPrice = getMarketOrBasicPrice(param, config);
        if (Objects.equals(salePrice, BigDecimal.ZERO) || Objects.equals(marketOrBasicPrice, BigDecimal.ZERO)) {
            return new ArrayList<>();
        }
        // 取整策略(不填就是四舍五入)
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        if (Objects.nonNull(config.getRoundingType()) && config.getRoundingType().equals(1)) {
            roundingMode = RoundingMode.UP;
        }

        BigDecimal discountVal = getDiscountVal(salePrice, marketOrBasicPrice, config, roundingMode);
        if (discountVal.compareTo(getTopLimit(config)) > 0) {
            return new ArrayList<>();
        }

        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(String.format("%s折", discountVal));
        return Lists.newArrayList(dzPromoVO);
    }

    private List<DzPromoVO> buildTimesCardTag(Param param, Config config) {
        String timesStr = ProductMAttrUtils.getAttrValue(param.getProductM(), TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        int times = NumberUtils.toInt(timesStr, 0);
        if (times <= 0) {
            return null;
        }
        String dealTag = getDealTimeTagFormat(param, config);
        if (StringUtils.isBlank(dealTag)) {
            return null;
        }
        DzPromoVO dzPromoVO = new DzPromoVO();
        String timeDealTag = String.format(dealTag, times, param.getSalePrice());
        dzPromoVO.setName(timeDealTag);
        return Lists.newArrayList(dzPromoVO);
    }

    private String getDealTimeTagFormat(Param param, Config config) {
        // 优先使用自定义的商品标签
        if (CollectionUtils.isNotEmpty(param.getProductM().getProductTagList())
                && MapUtils.isNotEmpty(config.getProductTag2timeDealTag())
                && param.getProductM().getProductTagList().stream()
                .anyMatch(tag -> config.getProductTag2timeDealTag().containsKey(tag.getId()))) {
            return param.getProductM().getProductTagList().stream()
                    .filter(tag -> config.getProductTag2timeDealTag().containsKey(tag.getId()))
                    .map(tag -> config.getProductTag2timeDealTag().get(tag.getId())).findFirst().orElse(null);
        }
        return config.getTimeDealTag();
    }


    private BigDecimal getTopLimit(Config config) {
        if (config == null || BigDecimal.valueOf(config.getMaxDiscount()).compareTo(BigDecimal.ZERO) <= 0) {
            return TOP_LIMIT;
        }
        return BigDecimal.valueOf(config.getMaxDiscount());
    }


    private BigDecimal getSalePrice(Param param) {
        if (StringUtils.isEmpty(param.getSalePrice())) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(param.getSalePrice());
    }

    private BigDecimal getMarketOrBasicPrice(Param param, Config config) {
        if (config != null && config.useBasicPrice) {
            return param.getProductM().getBasePrice();
        }
        if (StringUtils.isEmpty(param.getProductM().getMarketPrice())) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(param.getProductM().getMarketPrice());
    }

    private BigDecimal getDiscountVal(BigDecimal salePrice, BigDecimal marketOrBasicPrice, Config config, RoundingMode roundingMode) {
        //原始值
        BigDecimal discountVal = salePrice.divide(marketOrBasicPrice, 3, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(10))
                .setScale(1, roundingMode);
        BigDecimal finalDiscountVal = discountVal.compareTo(BOTTOM_LIMIT) > 0 ? discountVal : BOTTOM_LIMIT;
        if (config != null && Boolean.TRUE.equals(config.needRounding)) {
            finalDiscountVal = finalDiscountVal.stripTrailingZeros();
        }
        return finalDiscountVal;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 最大折扣
         */
        private double maxDiscount;
        /**
         * 是否使用基础价格做折扣计算，默认false：使用门市价，true:使用基础价格（没有减任何优惠）
         */
        private boolean useBasicPrice;

        /**
         * 是否整数取整，例如7.0折取整为7折
         */
        private boolean needRounding;

        /**
         * 斗斛实验策略列表
         */
        private List<String> douHuSks;

        /**
         * 取整策略，不填 就是四舍五入，1是向上取整
         */
        private Integer roundingType;
        /**
         * 是否使用团单次卡标签
         */
        private boolean useTimeDealTag;
        /**
         * 团单次卡标签
         */
        private String timeDealTag = "%s次¥%s";

        /**
         * 商品标签映射的团单次卡标签展示格式
         * key为商品tagId，value为团单次卡标签format格式，例如：{"100310590":"%s节¥%s"} // 100310590: 安心学商品
         */
        private Map<String, String> productTag2timeDealTag;
    }

}
