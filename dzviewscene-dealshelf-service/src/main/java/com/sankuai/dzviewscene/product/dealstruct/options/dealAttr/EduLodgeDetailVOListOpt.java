package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@VPointOption(name = "教育团单的寄宿详情AttrVO列表变化点", description = "教育团单的寄宿详情AttrVO列表变化点",code = EduLodgeDetailVOListOpt.CODE)
@Slf4j
public class EduLodgeDetailVOListOpt extends DealAttrVOListVP<EduLodgeDetailVOListOpt.Config> {

    public static final String CODE = "EduLodgeDetailVOListOpt";
    public static final String ATTR_BOARDING_SPECIFICATION = "boarding_specification";
    public static final String ATTR_SHOW_NAME_ROOM_SPEC = "房间规格";
    public static final String ROOM_FACILITY = "room_facility";
    private static final String ATTR_SHOW_NAME_ROOM_FACILITIES = "房间设施";
    public static final String ATTR_STUDY_ROOM_BEGIN_TIME = "study_room_begin_time";
    public static final String ATTR_STUDY_ROOM_END_TIME = "study_room_end_time";
    private static final String ATTR_SHOW_NAME_STUDY_ROOM = "自习室";
    public static final String STUDY_ROOM_VALUE_FORMAT = "%s-%s开放";
    public static final String ATTR_SHOW_NAME_CANTEEN_SUPPLY = "食堂供应";
    public static final String ATTR_CANTEEN_SUPPLY_MEAL_TYPE = "canteen_supply_meal_type";
    public static final String ATTR_SHOW_NAME_OTHER_FACILITY = "其他设施";
    public static final String ATTR_OTHER_FACILITY = "other_facility";
    public static final String FREE_STUDY_ROOM = "free_study_room";
    public static final String FREE_CANTEEN_SUPPLY = "free_canteen_supply";
    public static final String FREE_SUFFIX = "，免费使用";
    public static final String ATTR_YES = "是";


    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        List<DealDetailStructAttrModuleVO> dealDetailStructAttrModuleVOS = buildDealDetailStructAttrModuleVO(param);
        if (CollectionUtils.isEmpty(dealDetailStructAttrModuleVOS)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel groupModule = new DealDetailStructAttrModuleGroupModel();
        groupModule.setGroupName(config.getTitle());
        groupModule.setDealDetailStructAttrModuleVOS(dealDetailStructAttrModuleVOS);
        return Lists.newArrayList(groupModule);
    }

    private List<DealDetailStructAttrModuleVO> buildDealDetailStructAttrModuleVO(Param param) {
        List<DealDetailStructAttrModuleVO> result = Lists.newArrayList();
        // 房间规格
        result.add(buildRoomSpecVO(param));
        // 房间设施
        result.add(buildModuleVOFromStrListJsonAttr(param, ATTR_SHOW_NAME_ROOM_FACILITIES, ROOM_FACILITY));
        // 自习室
        result.add(buildStudyRoomVO(param));
        // 食堂供应
        result.add(buildCanteenSupplyVO(param));
        // 其他设施
        result.add(buildModuleVOFromStrListJsonAttr(param, ATTR_SHOW_NAME_OTHER_FACILITY, ATTR_OTHER_FACILITY));
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private DealDetailStructAttrModuleVO buildStudyRoomVO(Param param) {
        String studyRoomBeginTime = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), ATTR_STUDY_ROOM_BEGIN_TIME);
        String studyRoomEndTime = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), ATTR_STUDY_ROOM_END_TIME);
        if (StringUtils.isAnyBlank(studyRoomBeginTime, studyRoomEndTime)) {
            return null;
        }
        String openTimeStr = String.format(STUDY_ROOM_VALUE_FORMAT, studyRoomBeginTime, studyRoomEndTime);
        String isFreeStr = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), FREE_STUDY_ROOM);
        if (StringUtils.isNotEmpty(openTimeStr) && ATTR_YES.equals(isFreeStr)) {
            openTimeStr += FREE_SUFFIX;
        }
        DealDetailStructAttrModuleVO moduleVO = new DealDetailStructAttrModuleVO();
        moduleVO.setAttrName(ATTR_SHOW_NAME_STUDY_ROOM);
        moduleVO.setAttrValues(Lists.newArrayList(openTimeStr));
        return moduleVO;
    }

    private DealDetailStructAttrModuleVO buildCanteenSupplyVO(Param param) {
        List<String> readAttrValues = getStrListFromJsonAttr(param, ATTR_CANTEEN_SUPPLY_MEAL_TYPE);
        if (CollectionUtils.isEmpty(readAttrValues)) {
            return null;
        }

        String isFreeStr = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), FREE_CANTEEN_SUPPLY);
        String suffix = ATTR_YES.equals(isFreeStr) ? FREE_SUFFIX : "";
        DealDetailStructAttrModuleVO moduleVO = new DealDetailStructAttrModuleVO();
        moduleVO.setAttrName(ATTR_SHOW_NAME_CANTEEN_SUPPLY);
        moduleVO.setAttrValues(Lists.newArrayList(String.join("、", readAttrValues) + suffix));
        return moduleVO;
    }

    private DealDetailStructAttrModuleVO buildModuleVOFromStrListJsonAttr(Param param, String moduleName, String attrKey) {
        List<String> readAttrValues = getStrListFromJsonAttr(param, attrKey);
        if (CollectionUtils.isEmpty(readAttrValues)) {
            return null;
        }
        DealDetailStructAttrModuleVO moduleVO = new DealDetailStructAttrModuleVO();
        moduleVO.setAttrName(moduleName);
        moduleVO.setAttrValues(Lists.newArrayList(String.join("、", readAttrValues)));
        return moduleVO;
    }

    private DealDetailStructAttrModuleVO buildRoomSpecVO(Param param) {
        String roomSpec = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), ATTR_BOARDING_SPECIFICATION);
        if (StringUtils.isBlank(roomSpec)) {
            return null;
        }
        DealDetailStructAttrModuleVO moduleVO = new DealDetailStructAttrModuleVO();
        moduleVO.setAttrName(ATTR_SHOW_NAME_ROOM_SPEC);
        moduleVO.setAttrValues(Lists.newArrayList(roomSpec));
        return moduleVO;
    }

    private List<String> getStrListFromJsonAttr(Param param, String attrName) {
        String json = DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), attrName);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonCodec.decode(json, new TypeReference<List<String>>() {});
    }

    @Data
    @VPointCfg
    public static class Config {

        private String title = "寄宿详情";

    }

}
