package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembtn;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonVP;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@VPointOption(name = "商场货架按钮样式", description = "商场团购货架预热单定时释放库存抢购", code = "ShoppingMallItemButtonOpt")
public class ShoppingMallItemButtonOpt extends ItemButtonVP<ShoppingMallItemButtonOpt.Config> {

    @Override
    public DzSimpleButtonVO compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        // 预热
        DzSimpleButtonVO warmUpButton = buildWarmUpButton(productM);
        if (Objects.nonNull(warmUpButton)) {
            return warmUpButton;
        }
        // 无按钮
        if (config == null || config.getType() != 1) {
            return null;
        }
        // 领券
        DzSimpleButtonVO attrButton = buildAttrButton(productM, config);
        if (Objects.nonNull(attrButton)) {
            return attrButton;
        }
        return buildButton(null, "抢购");
    }

    private DzSimpleButtonVO buildAttrButton(ProductM productM, Config config) {
        if (haveCoupons(productM, config.getCouponAttr2Doc())) {
            return buildButton(null, config.getCouponAttr2Doc().values().stream().findFirst().orElse(config.getName()));
        }
        return null;
    }

    /**
     * 构建预热按钮
     *
     * @param productM
     * @return
     */
    private DzSimpleButtonVO buildWarmUpButton(ProductM productM) {
        WarmUpStageEnum.WarmUpStageResult warmUpStageResult = WarmUpStageEnum.getWarmUpStageResult(productM);
        if (!warmUpStageResult.isValidWarmUpStage()) {
            return null;
        }
        switch (warmUpStageResult.getStage()) {
            default:
            case ONLY_WARM_UP_PRESALE:
            case NOT_STARTED_TIME_STOCK:
            case ONLY_TIME_STOCK_PRESALE:
                return buildButton(productM.getJumpUrl(), "即将开抢");
            case ONLY_WARM_UP_SALE:
            case IN_PROCESS_TIME_STOCK:
                return buildButton(productM.getOrderUrl(), "立即抢");
            case CURRENT_TIME_END_HAVE_NEXT:
            case CURRENT_STOCK_END_HAVE_NEXT:
                return buildButton(productM.getJumpUrl(), "已抢光");
            case NO_NEXT_TIME_STOCK:
            case NO_STOCK:
                return buildButton(productM.getJumpUrl(), "抢购结束");
        }
    }


    private DzSimpleButtonVO buildButton(String jumpUrl, String buttonName) {
        DzSimpleButtonVO button = new DzSimpleButtonVO();
        button.setJumpUrl(jumpUrl);
        button.setName(buttonName);
        button.setType(1);
        return button;
    }

    private boolean haveCoupons(ProductM productM, Map<String, String> couponAttr2Doc) {
        if (MapUtils.isEmpty(couponAttr2Doc)) {
            return false;
        }
        String value = productM.getAttr(couponAttr2Doc.keySet().stream().findFirst().orElse(null));
        //主题文案false为代表有券未领用
        return "false".equals(value);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 1：按钮， 0 无按钮
         */
        private int type = 0;

        /**
         * 券attr和展示的文案，一般只有一个
         */
        private Map<String, String> couponAttr2Doc;

        /**
         * 按钮名称
         */
        private String name;
    }
}
