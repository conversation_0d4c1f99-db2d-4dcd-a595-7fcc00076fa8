package com.sankuai.dzviewscene.product.enums;

/**
 * 商品类型
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/1/9.
 */
public enum  ProductTypeEnum {
    UNKNOWN(0, 0,"未知"),
    DEAL(1, 1, "团购"),
    SPU(2, 2, "泛商品"),
    PACK(3, 3, "打包套餐商品"),
    GENERAL_SPU(4, 4, "标品"),
    TIME_CARD(5,5,"次卡"),
    SKU(7, 7, "SKU"),
    SPT_SPU(9, 9, "标准商品体系SPU")
    ;

    private int type;
    private int originType;
    private String desc;

    /**
     * 根据原始类型获取商品的类型
     * @param originType
     * @return
     */
    public static int getTypeByOriginType(int originType) {
        for (ProductTypeEnum productTypeEnum : ProductTypeEnum.values()) {
            if (productTypeEnum.originType == originType) {
                return productTypeEnum.type;
            }
        }
        return 0;
    }

    public static ProductTypeEnum getByType(int type) {
        for (ProductTypeEnum productTypeEnum : ProductTypeEnum.values()) {
            if (productTypeEnum.type == type) {
                return productTypeEnum;
            }
        }
        return UNKNOWN;
    }

    ProductTypeEnum(int type, int originType, String desc) {
        this.type = type;
        this.desc = desc;
        this.originType = originType;
    }

    public int getOriginType() {
        return originType;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
