package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.ItemProductTagsHeapOpt;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2023/5/26 5:51 PM
 **/
@VPointOption(name = "医疗体检-第二行副标题",
        description = "医疗体检-第二行副标题",
        code = "MedicalHospitalPriceAboveTagsOpt"
)
public class MedicalHospitalPriceAboveTagsOpt extends ItemPriceAboveTagsVP<MedicalHospitalPriceAboveTagsOpt.Config> {

    private static final String MEDICAL_BOOKINGTAG = "medicalBookingTag";
    private static final String MEDICAL_BOOKABLE = "medicalBookable";
    private static final String SERVICE_TYPE = "service_type";

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (productM == null) {
            return null;
        }
        List<DouHuM> douHuList = ShelfDouHuFetcher.getDouHuList(context);
        List<String> douHuId = Lion.getList("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.physical.examination.subtitle.douhu.exp.ids", String.class, Lists.newArrayList());
        DouHuM douHuM = douHuList.stream().filter(douHu -> douHuId.contains(douHu.getExpId())).findFirst().orElse(null);
        if (douHuM == null || douHuM.getSk() == null || douHuM.getSk().contains("a")) {
            return null;
        } else if (douHuM.getSk().contains("b")){
            List<DzTagVO> res = new ArrayList<>();
            String serviceType = getAttr(param.getProductM(),SERVICE_TYPE);
            addBookInfo(productM, config, res);
            String lion = getLionMap();
            LionConfig data = JSON.parseObject(lion, LionConfig.class);
            if (!Lists.newArrayList(config.getExamServiceType(),config.getHealthyServiceType()).contains(serviceType)) {
                //    健康体检/兜底
                addTargetPop(productM,res,data.getTargetPop(),config);
                addNormalTime(productM,res,data.getReportTime(),config);
                addSpecialService(productM,res,data.getSpecialService(),config);
            } else if (config.getExamServiceType().equals(serviceType)) {
                addNormalTime(productM,res,data.getReportTime(),config);
                addSpecialService(productM,res,data.getSpecialService(),config);
            } else {
                addNormalTime(productM,res,data.getElcCardTime(),config);
                addNormalTime(productM,res,data.getPhyCardTime(),config);
            }
            return res;
        }
        return null;
    }

    private String getLionMap() {
        return Lion.getString("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.medical.examination.product.tag.config");
    }

    private void addSpecialService(ProductM productM, List<DzTagVO> res, List<Tag> tags, Config config) {
        if (CollectionUtils.isEmpty(tags) || CollectionUtils.isEmpty(productM.getProductTagList())) {
            return;
        }
        List<String> tagIds = productM.getProductTagList().stream().map(TagM::getId).collect(Collectors.toList());
        List<Tag> filterContent = tags.stream().filter(content -> tagIds.contains(String.valueOf(content.getProductTagId()))).sorted(Comparator.comparing(Tag::getPriority)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterContent)){
            return;
        }
        if (filterContent.size() > 3) {
            filterContent = filterContent.subList(0, 3);
        }
        for (Tag content : filterContent) {
            DzTagVO dzTagVO = new DzTagVO();
            dzTagVO.setText(content.getProductTagName());
            dzTagVO.setTextColor(config.getTextColor());
            dzTagVO.setBackground(config.getBackground());
            dzTagVO.setBorderRadius(config.getBorderRadius());
            dzTagVO.setNoGapBetweenPicText(config.isNoGapText());
            res.add(dzTagVO);
        }
    }

    private void addNormalTime(ProductM productM, List<DzTagVO> res, List<Tag> tags, Config config) {
        if (CollectionUtils.isEmpty(tags) || CollectionUtils.isEmpty(productM.getProductTagList())) {
            return;
        }
        List<String> tagIds = productM.getProductTagList().stream().map(TagM::getId).collect(Collectors.toList());
        Tag filterContent = tags.stream().filter(ruleContent -> tagIds.contains(String.valueOf(ruleContent.getProductTagId()))).findFirst().orElse(null);
        if (filterContent == null) {
            return;
        }
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setText(filterContent.getProductTagName());
        dzTagVO.setTextColor(config.getTextColor());
        dzTagVO.setBackground(config.getBackground());
        dzTagVO.setBorderRadius(config.getBorderRadius());
        dzTagVO.setNoGapBetweenPicText(config.isNoGapText());
        res.add(dzTagVO);
    }

    private void addTargetPop(ProductM productM, List<DzTagVO> res, List<Tag> tags, Config config) {
        if (CollectionUtils.isEmpty(tags) || CollectionUtils.isEmpty(productM.getProductTagList())) {
            return;
        }
        List<String> tagIds = productM.getProductTagList().stream().map(TagM::getId).collect(Collectors.toList());
        List<Tag> filterContent = tags.stream().filter(content -> tagIds.contains(String.valueOf(content.getProductTagId()))).sorted(Comparator.comparing(Tag::getPriority)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterContent)){
            return;
        }
        StringBuilder sb = new StringBuilder();
        sb.append("适合");
        if (filterContent.size() >= 2) {
            List<Tag> list = filterContent.subList(0, 2);
            for (Tag ruleContent : list) {
                sb.append(ruleContent.getProductTagName()).append("/");
            }
            sb.deleteCharAt(sb.length() - 1);
            sb.append("人群");
        } else {
            for (Tag ruleContent : filterContent) {
                sb.append(ruleContent.getProductTagName()).append("人群");
            }
        }
        DzTagVO tagVO = new DzTagVO();
        tagVO.setText(sb.toString());
        tagVO.setTextColor(config.getTextColor());
        tagVO.setBackground(config.getBackground());
        tagVO.setBorderRadius(config.getBorderRadius());
        tagVO.setNoGapBetweenPicText(config.isNoGapText());
        res.add(tagVO);
    }

    private String getAttr(ProductM productM, String key) {
        if (productM == null || key == null) {
            return "";
        }
        String attr = productM.getAttr(key);
        if (attr == null) {
            return "";
        }
        return attr;
    }

    private List<DzTagVO> addBookInfo(ProductM productM, Config config, List<DzTagVO> res) {
        String bookable = getExtAttrValueByName(productM, MEDICAL_BOOKABLE);
        if (BooleanUtils.toBoolean(bookable) && bookable != null) {
            String bookingTag = getExtAttrValueByName(productM, MEDICAL_BOOKINGTAG);
            if (StringUtils.isEmpty(bookingTag)) {
                return res;
            }
            DzTagVO dzTagVO = new DzTagVO();
            dzTagVO.setText(bookingTag);
            dzTagVO.setTextColor(config.getBookColor());
            dzTagVO.setBackground(config.getBackground());
            dzTagVO.setBorderRadius(config.getBorderRadius());
            dzTagVO.setNoGapBetweenPicText(config.isNoGapText());
            res.add(dzTagVO);
            return res;
        }
        return res;
    }

    private String getExtAttrValueByName(ProductM productM, String name) {
        if (productM == null || productM.getExtAttrs() == null) {
            return null;
        }
        return productM.getExtAttrs().stream()
                .filter(attrM -> attrM != null && name.equals(attrM.getName()))
                .findFirst()
                .map(AttrM::getValue)
                .orElse(null);
    }

    @VPointCfg
    @Data
    public static class Config {

        String background;

        String textColor;

        String bookColor;

        double borderRadius;

        boolean noGapText;

        String examServiceType;

        String healthyServiceType;
    }

    @Data
    static class LionConfig {
        @JSONField(name = "targetPop")
        private List<Tag> targetPop;
        @JSONField(name = "reportTime")
        private List<Tag> reportTime;
        @JSONField(name = "specialService")
        private List<Tag> specialService;
        @JSONField(name = "elcCardTime")
        private List<Tag> elcCardTime;
        @JSONField(name = "phyCardTime")
        private List<Tag> phyCardTime;
        // getters and setters
    }

    @Data
    static class Tag {
        @JSONField(name = "productTagId")
        private Long productTagId;
        @JSONField(name = "productTagName")
        private String productTagName;
        @JSONField(name = "priority")
        private Integer priority;
    }
}
