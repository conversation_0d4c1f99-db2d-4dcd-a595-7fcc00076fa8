package com.sankuai.dzviewscene.product.shelf.ability.query;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.activity.product.ProductShelfActivity;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Created on 2021/8/21
 */
@Ability(code = ProductQueryFetcher.CODE,
        name = "商品信息统一融合查询能力",
        description = "包含商品召回、填充、排序的融合查询能力。\n" +
                "入参主要为外部传参(平台、商户ID)与能力配置信息。\n" +
                "能力为多种能力的集成，包括：1.多门店多组商品召回能力，可根据查询类型配置选择对应的召回渠道，召回门店下的多组商品。2.多组商品填充能力，可根据配置的填充类型选择填充能力，根据填充参数决定填充内容。3.可选排序能力，可根据配置的排序ID，商品组下的商品进行排序。\n" +
                "返回标准的商品组模型集合，以商品组名为Key，商品组模型为Model的Map形式。",
        activities = { DealShelfActivity.CODE, SpuDetailActivity.CODE, ProductShelfActivity.CODE}
)
public class ProductQueryFetcher extends PmfAbility<Map<String, ProductGroupM>, ProductQueryParam, ProductQueryCfg> {

    public static final String CODE = "productMergeQueryFetcher";

    @Resource
    private MultiGroupMergeQueryFetcher multiGroupMergeQueryFetcher;

    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityCxt activityCxt, ProductQueryParam mergeQueryRequest, ProductQueryCfg mergeQueryConfig) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.shelf.ability.query.ProductQueryFetcher.build(ActivityCxt,ProductQueryParam,ProductQueryCfg)");
        //1.增加配置化参数
        List<String> groupNames = mergeQueryConfig.getGroupNames();
        Map<String, Map<String, Object>> groupParams = mergeQueryConfig.getGroupParams();
        activityCxt.addParam(QueryFetcher.Params.groupNames, groupNames);
        activityCxt.addParam(QueryFetcher.Params.groupParams, groupParams);

        //2.调用原统一融合查询能力
        return multiGroupMergeQueryFetcher.build(ActivityCtxtUtils.toActivityContext(activityCxt));
    }
}
