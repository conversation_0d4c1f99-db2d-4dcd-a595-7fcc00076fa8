package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuItemModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * created by zhang<PERSON>yuan04 in 2022/1/4
 */
@VPoint(name = "Sku元素过滤能力", description = "过滤Sku元素份数信息，当返回值为False时，代表不展示该元素", code = SkuItemFilterVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuItemFilterVP<C> extends PmfVPoint<Boolean, SkuItemFilterVP.Param, C> {

    public static final String CODE = "SkuItemFilterVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 是否必选
         */
        private boolean isMust;

        /**
         * 分组标题
         */
        private String setTitle;

        /**
         * Sku信息
         */
        private SkuItemModel skuItemModel;

        /**
         * Sku category信息
         */
        private List<ProductSkuCategoryModel> productCategories;
    }
}
