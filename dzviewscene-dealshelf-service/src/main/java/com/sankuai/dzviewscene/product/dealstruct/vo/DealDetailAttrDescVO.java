package com.sankuai.dzviewscene.product.dealstruct.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * 团详属性的描述信息
 * https://mobile.sankuai.com/studio/model/info/28127
 * <AUTHOR>
 * @date 2022/2/23
 */
@MobileDo(id = 0x9ac0)
public class DealDetailAttrDescVO implements Serializable {
    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 属性的颜色
     */
    @MobileDo.MobileField(key = 0x4754)
    private String titleColor;


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitleColor() {
        return titleColor;
    }

    public void setTitleColor(String titleColor) {
        this.titleColor = titleColor;
    }

    @Override
    public String toString() {
        return "DealDetailAttrDescVO{" +
                "title='" + title + '\'' +
                ", titleColor='" + titleColor + '\'' +
                '}';
    }
}
