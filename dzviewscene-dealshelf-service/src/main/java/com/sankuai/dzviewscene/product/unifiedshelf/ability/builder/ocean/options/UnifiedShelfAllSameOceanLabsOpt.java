package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.options;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.vp.UnifiedShelfOceanLabsVP;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@VPointOption(name = "通用-labs 埋点",
        description = "按需填写需要埋点的字段和数据，缺点是打的都一样）",
        code = "UnifiedShelfAllSameOceanLabsOpt",
        isDefault = true)
public class UnifiedShelfAllSameOceanLabsOpt extends UnifiedShelfOceanLabsVP<UnifiedShelfAllSameOceanLabsOpt.Config> {
    @Override
    public Map<String, String> compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getFields())) {
            return new HashMap<>();
        }
        Map<String, Object> labsMap = new HashMap<>();
        if (config.isCategoryId()) {
            labsMap.put("cat_id", getCategoryId(param));
        }
        if (config.isPoiId()) {
            labsMap.put("poi_id", getPoiId(param));
        }
        if (config.isCustomizeArray()) {
            padCustomizeArray(labsMap, config);
        }
        return getResultLabsMap(JsonCodec.encode(labsMap), config.getFields());
    }

    private void padCustomizeArray(Map<String, Object> labsMap, Config config) {
        if (CollectionUtils.isNotEmpty(config.getCustomizeArrayList())) {
            labsMap.put("customize_array", config.getCustomizeArrayList());
            return;
        }
        labsMap.put("customize_array", -999);
    }

    private Map<String, String> getResultLabsMap(String labs, List<String> fields) {
        Map<String, String> resultLabsMap = new HashMap<>(fields.size());
        for (String field : fields) {
            resultLabsMap.put(field, labs);
        }
        return resultLabsMap;
    }

    @VPointCfg
    @Data
    public static class Config {
        // cat_id
        private boolean categoryId;
        // poi_id
        private boolean poiId;
        // customize_array
        private boolean customizeArray;

        private List<String> customizeArrayList;
        /**
         * ShelfOceanVO 中所需要打点的字段名
         * {@link com.sankuai.dzviewscene.product.shelf.ability.builder.ocean.ShelfOceanFieldEnum}
         */
        private List<String> fields;
    }
}
