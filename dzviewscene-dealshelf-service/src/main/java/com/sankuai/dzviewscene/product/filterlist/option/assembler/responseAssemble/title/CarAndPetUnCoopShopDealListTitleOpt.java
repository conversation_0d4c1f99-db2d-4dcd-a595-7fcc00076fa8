package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.CarAndPetUnCoopCommonInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListTitleVP;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

@VPointOption(name = "养车用车/宠物非合作商户团购列表标题",description = "养车用车/宠物非合作商户团购列表标题",code = CarAndPetUnCoopShopDealListTitleOpt.CODE )
public class CarAndPetUnCoopShopDealListTitleOpt extends DealFilterListTitleVP<CarAndPetUnCoopShopDealListTitleOpt.Config> {
    public static final String CODE = "CarAndPetUnCoopShopDealListTitleOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if (!config.isUseDefaultTitle()) {
            CarAndPetUncoopShopShelfAttrM carAndPetUncoopShopShelfAttrM = activityCxt
                    .getParam(CarAndPetUnCoopCommonInfoOpt.CODE);
            if (Objects.nonNull(carAndPetUncoopShopShelfAttrM)
                    && StringUtils.isNotBlank(carAndPetUncoopShopShelfAttrM.getModuleName())) {
                return carAndPetUncoopShopShelfAttrM.getModuleName();
            }
        }
        return config.getDefaultTitle();
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 是否使用配置默认标题，默认不使用
         */
        private boolean useDefaultTitle = false;
        /**
         * 默认标题
         */
        private String defaultTitle;
    }
}
