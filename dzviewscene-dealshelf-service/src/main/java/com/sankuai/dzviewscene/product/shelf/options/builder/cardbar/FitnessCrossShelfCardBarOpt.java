package com.sankuai.dzviewscene.product.shelf.options.builder.cardbar;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.options.FitnessCrossDataFetchOpt;
import com.sankuai.dzviewscene.product.enums.FitnessCrossIdentityEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.cardbar.ShelfCardBarVP;
import com.sankuai.dzviewscene.product.shelf.utils.FitnessCrossSkipUrlUtil;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfCardBarVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;

@Slf4j
@VPointOption(name = "货架横幅",
        description = "返回：货架横幅对象",
        code = "FitnessCrossShelfCardBarOpt")
public class FitnessCrossShelfCardBarOpt extends ShelfCardBarVP<FitnessCrossShelfCardBarOpt.Config> {

    @Override
    public DzShelfCardBarVO compute(ActivityCxt context, Param param, Config config) {
        // 获取身份
        FitnessCrossDataFetchOpt.Context dataFetchContext = ParamsUtil.getObjSafely(context, "FitnessCrossDataFetchOpt", FitnessCrossDataFetchOpt.Context.class);

        int platform = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.platform);
        long shopId = platform == VCPlatformEnum.DP.getType() ? ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.dpPoiIdL) : ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.mtPoiIdL);
        String shopUuid = ParamsUtil.getStringSafely(context, ShelfActivityConstants.Params.shopUuid);
        String skipUrl = FitnessCrossSkipUrlUtil.getRechargeUrl(VCPlatformEnum.DP.getType() == platform ? config.getDpJumpUrlFormat() : config.getMtJumpUrlFormat(), shopId, shopUuid);

        DzShelfCardBarVO cardBarVO = new DzShelfCardBarVO();

        cardBarVO.setJumpUrl(skipUrl);
        cardBarVO.setTitle(getTitle(config, dataFetchContext));
        cardBarVO.setType(getType(dataFetchContext));

        return cardBarVO;
    }

    /**
     * 获取类型
     */
    private int getType(FitnessCrossDataFetchOpt.Context dataFetchContext) {
        if (dataFetchContext == null) {
            return 0;
        }
        FitnessCrossIdentityEnum identityEnum = dataFetchContext.getIdentityEnum();
        // 老客返回1，其他返回0
        return (identityEnum == null || FitnessCrossIdentityEnum.TOURIST.equals(identityEnum) || FitnessCrossIdentityEnum.NOT_PAY.equals(identityEnum)) ? 0 : 1;
    }

    /**
     * 获取标题
     */
    private String getTitle(Config config, FitnessCrossDataFetchOpt.Context context) {
        if (context == null || context.getIdentityEnum() == null) {
            log.error("FitnessCrossShelfCardBarOpt.getTitle fail, param error, context is [{}]", JSON.toJSONString(context));
            return null;
        }
        return MapUtils.isEmpty(config.getTitleConfig()) ? null : config.getTitleConfig().get(context.getIdentityEnum().name());
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 文案
         * 首充用户/游客： 健身通3次团课兑换券，仅需XX元，每节课低至Y元
         * 复充用户：     老客特惠，健身通3次团课兑换券，仅需AA元
         */
        private Map<String, String> titleConfig;

        /**
         * 点评跳转链接
         */
        private String dpJumpUrlFormat;

        /**
         * 美团跳转链接
         */
        private String mtJumpUrlFormat;

    }

}
