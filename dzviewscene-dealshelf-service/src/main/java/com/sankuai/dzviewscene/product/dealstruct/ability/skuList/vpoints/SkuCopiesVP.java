package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/26 6:35 下午
 */

@VPoint(name = "团购详情sku份数变化点", description = "团购详情sku份数变化点", code = SkuCopiesVP.CODE, ability = DealDetailSkuListsBuilder.CODE)
public abstract class SkuCopiesVP<T> extends PmfVPoint<String, SkuCopiesVP.Param, T>{

    public static final String CODE = "SkuCopiesVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private int copies;
        private SkuItemDto skuItemDto;
        private List<AttrM> dealAttrs;
    }

}
