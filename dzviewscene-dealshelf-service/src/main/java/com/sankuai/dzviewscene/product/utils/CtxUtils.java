package com.sankuai.dzviewscene.product.utils;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.client.container.AthenaBeanFactory;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheCompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.cache.CacheGrayUtils;
import com.sankuai.dzviewscene.nr.atom.cache.CacheMethodEnum;
import com.sankuai.dzviewscene.product.ability.mergequery.ProductMergeQueryAbility;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

public class CtxUtils {

    public static final String DP_SHELF_DEFAULT_CONVERT = "DpShelfDefaultConvert";
    public static final String SHELF_DEFAULT_CONVERT = "shelfDefaultConvert";
    public static final String DEAL_SHELF = "dealShelf";
    public static final String CONVERT_OK = "convertOK";



    public static List<ProductM> getProductsFromCtx(ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.utils.CtxUtils.getProductsFromCtx(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        Map<String, ProductGroupM> productGroupMs = ctx.getSource(ProductMergeQueryAbility.CODE);
        if (MapUtils.isEmpty(productGroupMs)) {
            return Lists.newArrayList();
        }
        ProductGroupM groupM = productGroupMs.values().stream().findFirst().orElse(null);
        return groupM == null ? Lists.newArrayList() : groupM.getProducts();
    }

    public static ProductGroupM getFirstProductGroup(Map<String, ProductGroupM> productGroupMs) {
        if (MapUtils.isEmpty(productGroupMs)) {
            return null;
        }
        return productGroupMs.values().stream().findFirst().orElse(null);
    }

    /**
     * 给点评侧添加美团侧ID：用户ID、城市ID
     * @param parameters
     * @return
     */
    public static String addCityIdAndUserIdForMagicMember(Map<String, Object> parameters) {
        int platform = ParamsUtil.getIntSafely(parameters, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            parameters.put(ShelfActivityConstants.Params.mtLocationCityId, ParamsUtil.getIntSafely(parameters, ShelfActivityConstants.Params.locationCityId));
            long mtShopId = ParamsUtil.getLongSafely(parameters, ShelfActivityConstants.Params.mtPoiIdL);
            boolean cacheSwitch = CacheGrayUtils.graySwitch(mtShopId, VCPlatformEnum.MT.getType(), CacheMethodEnum.CITY_ID_DP_2_MT.getCode());
            addShopCityId(parameters, cacheSwitch).join();
            return CONVERT_OK;
        }
        Transaction t = Cat.newTransaction(DP_SHELF_DEFAULT_CONVERT, DEAL_SHELF);
        // 转换用户ID
        CompletableFuture<Void> userIdF = fillMtUserId(parameters);
        long dpShopId = ParamsUtil.getLongSafely(parameters, ShelfActivityConstants.Params.dpPoiIdL);
        boolean cacheSwitch = CacheGrayUtils.graySwitch(dpShopId, VCPlatformEnum.DP.getType(), CacheMethodEnum.CITY_ID_DP_2_MT.getCode());
        // 转换商户Id
        CompletableFuture<Void> mtShopIdF = fillMtShopId(parameters, dpShopId);
        // 转换城市ID
        CompletableFuture<Void> mtCityIdF = fillMtCityId(parameters, cacheSwitch, ShelfActivityConstants.Params.dpCityId, ShelfActivityConstants.Params.mtCityId);
        CompletableFuture<Void> mtLocationCityIdF = fillMtCityId(parameters, cacheSwitch, ShelfActivityConstants.Params.locationCityId, ShelfActivityConstants.Params.mtLocationCityId);
        // 转换商户的城市ID
        CompletableFuture<Void> shopCityIdF = addShopCityId(parameters, cacheSwitch);
        CompletableFuture.allOf(userIdF, mtCityIdF, mtLocationCityIdF, shopCityIdF, mtShopIdF).join();
        // 上报耗时
        t.complete();
        return CONVERT_OK;
    }

    private static CompletableFuture<Void> fillMtShopId(Map<String, Object> parameters, long dpShopId) {
        long mtShopIdFromParam = ParamsUtil.getLongSafely(parameters, ShelfActivityConstants.Params.mtPoiIdL);
        if (mtShopIdFromParam > 0) {
            // 已有的数据，不需要覆盖
            return CompletableFuture.completedFuture(null);
        }
        CacheCompositeAtomService cacheCompositeAtomService = AthenaBeanFactory.getBean(CacheCompositeAtomService.class);
        return cacheCompositeAtomService.getMtByDpPoiIdL(dpShopId).thenAccept(mtShopId -> {
            if (mtShopId == null || mtShopId == 0) {
                return;
            }
            parameters.put(ShelfActivityConstants.Params.mtPoiIdL, mtShopId);
        });
    }

    private static CompletableFuture<Void> addShopCityId(Map<String, Object> parameters, boolean cacheSwitch) {
        ShopM shopM = (ShopM)parameters.get(ShelfActivityConstants.Ctx.ctxShop);
        if (shopM == null) {
            return CompletableFuture.completedFuture(null);
        }
        parameters.put(ShelfActivityConstants.Params.shopDpCityId, shopM.getCityId());
        return fillMtCityId(parameters, cacheSwitch, ShelfActivityConstants.Params.shopDpCityId, ShelfActivityConstants.Params.shopMtCityId);
    }

    private static CompletableFuture<Void> fillMtCityId(Map<String, Object> parameters, boolean cacheSwitch, String readKey, String convertKey) {
        int dpId = ParamsUtil.getIntSafely(parameters, readKey);
        int mtId = ParamsUtil.getIntSafely(parameters, convertKey);
        if (dpId <= 0 || mtId > 0) {
            return CompletableFuture.completedFuture(null);
        }
        if (cacheSwitch) {
            CacheCompositeAtomService cacheCompositeAtomService = AthenaBeanFactory.getBean(CacheCompositeAtomService.class);
            return cacheCompositeAtomService.getMtCityIdByDp(dpId).thenAccept(mtCityId -> {
                if (mtCityId == null || mtCityId == 0) {
                    return;
                }
                parameters.put(convertKey, mtCityId);
            });
        }
        CompositeAtomService compositeAtomService = AthenaBeanFactory.getBean(CompositeAtomService.class);
        return compositeAtomService.getMtCityIdByDp(dpId).thenAccept(mtCityId -> {
            if (mtCityId == null || mtCityId == 0) {
                return;
            }
            parameters.put(convertKey, mtCityId);
        });
    }

    private static CompletableFuture<Void> fillMtUserId(Map<String, Object> parameters) {
        long dpUserId = ParamsUtil.getLongSafely(parameters, ShelfActivityConstants.Params.dpUserId);
        long mtUserId = ParamsUtil.getLongSafely(parameters, ShelfActivityConstants.Params.mtUserId);
        if (mtUserId > 0 || dpUserId <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        CompositeAtomService compositeAtomService = AthenaBeanFactory.getBean(CompositeAtomService.class);
        return compositeAtomService.getUserInfoByDpUserId(dpUserId).thenAccept(relation -> {
            if (relation == null) {
                return;
            }
            if (relation.getMtVirtualUserId() > 0) {
                parameters.put(ShelfActivityConstants.Params.mtVirtualUserId, relation.getMtVirtualUserId());
            }
            if (relation.getMtRealUserId() > 0) {
                parameters.put(ShelfActivityConstants.Params.mtUserId, relation.getMtRealUserId());
            }
        });
    }
}
