package com.sankuai.dzviewscene.product.shelf.options.builder.activity;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.activity.vp.ActivityEndTimeVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2023-06-05-16:02
 */
@VPointOption(name = "默认-整个货架-活动结束时间",
        description = "取彩虹秒杀活动的结束时间",
        code = "ActivitiesDefaultEndTimeOpt",
        isDefault = true)
public class ActivitiesDefaultEndTimeOpt extends ActivityEndTimeVP<ActivitiesDefaultEndTimeOpt.Config> {
    @Override
    public Long compute(ActivityCxt context, ActivityEndTimeVP.Param param, ActivitiesDefaultEndTimeOpt.Config config) {
        ShelfGroupM shelfGroupM = param.getShelfGroupM();
        if (Objects.isNull(shelfGroupM) || MapUtils.isEmpty(shelfGroupM.getProductGroupMs())) {
            return 0L;
        }
        for (Map.Entry<String, ProductGroupM> entry : shelfGroupM.getProductGroupMs().entrySet()) {
            //先不考虑多组情况，返回第一个有值的
            if (entry.getValue() != null && entry.getValue().getActivity() != null) {
                return entry.getValue().getActivity().getEndTime();
            }
        }
        return 0L;
    }

    @VPointCfg
    @Data
    public static class Config {
    }
}
