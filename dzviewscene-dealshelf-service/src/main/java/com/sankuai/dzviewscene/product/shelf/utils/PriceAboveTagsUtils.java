package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.lion.common.util.JsonUtils;
import com.dianping.lion.common.util.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/6/6 14:40
 */
public class PriceAboveTagsUtils {

    public static final String RECOMMEND_TAG_KEY = "recommend_product_tags_list";
    public static final String TRADE_RATE_TAG_KEY = "tradeCountRate";

    /**
     * 获取推荐理由标签
     *
     * @param productM
     * @return
     */
    public static List<String> getRecommendTag(ProductM productM) {
        List<String> result = new ArrayList<>();
        try {
            List<String> recommendTags = JsonUtils.fromJson(productM.getAttr(RECOMMEND_TAG_KEY), new TypeReference<List<String>>() {
            });
            if (CollectionUtils.isEmpty(recommendTags)) {
                return result;
            }
            result.addAll(recommendTags);
        } catch (Exception e) {
            if (!StringUtils.isEmpty(productM.getAttr(RECOMMEND_TAG_KEY)) && !productM.getAttr(RECOMMEND_TAG_KEY).contains("{")) {
                result.add(productM.getAttr(RECOMMEND_TAG_KEY));
            }
        }
        return result;
    }

    /**
     * 获取竞争圈标签信息
     *
     * @param productM
     * @return
     */
    public static String getTradeRateTag(ProductM productM) {
        return productM.getAttr(TRADE_RATE_TAG_KEY);
    }

}
