package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;
import org.python.parser.ast.Str;

import java.util.Collections;
import java.util.List;

@VPointOption(name = "代金券标题",
        description = "代金券标题",
        code = UnifiedShelfItemVoucherTitleOpt.CODE)
public class UnifiedShelfItemVoucherTitleOpt extends UnifiedShelfItemTitleVP<UnifiedShelfItemVoucherTitleOpt.Config> {

    public static final String CODE = "UnifiedShelfItemVoucherTitleOpt";

    @Override
    public List<StyleTextModel> compute(ActivityCxt activityCxt, Param param, Config config) {
        String title = getTitle(param.getProductM(), config);
        return build4TryHighlightText(activityCxt, title, config.isDisableHighlight());
    }

    private String getTitle(ProductM productM, Config config) {
        if (StringUtils.isNotBlank(config.getVoucherTitleFormat()) && StringUtils.isNotBlank(productM.getMarketPrice())) {
            return String.format(config.getVoucherTitleFormat(), productM.getMarketPrice());
        }
        return StringUtils.EMPTY;
    }

    @EqualsAndHashCode(callSuper = true)
    @VPointCfg
    @Data
    public static class Config extends UnifiedShelfItemTitleVP.Config {

        /**
         * 代金券标题格式，不填走默认
         */
        private String voucherTitleFormat = "%s元代金券";
    }
}
