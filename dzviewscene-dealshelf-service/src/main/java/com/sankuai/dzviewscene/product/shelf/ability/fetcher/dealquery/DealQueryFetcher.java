package com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.ability.extCtx.PreHandlerContextAbility;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.impl.DefaultDealIdQueryHandlerImpl;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.product.shelf.common.OldEngineAdaptCfg;
import com.sankuai.dzviewscene.product.shelf.utils.*;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.framework.monitor.AbilityExecuteMonitor;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/1/17
 */
@Ability(code = DealQueryFetcher.CODE,
        name = "Model-团单召回能力",
        description = "召回商品。构造 Map<String, ProductGroupM>",
        activities = {DealShelfActivity.CODE, DealFilterListActivity.CODE, UnifiedShelfActivity.CODE},
        dependency = {FilterFirstFetcher.CODE, PreHandlerContextAbility.CODE}
)
public class DealQueryFetcher extends PmfAbility<Map<String, ProductGroupM>, DealQueryFetcher.Request, DealQueryFetcher.Config> {

    public static final String CODE = "DealQueryFetcher";

    @Resource
    private List<DealIdQueryHandler> dealIdQueryHandlers;

    @Resource
    private BeanFactory beanFactory;


    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityCxt ctx, Request request, Config config) {
        //并行灰度
        if (QueryParallelUtils.parallelGray(ctx)) {
            return CompletableFuture.completedFuture(null);
        }
        // 查找团单ID查询处理器
        DealIdQueryHandler queryHandler = getExactBean(beanFactory, DefaultDealIdQueryHandlerImpl.class);
        for (DealIdQueryHandler handler : dealIdQueryHandlers) {
            if (handler.run(ctx)) {
                queryHandler = handler;
                break;
            }
        }
        return queryHandler.query(ctx, request, config);
    }

    public static <T> T getExactBean(BeanFactory beanFactory, Class<T> requiredType) {
        String[] beanNames = ((DefaultListableBeanFactory) beanFactory).getBeanNamesForType(requiredType);
        for (String beanName : beanNames) {
            Class<?> beanClass = beanFactory.getType(beanName);
            if (beanClass != null && beanClass.equals(requiredType)) {
                return beanFactory.getBean(beanName, requiredType);
            }
        }
        throw new NoSuchBeanDefinitionException(requiredType);
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 旧框架的适配配置
         */
        private OldEngineAdaptCfg oldEngineCfg;

        /**
         * 【一级参数, List<String>型, 必填】自定义组名
         * {@link QueryFetcher.Params#groupNames}
         */
        private List<String> groupNames;

        /**
         * 一级参数, Map<String, String>型, 选填】商品平台商品组件ID和商品组的映射关系, 当召回能力复用商品平台货架召回服务的时候使用
         * {@link QueryFetcher.Params#productComponent2Group}
         */
        private Map<String, String> productComponent2Group;

        /**
         * 【一级参数, Map<String, Object>型, 必填】指定每组商品召回参数
         * {@link QueryFetcher.Params#groupParams}
         */
        private Map<String, Map<String, Object>> groupParams;

        /**
         * 【一级参数, Map<String, Object>型, 必填】指定每组商品召回参数
         * {@link QueryFetcher.Params#groupParams}
         * 美团侧使用
         */
        private Map<String, Map<String, Object>> mtGroupParams;

        /**
         * 【一级参数，选填】指定每组商品召回多组参数
         * {@link QueryFetcher.Params#multiGroupParams}
         */
        private Map<String, List<Map<String, Object>>> multiGroupParams;

        /**
         * 请求页大小
         * {@link ShelfActivityConstants.Params#pageSize}
         */
        private int pageSize = 100;

        /**
         * 召回商品配置
         * {@link QueryFetcher.Params#recallConfig}
         */
        private Map<String, Object> recallConfig;
    }

    @AbilityRequest
    @Data
    public static class Request {
        private int platform;
    }
}
