package com.sankuai.dzviewscene.product.productdetail.ability.purchaserule.vpoints;


import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.productdetail.ability.purchaserule.DetailPurchaseRuleBuilder;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.PurchaseRuleModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "购买须知-购买须知变化点", description = "用于购买须知需要定制的场景", code = PurchaseRuleModuleVP.CODE, ability = DetailPurchaseRuleBuilder.CODE)
public abstract class PurchaseRuleModuleVP<T> extends PmfVPoint<PurchaseRuleModuleVO, PurchaseRuleModuleVP.Param, T> {

    public static final String CODE = "DetailPurchaseRuleModule";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<ProductM> productMs;
    }
}
