package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.InterceptHandlerVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;

@VPointOption(name = "穿戴甲专属店拦截",
        description = "穿戴甲专属店规则拦截",
        code = PressOnNailInterceptOpt.CODE
)
public class PressOnNailInterceptOpt extends InterceptHandlerVP<PressOnNailInterceptOpt.Config> {

    public static final String CODE = "PressOnNailInterceptOpt";

    @Override
    public Boolean compute(ActivityCxt context, Param param, Config config) {
        int shelfVersion = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.shelfVersion);
        return config.getMinSkipVersion() > 0 && shelfVersion >= config.getMinSkipVersion() && PressOnNailUtils.checkExclusive(context);
    }

    @Data
    @VPointCfg
    public static class Config {

        private int minSkipVersion = 0;
    }
}