package com.sankuai.dzviewscene.product.productdetail.options.purchserule;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import com.sankuai.dztheme.spuproduct.res.attrmodel.common.RuleDTO;
import com.sankuai.dzviewscene.product.productdetail.ability.purchaserule.vpoints.PurchaseRuleModuleVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.PurchaseRuleModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.RuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
@VPointOption(name = "一口价标品购买须知选项", description = "适用于一口价标品构造购买须知的场景", code = FixedPriceSpuPurchaseRuleOpt.CODE, isDefault = true)
public class FixedPriceSpuPurchaseRuleOpt extends PurchaseRuleModuleVP<FixedPriceSpuPurchaseRuleOpt.Cfg> {
    public static final String CODE = "FixedPriceSpuPurchaseRuleOpt";


    @Override
    public PurchaseRuleModuleVO compute(ActivityCxt context, PurchaseRuleModuleVP.Param param, Cfg cfg) {
        PurchaseRuleModuleVO purchaseRuleModuleVO = new PurchaseRuleModuleVO();
        purchaseRuleModuleVO.setRuleList(buildRuleList(param.getProductMs().get(0)));
        purchaseRuleModuleVO.setTitle("购买须知");
        return purchaseRuleModuleVO;
    }

    private List<RuleVO> buildRuleList(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.productdetail.options.purchserule.FixedPriceSpuPurchaseRuleOpt.buildRuleList(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        List<RuleDTO> ruleList = (List<RuleDTO>) productM.getObjAttr(SpuAttrEnum.ATTR_FIXED_PRICE_SPU_PURCHASE_RULE.getKey());
        if (CollectionUtils.isEmpty(ruleList)) {
            return Lists.newArrayList();
        }
        return ruleList.stream().map(rule->{
            RuleVO ruleVO = new RuleVO();
            ruleVO.setTitle(rule.getTitle());
            ruleVO.setRules(rule.getContents());
            return ruleVO;
        }).collect(Collectors.toList());
    }

    @Data
    @VPointCfg
    public static class Cfg {
    }
}