package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@VPointOption(name = "商品召回并行灰度控制",
        description = "商品召回并行灰度控制",
        code = DealQueryParallelGrayOpt.CODE)
public class DealQueryParallelGrayOpt extends PreAsyncHandlerVP<DealQueryParallelGrayOpt.Config> {

    public static final String CODE = "DealQueryParallelGrayOpt";

    @Override
    public CompletableFuture<Object> compute(ActivityCxt activityCxt, Param param, Config config) {
        return CompletableFuture.completedFuture(hitGray(activityCxt, config));
    }

    private boolean hitGray(ActivityCxt activityCxt, Config config) {
        long dpShopId = ParamsUtil.getLongSafely(activityCxt, ShelfActivityConstants.Params.dpPoiIdL);
        if (CollectionUtils.isNotEmpty(config.getWhiteShopIds()) && config.getWhiteShopIds().contains(dpShopId)) {
            return true;
        }
        if (dpShopId % 100 < config.getGrayPercent()) {
            return true;
        }
        return false;
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 灰度百分比
         */
        private int grayPercent;

        /**
         * 门店白名单
         */
        private List<Long> whiteShopIds;
    }
}