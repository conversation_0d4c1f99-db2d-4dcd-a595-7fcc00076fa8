package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsaleprice;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceVP;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lidu
 * @date: 2024/07/08
 **/

@VPointOption(name = "生活服务-美团放心改价格",
        description = "美团放心改价格字段，从sku中marketPrice字段获取",
        code = "LeFangxinChangeItemSalePriceOpt")
public class LeFangxinChangeItemSalePriceOpt extends ItemSalePriceVP<Void> {

    private static final String IS_FANGXIN_CHANGE = "isFangxinChange";

    private static final Integer PRODUCT_CATEGORY = 2200073;

    private static final String ATTR_DEAL_STRUCT_DETAIL = "attr_deal_struct_detail";

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        if (param == null || param.getProductM() == null) {
            return null;
        }
        // 判断是否放心改
        if (isFangxinChange(param)) {
            return getSKUPrice(param);
        }
        // 默认返回null，后面FloorsBuilder有兜底的默认逻辑
        return null;
    }

    /**
     * 判断商品是否为放心改。
     *
     * @param param 参数
     * @return 如果是放心改返回true，否则返回false
     */
    private boolean isFangxinChange(ItemSalePriceVP.Param param) {
        String value = param.getProductM().getAttr(IS_FANGXIN_CHANGE);
        return "true".equalsIgnoreCase(value);
    }

    /**
     * 获取指定商品的价格。
     *
     * @param param 商品销售价格视图参数
     * @return 商品价格，如果未找到则返回null
     */
    private String getSKUPrice(ItemSalePriceVP.Param param) {
        AttrM attrM = extractSpecificAttr(param);
        if (attrM == null) {
            return null;
        }
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(attrM.getValue());
        if (CollectionUtils.isEmpty(allSkus)) {
            return null;
        }

        return allSkus.stream()
                .filter(sku -> PRODUCT_CATEGORY == sku.getProductCategory())
                .findFirst()
                .map(this::extractPrice)
                .orElse(null);
    }

    /**
     * 从商品参数中提取特定的扩展属性。
     *
     * @param param 商品销售价格视图参数
     * @return 特定的扩展属性，如果未找到或扩展属性列表为空，则返回null
     */
    private AttrM extractSpecificAttr(ItemSalePriceVP.Param param) {
        List<AttrM> extAttrs = param.getProductM().getExtAttrs();
        if (CollectionUtils.isEmpty(extAttrs)) {
            return null;
        }
        return extAttrs.stream()
                .filter(attr -> ATTR_DEAL_STRUCT_DETAIL.equals(attr.getName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 从SKU项中提取价格信息。
     *
     * @param skuItemDto SKU项数据传输对象
     * @return SKU的市场价格字符串，如果SKU项为null则返回null
     */
    private String extractPrice(SkuItemDto skuItemDto) {
        if (skuItemDto == null || skuItemDto.getMarketPrice() == null) {
            return null;
        }
        BigDecimal marketPrice = skuItemDto.getMarketPrice();
        return marketPrice.toBigInteger().toString();
    }

}
