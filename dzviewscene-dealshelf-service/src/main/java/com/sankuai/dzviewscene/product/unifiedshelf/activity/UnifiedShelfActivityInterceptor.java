package com.sankuai.dzviewscene.product.unifiedshelf.activity;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.InterceptorContext;
import com.sankuai.athena.viewscene.framework.annotation.ActivityInterceptor;
import com.sankuai.athena.viewscene.framework.core.IActivityInterceptor;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.productshelf.vu.enums.RequestTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

@ActivityInterceptor(name = "货架活动打点拦截器", code = "activity_unified_shelf_interceptor", activity = UnifiedShelfActivity.CODE)
public class UnifiedShelfActivityInterceptor implements IActivityInterceptor<UnifiedShelfResponse> {

    private static final String REFRESH_PREFIX = "refresh.";

    private static final String FIRST_LOAD = "UNIFIED_SHELF_FIRST_LOAD_APP";

    @Override
    public void beforeExecute(InterceptorContext<UnifiedShelfResponse> interceptorContext) {

    }

    @Override
    public void complete(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse result) {
        try {
            Cat.logMetricForCount(interceptorContext.getActivityCode(), buildMetricTags(interceptorContext, result));
            catWithCategory(interceptorContext, result);
            logMetricForRefreshCount(interceptorContext, result);
        } catch (Exception e) {
            /*静默*/
            Cat.logError(e);
        }
    }

    private void catWithCategory(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse result) {
        String requestType = ParamsUtil.getStringSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.requestType);
        long startTime = ParamsUtil.getLongSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.startTime);
        ShopM shopM = (ShopM) interceptorContext.getParameters().get(ShelfActivityConstants.Ctx.ctxShop);
        int userAgent = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
        if (startTime <= 0 || shopM == null) {
            return;
        }
        //记录app内首屏请求耗时
        if (RequestTypeEnum.API_UNIFIED_SHELF.getType().equals(requestType) && PlatformUtil.isApp(userAgent)) {
            Cat.newCompletedTransactionWithDuration(FIRST_LOAD, String.valueOf(shopM.getShopType()), System.currentTimeMillis() - startTime);
        }
    }

    private void logMetricForRefreshCount(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse result) {
        String refreshTag = ParamsUtil.getStringSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.refreshTag);
        if (StringUtils.isBlank(refreshTag)) {
            return;
        }
        Cat.logMetricForCount(REFRESH_PREFIX + interceptorContext.getActivityCode(), buildRefreshMetricTags(interceptorContext, result, refreshTag));
    }

    private Map<String, String> buildMetricTags(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse UnifiedShelfResponse) {
        UnifiedShelfResponse finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : UnifiedShelfResponse;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("hasFilters", Boolean.toString(UnifiedShelfModelUtils.hasFilters(finalResult)));
            put("hasProducts", Boolean.toString(UnifiedShelfModelUtils.hasProducts(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if (StringUtils.isNotEmpty(clientTypeMsg)) {
                put("clientType", clientTypeMsg);
            }
        }};
    }

    private Map<String, String> buildRefreshMetricTags(InterceptorContext<UnifiedShelfResponse> interceptorContext, UnifiedShelfResponse UnifiedShelfResponse, String refreshTag) {
        UnifiedShelfResponse finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : UnifiedShelfResponse;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("refreshTag", refreshTag);
            put("hasProducts", Boolean.toString(UnifiedShelfModelUtils.hasProducts(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if (StringUtils.isNotEmpty(clientTypeMsg)) {
                put("clientType", clientTypeMsg);
            }
        }};
    }
}
