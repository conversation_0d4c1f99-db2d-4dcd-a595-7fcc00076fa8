package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import lombok.Data;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/25 20:00
 */
@VPointOption(name = "商品列表排序和截断",
        description = "商品列表排序和截断",
        code = "ProductListAcsLimitOpt")
public class ProductListAcsLimitOpt extends ProductListVP<ProductListAcsLimitOpt.Config> {

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        return productMS.stream()
                .filter(productM -> filterBlackList(productM, context))
                .sorted(Comparator.comparing((ProductM product)-> Optional.ofNullable(product.getSale()).map(ProductSaleM::getSale).orElse(0)).reversed())
                .limit(config.getLimit())
                .collect(Collectors.toList());
    }

    private boolean filterBlackList(ProductM productM, ActivityCxt context) {
        String currentDeal = String.valueOf(productM.getProductId());
        String purchasedDeal = ParamsUtil.getStringSafely(context, PmfConstants.Params.productIdL);
        return !currentDeal.equals(purchasedDeal);
    }

    @VPointCfg
    @Data
    public static class Config {
        private int limit = 10;
    }
}
