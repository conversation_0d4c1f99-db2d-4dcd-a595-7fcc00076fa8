package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.spuproduct.req.SpuRequest;
import com.sankuai.dztheme.spuproduct.res.ShopDTO;
import com.sankuai.dztheme.spuproduct.res.SpuDTO;
import com.sankuai.dztheme.spuproduct.res.SpuIdDTO;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.SpuPaddingConstants;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import graphql.execution.Async;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class SpuProductThemeHandler implements PaddingHandler {

    private static final int MAX_QUERY_SIZE = 10;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params) {
        if (!validateRequest(ctx, productGroupM, params)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        List<ProductIdM> productIds = productGroupM.getProducts().stream()
                .filter(Objects::nonNull)
                .map(ProductM::getId)
                .collect(Collectors.toList());

        CompletableFuture<List<SpuDTO>> themeResultCf = queryThemeResult(ctx, params, productIds);
        return themeResultCf.thenApply(themeResult -> fillProductGroupM(productGroupM, themeResult));
    }

    private CompletableFuture<List<SpuDTO>> queryThemeResult(ActivityCxt ctx, Map<String, Object> params, List<ProductIdM> spuIds) {
        List<SpuRequest> reqList = buildRequestList(ctx, params, spuIds);
        List<CompletableFuture<List<SpuDTO>>> cfList = reqList.stream().map(req -> compositeAtomService.querySpuTheme(req)).collect(Collectors.toList());
        return Async.each(cfList).thenApply(result -> {
            if (CollectionUtils.isEmpty(result)) {
                return Lists.newArrayList();
            }
            return result.stream()
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        });
    }

    private void fillProductM(ProductM productM, SpuDTO spu) {
        productM.setTitle(spu.getSpuName());
        productM.setPicUrl(spu.getHeadPic());
        productM.setJumpUrl(spu.getJumpUrl());
        productM.setProductTags(spu.getSpuTags());
        productM.setOrderUrl(spu.getOrderUrl());
        productM.setProductItemMList(Lists.newArrayList());
        productM.setPinPools(Lists.newArrayList());
        productM.setSale(buildSale(spu));
        productM.setAvailable(spu.isAvailable());
        fillShops(productM, spu);
        fillPrice(productM, spu);
        fillAllAttrs(productM, spu);
    }

    private void fillShops(ProductM productM, SpuDTO spu) {
        if (spu.getApplyShops() == null) {
            return;
        }
        productM.setShopMs(buildShops(spu));
        productM.setShopNum(spu.getApplyShops().getApplyShopNum());
        productM.setApplyShopsDesc(spu.getApplyShops().getApplyShopDesc());
        productM.setNearestShopDesc(spu.getApplyShops().getNearestShopDesc());
        productM.setNearestShopDistance(spu.getApplyShops().getNearestShopDistance());
    }

    private List<ShopM> buildShops(SpuDTO spu) {
        if (CollectionUtils.isEmpty(spu.getApplyShops().getShops())) {
            return Lists.newArrayList();
        }
        List<ShopM> shopMs = Lists.newArrayList();
        for (ShopDTO shopDTO : spu.getApplyShops().getShops()) {
            ShopM shopM = new ShopM();
            shopM.setLongShopId(shopDTO.getShopId());
            shopM.setShopName(shopDTO.getShopName());
            shopM.setBranchName(shopDTO.getBranchName());
            shopM.setDistance(shopDTO.getDistanceStr());
            shopM.setDistanceNum(shopDTO.getDistance());
            shopMs.add(shopM);
        }
        return shopMs;
    }

    private void fillPrice(ProductM productM, SpuDTO spu) {
        if (spu.getSpuPrice() == null) {
            return;
        }
        productM.setBasePriceTag(spu.getSpuPrice().getSalePrice());
        productM.setBasePriceDesc(spu.getSpuPrice().getSalePriceDesc());
    }

    private void fillAllAttrs(ProductM productM, SpuDTO spu) {
        if (CollectionUtils.isEmpty(spu.getSpuAttrs())) {
            return;
        }
        spu.getSpuAttrs().forEach(attr -> productM.setObjAttr(attr.getName(), attr.getValue()));
    }

    private ProductSaleM buildSale(SpuDTO spu) {
        if (spu.getSales() == null) {
            return null;
        }
        ProductSaleM saleM = new ProductSaleM();
        saleM.setSale(spu.getSales().getSaleCount());
        saleM.setSaleTag(spu.getSales().getSaleDesc());
        return saleM;
    }

    private List<SpuRequest> buildRequestList(ActivityCxt ctx, Map<String, Object> params, List<ProductIdM> spuIds) {
        List<List<ProductIdM>> spuIdList = Lists.partition(spuIds, MAX_QUERY_SIZE);
        return spuIdList.stream().map(ids -> buildSpuRequest(ctx, params, ids)).collect(Collectors.toList());
    }

    private SpuRequest buildSpuRequest(ActivityCxt ctx, Map<String, Object> params, List<ProductIdM> ids) {
        SpuRequest spuRequest = new SpuRequest();
        spuRequest.setPlanId(ParamsUtil.getStringSafely(params, PaddingFetcher.Params.planId));
        spuRequest.setSpuIds(buildSpuIds(ids));
        spuRequest.setExtParams(buildExtParams(ctx, params));
        return spuRequest;
    }

    private Map<String, Object> buildExtParams(ActivityCxt ctx, Map<String, Object> groupParams) {
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put(SpuPaddingConstants.Params.CITY_ID, loadCityId(ctx));
        extParams.put(SpuPaddingConstants.Params.DP_CITY_ID, ParamsUtil.getIntSafely(ctx,PmfConstants.Params.dpCityId));
        extParams.put(SpuPaddingConstants.Params.MT_CITY_ID, ParamsUtil.getIntSafely(ctx,PmfConstants.Params.mtCityId));
        extParams.put(SpuPaddingConstants.Params.POI_ID, loadPoiId(ctx));
        extParams.put(SpuPaddingConstants.Params.DP_POI_ID, ParamsUtil.getLongSafely(ctx,PmfConstants.Params.dpPoiIdL));
        extParams.put(SpuPaddingConstants.Params.MT_POI_ID, ParamsUtil.getLongSafely(ctx,PmfConstants.Params.mtPoiIdL));
        extParams.put(SpuPaddingConstants.Params.PLATFORM, ParamsUtil.getIntSafely(ctx,PmfConstants.Params.platform));
        extParams.put(SpuPaddingConstants.Params.UA_CODE, ParamsUtil.getIntSafely(ctx, PmfConstants.Params.userAgent));
        extParams.put(SpuPaddingConstants.Params.APP_VERSION, ParamsUtil.getStringSafely(ctx, PmfConstants.Params.appVersion));
        extParams.put(SpuPaddingConstants.Params.LAT, ParamsUtil.getDoubleSafely(ctx, PmfConstants.Params.lat));
        extParams.put(SpuPaddingConstants.Params.LNG, ParamsUtil.getDoubleSafely(ctx, PmfConstants.Params.lng));
        extParams.put(SpuPaddingConstants.Params.DEVICE_ID, ParamsUtil.getStringSafely(ctx, PmfConstants.Params.deviceId));
        extParams.put(SpuPaddingConstants.Params.UNION_ID, ParamsUtil.getStringSafely(ctx, PmfConstants.Params.unionId));
        extParams.put(SpuPaddingConstants.Params.USER_ID, loadUserId(ctx));
        extParams.put(SpuPaddingConstants.Params.DP_USER_ID, ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpUserId));
        extParams.put(SpuPaddingConstants.Params.MT_USER_ID, ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtUserId));
        extParams.put(SpuPaddingConstants.Params.SHOP_UUID, ParamsUtil.getStringSafely(ctx, PmfConstants.Params.shopUuid));
        extParams.put(SpuPaddingConstants.Params.SHOP_CITY_ID, loadShopCityId(ctx));
        extParams.put(SpuPaddingConstants.Params.SHOP_DP_CITY_ID, ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopDpCityId));
        extParams.put(SpuPaddingConstants.Params.SHOP_MT_CITY_ID, ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopMtCityId));
        putCfgParams(extParams, groupParams);
        return extParams;
    }

    private int loadShopCityId(ActivityCxt ctx) {
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        if (platform == VCPlatformEnum.DP.getType()) {
            return ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopDpCityId);
        }
        return ParamsUtil.getIntSafely(ctx, PmfConstants.Params.shopMtCityId);
    }

    private long loadUserId(ActivityCxt ctx) {
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        if (platform == VCPlatformEnum.DP.getType()) {
            return ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpUserId);

        }
        return ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtUserId);
    }

    private long loadPoiId(ActivityCxt ctx) {
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        if (platform == VCPlatformEnum.DP.getType()) {
            return ParamsUtil.getLongSafely(ctx, PmfConstants.Params.dpPoiIdL);

        }
        return ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
    }

    private int loadCityId(ActivityCxt ctx) {
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        if (platform == VCPlatformEnum.DP.getType()) {
            return ParamsUtil.getIntSafely(ctx, PmfConstants.Params.dpCityId);

        }
        return ParamsUtil.getIntSafely(ctx, PmfConstants.Params.mtCityId);
    }

    private void putCfgParams(Map<String, Object> extParams, Map<String, Object> groupParams) {
        Map<String, Object> configExtParams = loadConfigExtParams(groupParams);
        if (MapUtils.isEmpty(configExtParams)) {
            return;
        }
        extParams.putAll(configExtParams);
    }

    private Map<String, Object> loadConfigExtParams(Map<String, Object> groupParams) {
        try {
            Map<String, Object> extParams = (Map<String, Object>) groupParams.get("paddingExtParams");
            return extParams;
        } catch (Exception e) {
            return Maps.newHashMap();
        }
    }

    private List<SpuIdDTO> buildSpuIds(List<ProductIdM> ids) {
        return ids.stream().map(id -> {
            SpuIdDTO spuIdDTO = new SpuIdDTO();
            spuIdDTO.setId(id.getId());
            spuIdDTO.setIdType(id.getType());
            return spuIdDTO;
        }).collect(Collectors.toList());
    }

    private ProductGroupM fillProductGroupM(ProductGroupM productGroupM,
                                            List<SpuDTO> spuList) {
        for (ProductM productM : productGroupM.getProducts()) {
            SpuDTO spu = matchSpuProduct(spuList, productM);
            if (spu == null) {
                Cat.logEvent("matchSpuFailed", String.format("failedId：%s", JsonCodec.encodeWithUTF8(productM.getId())));
                continue;
            }
            fillProductM(productM, spu);
        }
        return productGroupM;
    }

    private SpuDTO matchSpuProduct(List<SpuDTO> spuDTOList, ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(spuDTOList)) {
            return null;
        }
        for (SpuDTO spu : spuDTOList) {
            SpuIdDTO spuId = spu.getSpuId();
            if (spuId == null) {
                continue;
            }
            if (spuId.getId() == productM.getId().getId() && spuId.getIdType() == productM.getId().getType()) {
                return spu;
            }
        }
        return null;
    }

    private boolean validateRequest(ActivityCxt ctx, ProductGroupM productGroupM, Map<String, Object> params) {
        String planId = ParamsUtil.getStringSafely(params, PaddingFetcher.Params.planId);
        if (StringUtils.isEmpty(planId)) {
            throw new BusinessException("填充失败, planId缺失");
        }
        return productGroupM != null && CollectionUtils.isNotEmpty(productGroupM.getProducts()) && MapUtils.isNotEmpty(params);
    }
}
