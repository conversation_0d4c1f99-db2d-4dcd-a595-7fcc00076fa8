package com.sankuai.dzviewscene.product.shelf.ability.builder.anchor;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfview.ShelfResponseAssembler;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by liujiacheng14 on 2023/8/21.
 */
@VPoint(name = "货架锚定位置信息", description = "商户详情页是否强制锚定至货架-默认false", code = ShelfAnchorVP.CODE, ability = ShelfResponseAssembler.CODE)
public abstract class ShelfAnchorVP<T> extends PmfVPoint<Bo<PERSON><PERSON>, ShelfAnchorVP.Param, T> {

    public static final String CODE = "ShelfAnchorVP";

    protected boolean defaultSet() {
        return false;
    }

    @Data
    @Builder
    @VPointParam
    public static class Param {

    }
}
