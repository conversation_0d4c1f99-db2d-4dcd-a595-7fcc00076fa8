package com.sankuai.dzviewscene.product.productdetail.options.productbasics.pretitletag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.productdetail.ability.productbasics.vpoints.DetailBasicPreTitleTagVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;


@VPointOption(name = "默认-基础信息标题前标签选项",
        description = "适用于从配置中获取标题前标签的场景",
        code = DefaultDetailBasicPreTitleTagOpt.CODE,
        isDefault = true)
public class DefaultDetailBasicPreTitleTagOpt extends DetailBasicPreTitleTagVP<DefaultDetailBasicPreTitleTagOpt.Cfg> {
    public static final String CODE = "DefaultDetailBasicPreTitleTagOpt";


    @Override
    public String compute(ActivityCxt context, Param param, Cfg cfg) {
        return cfg==null? StringUtils.EMPTY : cfg.getTag();
    }


    @Data
    @VPointCfg
    public class Cfg {
        private String tag;
    }
}