package com.sankuai.dzviewscene.product.shelf.options.filter.anchor;

import com.dianping.product.shelf.common.enums.NavRouterTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ProductAnchorVP;
import com.sankuai.dzviewscene.product.shelf.common.ProductAnchorInfo;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@VPointOption(name = "搜索词配置锚定导航名",
        description = "搜索词配置锚定导航名，来源MultipleConfigFilterFetcherExt的selected逻辑",
        code = "ConfigKeyAnchorOpt"
)
public class ConfigKeyAnchorOpt extends ProductAnchorVP<ConfigKeyAnchorOpt.Config> {

    private static final String GROUP_NAME = "团购";

    private static final String SPLIT = ",";

    private static final String ALL_NAME = "全部";

    @Resource
    private FilterConfig filterConfig;

    @Override
    public ProductAnchorInfo compute(ActivityCxt context, Param param, Config config) {
        String keyword = context.getParam(ShelfActivityConstants.Params.keyword);
        //搜索词为空，返回首tab
        if (StringUtils.isBlank(keyword)) {
            return getFirstNavAnchor();
        }
        Map<String, FilterConfig.MultipleFilterConfig> filterKeywords = filterConfig.getMultipleFilterKeywords(context.getSceneCode(), GROUP_NAME);
        if (MapUtils.isEmpty(filterKeywords)) {
            return getFirstNavAnchor();
        }
        List<String> matchedKeys = Lists.newArrayList();
        findMatchKey(keyword, filterKeywords, null, matchedKeys);
        return CollectionUtils.isEmpty(matchedKeys) ? getFirstNavAnchor() : buildMatchAnchor(matchedKeys);
    }

    private ProductAnchorInfo buildMatchAnchor(List<String> matchedKey) {
        ProductAnchorInfo productAnchorInfo = new ProductAnchorInfo();
        productAnchorInfo.setRouterType(NavRouterTypeEnum.DEAL_NAV_NAME_TYPE.getType());
        //命中多个导航名逗号分隔
        productAnchorInfo.setMatchKey(String.join(SPLIT, matchedKey));
        return productAnchorInfo;
    }

    private void findMatchKey(String keyword, Map<String, FilterConfig.MultipleFilterConfig> filterKeywords, String parentKey, List<String> matchedKeys) {
        for (Map.Entry<String, FilterConfig.MultipleFilterConfig> entry : filterKeywords.entrySet()) {
            //本身为空
            if (entry.getValue() == null || StringUtils.isEmpty(entry.getKey())) {
                continue;
            }
            //先匹配子节点
            if (MapUtils.isNotEmpty(entry.getValue().getChild())) {
                findMatchKey(keyword, entry.getValue().getChild(), entry.getKey(), matchedKeys);
            }
            //再匹配自身
            if (searchKeyWordIsMatchConfig(entry.getValue(), keyword)) {
                //如果为全部且父节点不为空，则添加父节点
                if (entry.getKey().equals(ALL_NAME) && StringUtils.isNotEmpty(parentKey)) {
                    putIfNotExist(matchedKeys, parentKey);
                } else {
                    putIfNotExist(matchedKeys, entry.getKey());
                }
            }
        }
    }

    private void putIfNotExist(List<String> matchedKeys, String key) {
        if (matchedKeys.contains(key)) {
            return;
        }
        matchedKeys.add(key);
    }


    private boolean searchKeyWordIsMatchConfig(FilterConfig.MultipleFilterConfig config, String keyword) {
        if (config == null || StringUtils.isEmpty(keyword) || CollectionUtils.isEmpty(config.getKey())) {
            return false;
        }
        //默认精确匹配策略
        String matchMethod = FilterConfig.MultipleFilterConfig.EXACT_MATCH_METHOD;
        if (Objects.equals(config.getMethod(), FilterConfig.MultipleFilterConfig.CONTAIN_MATCH_METHOD)) {
            matchMethod = config.getMethod();
        }
        if (Objects.equals(matchMethod, FilterConfig.MultipleFilterConfig.EXACT_MATCH_METHOD)) {
            //精确匹配
            return config.getKey().contains(keyword);
        } else if (Objects.equals(matchMethod, FilterConfig.MultipleFilterConfig.CONTAIN_MATCH_METHOD)) {
            //搜索关键词包含待命中词
            return config.getKey().stream().filter(word -> keyword.contains(word)).findFirst().isPresent();
        }
        //兜底默认不匹配
        return false;
    }

    @VPointCfg
    @Data
    public static class Config {

    }
}
