package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.faulttolerance.req.ActivityContextRequest;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.lang3.ObjectUtils;

public class MonitorUtils {

    public static void monitor(ActivityContextRequest contextRequest, String type) {
        try {
            Cat.logEvent(type, "access");
            if (PlatformUtil.isMT(contextRequest.getPlatform())) {
                Cat.logEvent(type, "MT");
            } else {
                Cat.logEvent(type, "DP");
            }
            if (contextRequest.getShopId() < 1) {
                Cat.logEvent(type, "noShop");
            }
            if (contextRequest.getUserId() < 1) {
                Cat.logEvent(type, "noUser");
            }
            if (ObjectUtils.defaultIfNull(contextRequest.getCityId(), 0) < 1) {
                Cat.logEvent(type, "noCity");
            }
            if (ObjectUtils.defaultIfNull(contextRequest.getLocationCityId(), 0) < 1) {
                Cat.logEvent(type, "noLocationCityId");
            }
            if (ObjectUtils.defaultIfNull(contextRequest.getLat(), 0.0) <= 0) {
                Cat.logEvent(type, "noLat");
            }
            if (ObjectUtils.defaultIfNull(contextRequest.getLng(), 0.0) <= 0) {
                Cat.logEvent(type, "noLng");
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
    }
}
