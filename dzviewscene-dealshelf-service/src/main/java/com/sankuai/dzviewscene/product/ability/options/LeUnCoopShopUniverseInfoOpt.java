package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.ability.model.LeUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductIdM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @author: wuwenqiang
 * @create: 2024-06-12
 * @description: LE非合作POI推荐列表通用信息
 */
@VPointOption(name = "LE非合作POI推荐列表通用信息", description = "LE非合作POI推荐列表通用信息", code = LeUnCoopShopUniverseInfoOpt.CODE)
@Slf4j
public class LeUnCoopShopUniverseInfoOpt extends PreSyncHandlerVP<LeUnCoopShopUniverseInfoOpt.Config> {

    public static final String CODE = "LeUnCoopShopUniverseInfoOpt";

    private static final String LE_UN_COOP_SHOP_RECOMMEND_SCENE = "le_uncoopshop_recommend_products";
    private static final String PLATFORM = "platform";
    private static final String MAIN_TITLE = "mainTitle";
    private static final String RECOMMEND_TOP_TITLE = "recommendTopTitle";
    private static final String RECOMMEND_TOP_TITLE_NEW = "recommendTopTitleNew";
    private static final String DEFAULT_SHOW_NUM = "defaultShowNum";
    private static final String MAX_SHOW_NUM = "maxShowNum";
    private static final String MORE_SHOW_TEXT = "moreShowText";
    private static final String SLOPE_COVER_DISTANCE = "slopeCoverDistance";
    private static final String NATURAL_FIRST_COVER_DISTANCE = "naturalFirstCoverDistance";
    private static final String NATURAL_SECOND_COVER_DISTANCE = "naturalSecondCoverDistance";
    private static final String TOP_PRODUCT_ID = "topProductId";
    private static final String PRODUCT_TYPE = "productType";
    private static final String BACK_CATE_ID_HAI_MA = "backCategoryId";
    public static final int EDU_SPU_PRODUCT_ID_TYPE = 16;
    public static final int HOT_SPU_PRODUCT_ID_TYPE = 10003;


    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public Map<String, Object> compute(ActivityCxt context, Param param, Config config) {
        HaimaRequest request = buildHaimaRequest(context, LE_UN_COOP_SHOP_RECOMMEND_SCENE);
        CompletableFuture<HaimaResponse> responseFuture = compositeAtomService.getHaiMaResponse(request);
        String sceneCode = ParamsUtil.getStringSafely(context.getParameters(), ShelfActivityConstants.Params.sceneCode);
        final String topTitleKey = (CollectionUtils.isNotEmpty(config.getSceneCodeUseNewTopTitle())
                && config.getSceneCodeUseNewTopTitle().contains(sceneCode))
                ? RECOMMEND_TOP_TITLE_NEW
                : RECOMMEND_TOP_TITLE;
        CompletableFuture<LeUncoopShopShelfAttrM> leUncoopShopShelfAttrCf = responseFuture.thenApply(response -> loadRecommendProduct(context, response, topTitleKey));
        Map<String, CompletableFuture<Object>> resultMap = Maps.newHashMap();
        resultMap.put(LeUnCoopShopUniverseInfoOpt.CODE, CompletableFutureUtil.covert2ObjCf(leUncoopShopShelfAttrCf));
        return CompletableFutureUtil.each(resultMap).join();
    }

    private HaimaRequest buildHaimaRequest(ActivityCxt context, String sceneKey) {
        HaimaRequest request = new HaimaRequest();
        int dpCityId = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        request.setCityId(dpCityId);
        // 后台类目需要子类目在前，父类目在后
        List<Integer> backCatIds = context.getParam(PmfConstants.Params.shopBackCatIds);
        request.addField(BACK_CATE_ID_HAI_MA, convertList2Str(reverseList(backCatIds)));
        request.setSceneKey(sceneKey);
        return request;
    }

    private LeUncoopShopShelfAttrM loadRecommendProduct(ActivityCxt context, HaimaResponse response, String topTitleKey) {
        if (!response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
            return null;
        }
        List<HaimaConfig> configs = response.getData();
        int platform = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        // 先筛选双侧是否有结果，找不到再根据当前平台进行查找
        List<HaimaConfig> haimaConfigs = configs.stream().filter(config -> filterConfig(config, platform)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(haimaConfigs) || haimaConfigs.get(0) == null) {
            return null;
        }
        HaimaConfig haimaConfig = haimaConfigs.get(0);
        return buildLeUncoopShopShelfAttrM(haimaConfig, topTitleKey);
    }

    private boolean filterConfig(HaimaConfig config, int platform) {
        if (config == null) {
            return false;
        }
        Integer configPlatform = NumberUtils.objToInt(config.getExtValue(PLATFORM));
        return configPlatform == PlatformEnum.bothPlatform.platform || configPlatform == platform;
    }

    private LeUncoopShopShelfAttrM buildLeUncoopShopShelfAttrM(HaimaConfig haimaConfig, String topTitleKey) {
        LeUncoopShopShelfAttrM leUncoopShopShelfAttrM = new LeUncoopShopShelfAttrM();
        leUncoopShopShelfAttrM.setMainTitle(haimaConfig.getExtString(MAIN_TITLE));
        leUncoopShopShelfAttrM.setRecommendTopTitle(haimaConfig.getExtString(topTitleKey));
        leUncoopShopShelfAttrM.setDefaultShowNum(NumberUtils.objToInt(haimaConfig.getExtValue(DEFAULT_SHOW_NUM)));
        leUncoopShopShelfAttrM.setMaxShowNum(NumberUtils.objToInt(haimaConfig.getExtValue(MAX_SHOW_NUM)));
        leUncoopShopShelfAttrM.setMoreShowText(haimaConfig.getExtString(MORE_SHOW_TEXT));
        leUncoopShopShelfAttrM.setSlopeCoverDistance(NumberUtils.objToInt(haimaConfig.getExtValue(SLOPE_COVER_DISTANCE)));
        leUncoopShopShelfAttrM.setNaturalFirstCoverDistance(NumberUtils.objToInt(haimaConfig.getExtValue(NATURAL_FIRST_COVER_DISTANCE)));
        leUncoopShopShelfAttrM.setNaturalSecondCoverDistance(NumberUtils.objToInt(haimaConfig.getExtValue(NATURAL_SECOND_COVER_DISTANCE)));
        leUncoopShopShelfAttrM.setTopProducts(buildProducts(haimaConfig));
        return leUncoopShopShelfAttrM;
    }

    private List<ProductM> buildProducts(HaimaConfig config) {
        if (CollectionUtils.isEmpty(config.getContents())) {
            return Lists.newArrayList();
        }
        List<HaimaContent> contents = config.getContents();
        List<ProductM> productMS = Lists.newArrayList();
        contents.forEach(content -> {
            List<String> topProductIds = convertStr2List(content.getContentString(TOP_PRODUCT_ID));
            Integer configProductType = NumberUtils.objToInt(content.getContent(PRODUCT_TYPE));
            List<ProductM> topProducts = convertProduct2ProductM(topProductIds, configProductType);
            productMS.addAll(topProducts);
        });
        return productMS;
    }

    private List<ProductM> convertProduct2ProductM(List<String> productIds, Integer configProductType) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        int productType;
        int productIdType;
        if (configProductType == null) {
            productType = ProductTypeEnum.UNKNOWN.getType();
            productIdType = ProductTypeEnum.UNKNOWN.getType();
        } else if (configProductType.equals(ProductTypeInnerEnum.DEAL.type)) {
            productType = ProductTypeEnum.DEAL.getType();
            productIdType = ProductTypeEnum.DEAL.getType();
        } else if (configProductType.equals(ProductTypeInnerEnum.GENERAL_SPU.type)) {
            productType = ProductTypeEnum.GENERAL_SPU.getType();
            productIdType = EDU_SPU_PRODUCT_ID_TYPE;
        } else if (configProductType.equals(ProductTypeInnerEnum.SPU.type)) {
            productType = ProductTypeEnum.SPU.getType();
            productIdType = ProductTypeEnum.SPU.getType();
        } else if (configProductType.equals(ProductTypeInnerEnum.HOT_SPU.type)) {
            productType = ProductTypeEnum.GENERAL_SPU.getType();
            productIdType = HOT_SPU_PRODUCT_ID_TYPE;
        }
        else {
            productType = ProductTypeEnum.UNKNOWN.getType();
            productIdType = ProductTypeEnum.UNKNOWN.getType();
        }
        return productIds.stream().filter(Objects::nonNull).map(productId -> buildProduct(productId, productType, productIdType))
                .filter(Objects::nonNull)
                .filter(product -> product.getProductType() == ProductTypeEnum.DEAL.getType()
                        || product.getProductType() == ProductTypeEnum.GENERAL_SPU.getType()
                        || product.getProductType() == ProductTypeEnum.SPU.getType())
                .collect(Collectors.toList());
    }

    private ProductM buildProduct(String productId, int productType, int productIdType) {
        try {
            ProductM productM = new ProductM();
            productM.setProductId(Integer.parseInt(productId));
            productM.setProductType(productType);
            productM.setId(new ProductIdM(Long.valueOf(productId), productIdType));
            return productM;
        } catch (Exception e) {
            log.error("LeUnCoopShopUniverseInfoOpt build Product error", e);
        }
        return null;
    }

    private List<Integer> reverseList(List<Integer> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Lists.newArrayList();
        }
        return IntStream.range(0, source.size())
                .mapToObj(i -> source.get(source.size() - i - 1))
                .collect(Collectors.toList());
    }

    private List<String> convertStr2List(String str) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(str.split(","))
                .map(String::trim).collect(Collectors.toList());
    }

    private String convertList2Str(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return StringUtils.join(list, ",");
    }

    @Data
    @VPointCfg
    public static class Config {
        List<String> sceneCodeUseNewTopTitle;
    }

    public enum PlatformEnum {

        dp(1, "点评"),
        mt(2, "美团"),
        bothPlatform(3, "美团点评双侧");

        private int platform;
        private String name;

        PlatformEnum(int platform, String name) {
            this.platform = platform;
            this.name = name;
        }
    }

    public enum ProductTypeInnerEnum {
        DEAL(1, "团购"),
        GENERAL_SPU(2, "标品"),
        SPU(3, "泛商品"),
        PRICE_PACK(4, "价目表"),
        HOT_SPU(5, "爆品")
        ;

        private int type;
        private String desc;

        ProductTypeInnerEnum(int type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }
}
