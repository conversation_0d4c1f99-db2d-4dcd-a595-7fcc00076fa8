package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.AbstractBarSkuCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.BarSkuCreatorFactory;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 酒吧sku列表构造器抽象类
 * <AUTHOR>
 * @date 2023/2/9 1:22 下午
 */
public abstract class AbstractBarSkuListCreator {

    /**
     * creator识别 抽象方法
     *@param
     *@return
     */
    public abstract boolean ideantify(boolean isMustGroupSku, List<SkuItemDto> skuList, BarDealDetailSkuListModuleOpt.Config config);

    /**
     * 构造sku列表 抽象方法
     *@return
     * @param
     * @param hitDouHu
     */
    public abstract List<DealSkuGroupModuleVO> buildSkuListModules(List<SkuItemDto> skuItemDtos, BarDealDetailSkuListModuleOpt.Config config, int optionalNum, boolean hitDouHu);

    protected void addSkuListModule(List<SkuItemDto> skus, String groupName, List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS,
                                    BarDealDetailSkuListModuleOpt.Config config, boolean isSameCategorySku, boolean isHitDouhu) {
        if (CollectionUtils.isEmpty(skus) || dealSkuGroupModuleVOS == null) {
            return;
        }
        List<DealSkuVO> dealSkuVOList = skus.stream().map(sku -> {
            AbstractBarSkuCreator barSkuBuilder = BarSkuCreatorFactory.creadSkuBuilder(sku, config);
            if (barSkuBuilder == null) {
                return null;
            }
            return barSkuBuilder.buildDealSkuVO(sku, isSameCategorySku, config, isHitDouhu);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        DealSkuGroupModuleVO dealSkuGroupModuleVO = buildDealSkuGroupModuleVO(groupName, dealSkuVOList);
        dealSkuGroupModuleVOS.add(dealSkuGroupModuleVO);
    }

    private static DealSkuGroupModuleVO buildDealSkuGroupModuleVO(String title, List<DealSkuVO> dealSkuList) {
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setTitle(title);
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        return dealSkuGroupModuleVO;
    }

}
