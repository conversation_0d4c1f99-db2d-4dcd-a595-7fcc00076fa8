package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSpecialTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSpecialTagVP;

@VPointOption(name = "默认实现-不返回", description = "默认实现-不返回", code = "UnifiedShelfItemDefaultSpecialTagOpt", isDefault = true)
public class UnifiedShelfItemDefaultSpecialTagOpt extends UnifiedShelfItemSpecialTagVP<Void> {
    @Override
    public ItemSpecialTagVO compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
