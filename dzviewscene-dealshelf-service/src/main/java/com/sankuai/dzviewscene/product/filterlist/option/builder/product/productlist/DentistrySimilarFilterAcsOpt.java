package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailSkuUniStructuredModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailStructuredModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealSkuAttrModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "口腔齿科相似团购",
        description = "口腔齿科相似团购",
        code = "DentistrySimilarFilterAcsOpt")
public class DentistrySimilarFilterAcsOpt extends ProductListVP<DentistrySimilarFilterAcsOpt.Config> {
    private static final String SERVICE_TYPE = "service_type";

    private static final String SUIT_CROWD = "suitCrowds";

    private static final String AVAILABLE_TIME = "available_time";

    private static final String CATEGORY = "category";

    private static final String SANDBLASTING = "sandblasting";

    private static final String QUANTITY_UNIT = "quantityUnit";

    private static final String SEALANT_MATERIAL = "Sealantmaterial";

    private static final String COMBO_TYPE = "combo_type";

    private static final String IMPLANT_TYPE = "种植牙";

    private static final String SERVICE_MENU = "种植服务套餐";

    private static final String DEAL_STRUCT_CONTENT_ATTR_NAME = "dealStructContent";

    private static final List<Integer> ALL_WEEKDAY = Lists.newArrayList(1, 2, 3, 4, 5, 6, 7);

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        if (ObjectUtils.isEmpty(productMS) || productMS.size() <= 1 || !hitGrey(param.getDouHuMS(), config)) {
            return Lists.newArrayList();
        }
        int entityId = ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId);
        int firstTopIndex = findTopProductIndex(productMS, entityId);
        if (firstTopIndex < 0) {
            return Lists.newArrayList();
        }
        ProductM topProduct = productMS.get(firstTopIndex);
        //先过滤出目标
        List<ProductM> similarList = productMS.stream().filter(this::isValidProduct)
                .filter(productM -> getAvailableProduct(productM, topProduct)).collect(Collectors.toList());
        if (similarList.size() <= 1) {
            return Lists.newArrayList();
        }
        //https://km.sankuai.com/collabpage/2337229728 排序逻辑
        List<ProductM> result = new ArrayList<>();
        switch (topProduct.getAttr(SERVICE_TYPE)) {
            case "洗牙":
                result = getWashSimilarList(similarList, topProduct);
                break;
            case "涂氟":
                result = getFluorideSimilarList(similarList, topProduct);
                break;
            case "窝沟封闭":
                result = getVaultSimilarList(similarList, topProduct);
                break;
            case "种植牙":
                result = getImplantSimilarList(similarList, topProduct);
                break;
            default:
                break;
        }
        return !ObjectUtils.isEmpty(config.getLimit())
                && config.getLimit() > 0 && result.size() > config.getLimit()
                ? result.subList(0, config.getLimit()) : result;
    }

    private boolean hitGrey(List<DouHuM> douHuMS, Config config) {
        if (!ObjectUtils.isEmpty(config) && !config.getNeedGrey()) {
            return true;
        }
        if (ObjectUtils.isEmpty(config.getHitGreyList()) || ObjectUtils.isEmpty(douHuMS)) {
            return false;
        }
        return douHuMS.stream().map(DouHuM::getSk).anyMatch(config.getHitGreyList()::contains);
    }

    private List<ProductM> getImplantSimilarList(List<ProductM> similarList, ProductM topProduct) {
        return similarList.stream()
                .sorted(Comparator.comparing((ProductM productM) -> getFirst(productM, topProduct))
                        .thenComparing((ProductM productM) -> getSaleScore(productM, topProduct))
                        .thenComparing((ProductM productM) -> getPriceScore(productM, topProduct)))
                .collect(Collectors.toList());
    }

    private Integer getFirst(ProductM productM, ProductM topProduct) {
        if (Objects.equals(productM.getProductId(), topProduct.getProductId())) {
            return -1;
        }
        return 0;
    }

    private List<ProductM> getVaultSimilarList(List<ProductM> similarList, ProductM topProduct) {
        return similarList.stream()
                .sorted(Comparator.comparing((ProductM productM) -> getFirst(productM, topProduct))
                        .thenComparing((ProductM productM) -> getAttrScore4Vault(productM, topProduct))
                        .thenComparing((ProductM productM) -> getSaleScore(productM, topProduct))
                        .thenComparing((ProductM productM) -> getPriceScore(productM, topProduct)))
                .collect(Collectors.toList());
    }

    private Integer getAttrScore4Vault(ProductM productM, ProductM topProduct) {
        List<Integer> topAvailable = getDisableDays(topProduct);
        List<Integer> available = getDisableDays(productM);
        String topQuantityUnit = getSkuAttrValue(topProduct, QUANTITY_UNIT);
        String quantityUnit = getSkuAttrValue(productM, QUANTITY_UNIT);
        String topSealantMaterial = getSkuAttrValue(topProduct, SEALANT_MATERIAL);
        String sealantMaterial = getSkuAttrValue(productM, SEALANT_MATERIAL);
        String topSuitCrowds = getSkuAttrValue(topProduct, SUIT_CROWD);
        String suitCrowds = getSkuAttrValue(productM, SUIT_CROWD);
        Integer attrScore = 0;
        if (topAvailable.containsAll(available)) {
            attrScore -= 10;
        }
        if (Objects.equals(topQuantityUnit, quantityUnit)) {
            attrScore -= 9;
        }
        if (Objects.equals(topSealantMaterial, sealantMaterial)) {
            attrScore -= 8;
        }
        if (Objects.equals(topSuitCrowds, suitCrowds)) {
            attrScore -= 7;
        }
        return attrScore;
    }

    private List<ProductM> getFluorideSimilarList(List<ProductM> similarList, ProductM topProduct) {
        return similarList.stream()
                .sorted(Comparator.comparing((ProductM productM) -> getFirst(productM, topProduct))
                        .thenComparing((ProductM productM) -> getAttrScore4Fluoride(productM, topProduct))
                        .thenComparing((ProductM productM) -> getSaleScore(productM, topProduct))
                        .thenComparing((ProductM productM) -> getPriceScore(productM, topProduct)))
                .collect(Collectors.toList());
    }

    private Integer getAttrScore4Fluoride(ProductM productM, ProductM topProduct) {
        List<Integer> topAvailable = getDisableDays(topProduct);
        List<Integer> available = getDisableDays(productM);
        String topSuitCrowds = getSkuAttrValue(topProduct, SUIT_CROWD);
        String suitCrowds = getSkuAttrValue(productM, SUIT_CROWD);
        Integer attrScore = 0;
        if (topAvailable.containsAll(available)) {
            attrScore -= 10;
        }
        if (Objects.equals(topSuitCrowds, suitCrowds)) {
            attrScore -= 9;
        }
        return attrScore;
    }

    private List<ProductM> getWashSimilarList(List<ProductM> similarList, ProductM topProduct) {
        return similarList.stream()
                .sorted(Comparator.comparing((ProductM productM) -> getFirst(productM, topProduct))
                        .thenComparing((ProductM productM) -> getSuitCrowdsScore(productM, topProduct))
                        .thenComparing((ProductM productM) -> getAttrScore(productM, topProduct))
                        .thenComparing((ProductM productM) -> getSaleScore(productM, topProduct))
                        .thenComparing((ProductM productM) -> getPriceScore(productM, topProduct)))
                .collect(Collectors.toList());
    }

    private Integer getPriceScore(ProductM productM, ProductM topProduct) {
        return Optional.ofNullable(productM.getSalePrice())
                .map(BigDecimal::intValue).orElse(Integer.MAX_VALUE);
    }

    private Integer getSaleScore(ProductM productM, ProductM topProduct) {
        Integer saleNum = Optional.ofNullable(topProduct.getSale())
                .map(ProductSaleM::getSale).orElse(0);
        return -saleNum;
    }

    private Integer getAttrScore(ProductM productM, ProductM topProduct) {
        List<Integer> topAvailable = getDisableDays(topProduct);
        List<Integer> available = getDisableDays(productM);
        String topCategory = getSkuAttrValue(topProduct, CATEGORY);
        String category = getSkuAttrValue(productM, CATEGORY);
        String topSandblasting = getSkuAttrValue(topProduct, SANDBLASTING);
        String sandblasting = getSkuAttrValue(productM, SANDBLASTING);
        Integer attrScore = 0;
        if (topAvailable.containsAll(available)) {
            attrScore -= 10;
        }
        if (Objects.equals(topCategory, category)) {
            attrScore -= 9;
        }
        if (Objects.equals(topSandblasting, sandblasting)) {
            attrScore -= 8;
        }
        return attrScore;
    }

    private List<Integer> getDisableDays(ProductM productM) {
        return Optional.ofNullable(productM.getUseRuleM()).map(UseRuleM::getDisableDate)
                .map(DisableDateM::getDisableDays).orElse(Lists.newArrayList()).stream()
                .filter(ALL_WEEKDAY::contains).collect(Collectors.toList());
    }


    private Integer getSuitCrowdsScore(ProductM productM, ProductM topProduct) {
        String topSuitCrowds = getSkuAttrValue(topProduct, SUIT_CROWD);
        String suitCrowds = getSkuAttrValue(productM, SUIT_CROWD);
        if (ObjectUtils.isEmpty(topSuitCrowds) || ObjectUtils.isEmpty(suitCrowds)) {
            return 0;
        }
        return (topSuitCrowds.contains("成人") && suitCrowds.contains("成人")
                || topSuitCrowds.contains("儿童") && suitCrowds.contains("儿童")) ? -1 : 0;
    }

    private String getSkuAttrValue(ProductM productM, String attrName) {
        DealStructModel dealStructModel = getDealStructModel(productM.getAttr(DEAL_STRUCT_CONTENT_ATTR_NAME));
        return Optional.ofNullable(dealStructModel).map(DealStructModel::getDealDetailStructuredData)
                .map(DealDetailStructuredModel::getDealDetailSkuUniStructuredModel)
                .map(DealDetailSkuUniStructuredModel::getMustGroups)
                .orElse(Lists.newArrayList())
                .stream()
                .filter(group -> !ObjectUtils.isEmpty(group) && !ObjectUtils.isEmpty(group.getSkus()))
                .flatMap(mustSkus -> mustSkus.getSkus().stream())
                .filter(sku -> !ObjectUtils.isEmpty(sku) && !ObjectUtils.isEmpty(sku.getSkuAttrs()))
                .flatMap(sku -> sku.getSkuAttrs().stream())
                .filter(skuAttr -> !ObjectUtils.isEmpty(skuAttr) && Objects.equals(attrName, skuAttr.getAttrName()))
                .map(DealSkuAttrModel::getAttrValue)
                .findFirst()
                .orElse(Strings.EMPTY);
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (ObjectUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    private boolean getAvailableProduct(ProductM productM, ProductM topProduct) {
        if (productM.getProductId() == topProduct.getProductId()) {
            return true;
        }
        return Objects.equals(topProduct.getCategoryId(), productM.getCategoryId())
                && Objects.equals(topProduct.getAttr(SERVICE_TYPE), productM.getAttr(SERVICE_TYPE));
    }

    private int findTopProductIndex(List<ProductM> productMS, int entityId) {
        int topIndex = -1;
        for (int index = 0; index < productMS.size(); index++) {
            if (productMS.get(index).getProductId() != entityId || !isValidProduct(productMS.get(index))) {
                continue;
            }
            topIndex = index;
            break;
        }
        return topIndex;
    }

    private boolean isValidProduct(ProductM productM) {
        int categoryId = productM.getCategoryId();
        String serviceType = productM.getAttr(SERVICE_TYPE);
        boolean notCheck = isCheckMenu(productM);
        return categoryId != 0 && StringUtils.isNotEmpty(serviceType) && notCheck;
    }

    private boolean isCheckMenu(ProductM productM) {
        String serviceType = productM.getAttr(SERVICE_TYPE);
        if (!Objects.equals(IMPLANT_TYPE, serviceType)) {
            return true;
        }
        String type = productM.getAttr(COMBO_TYPE);
        return !ObjectUtils.isEmpty(type) && Objects.equals(type, SERVICE_MENU);
    }

    @VPointCfg
    @Data
    public static class Config {
        private Integer limit = 5;
        private List<String> hitGreyList;
        private Boolean needGrey = true;
    }
}


