package com.sankuai.dzviewscene.product.dealstruct.ability.title.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.title.DealDetailTitleBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/24 7:14 下午
 */
@VPoint(name = "团购详情标题", description = "团购详情标题，支持配置",code = DealDetailTitleVP.CODE, ability = DealDetailTitleBuilder.CODE)
public abstract class DealDetailTitleVP<T> extends PmfVPoint<String, DealDetailTitleVP.Param, T> {

    public static final String CODE = "DealDetailTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private String title;
    }

}
