package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemposthandler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemBuildPostHandlerVP;

@VPointOption(name = "默认-空变化点",
        description = "默认不处理",
        code = DefaultPostHandlerOpt.CODE,
        isDefault = true)
public class DefaultPostHandlerOpt extends UnifiedShelfItemBuildPostHandlerVP<Void> {

    public static final String CODE = "DefaultPostHandlerOpt";

    @Override
    public Void compute(ActivityCxt context, Param param, Void config) {
        return null;
    }
}