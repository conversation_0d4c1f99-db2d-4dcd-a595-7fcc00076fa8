package com.sankuai.dzviewscene.product.filterlist.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * Created by float.lu on 2020/9/22.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@MobileDo(id = 0x4bfa)
public class DzFilterVO {

    /**
     * 筛选类型
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 筛选图片
     */
    @MobileDo.MobileField(key = 0xafc8)
    private String link;

    /**
     * 选中状态
     */
    @MobileDo.MobileField(key = 0xb59e)
    private boolean selected;

    /**
     * 筛选ID
     */
    private int id;

    /**
     * 筛选名字
     */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    /**
     * 筛选下商品数量
     */
    private int size;

    /**
     * 扩展参数
     */
    @MobileDo.MobileField(key = 0x91d7)
    private String extra;

    /**
     * 筛选按钮列表
     */
    @MobileDo.MobileField(key = 0x3f15)
    private List<DzFilterBtnVO> children;

    /**
     * 最小展示数量
     */
    private int minShowNum;
}
