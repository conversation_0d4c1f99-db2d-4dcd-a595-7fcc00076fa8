package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.concurrent.future.FutureUtils;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.nr.atom.impl.CompositeAtomServiceImpl;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.BaseDealIdQueryHandler;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailSkuUniStructuredModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealSkuAttrModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.MustSkusGroupModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import scala.math.BigDecimal;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@VPointOption(name = "生活服务洗涤排序和截断",
        description = "生活服务洗涤排序和截断",
        code = "LifeWashListAcsOpt")
public class LifeCleanSimilarFilterAcsOpt extends ProductListVP<LifeCleanSimilarFilterAcsOpt.Config> {

    private static final String SERVICE_TYPE = "service_type";

    private static final String DEAL_STRUCT_CONTENT_ATTR_NAME = "dealStructContent";
    private static final Logger log = LoggerFactory.getLogger(LifeCleanSimilarFilterAcsOpt.class);
    /**
     * 商品预约属性
     */
    public static final String RESERVATION_NEEDEDATTR = "reservation_is_needed_or_not";
    public static final String RESERVATION_NEEDEDATTR3 = "reservation_is_needed_or_not_3";
    @Autowired
    private CompositeAtomService compositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.life.wash.similar.list.category", defaultValue = "[]")
    private List<Long> validProductCategories;

    @ConfigValue(
            key = "com.sankuai.dzviewscene.dealshelf.life.wash.similar.support.reserve.category", defaultValue = "[]"
    )
    private List<Integer> supportCategories;

    @Resource
    private BaseDealIdQueryHandler baseDealIdQueryHandler;

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        if (CollectionUtils.isEmpty(productMS)) {
            return Lists.newArrayList();
        }
        List<ProductM> result;
        if (baseDealIdQueryHandler.runSelfOperatedCleaning(context)) {
            result = selfOperatedCleaningFilter(context, productMS, param, config);
        } else {
            result = defaultFilter(context, productMS, param, config);
        }
        return filterByIsSupportReserve(result, context);
    }


    private List<ProductM> filterByIsSupportReserve(List<ProductM> productMS, ActivityCxt context) {
        try {
            String pageSourceNew = context.getParam(ShelfActivityConstants.Params.pageSourceNew);

            if (!StringUtils.equals("pre_order_deal", pageSourceNew) || CollectionUtils.isEmpty(productMS)) {
                return productMS;
            }

            // 团单二级类目是否支持预约,支持预约则继续走后面的过滤逻辑,不支持预约则返回原来的商品数据
            int categoryId = ParamsUtil.getIntSafely(context, ProductDetailActivityConstants.Params.dealCategoryId);
            if (!supportCategories.contains(categoryId)) {
                return productMS;
            }

            // 商户维度是否支持预约
            Long dpShopId = PoiIdUtil.getDpPoiIdL(context, ShelfActivityConstants.Params.dpPoiIdL,
                    ShelfActivityConstants.Params.dpPoiId);
            CompletableFuture<Boolean> isSupportReserveFuture = compositeAtomService
                    .queryIsSupportStrictReserve(dpShopId);
            Boolean isSupportReserve = isSupportReserveFuture.join();
            if (isSupportReserve == null || !isSupportReserve) {
                return Lists.newArrayList();
            }

            // 识别商品是否支持预约
            return productMS.stream().filter(product -> {
                if (product == null || CollectionUtils.isEmpty(product.getExtAttrs())) {
                    return false;
                }
                List<AttrM> extAttrs = product.getExtAttrs();
                String reservationNeeded = DealDetailUtils.getAttrSingleValueByAttrName(extAttrs,
                        RESERVATION_NEEDEDATTR);
                String reservationNeeded3 = DealDetailUtils.getAttrSingleValueByAttrName(extAttrs,
                        RESERVATION_NEEDEDATTR3);
                return StringUtils.equals("是", reservationNeeded) || StringUtils.equals("是", reservationNeeded3);
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("filterByIsSupportReserve error,context:{}", JsonCodec.encode(context), e);
        }
        return productMS;
    }

    private List<ProductM> selfOperatedCleaningFilter(ActivityCxt context, List<ProductM> productMS, Param param, Config config) {
        ProductM currentProduct = getCurrentProduct(productMS, context);
        Map<Integer, DealServiceItem> productId2ServiceItemMap = productMS.stream()
                .collect(Collectors.toMap(ProductM::getProductId, v -> getDealServiceItem(context, v)));
        // 查询的团单
        DealServiceItem currentProductServiceItem = productId2ServiceItemMap.get(currentProduct.getProductId());
        List<DealServiceItem> dealServiceItems = productMS.stream()
                .map(productM -> getDealServiceItem(context, productM))
                .collect(Collectors.toList());
        // 同服务项目的团单信息
        List<DealServiceItem> sameCateServiceItems = dealServiceItems.stream()
                .filter(item -> isSameProductCategory(item.getProductCategories(), currentProductServiceItem.getProductCategories())
                        && hasFacadeCleanPartAttr(currentProductServiceItem, item))
                .collect(Collectors.toList());
        // 如果sameCateServiceItems只有当前团单，则不展示相似团购模块
        if (CollectionUtils.isEmpty(sameCateServiceItems) || sameCateServiceItems.size() == 1) {
            return Lists.newArrayList();
        }
        // productId -> productMs
        Map<Long, ProductM> productId2DetailMap = productMS.stream()
                .collect(Collectors.toMap(productM -> (long) productM.getProductId(), Function.identity(), (x, y) -> x));
        return Optional.of(sameCateServiceItems)
                .orElse(Lists.newArrayList())
                .stream()
                .map(item -> getRelatedProductMs(item, productId2DetailMap))
                .limit(config.getLimit())
                .sorted(new SelfOperatedCleaningDealSorter())
                .collect(Collectors.toList());
    }

    private ProductM getRelatedProductMs(DealServiceItem item, Map<Long, ProductM> productId2DetailMap) {
        if (MapUtils.isEmpty(productId2DetailMap)) {
            return null;
        }
        return productId2DetailMap.get(item.getDealGroupId());
    }

    private boolean isSameProductCategory(List<ProductSkuCategoryModel> skuCateA, List<ProductSkuCategoryModel> skuCateB) {
        if (CollectionUtils.isEmpty(skuCateA) || CollectionUtils.isEmpty(skuCateB)) {
            return false;
        }
        return skuCateA.stream()
                .anyMatch(s1 -> {
                    return skuCateB.stream()
                            .anyMatch(s2 -> Objects.equals(s1.getProductCategoryId(), s2.getProductCategoryId()));
                });
    }

    private boolean hasFacadeCleanPartAttr(DealServiceItem currentProductServiceItem, DealServiceItem item) {
        if (Objects.isNull(currentProductServiceItem) || Objects.isNull(item)) {
            return false;
        }
        boolean matched = currentProductServiceItem.getProductCategories()
                .stream()
                .anyMatch(skuCate -> Objects.equals(skuCate.getProductCategoryId(), 3145L));
        if (!matched) {
            // 如果当前团单的服务类型不是3145，则不进行FacadeCleanPart属性值的聚合
            return true;
        }
        // 按FacadeCleanPart是否有值来进行聚合
        return (StringUtils.isBlank(currentProductServiceItem.getFacadeCleanPart())
                && StringUtils.isBlank(item.getFacadeCleanPart()))
                ||
                (StringUtils.isNotBlank(currentProductServiceItem.getFacadeCleanPart())
                        && StringUtils.isNotBlank(item.getFacadeCleanPart()));
    }

    private DealServiceItem getDealServiceItem(ActivityCxt cxt, ProductM productM) {
        if (Objects.isNull(productM) || CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return new DealServiceItem();
        }
        DealServiceItem serviceItem = new DealServiceItem();
        // 团单ID
        serviceItem.setDealGroupId((long) productM.getProductId());

        String dealStructContent = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), "dealStructContent");
        DealStructModel dealStructModel;
        try {
            dealStructModel = JsonUtils.fromJson(dealStructContent, DealStructModel.class);
            if (Objects.isNull(dealStructModel)) {
                return serviceItem;
            }
            // 服务项目分类
            serviceItem.setProductCategories(dealStructModel.getProductCategories());

            if (Objects.isNull(dealStructModel.getDealDetailStructuredData())
                    || Objects.isNull(dealStructModel.getDealDetailStructuredData().getDealDetailSkuUniStructuredModel())) {
                return serviceItem;
            }
            DealDetailSkuUniStructuredModel dealDetailSkuUniStructuredModel = dealStructModel.getDealDetailStructuredData().getDealDetailSkuUniStructuredModel();
            // 擦窗/玻璃-属性
            String facadeCleanPart = getDealFacadeCleanPartAttr(dealDetailSkuUniStructuredModel.getMustGroups());
            serviceItem.setFacadeCleanPart(facadeCleanPart);
        } catch (Exception e) {
            log.error("getDealServiceItem json parse error, dealStructContent={}", dealStructContent, e);
        }
        return serviceItem;
    }

    private String getDealFacadeCleanPartAttr(List<MustSkusGroupModel> mustGroups) {
        if (CollectionUtils.isEmpty(mustGroups)) {
            return StringUtils.EMPTY;
        }
        Optional<String> facadeCleanPart = mustGroups.stream()
                .flatMap(mustGroup -> mustGroup.getSkus().stream())
                .flatMap(skuModel -> skuModel.getSkuAttrs().stream())
                .filter(skuAttrModel -> Objects.equals(skuAttrModel.getAttrName(), "facadeCleanPart"))
                .map(DealSkuAttrModel::getAttrValue)
                .findFirst();
        return facadeCleanPart.orElse(StringUtils.EMPTY);
    }
    
    private List<ProductM> defaultFilter(ActivityCxt context, List<ProductM> productMS, Param param, Config config) {
        //获取当前团单信息
        ProductM currentProduct = getCurrentProduct(productMS, context);
        List<ProductM> sortedProductMS = productMS.stream().filter(productM -> productM.getSale() != null).sorted(new Comparator<ProductM>() {
            @Override
            public int compare(ProductM o1, ProductM o2) {
                return o2.getSale().getSale() - o1.getSale().getSale();
            }
        }).collect(Collectors.toList());
        List<ProductM> sameProjectProductList = sortedProductMS.stream().filter(this::isValidProduct).filter(productM -> productM.getProductId() != currentProduct.getProductId()).filter(productM -> getAvailableProduct(productM, currentProduct)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sameProjectProductList) || sameProjectProductList.size() < 3) {
            return getSimilarProductList(currentProduct, sortedProductMS, sameProjectProductList).stream().limit(config.getLimit()).collect(Collectors.toList());
        }
        return sameProjectProductList.stream().limit(config.getLimit()).collect(Collectors.toList());
    }

    private ProductM getCurrentProduct(List<ProductM> productMS, ActivityCxt context) {
        return productMS.stream()
                .filter(productM -> productM.getProductId() == ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId))
                .findFirst().orElse(productMS.get(0));
    }

    private List<ProductM> getSimilarProductList(ProductM currentProduct, List<ProductM> sortedProductMS, List<ProductM> sameProjectProductList) {
        if (CollectionUtils.isEmpty(sortedProductMS)) {
            return CollectionUtils.isEmpty(sameProjectProductList) ? Lists.newArrayList() : sameProjectProductList;
        }
        sortedProductMS.forEach(m -> this.addValidProduct(currentProduct, sameProjectProductList, m));
        return CollectionUtils.isEmpty(sameProjectProductList) ? Lists.newArrayList() : sameProjectProductList;
    }

    private void addValidProduct(ProductM currentProduct, List<ProductM> sameProjectProductList, ProductM productM) {
        if (!isValidProduct(currentProduct)) {
            return;
        }
        String currentServiceType = currentProduct.getAttr(SERVICE_TYPE);
        String newServiceType = productM.getAttr(SERVICE_TYPE);
        if (currentServiceType.equals(newServiceType)) {
            List<ProductSkuCategoryModel> productCategories = getProductCategories(productM);
            if (CollectionUtils.isEmpty(productCategories)) {
                return;
            }
            List<Long> productCategoryIds = productCategories.stream().map(ProductSkuCategoryModel::getProductCategoryId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(productCategoryIds)) {
                return;
            }
            Long productCategory = productCategoryIds.get(0);
            if (validProductCategories.contains(productCategory) && currentProduct.getProductId() != productM.getProductId() && !sameProjectProductList.stream().map(ProductM::getProductId).collect(Collectors.toList()).contains(productM.getProductId())) {
                sameProjectProductList.add(productM);
            }
        }
    }

    public boolean getAvailableProduct(ProductM productM, ProductM currentProduct) {
        //服务项目
        List<ProductSkuCategoryModel> curProductCategories = getProductCategories(currentProduct);
        if (CollectionUtils.isEmpty(curProductCategories)) {
            return false;
        }
        List<Long> curCategories = curProductCategories.stream().map(ProductSkuCategoryModel::getProductCategoryId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(curCategories)) {
            return false;
        }
        Long currentProductCategory = curCategories.get(0);
        if (!validProductCategories.contains(currentProductCategory)) {
            return false;
        }
        List<ProductSkuCategoryModel> productCategories = getProductCategories(productM);
        if (CollectionUtils.isEmpty(productCategories)) {
            return false;
        }
        List<Long> productCategoryIds = productCategories.stream().map(ProductSkuCategoryModel::getProductCategoryId).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(productCategories) && productCategoryIds.get(0).equals(currentProductCategory);
    }

    private List<ProductSkuCategoryModel> getProductCategories(ProductM productM) {
        if (productM == null || StringUtils.isEmpty(productM.getAttr(DEAL_STRUCT_CONTENT_ATTR_NAME))) {
            return null;
        }
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENT_ATTR_NAME);
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null) {
            return null;
        }
        return dealStructModel.getProductCategories();
    }

    private boolean isValidProduct(ProductM productM) {
        int categoryId = productM.getCategoryId();
        String serviceType = productM.getAttr(SERVICE_TYPE);
        return categoryId != 0 && StringUtils.isNotEmpty(serviceType);
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (StringUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    @VPointCfg
    @Data
    public static class Config {
        private int limit = 10;
    }

    @Data
    private static class DealServiceItem {
        private Long dealGroupId;
        /**
         * 服务项目分类ID
         */
        private List<ProductSkuCategoryModel> productCategories;

        /**
         * 擦窗/玻璃
         */
        private String facadeCleanPart;
    }

    private static class SelfOperatedCleaningDealSorter implements Comparator<ProductM> {
        @Override
        public int compare(ProductM p1, ProductM p2) {
            // 处理销量为空的情况，销量为空放到列表后面
            if (Objects.isNull(p1.getSale())) {
                return Objects.isNull(p2.getSale()) ? 0 : 1;
            }
            if (Objects.isNull(p2.getSale())) {
                return -1;
            }
            // 首先按销量倒叙
            int saleCompare = Integer.compare(p2.getSale().getSale(), p1.getSale().getSale());
            if (saleCompare != 0) {
                return saleCompare;
            }
            // 处理价格为空的情况
            if (Objects.isNull(p1.getSalePrice())) {
                return Objects.isNull(p2.getSalePrice()) ? 0 : 1;
            }

            if (Objects.isNull(p2.getSalePrice())) {
                return -1;
            }

            // 如果销量相同，则按价格升序
            return p1.getSalePrice().compareTo(p2.getSalePrice());
        }
    }
}
