package com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import lombok.Builder;
import lombok.Data;

@VPoint(name = "筛选-副标题", description = "筛选-副标题", code = FilterSubTitleVP.CODE, ability = FilterBuilder.CODE)
public abstract class FilterSubTitleVP<T> extends PmfVPoint<RichLabelVO, FilterSubTitleVP.Param, T> {
    public static final String CODE = "FilterSubTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 筛选按钮
         */
        private FilterBtnM filterBtnM;

        /**
         * 层级，1开始
         */
        private int layer;

        /**
         * 属于父节点的第几个，从1开始
         */
        private int index;

    }
}