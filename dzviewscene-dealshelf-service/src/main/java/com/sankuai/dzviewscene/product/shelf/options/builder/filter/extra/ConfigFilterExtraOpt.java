package com.sankuai.dzviewscene.product.shelf.options.builder.filter.extra;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.gson.JsonObject;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterExtraVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhangsuping on 2023/2/13.
 */
@Slf4j
@VPointOption(name = "筛选项-扩展信息可选项", description = "后期有定制化需求再扩展", code = "ConfigFilterExtraOpt")
public class ConfigFilterExtraOpt extends FilterExtraVP<ConfigFilterExtraOpt.Config> {

    private static final String FILTER_KEY = "tabKey";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        Map<String, Object> extra = new HashMap<>();
        // 默认执行必要参数提取
        extractMustParameters(context, extra);

        String extraFromFilterId = getExtraFromFilterId(param, config);
        if (StringUtils.isNotBlank(extraFromFilterId)) {
            extra.putAll(parseJsonToMap(extraFromFilterId));
            return !MapUtils.isEmpty(extra) ? JsonCodec.encodeWithUTF8(extra) : null;
        }
        int productNum = getProductNum(param.getProductGroupM());
        if (CollectionUtils.isNotEmpty(param.getDouHuList())
                && MapUtils.isNotEmpty(config.getDouHuSkMinProductNumMap())) {
            for (DouHuM douHuM : param.getDouHuList()) {
                if (!config.getDouHuSkMinProductNumMap().containsKey(douHuM.getSk())) {
                    continue;
                }
                Integer minProductNum = config.getDouHuSkMinProductNumMap().get(douHuM.getSk());
                if (minProductNum != null && productNum >= minProductNum) {
                    if (StringUtils.isNotBlank(config.getExtra())) {
                        extra.putAll(parseJsonToMap(config.getExtra()));
                    }
                    return !MapUtils.isEmpty(extra) ? JsonCodec.encodeWithUTF8(extra) : null;
                }
            }
        }
        if (config.isEnableTotalProductNum()) {
            String totalProductNumExtra = buildExtraString(context, config);
            if (StringUtils.isNotBlank(totalProductNumExtra)) {
                extra.putAll(parseJsonToMap(totalProductNumExtra));
            }
        }
        return !MapUtils.isEmpty(extra) ? JsonCodec.encodeWithUTF8(extra) : null;
    }

    private String getExtraFromFilterId(Param param, Config config) {
        String filterKey = MapUtils.getString(config.getFilterId2FilterKey(), param.getFilterBtnM().getFilterId());
        if (StringUtils.isBlank(filterKey)) {
            return null;
        }
        Map<String, String> extraMap = new HashMap<>();
        extraMap.put(FILTER_KEY, filterKey);
        return JsonCodec.encodeWithUTF8(extraMap);
    }

    private String buildExtraString(ActivityCxt context, Config config) {
        if (Objects.isNull(context.getParam(ShelfActivityConstants.Params.totalProductNum))) {
            return null;
        }
        int totalProductNum = Integer
                .parseInt(context.getParam(ShelfActivityConstants.Params.totalProductNum).toString());
        // 在extra信息中返回 productNum
        JsonObject extra = new JsonObject();
        extra.addProperty(ShelfActivityConstants.Params.totalProductNum, totalProductNum);
        if (totalProductNum >= config.getDoubleColumnMinNum()) {
            extra.addProperty("isTwoColumnsShelf", true);
        }
        return extra.toString();
    }

    private int getProductNum(ProductGroupM productGroupM) {
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return 0;
        }
        return productGroupM.getProducts().size();
    }

    private Map<String, Object> parseJsonToMap(String jsonString) {
        try {
            return JsonCodec.decode(jsonString, Map.class);
        } catch (Exception e) {
            log.error("Failed to parse JSON string to Map. Invalid JSON format: {}", jsonString, e);
            return new HashMap<>();
        }
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 斗斛实验策略和最少商品数量映射关系
         */
        private Map<String, Integer> douHuSkMinProductNumMap;

        /**
         * 扩展信息，只有商品的梳理，命中douHuSkMinProductNumMap中的条件时才展示该配置信息
         */
        private String extra;

        /**
         * 返回 totalProductNum 字段
         */
        private boolean enableTotalProductNum = false;

        /**
         * 双列货架最少商品数量
         */
        private int doubleColumnMinNum = 4;

        private Map<Long, String> filterId2FilterKey;
    }
}
