package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceremark;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceRemarkVP;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@VPointOption(name = "默认-空变化点",
        description = "返回 null",
        code = "NullPriceRemarkOpt",
        isDefault = true)
public class NullPriceRemarkOpt extends ItemSalePriceRemarkVP<Void> {
    @Override
    public String compute(ActivityCxt activityCxt, Param param, Void unused) {
        return null;
    }
}
