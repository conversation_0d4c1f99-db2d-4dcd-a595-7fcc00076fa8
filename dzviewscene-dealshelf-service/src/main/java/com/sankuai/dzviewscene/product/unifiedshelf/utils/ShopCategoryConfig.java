package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 可以用泛型但没必要，默认的使用 string 格式作为配置，使用的时候通过指定 Class 来反序列化
 */
@Data
public class ShopCategoryConfig implements Serializable {

    private List<ShopCategoryCellConfig> configList;

    private String defaultConfig;

    /**
     * 这是一个无业务意义的值，但是会用来进行打点上报，建议添加
     */
    private String flag;

    @Data
    public static class ShopCategoryCellConfig implements Serializable {

        private List<Integer> backCategory;

        private String config;
    }

}
