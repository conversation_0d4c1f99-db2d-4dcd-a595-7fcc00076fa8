package com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga;

import com.alibaba.fastjson.JSONObject;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.beautyyoga.enums.BeautyYogaEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.ServiceFlowParseModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.MassageServiceTypeEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.BeautyYogaUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.OverNightModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2024/6/6 19:13
 */
public abstract class AbstractBeautyYogaModuleOpt<T> extends SkuListModuleVP<T> {

    protected  List<DealSkuVO> buildServiceItemsByServiceType(String serviceType, List<AttrM> dealAttrs, JSONObject unitJSON, String optionStr, BeautyYogaDealModuleOpt.Config config) {
        BeautyYogaEnum serviceTypeEnum = BeautyYogaEnum.getEnumByServiceType(serviceType);
        if (serviceTypeEnum == null) {
            return null;
        }
        switch (serviceTypeEnum) {
            case SINGLE_TIME_CARD:
            case MULTI_CYCLE_TIMES_CARD:
            case MULTI_CYCLE_TIMES_CARD_:
            case PERSONAL_TIMES_CARD:
            case PERSONAL_MULTI_CYCLE_TIMES_CARD:
            case PERSONAL_MULTI_CYCLE_TIMES_CARD_:
            case COACH_TRAINING:
            case CLASSROOM_RENTAL:
            case CHOREOGRAPHY:
                List<DealSkuVO> result = Lists.newArrayList();
                result.add(BeautyYogaUtils.parseServiceItems(serviceType, dealAttrs, config));
                return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
            case COMBINATION_LESSON_PACK:
                return BeautyYogaUtils.parseServiceItemsByUnit(serviceType,dealAttrs, unitJSON, optionStr, config);
            case PHYSICAL_GOODS:
                return null;
            default:
                return null;
        }
    }

    /**
     * 构建服务项目
     */
    protected DealSkuItemVO buildDealSkuItemVO(String name, int type, List<SkuAttrAttrItemVO> skuAttrAttrItemVOS, String value) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValueAttrs(skuAttrAttrItemVOS);
        dealSkuItemVO.setValue(value);
        return dealSkuItemVO;
    }



    protected DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String subTitle, List<DealSkuVO> dealSkuList) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName(groupName);
        dealDetailSkuListModuleGroupModel.setGroupSubtitle(subTitle);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailSkuListModuleGroupModel;
    }

    protected DealDetailSkuListModuleGroupModel buildDealDetailSkuListModuleGroupModel(String groupName, String groupTitle, String subTitle, List<DealSkuVO> dealSkuList) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        dealDetailSkuListModuleGroupModel.setGroupName(groupName);
        dealDetailSkuListModuleGroupModel.setGroupTitle(groupTitle);
        dealDetailSkuListModuleGroupModel.setGroupSubtitle(subTitle);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        return dealDetailSkuListModuleGroupModel;
    }


}
