package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common;

import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

public abstract class AbstractPriceBottomTagBuildStrategy implements PriceBottomTagBuildStrategy {

    public abstract DzTagVO buildTag(PriceBottomTagBuildReq req);

    public String getStrategyDesc(){
        return getName();
    }

    @Override
    public DzTagVO build(PriceBottomTagBuildReq req) {
        //因为当前的标签都是和商品相关的，所以在抽象类这层做了参数校验，极个别特殊情况可以选择不继承抽象类
        if (req == null || req.getProductM() == null) {
            return null;
        }
        //隐藏标签策略
        if(hidTag(req)){
            return null;
        }
        return buildTag(req);
    }

    public boolean hidTag(PriceBottomTagBuildReq req){
        //优惠减负不展示标签
        return simplifyRemoveTag() && PromoSimplifyUtils.hitPromoSimplify(req.getContext());
    }

    public boolean simplifyRemoveTag(){
        return false;
    }

    public void addComparePriceTagAttr(ProductM productM, DzTagVO dzTagVO) {
        if(dzTagVO == null){
            return;
        }
        //比价标签添加到扩展信息，方便后面埋点上报
        productM.setAttr(PriceDisplayUtils.ITEM_PRICE_POWER_TAG, dzTagVO.getName());
    }
}
