package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.life.builder.LifeDealFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.vo.RichLabel;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
@VPoint(name = "团单底部标签", description = "item-bottomTags", code = ItemBottomTagsVP.CODE, ability = FloorsBuilder.CODE)
public abstract class ItemBottomTagsVP<T> extends PmfVPoint<List<RichLabelVO>, ItemBottomTagsVP.Param, T> {
    public static final String CODE = "ItemBottomTagsVP";

    private static final String FONT_COLOR_777777 = "#777777";

    private static final String FONT_COLOR_FF6633 = "#FF6633";

    private static final String FONT_COLOR_FF4B10 = "#FF4B10";

    private static final int FONT_SIZE_11 = 11;

    /**
     * 获取默认的次卡、拼团标签
     *
     * @param productM
     * @param showDiscountByNum
     * @param platform
     * @return
     */
    protected List<RichLabelVO> getCardAndPinTags(ProductM productM, boolean showDiscountByNum, int platform) {
        RichLabelVO card = buildRichLabel(productM.getCardPrice());
        RichLabelVO pin = buildRichLabel(productM.getPinPrice());
        List<RichLabelVO> richLabelVOS = new ArrayList<>();
        if (showDiscountByNum) {
            RichLabelVO discount = buildDiscountByNum(productM, platform);
            if (discount != null) {
                richLabelVOS.add(discount);
            }
        }
        if (card != null) {
            richLabelVOS.add(card);
        }
        if (pin != null) {
            richLabelVOS.add(pin);
        }
        return richLabelVOS;
    }

    /**
     * 过滤空节点，若都为空 返回 null
     *
     * @param beforeTags
     * @return
     */
    protected List<RichLabelVO> filterEmptyTags(List<RichLabelVO> beforeTags) {
        if (CollectionUtils.isEmpty(beforeTags)) {
            return null;
        }
        List<RichLabelVO> afterTags = beforeTags.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(afterTags)) {
            return null;
        }
        return afterTags;
    }

    /**
     * 构造拼团、次卡 共省文案信息
     *
     * @param productPriceM
     * @return
     */
    private RichLabelVO buildRichLabel(ProductPriceM productPriceM) {
        if (productPriceM == null || StringUtils.isEmpty(productPriceM.getPriceTag())) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        String[] priceDesc = productPriceM.getPriceTag().split("/");
        if (priceDesc.length < 2) {
            List<LifeDealFloorsBuilderExt.RichLabel> richLabelList = Lists.newArrayList(
                    new LifeDealFloorsBuilderExt.RichLabel(productPriceM.getPriceDesc(), FONT_COLOR_777777, FONT_SIZE_11),
                    new LifeDealFloorsBuilderExt.RichLabel(productPriceM.getPriceTag(), FONT_COLOR_FF6633, FONT_SIZE_11));
            richLabelVO.setText(JsonCodec.encode(richLabelList));
            return richLabelVO;
        }
        List<LifeDealFloorsBuilderExt.RichLabel> richLabelList = Lists.newArrayList(
                new LifeDealFloorsBuilderExt.RichLabel(productPriceM.getPriceDesc(), FONT_COLOR_777777, FONT_SIZE_11),
                new LifeDealFloorsBuilderExt.RichLabel(priceDesc[0], FONT_COLOR_FF6633, FONT_SIZE_11),
                new LifeDealFloorsBuilderExt.RichLabel("/" + priceDesc[1], FONT_COLOR_FF6633, FONT_SIZE_11));
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return richLabelVO;
    }

    protected List<RichLabelVO> buildRichLabel(ProductPriceM productPriceM, String salePrice, String promoPreDesc) {
        if (productPriceM == null || StringUtils.isEmpty(productPriceM.getPriceTag())) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        String[] priceDesc = productPriceM.getPriceTag().split("/");
        RichLabelVO promoDescRichLabelVO = buildPromoDescRichLabel(promoPreDesc, getPriceTag(priceDesc, productPriceM, salePrice));
        if (promoDescRichLabelVO == null) {
            return null;
        }
        if (priceDesc.length < 2) {
            List<RichLabel> richLabelList = Lists.newArrayList(
                    new RichLabel(productPriceM.getPriceDesc(), FONT_COLOR_777777, FONT_SIZE_11),
                    new RichLabel(productPriceM.getPriceTag(), FONT_COLOR_FF6633, FONT_SIZE_11));
            richLabelVO.setText(JsonCodec.encode(richLabelList));
            return filterEmptyTags(Lists.newArrayList(richLabelVO, promoDescRichLabelVO));
        }
        List<RichLabel> richLabelList = Lists.newArrayList(
                new RichLabel(productPriceM.getPriceDesc(), FONT_COLOR_777777, FONT_SIZE_11),
                new RichLabel(priceDesc[0], FONT_COLOR_FF6633, FONT_SIZE_11),
                new RichLabel("/" + priceDesc[1], FONT_COLOR_FF6633, FONT_SIZE_11));
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return filterEmptyTags(Lists.newArrayList(richLabelVO, promoDescRichLabelVO));
    }

    protected String getPriceTag(String[] priceDesc, ProductPriceM productPriceM, String salePrice) {
        if (StringUtils.isEmpty(salePrice)) {
            return StringUtils.EMPTY;
        }
        String priceTag = priceDesc.length < 2 ? productPriceM.getPriceTag() : priceDesc[0];
        BigDecimal promoPrice = new BigDecimal(salePrice);
        BigDecimal price = new BigDecimal(priceTag.substring(1));
        if (promoPrice.compareTo(price) <= 0) {
            return StringUtils.EMPTY;
        }
        return String.format("￥%s", promoPrice.subtract(price).stripTrailingZeros().toPlainString());
    }

    protected RichLabelVO buildPromoDescRichLabel(String promoPreDesc, String priceTag) {
        if (StringUtils.isEmpty(priceTag)) {
            return null;
        }
        RichLabelVO promoDescRichLabelVO = new RichLabelVO();
        List<LifeDealFloorsBuilderExt.RichLabel> promoDescRichLabelList = Lists.newArrayList(
                new LifeDealFloorsBuilderExt.RichLabel(promoPreDesc, FONT_COLOR_777777, FONT_SIZE_11),
                new LifeDealFloorsBuilderExt.RichLabel(priceTag, FONT_COLOR_FF6633, FONT_SIZE_11));
        promoDescRichLabelVO.setText(JsonCodec.encode(promoDescRichLabelList));
        return promoDescRichLabelVO;
    }

    public RichLabelVO buildDiscountByNum(ProductM productM, int platform) {
        return ProductMPromoInfoUtils.getDiscountByNum(productM, platform);
    }

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        /**
         * 斗斛列表
         */
        private List<DouHuM> douHuList;

        private String salePrice;

    }
}
