package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.ImmutableMap;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.apache.commons.collections.CollectionUtils;
import org.h2.util.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component("chiMedAddAttrsGroupStrategy")
public class ChiMedAddAttrsGroupStrategy implements ModuleStrategy {

    private final String APPLICABLE_INDICATIONS = "Applicable_indications";
    private final String VALUE_ADDED_SERVICES = "Valueadded_services";
    private final String SYS_TEXTAREA = "sys_textArea";

    private ImmutableMap<String, String> addAttrsMap = ImmutableMap.of(
            APPLICABLE_INDICATIONS, "适用人群",
            VALUE_ADDED_SERVICES, "免费附赠",
            SYS_TEXTAREA, "补充信息"
    );

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return null;
        }

        List<AttrM> dealAttrs = Optional.of(dealDetailInfoModels)
                .flatMap(list -> list.stream().findFirst())
                .map(DealDetailInfoModel::getDealAttrs)
                .orElse(Collections.emptyList());

        Map<String, String> addValuesMap = dealAttrs.stream()
                .filter(dealAttr -> dealAttr != null && isAdditionalConfig(dealAttr.getName()))
                .collect(Collectors.toMap(
                        AttrM::getName,
                        AttrM::getValue,
                        (existing, replacement) -> existing // 如果有重复的键，保留现有的值
                ));
        if (addValuesMap.isEmpty()) {
            return null;
        }
        if(!StringUtils.isNullOrEmpty(dealDetailInfoModels.get(0).getDesc())) {
            addValuesMap.put(SYS_TEXTAREA, dealDetailInfoModels.get(0).getDesc());
        }

        return buildDealDetailModuleVO(addValuesMap);
    }

    private boolean isAdditionalConfig(String name) {
        return APPLICABLE_INDICATIONS.equals(name) || VALUE_ADDED_SERVICES.equals(name) || SYS_TEXTAREA.equals(name);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(Map<String, String> addValuesMap) {
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        StandardServiceVO standardServiceVO = new StandardServiceVO();
        List<StandardServiceContentVO> standardServiceContentVOS = new ArrayList<>();

        // 保证显示顺序
        List<String> keysArr = Arrays.asList(APPLICABLE_INDICATIONS, VALUE_ADDED_SERVICES, SYS_TEXTAREA);

        for (String key : keysArr) {
            if (!addValuesMap.containsKey(key)){
                continue;
            }
            StandardServiceContentVO standardServiceContentVO = new StandardServiceContentVO();
            standardServiceContentVO.setTitle(addAttrsMap.get(key));
            standardServiceContentVO.setDesc(addValuesMap.get(key));
            standardServiceContentVOS.add(standardServiceContentVO);
            standardServiceVO.setStandardServiceContents(standardServiceContentVOS);
            dealDetailModuleVO.setStandardServiceModel(standardServiceVO);
        }

        return dealDetailModuleVO;
    }

}
