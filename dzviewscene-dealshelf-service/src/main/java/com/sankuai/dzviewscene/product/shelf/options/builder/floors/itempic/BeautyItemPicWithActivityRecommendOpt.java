package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic;

import com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dztheme.deal.dto.enums.DealActivityTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPicVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@VPointOption(name = "丽人-商品图片-只含大促活动和商家推荐角标逻辑",
        description = "丽人新的团购货架的商品图片-只含大促活动和商家推荐两类角标逻辑，活动角标没有位置，前端展示环绕式样式",
        code = "BeautyShelfItemPicWithActivityRecommendOpt")
public class BeautyItemPicWithActivityRecommendOpt extends ItemPicVP<BeautyItemPicWithActivityRecommendOpt.Config> {

    private static final int PIC_WIDTH = 300;
    private static final int PIC_HEIGHT = 300;
    private static final int PIC_ASPECT_RADIO = 1;

    private static final String VIDEO_PATH = "deal_video_path";

    @Override
    public PicAreaVO compute(ActivityCxt context, Param param, Config config) {
        if (StringUtils.isEmpty(param.getProductM().getPicUrl())) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        context.getParam(ShelfActivityConstants.Params.userAgent);
        picAreaVO.setPic(buildDzPictureComponentVO(param.getProductM(), config));
        //
        picAreaVO.setFloatTags(mergeFloatTag(param.getProductM(), param.getFilterId(), config));
        return picAreaVO;
    }

    private DzPictureComponentVO buildDzPictureComponentVO(ProductM productM, Config config) {
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setAspectRadio(NumberUtils.getValidDouble(config.picAspectRadio, PIC_ASPECT_RADIO));
        pic.setPicUrl(getPicUrl(productM, config));
        return pic;
    }

    private String getPicUrl(ProductM productM, Config config) {
        String showUrl = productM.getPicUrl();

        if (config.isCalcBySpu()
                && productM.getDealSpuId() > 0
                && (ProductMAttrUtils.hotSpu(productM) || ProductMAttrUtils.superSpu(productM))
                && Objects.nonNull(productM.getSpuM())
                && StringUtils.isNotEmpty(productM.getSpuM().getHeadPic())) {
            // 当为标准团单时，展示置指定配置的头图
            showUrl =  productM.getSpuM().getHeadPic();
        }
        String url = toHttpsUrl(showUrl, NumberUtils.getValidInt(config.picWidth, PIC_WIDTH), NumberUtils.getValidInt(config.picHeight, PIC_HEIGHT), PictureURLBuilders.ScaleType.Cut);
        url = removeWaterMark(url);
        return url;
    }

    /**
     * 融合大促活动和商家推荐两类角标，大促活动优先级高于商家推荐
     * @param productM
     * @return
     */
    private List<FloatTagVO> mergeFloatTag(ProductM productM, long filterId, Config config) {
        List<FloatTagVO> allFloatTags = Lists.newArrayList();
        String activityUrl = findActivityUrl(productM.getActivities());
        if (StringUtils.isNotEmpty(activityUrl)) {
            //活动角标有效，优先展示活动角标
            allFloatTags.addAll(buildFloatTag(activityUrl,config));
        }else if (ProductMAttrUtils.isShopRecommend(productM, filterId)) {
            //商家推荐有效，展示商家推荐角标
            allFloatTags.addAll(buildFloatTag(config.getShopRecommendPic(),config));
        } else if (ProductMAttrUtils.isTopDisplayProduct(productM)) {
            //商家堆头商品，展示超值爆款角标
            allFloatTags.addAll(buildFloatTag(config.getShopSuperVoguePic(), config));
        }
        if(config.isShowVideoIcon()){
            appendVideoIcon(productM, allFloatTags);
        }
        return allFloatTags;
    }

    private String findActivityUrl(List<ProductActivityM> activities){
        if(CollectionUtils.isEmpty(activities)){
            return StringUtils.EMPTY;
        }
        ProductActivityM productActivityM = activities.stream().filter(item->MapUtils.isNotEmpty(item.getActivityPicUrlMap())).findFirst().orElse(null);
        if(Objects.nonNull(productActivityM)) {
            ActivityPicUrlDTO activityPicUrlDTO  = productActivityM.getActivityPicUrlMap().get(ExposurePicUrlKeyEnum.POI_DEAL_NEW_ICON_URL.getKey());
            if(Objects.nonNull(activityPicUrlDTO)){
                return activityPicUrlDTO.getUrl();
            }
        }
        return StringUtils.EMPTY;
    }

    private List<FloatTagVO> buildFloatTag(String pic,Config config) {
        if (StringUtils.isEmpty(pic)) {
            return new ArrayList<>();
        }
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        floatTagPic.setPicUrl(pic);
        floatTagPic.setPicHeight(config.getIconPicHeight());
        floatTagPic.setAspectRadio(config.getIconAspectRadio());
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        return Lists.newArrayList(floatTagVO);
    }

    private void appendVideoIcon(ProductM productM, List<FloatTagVO> allFloatTags){
        if(!hasDealVideo(productM)){
            return;
        }
        allFloatTags.add(buildVideoIcon(allFloatTags.size() > 0));
    }

    private FloatTagVO buildVideoIcon(boolean hasRound){
        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        if(hasRound){
            floatTagPic.setPicUrl("https://img.meituan.net/beautyimg/609b8652af078a12af68e329c2e84c9e1340.png");
            floatTagPic.setAspectRadio(0.591);
            floatTagPic.setPicHeight(44);
        }else{
            floatTagPic.setPicUrl("https://img.meituan.net/beautyimg/72f3c8ca2636376d8b9964746badd6151086.png");
            floatTagPic.setAspectRadio(1);
            floatTagPic.setPicHeight(26);
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(3);
        return floatTagVO;
    }

    /**
     * 团单有头图视频
     * @param productM
     * @return
     */
    private boolean hasDealVideo(ProductM productM) {
        return productM.getAttr(VIDEO_PATH) != null;
    }

    @VPointCfg
    @Data
    public static class Config {
        private double picAspectRadio = 1;
        private int picWidth = 300;
        private int picHeight = 300;

        private int iconPicHeight = 84;
        private int iconAspectRadio = 1;


        private String shopRecommendPic;
        /**
         * 图片映射
         * Key - 自定义Key
         * Vaule - 图片地址
         */
        private Map<String, String> customizePicMap;

        /**
         * 是否基于 SPU 进行转化
         */
        private boolean calcBySpu;

        /**
         * 是否展示视频图标
         */
        private boolean showVideoIcon;

        /**
         * 超值爆款角标
         */
        private String shopSuperVoguePic;
    }
}
