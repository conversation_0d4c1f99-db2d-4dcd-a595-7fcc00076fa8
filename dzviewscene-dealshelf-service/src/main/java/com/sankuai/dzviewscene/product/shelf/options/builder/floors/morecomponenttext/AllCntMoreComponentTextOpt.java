package com.sankuai.dzviewscene.product.shelf.options.builder.floors.morecomponenttext;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.MoreComponentTextVP;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/30
 */
@VPointOption(name = "商品区-查看更多-展示文案-全部团购数",
        description = " ",
        code = "AllCntMoreComponentTextOpt")
public class AllCntMoreComponentTextOpt extends MoreComponentTextVP<AllCntMoreComponentTextOpt.Config> {

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        int totalNum = config.isNeedCurrentFilterAllProductNum()
                ? ContextParamBuildUtils.getTotalProductNum(activityCxt, true)
                : param.getItemAreaItemCnt();
        if (totalNum <= param.getDefaultShowNum()) {
            return StringUtils.EMPTY;
        }
        String textFormat = getTextFormat(param, config);
        if (StringUtils.isEmpty(textFormat)) {
            return null;
        }
        return String.format(textFormat, totalNum);
    }

    private String getTextFormat(Param param, Config config) {
        if (MapUtils.isNotEmpty(config.getGroupName2TextFormat())) {
            String textFormat = config.getGroupName2TextFormat().get(param.getGroupName());
            if (StringUtils.isNotEmpty(textFormat)) {
                return textFormat;
            }
        }
        return config.getTextFormat();
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * key - groupName
         * value - 展示格式
         */
        private Map<String, String> groupName2TextFormat;

        /**
         * 展示文案
         */
        private String textFormat;

        /**
         * 是否使用当前Filter的全部商品数
         */
        private boolean needCurrentFilterAllProductNum = false;
    }
}
