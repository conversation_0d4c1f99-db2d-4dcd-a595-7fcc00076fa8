package com.sankuai.dzviewscene.product.dealstruct.activity.deal;

import com.sankuai.athena.viewscene.framework.annotation.Activity;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfActivity;

/**
 * <AUTHOR>
 * @date 2021/11/22 4:41 下午
 */
@Activity(name = "团购详情模块活动",
        code = DealDetailActivity.CODE,
        description = "团购标准团购详情模块活动")
public class DealDetailActivity extends PmfActivity<Object> {//lmd todo

    public static final String CODE = "activity_deal_detail";
}
