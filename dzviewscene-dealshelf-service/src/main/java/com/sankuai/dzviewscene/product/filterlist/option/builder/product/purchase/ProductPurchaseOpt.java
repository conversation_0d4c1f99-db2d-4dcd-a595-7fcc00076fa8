package com.sankuai.dzviewscene.product.filterlist.option.builder.product.purchase;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPurchaseVP;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by zhangfeibiao on 2023/2/23.
 */
@VPointOption(name = "融合预售和购买信息可选项",
        description = "预售团单展示预售信息，非预售团单展示最近购买信息",
        code = "ProductPurchaseOpt",
        isDefault = true)
public class ProductPurchaseOpt extends ProductPurchaseVP<Void> {

    @Override
    public RichLabelVO compute(ActivityCxt context, Param param, Void aVoid) {
        ProductM productM = param.getProductM();
        if (StringUtils.isEmpty(productM.getPurchase())) {
            return null;
        }
        return buildRichLabelVO(FrontSizeUtils.front10, ColorUtils.color999999, productM.getPurchase());
    }

    private RichLabelVO buildRichLabelVO(int textSize, String textColor, String text) {
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setTextColor(textColor);
        richLabelVO.setText(text);
        richLabelVO.setTextSize(textSize);
        return richLabelVO;
    }
}
