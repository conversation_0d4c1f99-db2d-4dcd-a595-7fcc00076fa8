package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemJumpUrlVP;
import lombok.Data;

@VPointOption(name = "家电货架跳转链接-根据配置属性条件返回小程序跳链/默认团购主题跳链",
        description = "家电货架跳转链接-根据配置属性条件返回小程序跳链/默认团购主题跳链",
        code = ApplianceShelfItemJumpUrlOpt.CODE)
public class ApplianceShelfItemJumpUrlOpt extends UnifiedShelfItemJumpUrlVP<ApplianceShelfItemJumpUrlOpt.Config> {

    public static final String CODE = "ApplianceShelfItemJumpUrlOpt";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Config config) {
        if (ApplianceShelfUtils.isMiniprogramLinked(param.getProductM())) {
            return param.getProductM().getAttr(config.getJumpUrlAttrName());
        }
        return getDefault(param.getProductM());
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 团购主题透传url属性名
         */
        private String jumpUrlAttrName;
    }
}
