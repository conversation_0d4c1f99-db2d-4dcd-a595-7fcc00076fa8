package com.sankuai.dzviewscene.product.shelf.options.builder.filterprdocuctareas;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filterprdocuctareas.FilterProductAreasPostVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.FilterBtnIdAndProAreasVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@VPointOption(name = "亲子货架筛选ProductAreas后置处理",
        description = "亲子货架筛选ProductAreas后置处理",
        code = "FilterJoyBabyProductAreasPostOpt")
public class FilterJoyBabyProductAreasPostOpt extends FilterProductAreasPostVP<Void> {

    @Override
    public List<FilterBtnIdAndProAreasVO> compute(ActivityCxt activityCxt, Param param, Void unused) {
        List<FilterBtnIdAndProAreasVO> filterIdAndProductAreas = activityCxt.getSource(FloorsBuilder.CODE);
        if(CollectionUtils.isEmpty(filterIdAndProductAreas)){
            return filterIdAndProductAreas;
        }
        long btnSelectId = 0;
        if(activityCxt.getParam("selectedOriginFilterId") != null){
            btnSelectId = (long) activityCxt.getParam("selectedOriginFilterId");
        }
        if(btnSelectId <= 0){
            return filterIdAndProductAreas;
        }
       for (FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO : filterIdAndProductAreas) {
           if(filterBtnIdAndProAreasVO.getFilterBtnId() == btnSelectId){
               return Lists.newArrayList(filterBtnIdAndProAreasVO);
           }
       }
       return null;
    }
}
