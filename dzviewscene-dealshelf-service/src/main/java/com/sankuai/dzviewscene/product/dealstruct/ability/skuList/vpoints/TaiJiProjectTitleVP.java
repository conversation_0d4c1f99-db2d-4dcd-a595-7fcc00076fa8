package com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.TaiJiProjectsModuleBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "团购详情太极项目标题变化点", description = "团购详情太极项目标题变化点", code = TaiJiProjectTitleVP.CODE, ability = TaiJiProjectsModuleBuilder.CODE)
public abstract class TaiJiProjectTitleVP<T> extends PmfVPoint<String, TaiJiProjectTitleVP.Param, T> {
    public static final String CODE = "TaiJiProjectTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<AttrM> dealAttrs;
        private StandardServiceProjectItemDTO taiJiProjectItem;
    }
}
