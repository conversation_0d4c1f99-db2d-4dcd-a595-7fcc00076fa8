package com.sankuai.dzviewscene.product.shelf.options.builder.floors.filteritemareaattrs;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.FilterItemAreaAttrsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.AttrVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import static com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.PlatformShelfCacheQueryFetcher.ADDITION_ATTR_KEY;

@VPointOption(name = "当前筛选下的商品平台返回的ATTR",
        description = "当前筛选下的商品平台返回的attr：navAdditionAttr",
        code = "NavAdditionAreaAttrsOpt")
public class NavAdditionAreaAttrsOpt extends FilterItemAreaAttrsVP<Void> {

    @Override
    public List<AttrVO> compute(ActivityCxt context, Param param, Void unused) {
        List<ProductM> productMs = param.getProducts();
        if (CollectionUtils.isEmpty(productMs)) {
            return null;
        }
        // 因为商品平台返回的是筛选项维度的，所以只获取第一个商品的ADDITION_ATTR_KEY即可
        String additionAttrStr = productMs.get(0).getAttr(ADDITION_ATTR_KEY);
        if (StringUtils.isEmpty(additionAttrStr)) {
            return null;
        }
        LinkedHashMap<String, String> additionAttrMap = JsonCodec.decode(additionAttrStr, LinkedHashMap.class);
        if (MapUtils.isEmpty(additionAttrMap)) {
            return null;
        }
        List<AttrVO> attrVOList = new ArrayList<>();
        additionAttrMap.forEach((key, value) -> {
            AttrVO attrVO = new AttrVO();
            attrVO.setName(key + "：");
            attrVO.setValue(value);
            attrVOList.add(attrVO);
        });
        return attrVOList;
    }

}
