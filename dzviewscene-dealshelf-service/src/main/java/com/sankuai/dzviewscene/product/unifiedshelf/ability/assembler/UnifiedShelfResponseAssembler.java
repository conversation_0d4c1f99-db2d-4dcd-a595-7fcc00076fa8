package com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.assembler.vp.UnifiedShelfShowTypeVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.maintitle.UnifiedShelfMainTitleBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.UnifiedShelfOceanBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.utils.UnifiedShelfCommonOcean;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.unifiedshelf.operator.api.req.OperatorShelfConfigFlashRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Ability(code = UnifiedShelfResponseAssembler.CODE,
        name = "VO-货架生成能力",
        description = "货架生成能力",
        activities = {UnifiedShelfActivity.CODE},
        dependency = {UnifiedShelfFilterBuilder.CODE, UnifiedShelfOceanBuilder.CODE, UnifiedProductAreaBuilder.CODE, UnifiedShelfMainTitleBuilder.CODE}
)
public class UnifiedShelfResponseAssembler extends PmfAbility<UnifiedShelfResponse, UnifiedShelfResponseAssembler.Request, UnifiedShelfResponseAssembler.Config> {

    public static final String CODE = "UnifiedShelfResponseAssembler";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<UnifiedShelfResponse> build(ActivityCxt ctx, Request request, Config config) {
        UnifiedShelfResponse shelfResponse = new UnifiedShelfResponse();
        shelfResponse.setMainTitle(ctx.getSource(UnifiedShelfMainTitleBuilder.CODE));
        shelfResponse.setFilter(findFirstFilterComponent(ctx.getSource(UnifiedShelfFilterBuilder.CODE)));
        shelfResponse.setFilterIdAndProductAreas(ctx.getSource(UnifiedProductAreaBuilder.CODE));
        shelfResponse.setShowType(getShowType(ctx, request, shelfResponse.getFilterIdAndProductAreas()));
        shelfResponse.setSceneCode(ctx.getSceneCode());
        shelfResponse.setOcean(UnifiedShelfCommonOcean.paddingCommonOcean(ctx.getSource(UnifiedShelfOceanBuilder.CODE), shelfResponse, ActivityCtxtUtils.toActivityContext(ctx)));
        // 异步刷新运营配置
        asyncFlashOperatorShelfConfig(ctx);
        return CompletableFuture.completedFuture(shelfResponse);
    }

    private void asyncFlashOperatorShelfConfig(ActivityCxt ctx) {
        try {
            boolean needFlashOperatorConfig = BooleanUtils.isTrue(ctx.getParam(ShelfActivityConstants.Ctx.needFlashOperatorConfigFlag));
            if (!needFlashOperatorConfig) {
                return;
            }
            ShopM shopM = ctx.getParam(ShelfActivityConstants.Ctx.ctxShop);
            if (shopM == null || CollectionUtils.isEmpty(shopM.getBackCategory())) {
                return;
            }
            OperatorShelfConfigFlashRequest flashRequest = new OperatorShelfConfigFlashRequest();
            flashRequest.setShopBackCategoryIds(shopM.getBackCategory());
            compositeAtomService.asyncFlashOperatorShelfConfig(flashRequest);
        } catch (Exception e) {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "UnifiedShelfResponseAssembler")
                    .putTag("method", "asyncFlashOperatorShelfConfig")
                    .message(String.format("asyncFlashOperatorShelfConfig error, params: %s", JsonCodec.encode(ctx.getParameters()))));
            Cat.logError("asyncFlashOperatorShelfConfigError", e);
        }
    }

    private ShelfFilterVO findFirstFilterComponent(Map<String, ShelfFilterVO> groupFilterComponents) {
        if (MapUtils.isEmpty(groupFilterComponents)) {
            return null;
        }
        return groupFilterComponents.values().stream().findFirst().orElse(null);
    }

    private int getShowType(ActivityCxt activityCxt, Request request, List<ShelfFilterProductAreaVO> filterIdAndProductAreas) {
        try {
            UnifiedShelfShowTypeVP<?> shelfShowTypeVP = findVPoint(activityCxt, UnifiedShelfShowTypeVP.CODE);
            Integer showType = shelfShowTypeVP.execute(activityCxt,
                    UnifiedShelfShowTypeVP.Param.builder()
                            .productTotalNum(getFirstValidProAreaProductTotals(filterIdAndProductAreas))
                            .douHuList(activityCxt.getSource(ShelfDouHuFetcher.CODE))
                            .shelfVersion(request.getShelfVersion())
                            .build());
            return showType != null && showType > 0 ? showType : UnifiedShelfShowTypeVP.DEFAULT_SHOW_TYPE;
        } catch (Exception ex) {
            Cat.logErrorWithCategory(String.format("UnifiedShelfResponseAssembler#calcShowType.%s", request.toString()), ex);
            return UnifiedShelfShowTypeVP.DEFAULT_SHOW_TYPE;
        }
    }

    private int getFirstValidProAreaProductTotals(List<ShelfFilterProductAreaVO> filterIdAndProductAreas) {
        if (CollectionUtils.isEmpty(filterIdAndProductAreas)) {
            return 0;
        }
        for (ShelfFilterProductAreaVO filterBtnIdAndProAreasVO : filterIdAndProductAreas) {
            if (filterBtnIdAndProAreasVO == null || CollectionUtils.isEmpty(filterBtnIdAndProAreasVO.getProductAreas())) {
                continue;
            }
            for (ShelfProductAreaVO productAreaComponentVO : filterBtnIdAndProAreasVO.getProductAreas()) {
                if (productAreaComponentVO == null || CollectionUtils.isEmpty(productAreaComponentVO.getItems())) {
                    continue;
                }
                return productAreaComponentVO.getItems().size();
            }
        }
        return 0;
    }

    @AbilityCfg
    @Data
    public static class Config {
    }

    @AbilityRequest
    @Data
    public static class Request {

        private ShopM ctxShop;

        /**
         * {@link ShelfActivityConstants.Params#subScene}
         */
        private String subScene;

        /**
         * {@link ShelfActivityConstants.Params#shelfVersion}
         */
        private int shelfVersion;
    }
}

