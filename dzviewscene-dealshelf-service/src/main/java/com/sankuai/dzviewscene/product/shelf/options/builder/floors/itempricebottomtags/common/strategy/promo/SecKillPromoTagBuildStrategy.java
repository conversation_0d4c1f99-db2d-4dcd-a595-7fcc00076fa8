package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 优惠减负秒杀收敛，该策略下线，秒杀使用TimeSecKillPromoTagBuildStrategy
 */
@Component
public class SecKillPromoTagBuildStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

    private static final String ICON_TAG = "限时秒杀";

    @Override
    public String getName() {
        return "彩虹活动秒杀";
    }

    @Override
    public DzTagVO buildTag(PriceBottomTagBuildReq req) {
        //命中优惠减负二期实验组，直接返回空，全量后策略下线
        if(PromoSimplifyUtils.hitPromoSimplifyV2(req.getContext())){
            return null;
        }
        if (!RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(req.getProductM())) {
            return null;
        }
        DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(req.getProductM().getPromoPrices(), req.getProductM().getMarketPrice(), req.getSalePrice(), req.getPlatform(), req.getPopType());
        if (Objects.isNull(secKillDzTag)) {
            return null;
        }
        secKillDzTag.setPrePic(new DzPictureComponentVO(
                PlatformUtil.isMT(req.getPlatform()) ? ProductMPromoInfoUtils.MT_SEC_KILL_TAG_URL : ProductMPromoInfoUtils.DP_SEC_KILL_TAG_URL, getPicRadio(req.getPlatform(), req.getCfg())));
        secKillDzTag.setAfterPic(buildPromoAfterPic(req.getPlatform()));
        secKillDzTag.setBorderRadius(PlatformUtil.isMT(req.getPlatform()) ? 3 : 1);
        secKillDzTag.setText(secKillDzTag.getName());
        promoSimplify(secKillDzTag, req, ICON_TAG, false);
        return secKillDzTag;
    }
}
