package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.options.labs;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp.UnifiedShelfFilterBtnLabsVP;

@VPointOption(name = "通用-通用筛选埋点",
        description = "返回通用的筛选埋点",
        code = "UnifiedShelfCommonFilterBtnLabsOpt",
        isDefault = true
)
public class UnifiedShelfCommonFilterBtnLabsOpt extends UnifiedShelfFilterBtnLabsVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        return JsonCodec.encode(getCommonOcean(param));
    }
}
