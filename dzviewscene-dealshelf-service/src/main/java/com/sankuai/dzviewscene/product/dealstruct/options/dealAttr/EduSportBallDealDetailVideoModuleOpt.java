package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints.DealDetailVideoModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/11/1 4:01 下午
 */
@VPointOption(name = "教培-运动培训-球类运动团购详情模块视频组件配置化变化点", description = "教培-运动培训-球类运动团购详情模块视频组件配置化变化点",code = EduSportBallDealDetailVideoModuleOpt.CODE, isDefault = false)
public class EduSportBallDealDetailVideoModuleOpt extends DealDetailVideoModuleVP<EduSportBallDealDetailVideoModuleOpt.Config> {

    public static final String CODE = "EduSportBallDealDetailVideoModuleOpt";

    @Override
    public VideoModuleVO compute(ActivityCxt context, Param param, Config config) {
        return getVideoModuleVO(param, config);
    }

    private VideoModuleVO getVideoModuleVO(Param param, Config config) {
        String classObjective = getClassObjective(param, config);
        if (classObjective == null) {
            return null;
        }
        VideoModuleVO videoModuleVO = new VideoModuleVO();
        videoModuleVO.setContent(classObjective);
        videoModuleVO.setTitle("课程目标");
        VideoUrlModel videoUrlModel = getVideoUrlModel(param, config);
        if (videoUrlModel == null) {
            return videoModuleVO;
        }
        videoModuleVO.setUrl(videoUrlModel.getVidelUrl());
        videoModuleVO.setThumbnailURL(videoUrlModel.getThumbnailURL());
        videoModuleVO.setDesc(videoModuleVO.getDesc());
        return videoModuleVO;
    }

    private VideoUrlModel getVideoUrlModel(Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getVideoUrlModels())) {
            return null;
        }
        String classObjectiveType = getClassObjectiveType(param);
        //课程目标类型是"自定义课程目标"时，不展示视频和备注文案
        if (StringUtils.isEmpty(classObjectiveType) || "自定义课程目标".equals(classObjectiveType)) {
            return null;
        }
        String serviceTypeLeafId = getServiceTypeLeafId(param);
        return config.getVideoUrlModels().stream().filter(model -> Objects.equals(serviceTypeLeafId, model.getServiceTypeLeafId())).findFirst().orElse(null);
    }

    /**
     * 获取课程目标
     *@param
     *@return
     */
    private String getClassObjectiveType(Param param) {
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(param.getDealDetailDtoModel());
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, "learningObjective");
    }

    /**
     * 获取课程目标
     *@return
     * @param
     * @param config
     */
    private String getClassObjective(Param param, Config config) {
        List<SkuAttrItemDto> skuAttrItemDtos = DealDetailUtils.getFirstMustGroupFirstSkuAttrList(param.getDealDetailDtoModel());
        if (CollectionUtils.isNotEmpty(config.getClassObjectiveKeys())) {
            return getClassObjectiveFromConfig(config, skuAttrItemDtos);
        }
        String classObjectiveType = getClassObjectiveType(param);
        if ("自定义课程目标".equals(classObjectiveType)) {
            return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, "classNature");
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, "classHighlights");
    }

    private String getClassObjectiveFromConfig(Config config, List<SkuAttrItemDto> skuAttrItemDtos) {
        for (String classObjectiveKey : config.getClassObjectiveKeys()) {
            String classObjective = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, classObjectiveKey);
            if (StringUtils.isNotBlank(classObjective)) {
                return classObjective;
            }
        }
        return null;
    }

    /**
     * 获取团单四级分类
     *@param
     *@return
     */
    private String getServiceTypeLeafId(Param param) {
        if (param == null || CollectionUtils.isEmpty(param.getDealAttrs())) {
            return null;
        }
        return DealDetailUtils.getAttrSingleValueByAttrName(param.getDealAttrs(), "service_type_leaf_id");
    }

    @Data
    @VPointCfg
    public static class Config {
        //视频配置信息
        private List<VideoUrlModel> videoUrlModels;
        // 课程目标字段
        private List<String> classObjectiveKeys = Lists.newArrayList("classNature", "classHighlights");
    }

    @Data
    public static class VideoUrlModel {
        //四级分类
        private String serviceTypeLeafId;
        //备注文案
        private String desc;
        //视频链接
        private String videlUrl;
        //视频首图
        private String thumbnailURL;
    }
}
