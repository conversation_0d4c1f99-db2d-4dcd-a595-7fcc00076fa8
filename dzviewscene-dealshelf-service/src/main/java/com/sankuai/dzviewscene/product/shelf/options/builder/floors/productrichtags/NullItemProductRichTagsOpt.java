package com.sankuai.dzviewscene.product.shelf.options.builder.floors.productrichtags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductRichTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/30
 */
@VPointOption(name = "默认-富文本副标题-空实现",
        description = "返回 null ，即不展示",
        code = "NullItemProductRichTagsOpt",
        isDefault = true)
public class NullItemProductRichTagsOpt extends ItemProductRichTagsVP<Void> {
    @Override
    public List<RichLabelVO> compute(ActivityCxt activityCxt, Param param, Void unused) {
        return null;
    }
}
