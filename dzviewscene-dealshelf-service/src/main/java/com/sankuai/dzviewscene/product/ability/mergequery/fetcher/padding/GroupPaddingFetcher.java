package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding;

import com.dianping.cat.Cat;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.exception.BusinessException;
import com.sankuai.dzviewscene.product.ability.mergequery.MergeQueryCfg;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.NoPaddingHandler;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.PaddingHandler;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.SpuProductThemeHandler;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.postpadding.FixedPriceSpuPostPaddingHandler;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.postpadding.PostPaddingHandler;
import com.sankuai.dzviewscene.product.utils.CfUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
@SuppressWarnings("all")
public class GroupPaddingFetcher extends PaddingConstants implements BeanFactoryAware {

    private static Map<String, Class<? extends PaddingHandler>> paddingType2PaddingHandlers = new HashMap<>();

    static {
        paddingType2PaddingHandlers.put(PaddingType.spuThemePadding.name(), SpuProductThemeHandler.class);
        paddingType2PaddingHandlers.put(PaddingType.noPaddingHandler.name(), NoPaddingHandler.class);
    }

    private static Map<String, Class<? extends PostPaddingHandler>> postPaddingType2PaddingHandlers = new HashMap<>();

    static {
        postPaddingType2PaddingHandlers.put(PostPaddingType.fixedPriceSpuPostPadding.name(), FixedPriceSpuPostPaddingHandler.class);
    }

    @Resource
    private BeanFactory beanFactory;

    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityCxt ctx, Map<String, ProductGroupM> groupNames2ProductGroupsMap, MergeQueryCfg config) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.GroupPaddingFetcher.build(ActivityCxt,Map,MergeQueryCfg)");

        // 1. 获取商品组名
        if (MapUtils.isEmpty(config.getGroupParams())) {
            throw new BusinessException("groupParams参数不能缺失");
        }

        // 2. 获取上下文多组商品
        if (groupNames2ProductGroupsMap == null) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }

        // 3. 批量填充
        Map<String, CompletableFuture<ProductGroupM>> productGroupCompletableFuture = batchPadding(ctx, groupNames2ProductGroupsMap, config.getGroupParams());

        // 4. 结构转换
        return CfUtils.each(productGroupCompletableFuture);
    }


    private Map<String, CompletableFuture<ProductGroupM>> batchPadding(ActivityCxt activityContext, Map<String, ProductGroupM> productGroups, Map<String, Map<String, Object>> multiGroupParams) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.GroupPaddingFetcher.batchPadding(com.sankuai.athena.viewscene.framework.ActivityCxt,java.util.Map,java.util.Map)");
        if (MapUtils.isEmpty(productGroups)) {
            return Maps.newHashMap();
        }
        return productGroups.entrySet()
                .stream()
                .collect(
                        HashMap::new,
                        (map, productGroupEntry) -> {
                            map.put(productGroupEntry.getKey(), paddingSingleProductGroup(activityContext, productGroupEntry.getValue(), multiGroupParams.get(productGroupEntry.getKey())));
                        },
                        HashMap::putAll
                );
    }

    // 填充单组商品
    private CompletableFuture<ProductGroupM> paddingSingleProductGroup(ActivityCxt ctx, ProductGroupM productGroup, Map<String, Object> groupParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.GroupPaddingFetcher.paddingSingleProductGroup(ActivityCxt,ProductGroupM,Map)");
        if (MapUtils.isEmpty(groupParams)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }

        String paddingType = ParamsUtil.getStringSafely(groupParams, PaddingConstants.Params.paddingType);
        Class paddingHandlerClazz = paddingType2PaddingHandlers.get(paddingType);
        if (paddingHandlerClazz == null) {
            throw new BusinessException(String.format("查找填充器=%s, 找不到", paddingType));
        }

        PaddingHandler groupPaddingHandler = (PaddingHandler) beanFactory.getBean(paddingHandlerClazz);
        if (groupPaddingHandler == null) {
            throw new BusinessException(String.format("查找填充器=%s, 找不到", paddingHandlerClazz.getName()));
        }

        return groupPaddingHandler.padding(ctx, productGroup, groupParams)
                .thenCompose(productGroups -> {
                    return postPadding(ctx, productGroups, groupParams);
                });
    }

    private CompletableFuture<ProductGroupM> postPadding(ActivityCxt ctx,
                                                         ProductGroupM productGroups,
                                                         Map<String, Object> groupParams) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.GroupPaddingFetcher.postPadding(ActivityCxt,ProductGroupM,Map)");

        String paddingType = ParamsUtil.getStringSafely(groupParams, Params.postPaddingType);
        Class paddingHandlerClazz = postPaddingType2PaddingHandlers.get(paddingType);
        if (paddingHandlerClazz == null) {
            return CompletableFuture.completedFuture(productGroups);
        }
        PostPaddingHandler postPaddingHandler = (PostPaddingHandler) beanFactory.getBean(paddingHandlerClazz);
        if (postPaddingHandler == null) {
            throw new BusinessException(String.format("查找填充器=%s, 找不到", paddingHandlerClazz.getName()));
        }
        return postPaddingHandler.postPadding(ctx, productGroups, groupParams);
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}
