package com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.vcpoints.DealDetailStandardServiceModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.DealDetailVideoModuleParam;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/05/23 11:43 上午
 */
@Ability(code = StandardServiceModuleBuilder.CODE,
        name = "团购标准流程模块构造能力",
        description = "团购标准流程模块构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class StandardServiceModuleBuilder extends PmfAbility<List<StandardServiceVO>, DealDetailVideoModuleParam, DealDetailVideoModuleCfg> implements ModuleStrategy {

    public static final String CODE = "standardServiceModuleBuilder";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.StandardServiceModuleBuilder.buildModelVO(ActivityCxt,DealDetailAssembleParam,String)");
        String abilityCode = StandardServiceModuleBuilder.CODE;
        List<StandardServiceVO> standardService= activityCxt.getSource(abilityCode);
        if (CollectionUtils.isEmpty(standardService)) {
            return null;
        }
        StandardServiceVO standardServiceVO = CollectUtils.firstValue(standardService);
        return buildDealDetailModuleVO(standardServiceVO);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(StandardServiceVO standardServiceVO) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.dealstruct.ability.servicefeatures.StandardServiceModuleBuilder.buildDealDetailModuleVO(com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceVO)");
        if (standardServiceVO == null) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setStandardServiceModel(standardServiceVO);
        return dealDetailModuleVO;
    }

    @Override
    public CompletableFuture<List<StandardServiceVO>> build(ActivityCxt activityCxt, DealDetailVideoModuleParam dealDetailVideoModuleParam, DealDetailVideoModuleCfg dealDetailVideoModuleCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(null);
        }
        List<StandardServiceVO> detailStructAttrModels = dealDetailInfoModels.stream().map(detailModel -> {
            if(detailModel == null) {
                return null;
            }
            List<AttrM> dealAttrs = detailModel.getDealAttrs();
            DealDetailStandardServiceModuleVP<?> dealDetailVideoModuleVP = findVPoint(activityCxt, DealDetailStandardServiceModuleVP.CODE);
            return dealDetailVideoModuleVP.execute(activityCxt,
                    DealDetailStandardServiceModuleVP.Param.builder().dealAttrs(dealAttrs).build());
        }).collect(Collectors.toList());
        return CompletableFuture.completedFuture(detailStructAttrModels);
    }
}
