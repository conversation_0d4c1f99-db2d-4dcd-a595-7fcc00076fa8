package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.product.shelf.utils.DzTagStyleWrapUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/1/8 8:41 下午
 */
@VPointOption(name = "非会员-通用优惠感知版本",
        description = "玩乐运健可以复用的优惠感智Opt",
        code = "NormalPromoPerceptionPriceBottomTagOpt")
public class NormalPromoPerceptionPriceBottomTagOpt extends ProductPriceBottomTagVP<NormalPromoPerceptionPriceBottomTagOpt.Config> {

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        //构造结果
        DzTagVO onlyDzTag = buildPromoPerceptionTag(param, param.getPlatform(), config);
        if (onlyDzTag == null) {
            return null;
        }
        //后置处理
        DzTagVO dzTagVO = postDealDzTag(onlyDzTag, config);
        return Lists.newArrayList(dzTagVO);
    }

    private DzTagVO postDealDzTag(DzTagVO onlyDzTag, Config config) {
        //样式后置处理
        DzTagStyleWrapUtils.overridePromoStyle(onlyDzTag);
        //是否隐藏弹窗
        if (config.isHidePop()) {
            onlyDzTag.setPromoDetail(null);
        }
        return onlyDzTag;
    }

    private DzTagVO buildPromoPerceptionTag(Param param, int platform, Config config) {
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(param.getProductM().getPromoPrices());
        if (MapUtils.isEmpty(promoPriceMMap)) {
            return null;
        }
        // 预售优先级最高
        DzTagVO preSaleDzTag = buildPreSaleDzTag(platform, promoPriceMMap, config);
        // 秒杀其次（非商品侧处理）
        DzTagVO secKillDzTag = buildSecKillDzTag(param);
        // 其他非会员，兜底优惠
        DzTagVO noMemberTag = buildNoMemberTag(param.getPlatform(), getNoMemberPromoM(param), config);
        //责任链模式
        return Stream.of(preSaleDzTag, secKillDzTag, noMemberTag).filter(Objects::nonNull).findFirst().orElse(null);
    }

    private ProductPromoPriceM getNoMemberPromoM(Param param) {
        List<ProductPromoPriceM> noMemberPriceM = param.getProductM().getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                // 过滤掉 使用了会员优惠的Promo
                .filter(a -> !CardPromoUtils.CARD_PROMOS.contains(a.getPromoType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }

    private DzTagVO buildSecKillDzTag(Param param) {
        if (!RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
            return null;
        }
        DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(param.getProductM().getPromoPrices(), param.getProductM().getMarketPrice(), param.getSalePrice(), param.getPlatform(), 3);
        if (secKillDzTag == null) {
            return null;
        }
        secKillDzTag.setPrePic(new DzPictureComponentVO(
                PlatformUtil.isMT(param.getPlatform()) ? ProductMPromoInfoUtils.MT_SEC_KILL_TAG_URL : ProductMPromoInfoUtils.DP_SEC_KILL_TAG_URL, 3.25));
        secKillDzTag.setAfterPic(buildAfterPic());
        secKillDzTag.setBorderRadius(PlatformUtil.isMT(param.getPlatform()) ? 3 : 1);
        return secKillDzTag;
    }

    private DzTagVO buildPreSaleDzTag(int platform, Map<Integer, ProductPromoPriceM> promoPriceMMap, Config config) {
        // 预售优先级最高
        List<Integer> preSaleOrders = Lists.newArrayList(PromoTagTypeEnum.PreSale_Member.getCode(), PromoTagTypeEnum.PreSale_NewUser.getCode(), PromoTagTypeEnum.PreSale.getCode());
        return preSaleOrders.stream()
                .filter(promoPriceMMap::containsKey)
                .map(promoPriceMMap::get)
                .filter(Objects::nonNull)
                .map(promoM -> buildNoMemberTag(platform, promoM, config))
                .findFirst().orElse(null);
    }

    private DzTagVO buildNoMemberTag(int platform, ProductPromoPriceM productPromoPriceM, Config config) {
        if (Objects.isNull(productPromoPriceM)) {
            return null;
        }
        DzTagVO dzTagVO = buildPromoTagVo(platform, productPromoPriceM);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        dzTagVO.setPrePic(new DzPictureComponentVO(productPromoPriceM.getIcon(), 3.25));
        dzTagVO.setAfterPic(buildAfterPic());
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(productPromoPriceM, config.getPopType()));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(platform) ? 3 : 1);
        return dzTagVO;
    }

    /**
     * 构建优惠感知版本的priceBottomTag，有特别的样式逻辑
     * 玩乐运健没有会员价
     * @return
     */
    private DzTagVO buildPromoTagVo(int platform, ProductPromoPriceM productPromoPriceM) {
        DzTagVO basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColor(platform, productPromoPriceM.getPromoTag(),
                    ColorUtils.colorFF4B10, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, ColorUtils.colorFFF2EE);
        // 点评侧的「新客特惠」、「特惠促销」样式不一样
        if (Objects.nonNull(basicTagVo) && !PlatformUtil.isMT(platform) && Objects.nonNull(productPromoPriceM.getPromoTagType()) &&
                (productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.NewUser.getCode()) || productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Other.getCode()))) {
            basicTagVo.setHasBorder(true);
            basicTagVo.setBorderColor(ColorUtils.colorFFCFBF);
            basicTagVo.setBackground(null);
        }
        return basicTagVo;
    }

    private DzPictureComponentVO buildAfterPic() {
        return new DzPictureComponentVO("https://p0.meituan.net/travelcube/b5ef0237b2cb19d166956ce499b6f0bb495.png", 0.875);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 是否隐藏弹窗
         */
        private boolean hidePop;
        /**
         * 弹窗样式
         */
        private int popType = 3;
        /**
         * 美团侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtPriceBottomTagPrePicAspectRadio = 3.25;
        /**
         * 点评侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpPriceBottomTagPrePicAspectRadio = 3.25;
    }
}
