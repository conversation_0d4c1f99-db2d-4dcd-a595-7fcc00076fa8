package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.NoStructDealSkuGroupBuildVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/27
 */
@VPointOption(name = "默认从 detailInfo 取数构造",
        description = "",
        code = "DefaultNoStructDealSkuGroupBuildOpt")
public class DefaultNoStructDealSkuGroupBuildOpt extends NoStructDealSkuGroupBuildVP<Void> {

    private static final String CONTENT_TYPE = "table";

    @Override
    public List<DealSkuGroupModuleVO> compute(ActivityCxt context, Param param, Void unused) {
        if (param.getDealDetailInfo() == null || CollectionUtils.isEmpty(param.getDealDetailInfo().getDealAttrs())) {
            return new ArrayList<>();
        }
        UniformStructContentDTO dealDetail = getTableContent(param.getDealDetailInfo().getDealAttrs());
        if (dealDetail == null || CollectionUtils.isEmpty(dealDetail.getGroups())) {
            return new ArrayList<>();
        }
        return dealDetail.getGroups().stream().map(this::uniformStructGroupDTO2DealSkuGroupModuleVO).collect(Collectors.toList());
    }

    /**
     * 从团单属性中取老的团购详情数据
     *
     * @param dealAttrs
     * @return
     */
    private UniformStructContentDTO getTableContent(List<AttrM> dealAttrs) {
        Optional<AttrM> attrMOpt = dealAttrs.stream().filter(o -> Objects.equals(o.getName(), DealDetailFetcher.DETAIL_INFO)).findFirst();
        if (!attrMOpt.isPresent()) {
            return null;
        }
        UniformStructModel uniformStructModel = JsonCodec.decode(attrMOpt.get().getValue(), UniformStructModel.class);
        if (uniformStructModel == null) {
            return null;
        }
        Optional<UniformStructContentModel> dataOpt = uniformStructModel.getContent().stream().filter(o -> Objects.equals(o.getType(), CONTENT_TYPE)).findFirst();
        if (!dataOpt.isPresent()) {
            return null;
        }
        return parseContentToTableData(dataOpt.get().getData());
    }

    private UniformStructContentDTO parseContentToTableData(Object data) {
        if (data == null) {
            return null;
        }
        String jsonStr = JsonCodec.encode(data);
        return JsonCodec.decode(jsonStr, UniformStructContentDTO.class);
    }

    private DealSkuGroupModuleVO uniformStructGroupDTO2DealSkuGroupModuleVO(UniformStructGroupDTO group) {
        DealSkuGroupModuleVO moduleVO = new DealSkuGroupModuleVO();
        moduleVO.setTitle(group.getFullName());
        List<DealSkuVO> dealSkuList = new ArrayList<>();
        for (List<String> values : group.getValues()) {
            if (CollectionUtils.isEmpty(values) || values.size() < 3) {
                continue;
            }
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setTitle(values.get(0));
            dealSkuVO.setCopies(values.get(1));
            dealSkuVO.setPrice(values.get(2));
            dealSkuList.add(dealSkuVO);
        }
        moduleVO.setDealSkuList(dealSkuList);
        return moduleVO;
    }

    @Data
    public static class UniformStructContentDTO {
        private List<UniformStructGroupDTO> groups;
    }

    @Data
    public static class UniformStructGroupDTO {

        private int optionalCount;

        private String fullName;

        private List<List<String>> values;
    }
}
