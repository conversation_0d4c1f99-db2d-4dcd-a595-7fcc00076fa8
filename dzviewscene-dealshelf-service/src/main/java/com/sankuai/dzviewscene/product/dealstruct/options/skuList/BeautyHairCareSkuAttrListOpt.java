package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@VPointOption(name = "丽人-养发sku属性列表变化点", description = "丽人-养发sku属性列表变化点，适用于团单二级类目=516的团单", code = BeautyHairCareSkuAttrListOpt.CODE)
public class BeautyHairCareSkuAttrListOpt extends SkuAttrListVP<BeautyHairCareSkuAttrListOpt.Config> {
    public static final String CODE = "BeautyHairCareSkuAttrListOpt";

    private static final String OTHER_CATEGORY = "其他";

    private static final String EFFECT_ELEMENT = "特色成分";

    private static final String SERVICE_EFFECT = "服务作用";

    private static final String PRODUCT = "使用产品";

    private static final String EQUIPMENT = "使用仪器";

    private static final String GRANT_PRODUCT = "项目附赠";

    private static final String SERVICE_DURATION = "服务时长";

    private static final String SERVICE_STEP =  "服务流程";

    private static final String SUIT_CROWNS = "适宜人群";

    private static final String BODY_PART = "服务部位";

    private static final String SERVICE_STEP_TEMPLATE = "%s步 （共%s）";

    private static final String SERVICE_STEP_TEMPLATE_WITHOUT_DURATION = "%s步";

    private static final String DESCRIPTION = "说明";

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItems = skuItemDto.getAttrItems();
        List<DealSkuItemVO> dealSkuItems = new ArrayList<>();
        // 判断是否归属"其他"分类
        boolean notBelongToOtherCategory = false == OTHER_CATEGORY.equalsIgnoreCase(DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "category2"));
        //服务部位
        String bodyPart = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "bodyPart");
        if (StringUtils.isNotEmpty(bodyPart)) {
            dealSkuItems.add(buildDealSkuItemVO(BODY_PART, bodyPart, null, 0));
        }
        //服务功效 服务作用
        String serviceEffect = getServiceEffect(skuAttrItems, notBelongToOtherCategory);
        if (StringUtils.isNotEmpty(serviceEffect)) {
            dealSkuItems.add(buildDealSkuItemVO(SERVICE_EFFECT, serviceEffect, null, 0));
        }
        //使用产品
        String product = getProduct(skuAttrItems, notBelongToOtherCategory);
        if (StringUtils.isNotEmpty(product)) {
            dealSkuItems.add(buildDealSkuItemVO(PRODUCT, product, null, 0));
        }
        //特色成分
        String effectElement = getEffectElement(skuAttrItems, notBelongToOtherCategory);
        if (StringUtils.isNotEmpty(effectElement)) {
            dealSkuItems.add(buildDealSkuItemVO(EFFECT_ELEMENT, effectElement, null, 0));
        }
        //适宜人群
        String suitCrowds = getSuitCrowds(skuAttrItems, notBelongToOtherCategory);
        if (StringUtils.isNotEmpty(suitCrowds)) {
            dealSkuItems.add(buildDealSkuItemVO(SUIT_CROWNS, suitCrowds, null, 0));
        }
        //使用仪器
        String equipment = getEquipment(skuAttrItems, notBelongToOtherCategory);
        if (StringUtils.isNotEmpty(equipment)) {
            dealSkuItems.add(buildDealSkuItemVO(EQUIPMENT, equipment, null, 0));
        }
        //项目附赠
        String grantProduct = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "grantProduct");
        if (StringUtils.isNotEmpty(grantProduct)) {
            dealSkuItems.add(buildDealSkuItemVO(GRANT_PRODUCT, grantProduct, null, 0));
        }
        //服务流程
        List<DealSkuItemVO> serviceProcessSkuItems = buildServiceProcessSkuItems(skuAttrItems, notBelongToOtherCategory);
        if(CollectionUtils.isNotEmpty(serviceProcessSkuItems)){
            dealSkuItems.addAll(serviceProcessSkuItems);
        }
        //服务流程为空，服务时长需要直接露出
        else {
            String duration = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "duration");
            if (StringUtils.isNotEmpty(duration)) {
                dealSkuItems.add(buildDealSkuItemVO(SERVICE_DURATION, duration, null, 0));
            }
        }
        return dealSkuItems;
    }

    private List<DealSkuItemVO> buildServiceProcessSkuItems(List<SkuAttrItemDto> skuAttrItems, boolean notBelongToOtherCategory) {
        String serviceStep =
                notBelongToOtherCategory
                        ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "servicestep")
                        : StringUtils.EMPTY;
        if (StringUtils.isEmpty(serviceStep)) {
            return null;
        }
        List<ServiceStepModel> serviceStepModels = JsonCodec.converseList(serviceStep, ServiceStepModel.class);
        if (CollectionUtils.isEmpty(serviceStepModels)) {
            return null;
        }
        List<SkuAttrAttrItemVO> skuAttrAttrItemVOList = serviceStepModels
                .stream()
                .map(this::buildSkuAttrAttrItemVO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(skuAttrAttrItemVOList)){
            return null;
        }
        List<DealSkuItemVO> dealSkuItems = new ArrayList<>();
        dealSkuItems.add(buildDealSkuItemVO(SERVICE_STEP, buildServiceStepValue(skuAttrItems, skuAttrAttrItemVOList.size()), null, 0));
        dealSkuItems.add(buildDealSkuItemVO(StringUtils.EMPTY, null, skuAttrAttrItemVOList, 2));
        return dealSkuItems;
    }

    private String buildServiceStepValue(List<SkuAttrItemDto> skuAttrItems, int serviceStepNum){
        String duration = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "duration");
        if(StringUtils.isEmpty(duration)){
            return String.format(SERVICE_STEP_TEMPLATE_WITHOUT_DURATION, serviceStepNum);
        }
        return String.format(SERVICE_STEP_TEMPLATE, serviceStepNum, duration);
    }

    private SkuAttrAttrItemVO buildSkuAttrAttrItemVO(ServiceStepModel serviceStepModel){
        if(serviceStepModel == null || StringUtils.isEmpty(serviceStepModel.getStepDesc()) || StringUtils.isEmpty(serviceStepModel.getStepName())){
            return null;
        }
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        skuAttrAttrItemVO.setName(serviceStepModel.getStepName());
        List<CommonAttrVO> commonAttrs = new ArrayList<>();
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName(DESCRIPTION);
        commonAttrVO.setValue(serviceStepModel.getStepDesc());
        commonAttrs.add(commonAttrVO);
        skuAttrAttrItemVO.setValues(commonAttrs);
        return skuAttrAttrItemVO;
    }

    private String getServiceEffect(List<SkuAttrItemDto> skuAttrItems, boolean notBelongToOtherCategory) {
        //主要功效
        String serviceEffect =
                notBelongToOtherCategory
                        ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "serviceeffect")
                        : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "otherServiceeffect");
        //次要功效
        String minorServicEeffect =
                notBelongToOtherCategory
                        ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "minorServicEeffect")
                        : StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(serviceEffect) && StringUtils.isNotEmpty(minorServicEeffect)) {
            return serviceEffect + "、" + minorServicEeffect;
        }
        return StringUtils.isNotEmpty(serviceEffect) ? serviceEffect : minorServicEeffect;
    }

    @Nullable
    private String getProduct(List<SkuAttrItemDto> skuAttrItems, boolean notBelongToOtherCategory) {
        return notBelongToOtherCategory
                ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "product")
                : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "otherProduct");
    }

    private String getEffectElement(List<SkuAttrItemDto> skuAttrItems, boolean notBelongToOtherCategory) {
        //主要有效成分
        String effectElement =
                notBelongToOtherCategory
                        ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "effectElement")
                        : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "otherEffectElement");
        //次要有效成分
        String minorEffectElement =
                notBelongToOtherCategory
                        ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "minorEffectElement")
                        : StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(effectElement) && StringUtils.isNotEmpty(minorEffectElement)) {
            return effectElement + "、" + minorEffectElement;
        }
        return StringUtils.isNotEmpty(effectElement) ? effectElement : minorEffectElement;
    }

    @Nullable
    private String getSuitCrowds(List<SkuAttrItemDto> skuAttrItems, boolean notBelongToOtherCategory) {
        return notBelongToOtherCategory
                ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "suitCrowds")
                : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "otherSuitCrowds");
    }

    @Nullable
    private String getEquipment(List<SkuAttrItemDto> skuAttrItems, boolean notBelongToOtherCategory) {
        return notBelongToOtherCategory
                ? DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "equipment")
                : DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "otherEquipment");
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value, List<SkuAttrAttrItemVO> valueAttrs, int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }


    @Data
    private static class ServiceStepModel {

        //步骤名称
        private String stepName;

        //步骤说明
        private String stepDesc;

    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
