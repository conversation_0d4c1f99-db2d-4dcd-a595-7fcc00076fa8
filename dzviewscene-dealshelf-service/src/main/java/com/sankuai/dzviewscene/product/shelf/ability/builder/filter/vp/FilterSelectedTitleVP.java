package com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/10
 */
@VPoint(name = "筛选-选中状态标签名", description = "筛选-selectedTitle", code = FilterSelectedTitleVP.CODE, ability = FilterBuilder.CODE)
public abstract class FilterSelectedTitleVP<T> extends PmfVPoint<RichLabelVO, FilterSelectedTitleVP.Param, T> {
    public static final String CODE = "FilterSelectedTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 筛选按钮
         */
        private FilterBtnM filterBtnM;

        /**
         * 层级，1开始
         */
        private int layer;

        /**
         * 属于父节点的第几个，从1开始
         */
        private int index;
    }
}