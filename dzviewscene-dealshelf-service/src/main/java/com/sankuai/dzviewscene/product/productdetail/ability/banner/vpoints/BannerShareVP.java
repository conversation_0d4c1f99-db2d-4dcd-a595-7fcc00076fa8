package com.sankuai.dzviewscene.product.productdetail.ability.banner.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.productdetail.ability.banner.DetailBannerBuilder;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ShareVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "横幅-分享模块变化点", description = "适用于需要分享模块定制的场景",code = BannerShareVP.CODE, ability = DetailBannerBuilder.CODE)
public abstract class BannerShareVP<T> extends PmfVPoint<ShareVO, BannerShareVP.Param, T> {

    public static final String CODE = "BannerMoreComponentVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<ProductM> productMs;
    }
}
