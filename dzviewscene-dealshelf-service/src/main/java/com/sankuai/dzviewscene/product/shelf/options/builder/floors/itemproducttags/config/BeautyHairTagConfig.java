package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.config;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 丽人美发副标题配置
 * 尝试充血对象，即带行为 非简单 POJO 类
 * <AUTHOR>
 * @date 2022/11/2
 */
@Data
public class BeautyHairTagConfig {
    /**
     * 展示丽人主标签的筛选
     */
    private List<Long> beautyMainTagFilterIds;

    /*** 服务分类配置·Start ***/

    /**
     * 烫染全部时展示服务分类
     */
    private LinkedHashMap<Long, String> tangRanCategoryTagMap;

    /**
     * 展示详细烫染分类id的筛选项id
     */
    private List<Long> tangRanCategoryFilterIds;

    /**
     * 染发全部时展示服务分类
     */
    private LinkedHashMap<Long, String> ranFaCategoryTagMap;

    /**
     * 展示详细染发分类id的筛选id
     */
    private List<Long> ranFaCategoryFilterIds;

    /**
     * 烫发全部时展示服务分类
     */
    private LinkedHashMap<Long, String> tangFaCategoryTagMap;

    /**
     * 展示详细烫发分类id的筛选id
     */
    private List<Long> tangFaCategoryFilterIds;

    /*** 服务分类配置·End ***/

    /**
     * 发型师等级
     */
    private LinkedHashMap<Long, String> barberTitleTagMap;

    /**
     * 发型师id-展示等级
     */
    private Map<String, String> barberId2TitleMap;

    /**
     * 展示的药水品牌库列表
     */
    private List<String> pharmaceuticsBrandShowList;

    /**
     * 药水品牌结构化信息读取规则
     */
    private Map<Long, PharmaceuticsFilterCfg> pharmaceuticsFilterCfgMap;

    @Data
    static class PharmaceuticsFilterCfg {
        //按序取服务项目
        private List<Long> productCategories;
        //服务项目名过滤
        private String equalsProductCategoryName;
        //二级分类属性过滤
        private String equalsCategory2;
        //用以展示的属性名
        private String showAttrName;
    }

    /**
     * @param productM
     * @param filterId
     * @return 指定 filter 下需要展示主标签
     */
    public String doGetHotFilterBeautyMainTag(ProductM productM, long filterId) {
        if (CollectionUtils.isNotEmpty(this.beautyMainTagFilterIds) && this.beautyMainTagFilterIds.contains(filterId)) {
            return productM.getAttr("beautyMainTag");
        }
        return null;
    }

    /**
     * @param productTagIds
     * @param filterId
     * @return 烫染服务分类，根据团单所包含标签与筛选id，根据优先级匹配出要展示的服务分类
     */
    public String doGetCategoryTag(List<Long> productTagIds, long filterId) {
        //染发
        if (CollectionUtils.isNotEmpty(this.ranFaCategoryFilterIds) && this.ranFaCategoryFilterIds.contains(filterId)) {
            return getTagFromPriorityMap(productTagIds, this.ranFaCategoryTagMap);
        }
        //烫发
        if (CollectionUtils.isNotEmpty(this.tangFaCategoryFilterIds) && this.tangFaCategoryFilterIds.contains(filterId)) {
            return getTagFromPriorityMap(productTagIds, this.tangFaCategoryTagMap);
        }
        //烫染
        if (CollectionUtils.isNotEmpty(this.tangRanCategoryFilterIds) && this.tangRanCategoryFilterIds.contains(filterId)) {
            return getTagFromPriorityMap(productTagIds, this.tangRanCategoryTagMap);
        }
        return null;
    }

    /**
     * @param productM
     * @param filterId
     * @return 获取药水品牌标签
     */
    public String doGetPharmaceuticsBrandTag(ProductM productM, long filterId) {
        if (CollectionUtils.isEmpty(this.pharmaceuticsBrandShowList)) {
            return null;
        }
        String brandFromStruct = doGetPharmaceuticsBrandFromStruct(productM, filterId);
        if (StringUtils.isEmpty(brandFromStruct)) {
            return null;
        }
        //仅展示品牌库内的品牌
        for (String brand4Show : this.pharmaceuticsBrandShowList) {
            if (brandFromStruct.contains(brand4Show)) {
                return brand4Show;
            }
        }
        return null;
    }

    /**
     * @param productM
     * @param filterId
     * @return 从结构化信息中取得药水品牌的原值
     */
    private String doGetPharmaceuticsBrandFromStruct(ProductM productM, long filterId) {
        PharmaceuticsFilterCfg cfg = doGetValidPharmaceuticsFilterCfg(filterId);
        if (cfg == null) {
            return null;
        }
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(productM.getAttr(ProductMAttrUtils.DEAL_STRUCT_DETAIL));
        if (CollectionUtils.isEmpty(allSkus)) {
            return null;
        }
        for (Long productCategory : cfg.getProductCategories()) {
            for (SkuItemDto skuItemDto : allSkus) {
                if (checkSkuMatchPharmaceuticsFilterCfg(skuItemDto, cfg, productCategory)) {
                    return DealStructUtils.getAttrValueByAttrKey(skuItemDto, cfg.getShowAttrName());
                }
            }
        }
        return null;
    }

    private PharmaceuticsFilterCfg doGetValidPharmaceuticsFilterCfg(long filterId) {
        if (MapUtils.isEmpty(this.pharmaceuticsFilterCfgMap)) {
            return null;
        }
        PharmaceuticsFilterCfg cfg = this.pharmaceuticsFilterCfgMap.get(filterId);
        if (cfg == null || CollectionUtils.isEmpty(cfg.getProductCategories()) || StringUtils.isEmpty(cfg.getShowAttrName())) {
            return null;
        }
        return cfg;
    }

    /**
     * @param skuItemDto
     * @param cfg
     * @return 校验服务项目内的属性是否满足配置内容，若满足 true 即取该服务项的值
     */
    private boolean checkSkuMatchPharmaceuticsFilterCfg(SkuItemDto skuItemDto, PharmaceuticsFilterCfg cfg, Long currentProductCategory) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.config.BeautyHairTagConfig.checkSkuMatchPharmaceuticsFilterCfg(SkuItemDto,BeautyHairTagConfig$PharmaceuticsFilterCfg,Long)");
        //匹配服务项目
        if (skuItemDto.getProductCategory() != currentProductCategory) {
            return false;
        }
        //校验是否有值
        String showValue = DealStructUtils.getAttrValueByAttrKey(skuItemDto, cfg.getShowAttrName());
        if (StringUtils.isEmpty(showValue)) {
            return false;
        }
        //校验是否满足配置要求
        if (StringUtils.isNotEmpty(cfg.getEqualsCategory2()) && !Objects.equals(DealStructUtils.getAttrValueByAttrKey(skuItemDto, "category2"), cfg.getEqualsCategory2())) {
            return false;
        }
        if (StringUtils.isNotEmpty(cfg.getEqualsProductCategoryName()) && !Objects.equals(skuItemDto.getName(), cfg.getEqualsProductCategoryName())) {
            return false;
        }
        return true;
    }

    /**
     * @param tagIds
     * @return 获取发型师等级
     */
    public String doGetBarberTitleTag(List<Long> tagIds, ProductM productM) {
        //首先从行业属性中判断
        String tagFromAttr = doGetBarberTitleTagFromAttr(productM);
        if (StringUtils.isNotEmpty(tagFromAttr)) {
            return tagFromAttr;
        }
        //如果团单没有该属性，则根据标签判断
        return getTagFromPriorityMap(tagIds, this.barberTitleTagMap);
    }

    public String doGetBarberTitleTagFromAttr(ProductM productM) {
        String unlimitedHairdresser = productM.getAttr("unlimited_hairdresser");
        if (Objects.equals(unlimitedHairdresser, "所有等级可用")) {
            return "不限发型师";
        }
        if (Objects.equals(unlimitedHairdresser, "部分等级可用")) {
            List<String> availableLevels = JsonCodec.converseList(productM.getAttr("hairdresser_available_levels"), String.class);
            if (CollectionUtils.isEmpty(availableLevels) || MapUtils.isEmpty(this.barberId2TitleMap)) {
                return null;
            }
            List<String> availableTitles = availableLevels.stream().map(id -> this.barberId2TitleMap.get(id)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (availableTitles.size() == 1) {
                return availableTitles.get(0);
            }
            if (availableTitles.size() > 1){
                return String.format("%s等%d档", availableTitles.get(0), availableTitles.size());
            }
        }
        return null;
    }

    private String getTagFromPriorityMap(List<Long> tagIds, LinkedHashMap<Long, String> tagMap) {
        if (CollectionUtils.isEmpty(tagIds) || MapUtils.isEmpty(tagMap)) {
            return null;
        }
        for (Map.Entry<Long, String> entry : tagMap.entrySet()) {
            if (tagIds.contains(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }
}
