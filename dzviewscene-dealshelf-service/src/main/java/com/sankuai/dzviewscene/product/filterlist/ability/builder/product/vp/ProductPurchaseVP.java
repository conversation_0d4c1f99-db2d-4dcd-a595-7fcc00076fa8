package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/2/23
 */
@VPoint(name = "商品-购买信息", description = "如最近购买、剩余库存、预售时间等", code = ProductPurchaseVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductPurchaseVP<T> extends PmfVPoint<RichLabelVO, ProductPurchaseVP.Param, T> {
    public static final String CODE = "ProductPurchaseVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}
