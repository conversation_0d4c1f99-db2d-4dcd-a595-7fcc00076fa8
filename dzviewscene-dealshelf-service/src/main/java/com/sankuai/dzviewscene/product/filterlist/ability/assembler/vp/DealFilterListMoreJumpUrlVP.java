package com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp;

import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Builder;
import lombok.Data;

/**
 * Created by wangxinyuan on 2022/05/07.
 */
@VPoint(name = "更多跳转链接", description = "更多跳转链接",code = DealFilterListMoreJumpUrlVP.CODE, ability = DealListResponseAssembler.CODE)
public abstract class DealFilterListMoreJumpUrlVP<T> extends PmfVPoint<String, DealFilterListMoreJumpUrlVP.Param, T> {

    public static final String CODE = "DealFilterListMoreJumpUrlVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * {@link  VCPlatformEnum}
         */
        private int platform;

        /**
         * {@link ShelfActivityConstants.Params#entityId}
         */
        private String entityId;

        /**
         * {@link ShelfActivityConstants.Params#userAgent}
         */
        private int userAgent;

        /**
         * 相应平台的 cityId
         */
        private int platformCityId;
    }

}
