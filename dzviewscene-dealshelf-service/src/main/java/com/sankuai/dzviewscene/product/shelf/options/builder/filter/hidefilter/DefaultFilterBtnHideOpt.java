package com.sankuai.dzviewscene.product.shelf.options.builder.filter.hidefilter;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterBtnHideVP;

/**
 * <AUTHOR>
 * @since 2023/5/31 15:21
 */
@VPointOption(name = "筛选项隐藏-默认实现",
        description = "默认不隐藏筛选项）",
        code = "DefaultFilterBtnHideOpt",
        isDefault = true)
public class DefaultFilterBtnHideOpt extends FilterBtnHideVP<Void> {
    @Override
    public Boolean compute(ActivityCxt context, FilterBtnHideVP.Param param, Void unused) {
        return super.defaultSet();
    }
}
