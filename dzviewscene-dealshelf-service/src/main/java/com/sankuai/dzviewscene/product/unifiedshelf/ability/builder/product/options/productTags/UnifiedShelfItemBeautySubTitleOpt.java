package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.dianping.gm.marketing.times.card.api.enums.TimesCardTypeEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@VPointOption(name = "丽人三美-副标题构造", description = "丽人三美-副标题构造", code = "UnifiedShelfItemBeautySubTitleOpt")
public class UnifiedShelfItemBeautySubTitleOpt extends UnifiedShelfItemSubTitleVP<UnifiedShelfItemBeautySubTitleOpt.Config> {

    @Override
    public ItemSubTitleVO computeFromOpt(ActivityCxt context, Param param, Config config) {
        ItemSubTitleVO itemSubTitleVO = new ItemSubTitleVO();
        itemSubTitleVO.setJoinType(0);
        List<StyleTextModel> models = build(context, param, config);
        itemSubTitleVO.setTags(models);
        return itemSubTitleVO;
    }

    public List<StyleTextModel> build(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        // 丽人标准化团单的特殊处理
        if (config.isCalcBySpu()
                && ProductMAttrUtils.standardSpu(param.getProductM())) {
            List<String> standardSpuTags = ProductMAttrUtils.getStandardSpuSpecialProductTags(productM);
            if (CollectionUtils.isNotEmpty(standardSpuTags)) {
                return buildProductRichLabelTags(standardSpuTags);
            }
        }
        // 预售单逻辑处理
        if (PreSaleUtils.isPreSaleDeal(productM)) {
            return getPreSaleProductRichTags(productM, config);
        }
        // 次卡逻辑处理
        if (isTimesCard(productM)) {
            List<StyleTextModel> timesCardProductRichTags = getTimesCardProductRichTags(productM, config);
            if (ProductMAttrUtils.timeCardIsShowAfterPayTag(param.getProductM(),context,config.getAfterPayExps())) {
                StyleTextModel afterPayTag = buildPreAfterPayTag(config);
                timesCardProductRichTags.add(0, afterPayTag);
            }
            return timesCardProductRichTags;
        }
        List<StyleTextModel> styleTextModels = new ArrayList<>();
        //先用后付款
        if(ProductMAttrUtils.timeCardIsShowAfterPayTag(param.getProductM(),context,config.getAfterPayExps())){
            StyleTextModel afterPayTag = buildPreAfterPayTag(config);
            styleTextModels.add(afterPayTag);
        }
        //联合星品
        if (ProductMAttrUtils.isUniteStarProduct(productM)) {
            StyleTextModel preUniteStarTag = buildPreUniteStarTag(config);
            styleTextModels.add(preUniteStarTag);
        }
        List<StyleTextModel> productTags = buildProductRichLabelTags(param.getProductM().getProductTags());
        StyleTextModel meiJiaKuanShiRenXuanTag = buildMeiJiaKuanShiRenXuanTag(param.getProductM(), config);
        if (meiJiaKuanShiRenXuanTag != null) {
            styleTextModels.add(meiJiaKuanShiRenXuanTag);
        }
        styleTextModels.addAll(productTags);
        return styleTextModels;
    }

    public StyleTextModel buildPreUniteStarTag(Config config) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(config.getPreUniteStarTag());
        styleTextModel.setStyle(TextStyleEnum.TEXT_HIGHLIGHT.getType());
        return styleTextModel;
    }

    public StyleTextModel buildPreAfterPayTag(Config config) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(config.getPreAfterTag());
        styleTextModel.setStyle(TextStyleEnum.TEXT_GREEN.getType());
        return styleTextModel;
    }

    private boolean isTimesCard(ProductM productM) {
        return productM.getProductType() == ProductTypeEnum.TIME_CARD.getType();
    }

    private List<StyleTextModel> getTimesCardProductRichTags(ProductM productM, Config config) {
        List<StyleTextModel> productTags = buildProductRichLabelTags(productM.getProductTags());
        if (CollectionUtils.isEmpty(productTags)) {
            return Lists.newArrayList();
        }
        int timesCardType = NumberUtils.toInt(ProductMAttrUtils.getAttrValue(productM, "times_card_type"));
        if (timesCardType == TimesCardTypeEnum.PLAIN_TIMESCARD.getCode()
                || timesCardType == TimesCardTypeEnum.FLEXIBLE_TIMESCARD.getCode()) {
            String countTemplate = timesCardType == TimesCardTypeEnum.PLAIN_TIMESCARD.getCode() ? "%d项" : "%d项任选";
            String countTag = String.format(countTemplate, productTags.size());
            StyleTextModel countRichLabel = buildGrayText(countTag);
            //限制前3项
            productTags = productTags.subList(0, Math.min(productTags.size(), 3));
            //加上统计项标签
            productTags.add(0, countRichLabel);
        }
        return productTags;
    }

    private StyleTextModel buildMeiJiaKuanShiRenXuanTag(ProductM productM, Config config) {
        //美甲团单，包含款式任选， https://km.sankuai.com/collabpage/1681432462
        String tagIdStr = ProductMAttrUtils.getProductTagIdStr(productM);
        if (CollectionUtils.isNotEmpty(config.getStyleTag()) && StringUtils.isNotEmpty(tagIdStr)) {
            for (String styleTag : config.getStyleTag()) {
                if (tagIdStr.contains(styleTag) && MapUtils.isNotEmpty(config.getStyleTagText())
                        && config.getStyleTagText().containsKey(styleTag)) {
//                    return new StyleTextModel(FrontSizeUtils.front12, ColorUtils.colorFF6633, config.getStyleTagText().get(styleTag));
                    return buildGrayText(config.getStyleTagText().get(styleTag));
                }
            }
        }
        return null;
    }

    private List<StyleTextModel> buildProductRichLabelTags(List<String> productTags) {
        if (CollectionUtils.isEmpty(productTags)) {
            return Lists.newArrayList();
        }
        return productTags.stream().filter(StringUtils::isNotEmpty)
                .map(UnifiedShelfItemSubTitleVP::buildGrayText)
                .collect(Collectors.toList());
    }

    @EqualsAndHashCode(callSuper = true)
    @VPointCfg
    @Data
    public static class Config extends UnifiedShelfItemSubTitleVP.Config {
        /**
         * 是否基于 SPU 进行转化
         */
        private boolean calcBySpu;

        private List<String> styleTag;

        private Map<String, String> styleTagText;

        private String preUniteStarTag = "品牌官方保障 美团联合出品";

        private String preAfterTag = "先用后付";

        private List<String> afterPayExps;
    }
}
