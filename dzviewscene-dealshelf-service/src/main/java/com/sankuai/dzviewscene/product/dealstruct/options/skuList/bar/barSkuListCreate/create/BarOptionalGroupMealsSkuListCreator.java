package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.AbstractBarSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;

import java.util.List;

import java.util.stream.Collectors;

/**
 * 酒吧可选项目组参食sku列表构造器
 * <AUTHOR>
 * @date 2023/2/9 2:08 下午
 */
public class BarOptionalGroupMealsSkuListCreator extends AbstractBarSkuListCreator {

    @Override
    public boolean ideantify(boolean isMustGroupSku, List<SkuItemDto> skuList, BarDealDetailSkuListModuleOpt.Config config) {
        if (isMustGroupSku || CollectionUtils.isEmpty(skuList) || CollectionUtils.isEmpty(config.getMealsSkuCateIds())) {
            return false;
        }
        return skuList.stream().filter(sku -> !config.getMealsSkuCateIds().contains(sku.getProductCategory())).collect(Collectors.toList()).size() == 0;
    }

    @Override
    public List<DealSkuGroupModuleVO> buildSkuListModules(List<SkuItemDto> skuItemDtos, BarDealDetailSkuListModuleOpt.Config config, int optionalNum, boolean hitDouHu) {
        List<DealSkuGroupModuleVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return result;
        }
        String groupName = hitDouHu && StringUtils.isNotEmpty(config.getOptionalFormat()) ? String.format(config.getOptionalFormat(), skuItemDtos.size(), optionalNum) : getMealGroupName(skuItemDtos, optionalNum, config);
        addSkuListModule(skuItemDtos, groupName, result, config, true, hitDouHu);
        return result;
    }

    private String getMealGroupName(List<SkuItemDto> skuItemDtos, int optionalNum, BarDealDetailSkuListModuleOpt.Config config) {
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return null;
        }
        //提取所有服务项目的数量
        List<Integer> allNumList = skuItemDtos.stream()
                .map(sku -> {
                    int num = NumberUtils.objToInt(DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), "unit"));
                    if (num == -1) {
                        num = 1;
                    }
                    return sku.getCopies() * num;
                }).distinct().collect(Collectors.toList());
        if (allNumList.size() == 1) {
            return String.format(config.getOptionalMealsSkusWithSameNumGroupNameFormat(), CollectUtils.firstValue(allNumList), skuItemDtos.size(), optionalNum);
        }
        return String.format(config.getOptionalMealsSkusGroupNameFormat(), skuItemDtos.size(), optionalNum);
    }

}
