package com.sankuai.dzviewscene.product.filterlist.option.factory.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.AbstractMassageStrategy;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/9 16:08
 */
@Component
public class CupStrategyImpl extends AbstractMassageStrategy {

    @Override
    public String getFilterListTitle(SkuItemDto skuItemDto, String serviceType) {
        String serviceDuration = getServiceDuration(skuItemDto);
        String serviceTechnique = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItemDto, SERVICE_TECHNIQUE);
        if (StringUtils.isNotBlank(serviceTechnique)) {
            serviceTechnique = String.format("（%s）", serviceTechnique);
        }
        return String.format("%s%s%s", serviceDuration, serviceType, serviceTechnique);
    }

    @Override
    public List<Long> getProductCategorys() {
        return Lists.newArrayList(ProductCategoryEnum.CUP.getProductCategoryId());
    }

}
