package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemcceanlabs;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemOceanLabsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoPerItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/15
 */
@VPointOption(name = "空变化点-返回 null",
        description = "",
        code = "NullItemOceanLabsOpt")
public class NullItemOceanLabsOpt extends ItemOceanLabsVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
