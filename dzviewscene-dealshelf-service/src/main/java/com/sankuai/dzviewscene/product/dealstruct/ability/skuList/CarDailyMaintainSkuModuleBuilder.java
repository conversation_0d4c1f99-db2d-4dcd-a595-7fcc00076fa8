package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


@Ability(code = CarDailyMaintainSkuModuleBuilder.CODE,
        name = "爱车-保养-日常养护服务项目模块构造能力",
        description = "爱车-保养-日常养护服务项目模块构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE
        }
)
public class CarDailyMaintainSkuModuleBuilder extends PmfAbility<List<DealDetailSkuListModuleGroupModel>, DealDetailSkuListsParam, CarDailyMaintainSkuModuleBuilder.Config> implements ModuleStrategy {

    public static final String CODE = "carDailyMaintainSkuModuleBuilder";

    /**
     * 组装策略实现
     */
    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<DealDetailSkuListModuleGroupModel> dealDetailSkuListModuleGroupModels = activityCxt.getSource(CarDailyMaintainSkuModuleBuilder.CODE);
        if (CollectionUtils.isEmpty(dealDetailSkuListModuleGroupModels)) {
            return null;
        }
        DealDetailSkuListModuleGroupModel groupModel = dealDetailSkuListModuleGroupModels.get(0);
        if (groupModel == null || CollectionUtils.isEmpty(groupModel.getDealSkuGroupModuleVOS())) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(groupModel.getDealSkuGroupModuleVOS());
        dealDetailModuleVO.setName(groupModel.getGroupName());
        return dealDetailModuleVO;
    }

    /**
     * 构造能力实现
     */
    @Override
    public CompletableFuture<List<DealDetailSkuListModuleGroupModel>> build(ActivityCxt activityCxt, DealDetailSkuListsParam dealDetailSkuListsParam, CarDailyMaintainSkuModuleBuilder.Config config) {
        // 获取团单数据
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        if (!isValidDealDetail(dealDetailInfoModel)) {
            return CompletableFuture.completedFuture(null);
        }

        // 获取必选组的所有sku
        List<SkuItemDto> skuItemList = DealDetailUtils.extractMustSkuFromDealDetailInfoModel(dealDetailInfoModel);
        if (CollectionUtils.isEmpty(skuItemList)) {
            return CompletableFuture.completedFuture(null);
        }
        DealDetailSkuListModuleGroupModel dealDetailSkuListModuleGroupModel = new DealDetailSkuListModuleGroupModel();
        DealSkuGroupModuleVO dealSkuGroupModuleVO = buildDealSkuGroup(skuItemList, config);
        dealDetailSkuListModuleGroupModel.setDealSkuGroupModuleVOS(Lists.newArrayList(dealSkuGroupModuleVO));
        dealDetailSkuListModuleGroupModel.setGroupName(buildGroupName(dealSkuGroupModuleVO.getDealSkuList(), config));

        return CompletableFuture.completedFuture(Lists.newArrayList(dealDetailSkuListModuleGroupModel));
    }

    private DealSkuGroupModuleVO buildDealSkuGroup(List<SkuItemDto> skuItemList, Config config) {
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();

        List<DealSkuVO> dealSkuList = Lists.newArrayList();
        for (SkuItemDto skuItemDto : skuItemList) {
            String serviceProduct = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "serviceProduct");
            if (StringUtils.isBlank(serviceProduct)) {
                continue;
            }
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setCopies(skuItemDto.getCopies() + "份");
            dealSkuVO.setPrice(skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString() + "元");
            dealSkuVO.setTitle(serviceProduct);
            dealSkuVO.setIcon(config.getServiceName2IconMap().containsKey(serviceProduct) ? config.getServiceName2IconMap().get(serviceProduct) : config.getDefaultIcon());
            dealSkuList.add(dealSkuVO);
        }
        dealSkuGroupModuleVO.setDealSkuList(dealSkuList);
        return dealSkuGroupModuleVO;
    }

    private String buildGroupName(List<DealSkuVO> dealSkuList, Config config) {
        if (CollectionUtils.isEmpty(dealSkuList)) {
            return null;
        }
        return dealSkuList.size() == 1 ? config.getOneSkuName() : String.format(config.getMoreSkusName(), dealSkuList.size());
    }

    private boolean isValidDealDetail(DealDetailInfoModel detailModel) {
        return detailModel != null && detailModel.getDealDetailDtoModel() != null && detailModel.getDealDetailDtoModel().getSkuUniStructuredDto() != null;
    }

    @Data
    @VPointCfg
    public static class Config {

        /**
         * 服务项目 -> 对应的icon集合
         */
        private Map<String, String> serviceName2IconMap = new HashMap<>();

        /**
         * 默认的icon
         */
        private String defaultIcon;

        /**
         * 单sku时的模块名称
         */
        private String oneSkuName;

        /**
         * 多sku时的模块名称（拼接上数量）
         */
        private String moreSkusName;

    }

}
