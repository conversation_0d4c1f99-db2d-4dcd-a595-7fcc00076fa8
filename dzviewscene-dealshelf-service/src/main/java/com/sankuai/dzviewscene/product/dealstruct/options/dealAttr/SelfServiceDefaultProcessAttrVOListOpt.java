package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.customer.dto.CustomerShopNew;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.joygeneral.api.thirdpart.dto.QueryAutoOpenTableReqDTO;
import com.dianping.joygeneral.api.thirdpart.dto.Response;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DefaultSelfProcessAttrModelConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.other.vo.SelfServiceCarWashHmConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.it.iam.common.base.gson.bridge.JSON;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@VPointOption(name = "台球-自助服务流程DealAttrVO列表变化点", description = "台球-自助服务流程DealAttrVO列表变化点",code = SelfServiceDefaultProcessAttrVOListOpt.CODE)
public class SelfServiceDefaultProcessAttrVOListOpt extends DealAttrVOListVP<SelfServiceDefaultProcessAttrVOListOpt.Config> {
    public static final String CODE = "SelfServiceDefaultProcessAttrVOListOpt";

    private static final String SERVICE_DETAIL_PIC = "操作流程";

    private static final String SERVICE_DETAIL = "服务内容";

    private static final String SERVICE_PROCESS = "serviceProcess";

    private static final Integer ORDER = 1;//控制优先级

    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource
    private AtomFacadeService atomFacadeService;

    @Override
    public List<DealDetailStructAttrModuleGroupModel> compute(ActivityCxt context, DealAttrVOListVP.Param param, SelfServiceDefaultProcessAttrVOListOpt.Config config) {
        if(config == null || !config.getProcessSwitch()) {
            return null;
        }

        Boolean needDefaultSelfServiceProcess = needDefaultSelfServiceProcess(context);

        return buildDefaultDealDetailStructAttrModuleGroupModel(config,context, needDefaultSelfServiceProcess);
    }

    /**
     * 填充平台默认流程
     * @param
     * @return
     */
    public static List<DealDetailStructAttrModuleGroupModel> buildDefaultDealDetailStructAttrModuleGroupModel(SelfServiceDefaultProcessAttrVOListOpt.Config config,ActivityCxt activityCxt, Boolean needDefaultSelfServiceProcess){
        List<ProcessModel> defaultServiceProcessModels = config.getServiceProcessProcessModel();
        if (CollectionUtils.isEmpty(defaultServiceProcessModels) || !needDefaultSelfServiceProcess){
            return null;
        }

        ArrayList<DealDetailStructAttrModuleVO> detailAttrModuleVOS = new ArrayList<>();
        ArrayList<DealDetailStructAttrModuleVO> picAttrModuleVOS = new ArrayList<>();
        defaultServiceProcessModels.forEach(defaultServiceProcessModel -> {
            if (StringUtils.isEmpty(defaultServiceProcessModel.getProcessName()) || StringUtils.isEmpty(defaultServiceProcessModel.getProcessDesc())) {
                return;
            }

            DealDetailStructAttrModuleVO detailAttrModuleVO = new DealDetailStructAttrModuleVO();
            detailAttrModuleVO.setAttrName(defaultServiceProcessModel.getProcessName());
            detailAttrModuleVO.setAttrValues(Lists.newArrayList(defaultServiceProcessModel.getProcessDesc()));

            DealDetailStructAttrModuleVO picAttrModuleVO = new DealDetailStructAttrModuleVO();
            picAttrModuleVO.setIcon(defaultServiceProcessModel.getPicUrl());
            picAttrModuleVO.setAttrName(defaultServiceProcessModel.getProcessName());
            picAttrModuleVO.setAttrValues(Lists.newArrayList(defaultServiceProcessModel.getProcessName()));

            detailAttrModuleVOS.add(detailAttrModuleVO);
            picAttrModuleVOS.add(picAttrModuleVO);
        });

        if (CollectionUtils.isEmpty(detailAttrModuleVOS) || CollectionUtils.isEmpty(picAttrModuleVOS)) {
            return null;
        }
        DealDetailStructAttrModuleGroupModel dealDetailModel = new DealDetailStructAttrModuleGroupModel();
        DealDetailStructAttrModuleGroupModel picModel = new DealDetailStructAttrModuleGroupModel();

        dealDetailModel.setGroupName(config.getGroupDescName());
        dealDetailModel.setDealDetailStructAttrModuleVOS(detailAttrModuleVOS);
        dealDetailModel.setOrder(ORDER);

        picModel.setGroupName(config.getGroupName());
        picModel.setDealDetailStructAttrModuleVOS(picAttrModuleVOS);
        picModel.setOrder(ORDER);
        return Lists.newArrayList(dealDetailModel,picModel);
    }

    //是否需要填充默认自助服务流程
    private Boolean needDefaultSelfServiceProcess(ActivityCxt activityContext){
        long dpShopId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        long mtShopId = PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        int productId = activityContext.getParam(ProductDetailActivityConstants.Params.productId);

        //查海马配置，洗车机是白名单/台球是黑名单
        List<CustomerShopNew> customerShopsByShopIdsNew = atomFacadeService.getCustomerShopsByShopIdsNew(Lists.newArrayList(dpShopId)).join();
        if (CollectionUtils.isEmpty(customerShopsByShopIdsNew)){
            //未查到有效客户信息，直接失败
            return false;
        }
        List<Integer> customerIds = customerShopsByShopIdsNew.stream().map(CustomerShopNew::getCustomerID).collect(Collectors.toList());
        HaimaRequest hmRequest = new HaimaRequest();
        hmRequest.setSceneKey("general_groupbuy_auto_billiards_customer_config");
        customerIds.forEach(customerId -> {
            hmRequest.addField("customerId",String.valueOf(customerId));
        });
        CompletableFuture<HaimaResponse> haiMaResponse = compositeAtomService.getHaiMaResponse(hmRequest);
        HaimaResponse hmResponse = haiMaResponse.join();

        if (matchList(hmResponse,customerIds,productId,dpShopId)){
            return false;
        }
        QueryAutoOpenTableReqDTO request = new QueryAutoOpenTableReqDTO();
        request.setDpPoiId(dpShopId == 0 ? null : dpShopId); //queryAutoOpenTable没有判断0，如果值为0要置空
        request.setMtPoiId(mtShopId == 0 ? null : mtShopId);
        Response<Boolean> response = compositeAtomService.queryAutoOpenTable(request).join();
        if (response == null || response.getData() == null) {
            return false;
        }
        return response.getData();
    }

    /**
     * 是否命中名单
     * @return
     */
    public static Boolean matchList(HaimaResponse hmResponse,List<Integer> customerIds,Integer productId,Long dpShopId){
        if (hmResponse == null || CollectionUtils.isEmpty(hmResponse.getData())){
            //没有接入的客户配置，直接失败
            return false;
        }
        List<HaimaConfig> configs = hmResponse.getData();
        ArrayList<SelfServiceCarWashHmConfig> selfServiceCarWashHmConfigs = new ArrayList<>();
        for (HaimaConfig config : configs) {
            if (CollectionUtils.isEmpty(config.getContents())) {
                continue;
            }
            config.getContents().forEach(item -> {
                int customerId = item.getContentInt("customerId");
                if (customerIds.contains(customerId)){
                    List<Integer> productIdList = JSON.parseArray(String.valueOf(item.getContent("productIdList")), Integer.class);
                    List<Long> shopIdList = JSON.parseArray(String.valueOf(item.getContent("shopIdList")), Long.class);

                    SelfServiceCarWashHmConfig selfServiceCarWashHmConfig = new SelfServiceCarWashHmConfig();
                    selfServiceCarWashHmConfig.setCustomerId(customerId);
                    selfServiceCarWashHmConfig.setProductIdList(productIdList);
                    selfServiceCarWashHmConfig.setShopIdList(shopIdList);
                    selfServiceCarWashHmConfigs.add(selfServiceCarWashHmConfig);
                }
            });
        }
        if (CollectionUtils.isEmpty(selfServiceCarWashHmConfigs)){
            return false;
        }
        for (SelfServiceCarWashHmConfig config:selfServiceCarWashHmConfigs){
            if (CollectionUtils.isEmpty(config.getProductIdList())){
                return true;
            }
            List<Integer> productIdList = config.getProductIdList();
            if (productIdList.contains(productId) && ((CollectionUtils.isEmpty(config.getShopIdList()) || config.getShopIdList().contains(dpShopId)))){
                return true;
            }
        }
        return false;
    }

    @Data
    static private class ProcessModel {
        //图标
        private String picUrl;
        //名称
        private String processName;
        //描述
        private String processDesc;
    }

    @Data
    @VPointCfg
    public static class Config {
        //服务流程图配置
        private List<ProcessModel> serviceProcessProcessModel;

        private String groupName;

        private String groupDescName;

        private Boolean processSwitch;

        public String getGroupName() {
            return StringUtils.isEmpty(groupName) ? SERVICE_DETAIL_PIC : groupName;
        }

        public String getGroupDescName() {
            return StringUtils.isEmpty(groupDescName) ? SERVICE_DETAIL : groupDescName;
        }

    }
}
