package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemcceanlabs;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.options.VRInterestFetcherOpt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemOceanLabsVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@VPointOption(name = "月子中心房型货架商品埋点",
        description = "返回月子中心房型货架的商品埋点",
        code = "NursingCenterRoomItemOceanLabsOpt")
public class NursingCenterRoomItemOceanLabsOpt extends ItemOceanLabsVP<NursingCenterRoomItemOceanLabsOpt.Config> {
    public static final String VR_URL_ATTR = "nursingCenterRoomVrUrl";
    
    private static final String HAS_VR_LAB_KEY = "hasVr";
    
    @Override
    public String compute(ActivityCxt activityCxt, Param param, NursingCenterRoomItemOceanLabsOpt.Config config) {
        Map<String, Object> oceanMap = getCommonOcean(param);
        paddingDiscountAndComparePriceLab(oceanMap, param.getProductM());
        addVROceanLabIfPresent(oceanMap, activityCxt, param.getProductM());
        addTopDisplayLabIfNeeded(oceanMap, param.getProductM(), config);
        return JsonCodec.encode(oceanMap);
    }

    private boolean isValidLabConfig(LabConfig topDisplayLabConfig) {
        return topDisplayLabConfig != null
                && !StringUtils.isAnyBlank(topDisplayLabConfig.getLabKey(), topDisplayLabConfig.getLabValue());
    }

    private void addVROceanLabIfPresent(Map<String, Object> oceanMap, ActivityCxt activityCxt, ProductM productM) {
        Boolean hasVRInterest = activityCxt.getParam(VRInterestFetcherOpt.CODE);
        String vrJumpUrl = productM.getAttr(VR_URL_ATTR);
        if (hasVRInterest != null && hasVRInterest && StringUtils.isNotBlank(vrJumpUrl)) {
            oceanMap.put(HAS_VR_LAB_KEY, true);
        }
    }

    private void addTopDisplayLabIfNeeded(Map<String, Object> oceanMap, ProductM productM, NursingCenterRoomItemOceanLabsOpt.Config config) {
        if (config.isEnableTopDisplayInfoLabs() && ProductMAttrUtils.isTopDisplayProduct(productM)) {
            LabConfig topDisplayLabConfig = config.getTopDisplayLabConfig();
            if (isValidLabConfig(topDisplayLabConfig)) {
                oceanMap.put(topDisplayLabConfig.getLabKey(), topDisplayLabConfig.getLabValue());
            }
        }
    }

    @VPointCfg
    @Data
    public static class Config {
        private boolean enableTopDisplayInfoLabs = false;
        private LabConfig topDisplayLabConfig;
    }

    @Data
    public static class LabConfig {
        private String labKey;
        private String labValue;
    }
}
