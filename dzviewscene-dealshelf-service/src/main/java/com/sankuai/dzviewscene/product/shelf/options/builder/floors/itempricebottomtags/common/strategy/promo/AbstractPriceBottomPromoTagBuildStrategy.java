package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.AbstractPriceBottomTagBuildStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.DzTagStyleWrapUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * 优惠感知标签策略抽象类
 */
public abstract class AbstractPriceBottomPromoTagBuildStrategy extends AbstractPriceBottomTagBuildStrategy {

    private final String COMMON_AFTER_PIC = "https://p0.meituan.net/ingee/b42e754af807e76311ca73b422b9ee92385.png";

    private final String MEMBER_AFTER_PIC = "https://p1.meituan.net/travelcube/03ff48944174e7edd6dfc5ba7cbd73a5613.png";

    /**
     * 优惠标签前缀图片宽高比
     */
    private static final double MT_PROMO_TAG_PRE_PIC_RADIO = 3.25;

    private static final double DP_PROMO_TAG_PRE_PIC_RADIO = 3.25;

    @Override
    public DzTagVO build(PriceBottomTagBuildReq req) {
        //优惠感知都必须要有优惠信息，这里统一校验了，特殊情况可以不继承该类
        if (req == null || req.getProductM() == null
                || CollectionUtils.isEmpty(req.getProductM().getPromoPrices())) {
            return null;
        }
        DzTagVO promoTag = buildTag(req);
        //优惠减负标签不需要覆盖样式
        if(!PromoSimplifyUtils.hitPromoSimplify(req.getContext())){
            DzTagStyleWrapUtils.overridePromoStyle(promoTag);
        }
        return promoTag;
    }

    public DzPictureComponentVO buildPromoAfterPic(int platform) {
        if (PlatformUtil.isMT(platform)) {
            return new DzPictureComponentVO("https://p0.meituan.net/travelcube/ac0b1b7e016f85fcf9b8784d34d1bc14439.png", 1);
        } else {
            return new DzPictureComponentVO("https://p1.meituan.net/travelcube/4b5a342d1d0c2ae89ea51d8a2a6f7d75459.png", 1);
        }
    }

    public double getPicRadio(int platform, PriceBottomTagBuildCfg cfg) {
        return getPicRadio(platform, cfg, MT_PROMO_TAG_PRE_PIC_RADIO, DP_PROMO_TAG_PRE_PIC_RADIO);
    }

    public double getPicRadio(int platform, PriceBottomTagBuildCfg cfg, double defaultMTRadio, double defaultDPRadio) {
        if (PlatformUtil.isMT(platform)) {
            return cfg != null && cfg.getMtRadio() > 0 ? cfg.getMtRadio() : defaultMTRadio;
        }
        return cfg != null && cfg.getDpRadio() > 0 ? cfg.getDpRadio() : defaultDPRadio;
    }

    public void promoSimplify(DzTagVO promoTag, PriceBottomTagBuildReq req, String preTagText, boolean isMember) {
        if(promoTag == null || StringUtils.isEmpty(promoTag.getText())){
            return;
        }
        if(!PromoSimplifyUtils.hitPromoSimplify(req.getContext())){
            return;
        }
        promoTag.setText(promoTag.getText().replace("¥", ""));
        if(promoTag.getPrePic() != null && StringUtils.isNotEmpty(promoTag.getPrePic().getPicUrl())
                && StringUtils.isNotEmpty(preTagText)){
            promoTag.setMultiText(Lists.newArrayList(preTagText, promoTag.getText()));
        }
        if (isMember) {
            setMemberPromoStyle(promoTag);
        } else {
            setCommonPromoStyle(promoTag);
        }
    }

    public void setCommonPromoStyle(DzTagVO promoTag){
        promoTag.setAfterPic(buildAfterPic(COMMON_AFTER_PIC));
        promoTag.setTextColor(ColorUtils.colorFF4B10);
        promoTag.setBackground(ColorUtils.colorFFF1EC);
        promoTag.setBorderRadius(3);
        promoTag.setHasBorder(false);
    }

    public void setMemberPromoStyle(DzTagVO promoTag){
        promoTag.setAfterPic(buildAfterPic(MEMBER_AFTER_PIC));
        promoTag.setTextColor(ColorUtils.color8E3C12);
        promoTag.setBackground(ColorUtils.colorFFEDDE);
        promoTag.setBorderRadius(3);
        promoTag.setHasBorder(false);
    }

    private DzPictureComponentVO buildAfterPic(String icon) {
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO(icon, 1.1);
        dzPictureComponentVO.setPicHeight(10);
        return dzPictureComponentVO;
    }
}
