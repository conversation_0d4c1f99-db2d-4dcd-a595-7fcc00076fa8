package com.sankuai.dzviewscene.product.productdetail.activity;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.ActivityRequest;
import com.sankuai.athena.viewscene.framework.annotation.ContextBuilder;
import com.sankuai.athena.viewscene.framework.core.IContextBuilder;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@ContextBuilder(activityCode = SpuDetailActivity.CODE, name = "标品详情上下文构造器")
public class SpuDetailActivityCtxBuilder implements IContextBuilder {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public ActivityCxt build(ActivityRequest activityRequest) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivityCtxBuilder.build(com.sankuai.athena.viewscene.framework.ActivityRequest)");
        ActivityCxt ctx = initDefaultCtx(activityRequest);
        fillPlatformCtx(ctx);
        return ctx;
    }

    private ActivityCxt initDefaultCtx(ActivityRequest activityRequest) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivityCtxBuilder.initDefaultCtx(com.sankuai.athena.viewscene.framework.ActivityRequest)");
        ActivityCxt ctx = new ActivityCxt();
        ctx.setParameters(activityRequest.getParams());
        return ctx;
    }

    private void fillPlatformCtx(ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivityCtxBuilder.fillPlatformCtx(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        boolean isMT = PlatformUtil.isMT(platform);
        if (isMT) {
            initCtxForMT(ctx);
            return;
        }
        initCtxForDP(ctx);
    }

    private void initCtxForDP(ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivityCtxBuilder.initCtxForDP(com.sankuai.athena.viewscene.framework.ActivityCxt)");
    }

    private void initCtxForMT(ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivityCtxBuilder.initCtxForMT(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        long mtPoiId = ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtPoiIdL);
        int mtCityId = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.mtCityId);

        CompletableFuture<Long> dpPoiIdCf = convertPoiId(mtPoiId, VCPlatformEnum.MT.getType());
        CompletableFuture<Integer> dpCityIdCf = convertCityId(mtCityId, VCPlatformEnum.MT.getType());

        ctx.addParam(PmfConstants.Params.dpPoiIdL, dpPoiIdCf.join());
        ctx.addParam(PmfConstants.Params.dpCityId, dpCityIdCf.join());
    }

    private CompletableFuture<Integer> convertCityId(int cityId, int platform) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivityCtxBuilder.convertCityId(int,int)");
        if (cityId <= 0) {
            return CompletableFuture.completedFuture(0);
        }
        if (PlatformUtil.isMT(platform)) {
            return compositeAtomService.getDpCityIdByMt(cityId).thenApply(id -> id == null ? 0 : id);
        }
        return compositeAtomService.getMtCityIdByDp(cityId).thenApply(id -> id == null ? 0 : id);
    }

    private CompletableFuture<Long> convertPoiId(long poiId, int platform) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.productdetail.activity.SpuDetailActivityCtxBuilder.convertPoiId(long,int)");
        if (poiId <= 0) {
            return CompletableFuture.completedFuture(0L);
        }
        if (PlatformUtil.isMT(platform)) {
            return compositeAtomService.getDpByMtPoiIdL(poiId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
        }
        return compositeAtomService.getMtByDpPoiIdL(poiId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
    }
}
