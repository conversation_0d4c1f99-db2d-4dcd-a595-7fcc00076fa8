package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DataUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;

@VPointOption(name = "穿戴甲副标题",
        description = "穿戴甲副标题",
        code = "PressOnNailItemSubTitleOpt")
public class PressOnNailItemSubTitleOpt extends UnifiedShelfItemSubTitleVP<PressOnNailItemSubTitleOpt.Config> {

    @Override
    public ItemSubTitleVO computeFromOpt(ActivityCxt context, Param param, Config config) {
        if (param.getProductM() == null || CollectionUtils.isEmpty(param.getProductM().getExtAttrs()) || PressOnNailUtils.checkExclusive(context)) {
            return null;
        }
        List<StyleTextModel> tags = Lists.newArrayList();
        List<AttrM> attrs = param.getProductM().getExtAttrs();
        if (DealDetailUtils.isCanWearAtStore(attrs)) {
            tags.add(buildHighlightText("到店免费佩戴"));
        }
        String wearingNailsType = DealDetailUtils.getAttrSingleValueByAttrName(attrs, "wearingNailsType");
        if (StringUtils.isNotBlank(wearingNailsType)) {
            tags.add(buildGrayText(wearingNailsType));
        }
        String nailAdditionalItem = DealDetailUtils.getAttrSingleValueByAttrName(attrs, "nail_additional_item");
        if (StringUtils.isNotBlank(nailAdditionalItem)) {
            List<String> additionalItemList;
            if (JSONValidator.from(nailAdditionalItem).validate()) {
                additionalItemList = JSON.parseArray(nailAdditionalItem, String.class);
            }else {
                additionalItemList = DataUtils.toList(nailAdditionalItem, ",");
            }
            if(CollectionUtils.isNotEmpty(additionalItemList)) {
                tags.add(buildGrayText(additionalItemList.get(0)));
            }
        }
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        ItemSubTitleVO itemSubTitleVO = new ItemSubTitleVO();
        itemSubTitleVO.setJoinType(0);
        itemSubTitleVO.setTags(tags);
        return itemSubTitleVO;
    }

    @Data
    @VPointCfg
    public static class Config {

    }
}
