package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceremark;

import com.dianping.lion.client.Lion;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceRemarkVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

@VPointOption(name = "0元预约货架",
        description = "返回 起",
        code = "ZeroVaccinePriceRemarkOpt")
public class ZeroVaccinePriceRemarkOpt extends ItemSalePriceRemarkVP<Void> {
    /*
     * key:"retailPriceStyle"
     * value:"1"
     * 1：正常展示
     * 2：隐藏
     * 3：起价
     */
    private static final String RETAIL_PRICE_STYLE = "retailPriceStyle";
    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        //只有当retailPriceStyle=3时候才展示起
        ProductM productM = param.getProductM();
        String retailPriceStyle = getExtAttrValueByName(productM, RETAIL_PRICE_STYLE);
        if (("3").equals(retailPriceStyle)) {
            return "起";
        }
        return null;
    }

    private String getExtAttrValueByName(ProductM productM, String name) {
        if (productM == null || productM.getExtAttrs() == null) {
            return null;
        }
        return productM.getExtAttrs().stream().filter(attrM -> attrM != null && name.equals(attrM.getName()))
                .findFirst().map(AttrM::getValue).orElse(null);
    }
}
