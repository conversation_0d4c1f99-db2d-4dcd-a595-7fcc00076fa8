package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.cat.Cat;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.shop.dto.DealGroupShopSearchRequest;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderSKUDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.list.ProductListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.ap.internal.util.Collections;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/7/19 7:33 下午
 */
@VPointOption(name = "最近团单门店id取数", description = "最近团单门店id取数", code = FindDealNearestShopIdOpt.CODE)
public class FindDealNearestShopIdOpt extends PreSyncHandlerVP<FindDealNearestShopIdOpt.Config> {

    public static final String CODE = "FindDealNearestShopIdOpt";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public Map<String, Object> compute(ActivityCxt activityCxt, Param param, Config config) {
        String orderId = ParamsUtil.getStringSafely(activityCxt, ProductListActivityConstants.Params.entityId);
        CompletableFuture<Integer> dealIdCF = getDealId(orderId);
        int platform = ParamsUtil.getIntSafely(activityCxt, PmfConstants.Params.platform);
        double lat = ParamsUtil.getDoubleSafely(activityCxt, PmfConstants.Params.lat);
        double lng = ParamsUtil.getDoubleSafely(activityCxt, PmfConstants.Params.lng);
        Map<String, CompletableFuture<Object>> resultMap = Maps.newHashMap();
        //公共的
        resultMap.put(PmfConstants.Params.productIdL, CompletableFutureUtil.covert2ObjCf(dealIdCF));
        // poiMigrate
        boolean isNeedPoiMigrate = PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.FindDealNearestShopIdOpt.getScene());

        //分平台
        if (PlatformUtil.isMT(platform)) {
            int mtCityId = ParamsUtil.getIntSafely(activityCxt, PmfConstants.Params.mtCityId);
            CompletableFuture<Long> dpShopIdLCF = dealIdCF.thenCompose(dealId -> getNearestShopIdByMtDealId(dealId, mtCityId, lat, lng));
            CompletableFuture<Long> mtShopIdLCF = dpShopIdLCF.thenCompose(this::getMtShopIdByDp);
            CompletableFuture<ShopM> ctxShopMCf;
            if (isNeedPoiMigrate) {
                ctxShopMCf = dpShopIdLCF.thenCompose(this::loadShopPoiMigrate);
            } else {
                // 原逻辑
                ctxShopMCf = dpShopIdLCF.thenCompose(this::loadShop);
            }
            resultMap.put(PmfConstants.Ctx.ctxShop, CompletableFutureUtil.covert2ObjCf(ctxShopMCf));
            resultMap.put(PmfConstants.Params.dpPoiIdL, CompletableFutureUtil.covert2ObjCf(dpShopIdLCF));
            resultMap.put(PmfConstants.Params.dpPoiId, dpShopIdLCF.thenApply(NumberUtils::objToInt));
            resultMap.put(PmfConstants.Params.mtPoiIdL, CompletableFutureUtil.covert2ObjCf(mtShopIdLCF));
            resultMap.put(PmfConstants.Params.mtPoiId, mtShopIdLCF.thenApply(NumberUtils::objToInt));
            return CompletableFutureUtil.each(resultMap).join();
        }
        int dpCityId = ParamsUtil.getIntSafely(activityCxt, PmfConstants.Params.dpCityId);
        CompletableFuture<Long> dpShopIdLCF = dealIdCF.thenCompose(dealId -> getNearestShopIdByDpDealId(dealId, dpCityId, lat, lng));
        CompletableFuture<ShopM> ctxShopMCf;
        if (isNeedPoiMigrate) {
            ctxShopMCf = dpShopIdLCF.thenCompose(this::loadShopPoiMigrate);
        } else {
            // 原逻辑
            ctxShopMCf = dpShopIdLCF.thenCompose(this::loadShop);
        }
        resultMap.put(PmfConstants.Ctx.ctxShop, CompletableFutureUtil.covert2ObjCf(ctxShopMCf));
        resultMap.put(PmfConstants.Params.dpPoiIdL, CompletableFutureUtil.covert2ObjCf(dpShopIdLCF));
        resultMap.put(PmfConstants.Params.dpPoiId, dpShopIdLCF.thenApply(NumberUtils::objToInt));
        return CompletableFutureUtil.each(resultMap).join();
    }

    private CompletableFuture<Integer> getDealId(String orderId) {
        if (orderId == null) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.getUnifiedOrder(orderId).thenApply(unifiedOrderWithId -> {
            if (unifiedOrderWithId == null) {
                return null;
            }
            List<UnifiedOrderSKUDTO> unifiedOrderSKUDTOS = unifiedOrderWithId.getSkus();
            if (CollectionUtils.isEmpty(unifiedOrderSKUDTOS) || unifiedOrderSKUDTOS.get(0) == null) {
                return null;
            }
            UnifiedOrderSKUDTO unifiedOrderSKUDTO = unifiedOrderSKUDTOS.get(0);
            return Integer.valueOf(unifiedOrderSKUDTO.getSpugId());
        });
    }

    private CompletableFuture<Integer> getDpCityId(Integer mtCityId) {
        if (mtCityId == null) {
            return CompletableFuture.completedFuture(0);
        }
        return compositeAtomService.getDpCityIdByMt(mtCityId).thenApply(id -> id == null ? 0 : id);
    }


    private CompletableFuture<Long> getMtShopIdByDp(Long dpShopId) {
        if (dpShopId == null) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.getMtByDpPoiIdL(dpShopId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
    }

    private CompletableFuture<Long> getNearestShopIdByDpDealId(Integer dealId, int cityId, double lat, double lng) {
        if (dealId == null) {
            return CompletableFuture.completedFuture(null);
        }
        DealGroupShopSearchRequest request = buildDealGroupShopSearchRequest(dealId, cityId, lat, lng);
        return compositeAtomService.getDealNearestShopResult(request).thenApply(dealGroupShop -> {
            if (dealGroupShop == null) {
                return null;
            }
            return dealGroupShop.getLongShopId();
        });
    }

    private CompletableFuture<Long> getNearestShopIdByMtDealId(Integer dealId, int cityId, double lat, double lng) {
        if (dealId == null) {
            return CompletableFuture.completedFuture(null);
        }
        CompletableFuture<List<IdMapper>> idMappersCf = compositeAtomService.batchGetDealIdByMtId(Lists.newArrayList(dealId));
        CompletableFuture<Integer> dpCityIdCF = getDpCityId(cityId);
        return CompletableFuture.allOf(idMappersCf, dpCityIdCF).thenCompose(aVoid -> assembleDealAndCity2LongCf(idMappersCf.join(), dpCityIdCF.join(), lat, lng));
    }

    private CompletableFuture<Long> assembleDealAndCity2LongCf(List<IdMapper> idMappers, Integer dpCityId, double lat, double lng) {
        if (CollectionUtils.isEmpty(idMappers)) {
            return CompletableFuture.completedFuture(null);
        }
        IdMapper idMapper = CollectUtils.firstValue(idMappers);
        if (idMapper == null) {
            return CompletableFuture.completedFuture(null);
        }
        DealGroupShopSearchRequest request = buildDealGroupShopSearchRequest(idMapper.getDpDealGroupID(), dpCityId, lat, lng);
        return compositeAtomService.getDealNearestShopResult(request).thenApply(dealGroupShop -> {
            if (Objects.isNull(dealGroupShop)) {
                return null;
            }
            return dealGroupShop.getLongShopId();
        });
    }

    private DealGroupShopSearchRequest buildDealGroupShopSearchRequest(Integer dealId, int cityId, double lat, double lng) {
        if (dealId == null) {
            return null;
        }
        DealGroupShopSearchRequest dealGroupShopSearchRequest = new DealGroupShopSearchRequest();
        dealGroupShopSearchRequest.setCityIds(Lists.newArrayList(cityId));
        dealGroupShopSearchRequest.setDealGroupIds(Lists.newArrayList(dealId));
        dealGroupShopSearchRequest.setGoogleLat(lat);
        dealGroupShopSearchRequest.setGoogleLng(lng);
        return dealGroupShopSearchRequest;
    }

    private CompletableFuture<ShopM> loadShop(Long dpShopId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.options.FindDealNearestShopIdOpt.loadShop(java.lang.Long)");
        if (dpShopId == null) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.loadShop(dpShopId.intValue()).thenApply(shopDTO -> {
            if (shopDTO == null) {
                return null;
            }
            ShopM shopM = new ShopM();
            shopM.setShopId(shopDTO.getShopId() == null ? 0 : shopDTO.getShopId());
            shopM.setLongShopId(shopM.getShopId());
            shopM.setShopUuid(shopDTO.getShopUuid() == null ? "" : shopDTO.getShopUuid());
            shopM.setShopName(shopDTO.getShopName());
            shopM.setShopType(shopDTO.getShopType() == null ? 0 : shopDTO.getShopType());
            shopM.setCategory(shopDTO.getMainCategoryId() == null ? 0 : shopDTO.getMainCategoryId());
            shopM.setLat(shopDTO.getGlat() == null ? 0 : shopDTO.getGlat());
            shopM.setLng(shopDTO.getGlng() == null ? 0 : shopDTO.getGlng());
            shopM.setCityId(shopDTO.getCityId() == null ? 0 : shopDTO.getCityId());
            shopM.setStatus(shopDTO.getPower() == null ? -1 : shopDTO.getPower());
            shopM.setBusinessHours(shopDTO.getBusinessHours());
            return shopM;
        });
    }

    private CompletableFuture<ShopM> loadShopPoiMigrate(Long dpShopId) {
        if (dpShopId == null) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(Lists.newArrayList(dpShopId))).thenApply(dpPoiDTOList -> {
            if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                return null;
            }
            DpPoiDTO dpPoiDTO = Collections.first(dpPoiDTOList);
            ShopM shopM = new ShopM();
            shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId().intValue());
            shopM.setLongShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
            shopM.setShopUuid(dpPoiDTO.getUuid() == null ? "" : dpPoiDTO.getUuid());
            shopM.setShopName(dpPoiDTO.getShopName());
            shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
            shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
            shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
            shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
            shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
            shopM.setStatus(dpPoiDTO.getPower() == null ? -1 : dpPoiDTO.getPower());
            shopM.setBusinessHours(dpPoiDTO.getBusinessHours());
            return shopM;
        });
    }

    private DpPoiRequest buildDpPoiRequest(List<Long> dpPoiIds) {
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }


    @Data
    @VPointCfg
    public static class Config {
    }
}