package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.filterlist.option.factory.MassageStrategy;
import com.sankuai.dzviewscene.product.filterlist.utils.SkuItemUtils;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.math.NumberUtils;


/**
 * @author: created by hang.yu on 2024/4/2 15:01
 */
@VPointOption(name = "足疗相似团购筛选列表过滤与排序",
        description = "足疗相似团购筛选列表过滤与排序",
        code = "MassageSimilarListOpt")
public class MassageSimilarListOpt extends AbstractTimesDealListOpt {

    @Override
    protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
        String pageSource = context.getParam(ShelfActivityConstants.Params.pageSource);

        if (GUESS.equals(pageSource)) {
            return sortMassageList(list);
        } else if (SHELF.equals(pageSource)) {
            String currentServiceType = DealDetailUtils.getAttrSingleValueByAttrName(currentProduct.getExtAttrs(), "service_type");
            return sortMassageList(list.stream().filter(productM -> compareServiceType(productM, currentServiceType)).collect(Collectors.toList()));
        }
        return Lists.newArrayList();
    }

    private List<ProductM> sortMassageList(List<ProductM> list) {
        return list.stream()
                .sorted(Comparator.comparing(this::getServiceDuration).thenComparing(this::getSale, Comparator.reverseOrder()))
                .collect(Collectors.toList());
    }

    private int getServiceDuration(ProductM product) {
        SkuItemDto skuItem = SkuItemUtils.getSkuItem(product);
        String serviceDuration = DealStructUtils.getAttrValueByAttrKeyOrElseEmpty(skuItem, MassageStrategy.SERVICE_DURATION_INT);
        return NumberUtils.toInt(serviceDuration);
    }

    private int getSale(ProductM product) {
        return product.getSale().getSale();
    }

}
