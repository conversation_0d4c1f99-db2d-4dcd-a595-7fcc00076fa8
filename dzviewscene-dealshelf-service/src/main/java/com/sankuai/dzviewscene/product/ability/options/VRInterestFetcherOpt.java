package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreAsyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.CtxUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateRequest;
import com.sankuai.interest.core.thrift.remote.dto.InterestCalculateResponse;
import com.sankuai.interest.core.thrift.remote.enums.InterestBizTypeEnum;
import lombok.Data;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

@VPointOption(name = "查询VR权益", description = "前置查询门店是否拥有VR权益，用于货架展示VR入口", code = VRInterestFetcherOpt.CODE)
public class VRInterestFetcherOpt extends PreAsyncHandlerVP<VRInterestFetcherOpt.Config> {

    public static final String CODE = "VRInterestFetcherOpt";

    @Resource
    private CompositeAtomService compositeAtomService;

    /**
     * VR权益code
     */
    private final Long VR_INTEREST_CODE = 1000616L;

    /**
     * 默认权益场景id
     */
    private final int DEFAULT_INTEREST_SCENE_ID = 1;

    @Override
    public CompletableFuture<Object> compute(ActivityCxt activityCxt, Param param, Config config) {
        InterestCalculateRequest request = new InterestCalculateRequest();
        request.setInterestCode(VR_INTEREST_CODE);
        request.setInterestSceneId(DEFAULT_INTEREST_SCENE_ID);
        request.setInterestBizType(InterestBizTypeEnum.SHOP.getType());
        CompletableFuture<Void> convertF = (CompletableFuture<Void>)activityCxt.getParameters().get(CtxUtils.SHELF_DEFAULT_CONVERT);
        if (convertF == null) {
            return CompletableFuture.completedFuture(false);
        }
        convertF.join();
        Long mtPoiId = ParamsUtil.getLongSafely(activityCxt, PmfConstants.Params.mtPoiIdL);
        request.setInterestBizId(mtPoiId);
        return compositeAtomService.interestCalculate(request).thenApply(this::hasVR);
    }

    private Boolean hasVR(InterestCalculateResponse response) {
        return response != null && Boolean.TRUE.equals(response.getResult());
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
