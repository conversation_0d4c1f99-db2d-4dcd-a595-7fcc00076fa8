package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceremark;


import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceRemarkVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterOptionM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "种植牙价格后缀",
        description = "根据不同筛选tab展示不同的价格后缀描述",
        code = "ImplantToothPriceRemarkOpt")
public class ImplantToothPriceRemarkOpt extends ItemSalePriceRemarkVP<ImplantToothPriceRemarkOpt.Config> {

    private static final int IMPLANT_TEETH_FILTER_ID = 200127909;

    private static final String FILTER_PARAM_SEPARATOR = ",";
    private static final String FILTER_PARAM_NAV_SEPARATOR = "$";

    private static final String TOOTH_COUNT_IDENTITY = "tooth_count";


    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param == null || param.getFilterId() != IMPLANT_TEETH_FILTER_ID) {
            return null;
        }
        String identityName = getIdentityName(context, param);
        return getShowName(identityName, config);
    }

    private FilterBtnM getImplantTeethFilter(Param param) {
        if (param.getFilterM() == null) {
            return null;
        }
        List<FilterBtnM> filters = param.getFilterM().getFilters();
        if (CollectionUtils.isEmpty(filters)) {
            return null;
        }
        return filters.stream()
                .filter(filterBtn -> filterBtn.getFilterId() == IMPLANT_TEETH_FILTER_ID)
                .findFirst()
                .orElse(null);
    }

    private FilterOptionM getFirstLevelOption(FilterBtnM implantTeethFilter) {
        FilterOptionM filterOptionTree = implantTeethFilter.getFilterOptionTree();
        if (filterOptionTree == null || CollectionUtils.isEmpty(filterOptionTree.getChildren())) {
            return null;
        }
        Optional<FilterOptionM> firstLevelOption = filterOptionTree.getChildren().stream()
                .filter(option -> TOOTH_COUNT_IDENTITY.equals(option.getIdentityName()))
                .findFirst();

        if (firstLevelOption.isPresent()) {
            FilterOptionM filterOptionM = firstLevelOption.get();
            return CollectionUtils.isEmpty(filterOptionM.getChildren()) ? null : filterOptionM;
        }
        return null;
    }

    private String getIdentityName(ActivityCxt context, Param param) {
        String filterParams = context.getParam(ShelfActivityConstants.Params.selectedFilterParams);
        if (StringUtils.isEmpty(filterParams)) {
            return getAnchoringIdentityName(param);
        }
        List<String> filterParamList = Arrays.stream(filterParams.split(FILTER_PARAM_SEPARATOR)).distinct().collect(Collectors.toList());

        List<String> filterIdentityNameList = getFilterIdentityNameList(filterParamList);

        if (CollectionUtils.isNotEmpty(filterIdentityNameList)) {
            return filterIdentityNameList.get(0);
        }
        return null;
    }

    private String getAnchoringIdentityName(Param param) {
        //首屏接口判断是否有锚定种植牙identityName
        FilterBtnM implantTeethFilter = getImplantTeethFilter(param);
        if (implantTeethFilter == null) {
            return null;
        }
        FilterOptionM firstLevelOption = getFirstLevelOption(implantTeethFilter);
        if (firstLevelOption == null) {
            return null;
        }
        return firstLevelOption.getChildren().stream()
                .filter(option -> StringUtils.isNotEmpty(option.getIdentityName())
                        && option.isSelected())
                .findFirst()
                .map(FilterOptionM::getIdentityName)
                .orElse(null);
    }

    private List<String> getFilterIdentityNameList(List<String> filterParamList) {
        return filterParamList.stream()
                .map(this::getFilterIdentityName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private String getFilterIdentityName(String filterParam) {
        String identityName = StringUtils.substringAfter(filterParam, FILTER_PARAM_NAV_SEPARATOR);
        if (StringUtils.isEmpty(identityName)) {
            return null;
        }
        return identityName;
    }

    private String getShowName(String identityName, Config config) {
        if (StringUtils.isEmpty(identityName) || config == null
                || MapUtils.isEmpty(config.getIdentityName2ShowNameMap())) {
            return null;
        }
        return config.getIdentityName2ShowNameMap().get(identityName);
    }

    @VPointCfg
    @Data
    public static class Config {

        private Map<String, String> identityName2ShowNameMap;

    }
}
