package com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * created by zhang<PERSON>yuan04 in 2022/1/4
 */
@VPoint(name = "Sku元素优先级", description = "构造Sku元素优先级信息，数字越小优先级越高", code = SkuItemPriorityVP.CODE, ability = DealDetailSkuGroupsBuilder.CODE)
public abstract class SkuItemPriorityVP<C> extends PmfVPoint<Integer, SkuItemPriorityVP.Param, C> {

    public static final String CODE = "SkuItemPriorityVP";


    @Data
    @Builder
    @VPointParam
    public static class Param {

        /**
         * 是否必选
         */
        private boolean isMust;

        /**
         * 分组标题
         */
        private String setTitle;

        /**
         * Sku信息
         */
        private SkuItemDto skuItemDto;

        /**
         * Sku category信息
         */
        private List<ProductSkuCategoryModel> productCategories;
    }
}
