package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/27 9:17 下午
 */
@VPointOption(name = "爱车-美容-打蜡团购详情sku货标题变化点", description = "爱车-美容-打蜡团购详情sku货标题变化点",code = CarBeautyWaxingSkuItemTitleVPO.CODE, isDefault = false)
public class CarBeautyWaxingSkuItemTitleVPO extends SkuItemTitleVP<CarBeautyWaxingSkuItemTitleVPO.Config> {

    public static final String CODE = "CarBeautyWaxingSkuItemTitleVPO";

    private static final String BASE_SERVICE_CONTENT_ATTR_NAME = "basicServiceContent";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param == null || param.getSkuItemDto() == null || CollectionUtils.isEmpty(param.getSkuItemDto().getAttrItems())) {
            return null;
        }
        List<SkuAttrItemDto> attrItems = param.getSkuItemDto().getAttrItems();
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, BASE_SERVICE_CONTENT_ATTR_NAME);
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
