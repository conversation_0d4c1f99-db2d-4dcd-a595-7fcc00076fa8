package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
public class PreSalePromoTagBuildStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

    private static final String ICON_TAG = "超值预售";

    @Override
    public String getName() {
        return "预售";
    }

    @Override
    public DzTagVO buildTag(PriceBottomTagBuildReq req) {
        ProductPromoPriceM productPromoPriceM = PriceUtils.getUserHasPromoPrice(req.getProductM(),req.getCardM());
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(Lists.newArrayList(productPromoPriceM));
        if (MapUtils.isEmpty(promoPriceMMap)) {
            return null;
        }
        ProductPromoPriceM preSalePromoPrice = null;
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_Member.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_Member.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_NewUser.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_NewUser.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_MerchantMember.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_MerchantMember.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale.getCode())) {
            preSalePromoPrice = promoPriceMMap.get((PromoTagTypeEnum.PreSale.getCode()));
        }
        if (Objects.isNull(preSalePromoPrice)) {
            return null;
        }
        DzTagVO dzTagVO = buildPromoTagVo(req.getPlatform(), preSalePromoPrice);
        dzTagVO.setPrePic(new DzPictureComponentVO(preSalePromoPrice.getIcon(), getPicRadio(req.getPlatform(), req.getCfg())));
        dzTagVO.setAfterPic(buildPromoAfterPic(req.getPlatform()));
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(preSalePromoPrice, req.getPopType()));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(req.getPlatform()) ? 3 : 1);
        dzTagVO.setText(dzTagVO.getName());
        promoSimplify(dzTagVO, req, ICON_TAG, false);
        return dzTagVO;
    }

    private DzTagVO buildPromoTagVo(int platform, ProductPromoPriceM productPromoPriceM) {
        return DzPromoUtils.buildBasicDzTagVOWithColor(platform, productPromoPriceM.getPromoTag(),
                ColorUtils.colorFF5500, ColorUtils.colorFFD6BE, null, ColorUtils.colorFF6633, ColorUtils.colorFFF2EE);
    }
}
