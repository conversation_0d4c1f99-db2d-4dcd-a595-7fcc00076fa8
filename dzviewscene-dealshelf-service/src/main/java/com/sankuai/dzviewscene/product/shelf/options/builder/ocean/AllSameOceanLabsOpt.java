package com.sankuai.dzviewscene.product.shelf.options.builder.ocean;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.ocean.vp.OceanLabsVP;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 后续可以考虑基于动态表达式计算出来
 *
 * <AUTHOR>
 * @date 2022/3/17
 */
@VPointOption(name = "通用-labs 埋点",
        description = "按需填写需要埋点的字段和数据，缺点是打的都一样）",
        code = "AllSameOceanLabsOpt",
        isDefault = true)
public class AllSameOceanLabsOpt extends OceanLabsVP<AllSameOceanLabsOpt.Config> {
    @Override
    public Map<String, String> compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getFields())) {
            return new HashMap<>();
        }
        Map<String, Object> labsMap = new HashMap<>();
        if (config.isCategoryId()) {
            labsMap.put("cat_id", getCategoryId(param));
        }
        if (config.isPoiId()) {
            labsMap.put("poi_id", getPoiId(param));
        }
        if(MapUtils.isNotEmpty(config.getAppendLabsMap())){
            labsMap.putAll(config.getAppendLabsMap());
        }
        return getResultLabsMap(JsonCodec.encode(labsMap), config.getFields());
    }

    private Map<String, String> getResultLabsMap(String labs, List<String> fields) {
        Map<String, String> resultLabsMap = new HashMap<>(fields.size());
        for (String field : fields) {
            resultLabsMap.put(field, labs);
        }
        return resultLabsMap;
    }

    @VPointCfg
    @Data
    public static class Config {
        // cat_id
        private boolean categoryId;
        // poi_id
        private boolean poiId;
        /**
         * ShelfOceanVO 中所需要打点的字段名
         * {@link com.sankuai.dzviewscene.product.shelf.ability.builder.ocean.ShelfOceanFieldEnum}
         */
        private List<String> fields;
        
        private Map<String,String> appendLabsMap;
    }
}
