package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempurchase;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPurchaseVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;


@VPointOption(name = "空白商品购买信息",
        description = "空白商品购买信息，一般用于AB",
        code = "NullItemPurchaseOpt")
public class NullItemPurchaseOpt extends ItemPurchaseVP<Void> {

    @Override
    public RichLabelVO compute(ActivityCxt activityCxt, Param param, Void config) {
        return null;
    }
}
