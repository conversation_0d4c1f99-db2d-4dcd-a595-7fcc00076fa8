package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

import java.util.Optional;

/**
 * @author: created by hang.yu on 2024/3/4 14:34
 */
@VPointOption(name = "默认商品筛选列表名称展示Opt",
        description = "默认商品筛选列表名称展示Opt",
        code = "DefaultProductTitleOpt",
        isDefault = true)
public class DefaultProductTitleOpt extends ProductTitleVP<Void> {

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        return Optional.ofNullable(param).map(Param::getProductM).map(ProductM::getTitle).orElse(null);
    }

}
