package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPromosVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@VPointOption(name = "折扣标签，支持追加文案",
        description = "折扣标签，支持追加文案",
        code = "ConfigDiscountPromoOpt")
public class ConfigDiscountProductPromoOpt extends ProductPromosVP<ConfigDiscountProductPromoOpt.Config> {

    private static final BigDecimal TOP_LIMIT = new BigDecimal("9.9");

    private static final BigDecimal BOTTOM_LIMIT = new BigDecimal("0.1");
    public static final String DISCOUNT_FORMAT = "%s折";

    @Override
    public List<DzPromoVO> compute(ActivityCxt context, Param param, Config config) {
        BigDecimal salePrice = getSalePrice(param);
        BigDecimal marketOrBasicPrice = getMarketOrBasicPrice(param, config);
        if (Objects.isNull(salePrice) || Objects.isNull(marketOrBasicPrice)
                || Objects.equals(salePrice, BigDecimal.ZERO) || Objects.equals(marketOrBasicPrice, BigDecimal.ZERO)) {
            if (!config.isShowAppendValueWhenNoDiscount()) {
                return new ArrayList<>();
            }
            return justReturnAppendValue(param, config);
        }
        // 取整策略(不填就是四舍五入)
        RoundingMode roundingMode = getRoundingMode(config);
        BigDecimal discountVal = getDiscountVal(salePrice, marketOrBasicPrice, config, roundingMode);
        if (discountVal.compareTo(getTopLimit(config)) > 0) {
            if (!config.isShowAppendValueWhenNoDiscount()) {
                return new ArrayList<>();
            }
            return justReturnAppendValue(param, config);
        }
        String tagValue = getTagStr(discountVal, param, config);
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(tagValue);
        return Lists.newArrayList(dzPromoVO);
    }

    private String getTagStr(BigDecimal discountVal, Param param, Config config) {
        String discountStr = String.format(DISCOUNT_FORMAT, discountVal);
        String appendValue = getAppendValue(param, config);
        if (StringUtils.isAnyBlank(appendValue, config.getAppendTemplate())) {
            return discountStr;
        }
        return String.format(config.getAppendTemplate(), discountStr, appendValue);
    }

    @NotNull
    private RoundingMode getRoundingMode(Config config) {
        RoundingMode roundingMode = RoundingMode.HALF_UP;
        if (Objects.nonNull(config.getRoundingType()) && config.getRoundingType().equals(1)) {
            roundingMode = RoundingMode.UP;
        }
        return roundingMode;
    }

    private List<DzPromoVO> justReturnAppendValue(Param param, Config config) {
        String appendValue = getAppendValue(param, config);
        if (StringUtils.isBlank(appendValue)) {
            return new ArrayList<>();
        }
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(appendValue);
        return Lists.newArrayList(dzPromoVO);
    }

    private String getAppendValue(Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getAppendAttrKeys())) {
            return null;
        }
        for (String attrKey : config.getAppendAttrKeys()) {
            // 优先使用简版的attr
            String attrValue = param.getProductM().getAttr(attrKey);
            if (StringUtils.isNotBlank(attrValue)) {
                return attrValue;
            }
            // 然后尝试获取带类型的attr（来自标品主题）
            attrValue = param.getProductM().getObjAttr(attrKey);
            if (StringUtils.isNotBlank(attrValue)) {
                return attrValue;
            }
        }
        return null;
    }

    private BigDecimal getTopLimit(Config config) {
        if (config == null || BigDecimal.valueOf(config.getMaxDiscount()).compareTo(BigDecimal.ZERO) <= 0) {
            return TOP_LIMIT;
        }
        return BigDecimal.valueOf(config.getMaxDiscount());
    }


    private BigDecimal getSalePrice(Param param) {
        if (StringUtils.isEmpty(param.getSalePrice())) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(param.getSalePrice());
    }

    private BigDecimal getMarketOrBasicPrice(Param param, Config config) {
        if (config != null && config.useBasicPrice) {
            return param.getProductM().getBasePrice();
        }
        if (StringUtils.isEmpty(param.getProductM().getMarketPrice())) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(param.getProductM().getMarketPrice());
    }

    private BigDecimal getDiscountVal(BigDecimal salePrice, BigDecimal marketOrBasicPrice, Config config, RoundingMode roundingMode) {
        //原始值
        BigDecimal discountVal = salePrice.divide(marketOrBasicPrice, 3, RoundingMode.DOWN)
                .multiply(BigDecimal.valueOf(10))
                .setScale(1, roundingMode);
        BigDecimal finalDiscountVal = discountVal.compareTo(BOTTOM_LIMIT) > 0 ? discountVal : BOTTOM_LIMIT;
        if (config != null && Boolean.TRUE.equals(config.needRounding)) {
            finalDiscountVal = finalDiscountVal.stripTrailingZeros();
        }
        return finalDiscountVal;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 最大折扣
         */
        private double maxDiscount;
        /**
         * 是否使用基础价格做折扣计算，默认false：使用门市价，true:使用基础价格（没有减任何优惠）
         */
        private boolean useBasicPrice;

        /**
         * 是否整数取整，例如7.0折取整为7折
         */
        private boolean needRounding;

        /**
         * 取整策略，不填 就是四舍五入，1是向上取整
         */
        private Integer roundingType;

        /**
         * 当没有折扣时是否显示追加信息，默认是true，显示追加信息
         */
        private boolean showAppendValueWhenNoDiscount = true;

        /**
         * 追加文案模板
         */
        private String appendTemplate = "%s|%s";

        /**
         * 追加的文案AttrKey，按顺序取有值的第一个
         */
        private List<String> appendAttrKeys;
    }

}
