package com.sankuai.dzviewscene.product.shelf.ability.fetcher.card;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.FindCardHoldStatusReqDTO;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.unifiedshelf.activity.UnifiedShelfActivity;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/4/13
 */
@Ability(code = CardFetcher.CODE,
        name = "Model-卡数据查询",
        description = "查询卡数据，包括持卡状态等。构造 <CardM> ",
        activities = {DealShelfActivity.CODE, DealFilterListActivity.CODE, UnifiedShelfActivity.CODE}
)
public class CardFetcher extends PmfAbility<CardM, CardFetcher.Request, CardFetcher.Config> {

    public static final String CODE = "CardFetcher";

    @Autowired
    private AtomFacadeService facadeService;

    @Override
    public CompletableFuture<CardM> build(ActivityCxt ctx, Request request, Config config) {
        CompletableFuture<CardHoldStatusDTO> cardHoldStatusFuture = invokeRemoteGetCardHoldStatus(request);
        return cardHoldStatusFuture.thenApply(cardHoldStatusDTO -> {
            CardM cardM = new CardM();
            //填充店铺与用户的持卡状态
            paddingUserAndShopCardHoldStatus(cardM, cardHoldStatusDTO);
            return cardM;
        });
    }

    private void paddingUserAndShopCardHoldStatus(CardM cardM, CardHoldStatusDTO cardHoldStatusDTO) {
        if (cardHoldStatusDTO == null) {
            return;
        }
        cardM.setShopCardList(cardHoldStatusDTO.getShopHasCardTypeList());
        cardM.setUserCardList(cardHoldStatusDTO.getUserHoldCardTypeList());
    }

    private CompletableFuture<CardHoldStatusDTO> invokeRemoteGetCardHoldStatus(Request request) {
        FindCardHoldStatusReqDTO remoteReq = new FindCardHoldStatusReqDTO();
        if (PlatformUtil.isMT(request.getPlatform())) {
            remoteReq.setShopId(request.getMtPoiId());
            remoteReq.setLongShopId(PoiIdUtil.getMtPoiIdL(request));
            //美团
            remoteReq.setPlatform(2);
            remoteReq.setUserId(request.getMtUserId());
        } else {
            remoteReq.setShopId(request.getDpPoiId());
            remoteReq.setLongShopId(PoiIdUtil.getDpPoiIdL(request));
            //点评
            remoteReq.setPlatform(1);
            remoteReq.setUserId(request.getDpUserId());
        }
        return facadeService.getShopAndUserCardHoldStatus(remoteReq);
    }

    @AbilityCfg
    @Data
    public static class Config {
    }

    @AbilityRequest
    @Data
    public static class Request {
        /**
         * 平台
         */
        private int platform;

        private int dpPoiId;
        private long dpPoiIdL;

        private int mtPoiId;
        private long mtPoiIdL;

        private long dpUserId;

        private long mtUserId;
    }
}

