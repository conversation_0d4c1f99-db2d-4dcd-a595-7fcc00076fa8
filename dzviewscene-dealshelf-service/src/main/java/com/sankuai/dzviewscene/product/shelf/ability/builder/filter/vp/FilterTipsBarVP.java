package com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.TipsBarVO;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@VPoint(name = "筛选按钮提示框", description = "筛选按钮下的提示框，大多都没有", code = FilterTipsBarVP.CODE, ability = FilterBuilder.CODE)
public abstract class FilterTipsBarVP<T> extends PmfVPoint<TipsBarVO, FilterTipsBarVP.Param, T>{
    public static final String CODE = "FilterTipsBarVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private long filterId;
    }
}
