package com.sankuai.dzviewscene.product.shelf.ability.ocean.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.ocean.ProductShelfOceanBuilder;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * created by z<PERSON><PERSON>yuan04 in 2021/8/26
 */
@VPoint(name = "货架弹窗打点配置", description = "专用于弹窗的打点",code = PopLabOceanVP.CODE, ability = ProductShelfOceanBuilder.CODE)
public abstract class PopLabOceanVP<T> extends PmfVPoint<Map<String,String>, PopLabOceanVP.Param, T> {

    public static final String CODE = "PopLabOceanVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private int categoryId;
    }
}
