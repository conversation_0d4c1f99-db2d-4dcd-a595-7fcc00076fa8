package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.gmkt.activ.api.enums.ExposureActivityTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemActivityVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.CountdownLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
public class RainbowSecKillUtils {

    public static final String RainbowSecKillSource = "RainbowSecKillSource";

    /**
     * 注：这块原逻辑有坑解释下，彩虹营销接口的 A 级大促对应的枚举叫秒杀，但实际上真正的秒杀是通过秒杀接口查的，主题层也赋值了秒杀的枚举，导致现在区分不了秒杀和A级大促。
     * 当前使用秒杀枚举 + 秒杀活动扩展的值为 true 判断秒杀
     *
     * @param productM
     * @return 是否彩虹秒杀团单(需判断是否有立减 ， 这块逻辑营销后续会收)，true - 是
     */
    @Deprecated
    public static boolean isRainbowSecKillDeal(ProductM productM) {
        return isRainbowSecKillDealFromRainbow(productM);
    }

    @Deprecated
    public static boolean isRainbowSecKillActivity(ProductActivityM activityM) {
        return isRainbowSecKillActivityFromRainbow(activityM);
    }


    public static CountdownLabelVO getSecKillCountdownLabelWithTimePreStr(ProductM productM, String timePreStr) {
        if (!isRainbowSecKillDeal(productM)) {
            return null;
        }
        ProductActivityM productActivityM = productM.getActivities().stream().filter(RainbowSecKillUtils::isRainbowSecKillActivity).findAny().orElse(null);
        if (productActivityM == null) {
            return null;
        }
        CountdownLabelVO countdownLabelVO = new CountdownLabelVO();
        countdownLabelVO.setTimePrefix(timePreStr);
        //ms
        countdownLabelVO.setTimestamp(productActivityM.getRemainingTime() * 1000);
        return countdownLabelVO;
    }

    public static ShelfItemActivityVO getSecKillShelfItemActivityVOWithTimePreStr(ProductM productM, String timePreStr) {
        if (!isRainbowSecKillDeal(productM)) {
            return null;
        }
        ProductActivityM productActivityM = productM.getActivities().stream().filter(RainbowSecKillUtils::isRainbowSecKillActivity).findAny().orElse(null);
        if (productActivityM == null) {
            return null;
        }
        ShelfItemActivityVO countdownLabelVO = new ShelfItemActivityVO();
        countdownLabelVO.setPreText(timePreStr);
        //ms
        countdownLabelVO.setActivityEndTime(productActivityM.getRemainingTime() * 1000);
        return countdownLabelVO;
    }


    /**
     * @param productM
     * @return 是否彩虹秒杀团单(且需要来自彩虹活动不能是曝光等活动)，true - 是
     */
    public static boolean isRainbowSecKillDealFromRainbow(ProductM productM) {
        if (CollectionUtils.isEmpty(productM.getActivities())) {
            return false;
        }
        return ShelfPromoUtils.hasAnyDirectPromo(productM.getPromoPrices()) && productM.getActivities().stream().anyMatch(RainbowSecKillUtils::isRainbowSecKillActivityFromRainbow);
    }

    public static boolean isRainbowSecKillActivityFromRainbow(ProductActivityM activityM) {
        if (activityM == null || activityM.getShelfActivityType() == null) {
            return false;
        }
        if (activityM.getShelfActivityType() == ExposureActivityTypeEnum.COST_EFFECTIVE_SECKILL.getType()) {
            return true;
        }
        return activityM.getShelfActivityType() == ExposureActivityTypeEnum.SEC_KILL.getType() && MapUtils.isNotEmpty(activityM.getActivityExtraAttrs()) && Objects.equals(activityM.getActivityExtraAttrs().get(RainbowSecKillSource), true);
    }

    /**
     * @param productM
     * @return 彩虹秒杀倒计时(且需要来自彩虹活动不能是曝光等活动)
     */
    public static CountdownLabelVO getSecKillCountdownLabelFromRainbow(ProductM productM) {
        if (!isRainbowSecKillDealFromRainbow(productM)) {
            return null;
        }
        ProductActivityM productActivityM = productM.getActivities().stream().filter(RainbowSecKillUtils::isRainbowSecKillActivityFromRainbow).findAny().orElse(null);
        if (productActivityM == null) {
            return null;
        }
        CountdownLabelVO countdownLabelVO = new CountdownLabelVO();
        countdownLabelVO.setTimePrefix("仅剩");
        //ms
        countdownLabelVO.setTimestamp(productActivityM.getRemainingTime() * 1000);
        return countdownLabelVO;
    }
}
