package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/11
 */
@VPointOption(name = "默认-商品-价格下方标签",
        description = "返回「返券」描述",
        code = "DefaultItemPriceBottomTagsOpt",
        isDefault = true)
public class DefaultItemPriceBottomTagsOpt extends ItemPriceBottomTagsVP<Void> {

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Void config) {
        return buildDefCouponTags(param.getProductM());
    }
}
