package com.sankuai.dzviewscene.product.shelf.activity.deal;

import com.sankuai.athena.viewscene.framework.annotation.Activity;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfActivity;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;

/**
 * created by zhangzhiyuan04 in 2021/8/18
 */
@Activity(name = "团购货架活动",
        code = DealShelfActivity.CODE,
        description = "团购标准货架活动",
        basePackage = "com.sankuai.dzviewscene.product.shelf")
public class DealShelfActivity extends PmfActivity<DzShelfResponseVO> {

    public static final String CODE = "activity_deal_shelf";
}
