package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.utils.PlayActivityUtil;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategy;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy.ProductTagStrategyFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.TextStyleEnum;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@VPointOption(name = "默认-富文本副标题", description = "返回团单标签副标题", code = "UnifiedShelfItemCommonSubTitleOpt", isDefault = true)
public class UnifiedShelfItemCommonSubTitleOpt extends UnifiedShelfItemSubTitleVP<UnifiedShelfItemCommonSubTitleOpt.Config> {

    private static final String IS_FANGXIN_CHANGE = "isFangxinChange";

    private static final String ATTR_DEAL_STRUCT_DETAIL = "attr_deal_struct_detail";

    private static final Integer PRODUCT_CATEGORY = 2200073;

    private static final String SHI_YONG_WEN_TI = "shiyongwenti";

    @Autowired
    private ProductTagStrategyFactory productTagStrategyFactory;

    @Override
    public ItemSubTitleVO computeFromOpt(ActivityCxt context, Param param, Config config) {
        if (config.isForceNull()) {
            return null;
        }
        List<String> productTags = getProductTags(context, param.getProductM(), config);
        ItemSubTitleVO itemSubTitleVO = build4Default(productTags);
        if (config.isEnableAfterPay() && ProductMAttrUtils.timeCardIsShowAfterPayTag(param.getProductM(),context,config.getAfterPayExps())) {
            StyleTextModel afterPayTag = buildPreAfterPayTag(config);
            itemSubTitleVO = itemSubTitleVO == null ? new ItemSubTitleVO() : itemSubTitleVO;
            itemSubTitleVO.setJoinType(0);
            List<StyleTextModel> tags = itemSubTitleVO.getTags() == null ? new ArrayList<>() : itemSubTitleVO.getTags();
            tags.add(0, afterPayTag);
            itemSubTitleVO.setTags(tags);
        }
        tryQueryHighLight(context, itemSubTitleVO, config.isDisableHighlight());
        return itemSubTitleVO;
    }

    private List<String> getProductTags(ActivityCxt context, ProductM productM, Config config){
        List<String> result = Lists.newArrayList();
        //策略副标题
        String strategy = ShopCategoryConfigUtils.getHitConfig(context, config.getBackCategory2Strategy(), String.class);
        if(StringUtils.isNotBlank(strategy)){
            ProductTagStrategy buildStrategy = productTagStrategyFactory.getProductTagStrategy(strategy);
            if (Objects.nonNull(buildStrategy)) {
                ProductTagBuildReq req = buildRequest(productM);
                List<String> build = buildStrategy.build(req);
                if (CollectionUtils.isNotEmpty(build)) {
                    result.addAll(build);
                    return result;
                }
            }
        }
        //静态副标题
        if (StringUtils.isNotBlank(config.getStaticSubTitle())) {
            return Lists.newArrayList(config.getStaticSubTitle());
        }
        //美团放心改
        if (isFangxinChange(productM)) {
            return Lists.newArrayList(getSKUApplicableIssues(productM));
        }
        //预售单逻辑
        if (PreSaleUtils.isPreSaleDeal(productM)) {
            String preSaleDate = PreSaleUtils.getPreSaleDateProductTag(productM);
            if(StringUtils.isNotBlank(preSaleDate)){
                return Lists.newArrayList(preSaleDate);
            }
        }
        //爆品副标题
        if (config.isEnableHotSpuTag() && ProductMAttrUtils.hotSpu(productM)) {
            List<String> hotSpuTags = ProductMAttrUtils.getHotSpuSpecialProductTags(productM);
            if (CollectionUtils.isNotEmpty(hotSpuTags)) {
                result.addAll(hotSpuTags);
                return result;
            }
        }
        //通用副标题
        if(CollectionUtils.isNotEmpty(productM.getProductTags())){
            result.addAll(productM.getProductTags());
        }
        return result;
    }

    private String getSKUApplicableIssues(ProductM productM) {
        List<SkuItemDto> allSkus = DealStructUtils.getTotalSkuItem(productM.getAttr(ATTR_DEAL_STRUCT_DETAIL));
        if (CollectionUtils.isEmpty(allSkus)) {
            return null;
        }
        return allSkus.stream()
                .filter(sku -> PRODUCT_CATEGORY == sku.getProductCategory())
                .findFirst()
                .map(this::extractApplicableIssues)
                .orElse(null);
    }

    private String extractApplicableIssues(SkuItemDto skuItemDto) {
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        return skuItemDto.getAttrItems().stream()
                .filter(attr -> SHI_YONG_WEN_TI.equals(attr.getAttrName()))
                .findFirst()
                .map(SkuAttrItemDto::getAttrValue)
                .orElse(null);
    }

    private boolean isFangxinChange(ProductM productM) {
        return "true".equals(productM.getAttr(IS_FANGXIN_CHANGE));
    }

    private ProductTagBuildReq buildRequest(ProductM productM) {
        ProductTagBuildReq req = new ProductTagBuildReq();
        req.setProductM(productM);
        return req;
    }

    public StyleTextModel buildPreAfterPayTag(Config config) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(config.getPreAfterTag());
        styleTextModel.setStyle(TextStyleEnum.TEXT_GREEN.getType());
        return styleTextModel;
    }

    @EqualsAndHashCode(callSuper = true)
    @VPointCfg
    @Data
    public static class Config extends UnifiedShelfItemSubTitleVP.Config {

        /**
         * 静态副标题，用于配置直接返回
         */
        private String staticSubTitle;

        private boolean forceNull;

        /**
         * 使用爆品副标题
         */
        private boolean enableHotSpuTag;

        /**
         * 根据后台类目树配置副标题策略
         * 策略类型：{@link String}
         */
        private ShopCategoryConfig backCategory2Strategy;

        /**
         * 是否支持先用后付副标题展示
         */
        private boolean enableAfterPay;

        private String preAfterTag;

        private List<String> afterPayExps;

    }
}
