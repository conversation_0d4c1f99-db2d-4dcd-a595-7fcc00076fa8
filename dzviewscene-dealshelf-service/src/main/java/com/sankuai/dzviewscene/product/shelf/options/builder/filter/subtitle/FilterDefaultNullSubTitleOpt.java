package com.sankuai.dzviewscene.product.shelf.options.builder.filter.subtitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterSubTitleVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;

@VPointOption(name = "默认副标题，返回空",
        description = "默认副标题，返回空",
        code = "FilterDefaultNullSubTitleOpt", isDefault = true)
public class FilterDefaultNullSubTitleOpt extends FilterSubTitleVP<Void> {
    @Override
    public RichLabelVO compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
