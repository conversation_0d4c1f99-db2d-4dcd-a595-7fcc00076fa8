package com.sankuai.dzviewscene.product.productdetail.options.banner.collect;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.ability.collect.CollectInfoAbility;
import com.sankuai.dzviewscene.product.productdetail.ability.banner.vpoints.BannerCollectStatusVP;
import lombok.Data;
import org.apache.commons.lang.BooleanUtils;

@VPointOption(name = "默认-横幅区-收藏选项", description = "适用于通过商品属性获取收藏状态场景", code = DefaultCollectStatusOpt.CODE, isDefault = true)
public class DefaultCollectStatusOpt extends BannerCollectStatusVP<DefaultCollectStatusOpt.Config> {

    public static final String CODE = "DefaultCollectStatusOpt";

    @Override
    public Boolean compute(ActivityCxt ctx, DefaultCollectStatusOpt.Param param, DefaultCollectStatusOpt.Config cfg) {
        return BooleanUtils.isTrue(ctx.getSource(CollectInfoAbility.CODE));
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}