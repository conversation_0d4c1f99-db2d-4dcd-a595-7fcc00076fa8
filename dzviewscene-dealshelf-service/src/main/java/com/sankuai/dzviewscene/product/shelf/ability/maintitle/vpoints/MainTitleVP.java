package com.sankuai.dzviewscene.product.shelf.ability.maintitle.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.maintitle.ShelfProductMainTitleBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import lombok.Builder;
import lombok.Data;

/**
 * Created by float on 2021/8/21.
 */
@VPoint(name = "货架主标题定制", description = "货架主标题定制",code = MainTitleVP.CODE, ability = ShelfProductMainTitleBuilder.CODE)
public abstract class MainTitleVP<T> extends PmfVPoint<MainTitleComponentVO, MainTitleVP.Param,T> {

    public static final String CODE = "MainTitleContentVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        int platform;
    }
}
