package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2023/5/26 5:51 PM
 **/
@VPointOption(name = "职业教育-商品价格上方标签",
        description = "职业教育 价格上方标签",
        code = "VocationalEduItemPriceAboveTagsOpt"
)
public class VocationalEduItemPriceAboveTagsOpt extends ItemPriceAboveTagsVP<VocationalEduItemPriceAboveTagsOpt.Config> {

    // 免费试听课时文案key
    private static final String FREE_AUDITION_TAG_DOC = "dealEduFreeAuditionTagDocument";
    // 预约留资数文案key
    private static final String LEAD_SALES_TAG_DOC = "dealEduLeadSalesTagDocument";

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        List<DzTagVO> dzTagVOs = new ArrayList<>();
        // 免费试听课时的标签
        String eduFreeAuditionTag = param.getProductM().getAttr(FREE_AUDITION_TAG_DOC);
        if (StringUtils.isNotBlank(eduFreeAuditionTag)) {
            // 预约留资数量的标签
            String edmuLeadSalesTag = param.getProductM().getAttr(LEAD_SALES_TAG_DOC);
            if (StringUtils.isNotBlank(edmuLeadSalesTag)) {
                eduFreeAuditionTag += "·" + edmuLeadSalesTag;
            }
            DzTagVO dzTagVO = new DzTagVO();
            BeanUtils.copyProperties(config.getConfigTag(), dzTagVO);
            dzTagVO.setText(eduFreeAuditionTag);
            dzTagVOs.add(dzTagVO);
        }
        // 简单标签
        if (CollectionUtils.isNotEmpty(config.getSimpleTagAttrs())) {
            for (String simpleTagAttr : config.getSimpleTagAttrs()) {
                String simpleTag = param.getProductM().getAttr(simpleTagAttr);
                if (StringUtils.isBlank(simpleTag)) {
                    continue;
                }
                DzTagVO dzTagVO = new DzTagVO();
                dzTagVO.setText(simpleTag);
                dzTagVOs.add(dzTagVO);
            }
        }
        return dzTagVOs.stream().limit(1).collect(Collectors.toList());
    }

    @Data
    @VPointCfg
    static class Config {

        /**
         * 样式配置
         */
        private DzTagVO configTag;

        /**
         * 简单的标签，没有样式
         */
        private List<String> simpleTagAttrs = Lists.newArrayList("attr_eduClassLocationNum");

    }
}
