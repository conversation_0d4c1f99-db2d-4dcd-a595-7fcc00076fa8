package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess.DealStandardServiceProcessBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * created by zhang<PERSON>yuan04 in 2021/12/14
 */
@Component("standardServiceProcessStrategy")
public class StandardServiceProcessStrategy implements ModuleStrategy {

    private static final String MODULE_NAME = "服务流程";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<StandardServiceProcessVO> standardServiceProcessVOS = activityCxt.getSource(DealStandardServiceProcessBuilder.CODE);
        if (CollectionUtils.isEmpty(standardServiceProcessVOS)) {
            return null;
        }
        return buildDealDetailModuleVO(standardServiceProcessVOS);
    }

    private DealDetailModuleVO buildDealDetailModuleVO(List<StandardServiceProcessVO> standardServiceProcessVOS) {
        if (CollectionUtils.isEmpty(standardServiceProcessVOS)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        List<DealSkuItemVO> skuItems = buildDealSkuList(standardServiceProcessVOS);
        DealSkuGroupModuleVO skuGroupsModel1 = new DealSkuGroupModuleVO();
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setItems(skuItems);
        skuGroupsModel1.setDealSkuList(Collections.singletonList(dealSkuVO));
        dealDetailModuleVO.setSkuGroupsModel1(Collections.singletonList(skuGroupsModel1));
        return dealDetailModuleVO;
    }

    private List<DealSkuItemVO> buildDealSkuList(List<StandardServiceProcessVO> standardServiceProcessVOS) {
        if (CollectionUtils.isEmpty(standardServiceProcessVOS)) {
            return null;
        }
        List<DealSkuItemVO> skuItems = Lists.newArrayList();
        for (StandardServiceProcessVO processVO : standardServiceProcessVOS) {
            DealSkuItemVO skuItemVO = new DealSkuItemVO();
            skuItemVO.setName(processVO.getStepName());
            skuItemVO.setIcon(processVO.getStepIcon());
            skuItemVO.setRightText(processVO.getArrowIcon());
            skuItems.add(skuItemVO);
        }
        return skuItems;
    }
}
