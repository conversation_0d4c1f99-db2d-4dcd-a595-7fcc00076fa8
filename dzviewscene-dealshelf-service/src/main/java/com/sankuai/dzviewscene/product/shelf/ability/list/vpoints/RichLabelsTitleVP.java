package com.sankuai.dzviewscene.product.shelf.ability.list.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.list.ShelfProductListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * @auther: liweilong06
 * @date: 2021/12/22 上午10:35
 */
@VPoint(name = "商品的富文本标题", description = "商品的富文本标题",code = RichLabelsTitleVP.CODE, ability = ShelfProductListBuilder.CODE)
public abstract class RichLabelsTitleVP <T> extends PmfVPoint<String, RichLabelsTitleVP.Param, T> {

    public static final String CODE = "RichLabelsTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
        private int platform;
        private String searchKeyword;
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 高亮类型
         * keyWord：关键词高亮
         * title：整个标题高亮
         */
        private String lightType;

    }
}
