package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.product.shelf.utils.DealShanKaiAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/14
 */
@VPoint(name = "团单扩展数据", description = "item-extra", code = ItemExtraVP.CODE, ability = FloorsBuilder.CODE)
public abstract class ItemExtraVP<T> extends PmfVPoint<String, ItemExtraVP.Param, T> {
    public static final String CODE = "ItemExtraVP";

    @Override
    public String compute(ActivityCxt activityCxt, Param param, T config) {
        Map<String, Object> extraMap = buildExtraMap(activityCxt, param, config);
        if (extraMap == null) {
            extraMap = new HashMap<>();
        }
        DealShanKaiAttrUtils.processShanKai(param.getProductM(), extraMap);
        return MapUtils.isNotEmpty(extraMap) ? JsonCodec.encodeWithUTF8(extraMap) : null;
    }

    protected abstract Map<String, Object> buildExtraMap(ActivityCxt activityCxt, Param param, T config);

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private List<DouHuM> douHuList;
    }
}
