package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@VPoint(name = "聚合后置处理", description = "聚合后置处理-用于处理聚合节点构造特殊逻辑", code = AggregatePostHandlerVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class AggregatePostHandlerVP<T> extends PmfVPoint<ShelfItemVO, AggregatePostHandlerVP.Param, T> {

    public static final String CODE = "AggregatePostHandleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ShelfItemVO shelfItemVO;

        private Map<String, ProductM> productMMap;
    }
}