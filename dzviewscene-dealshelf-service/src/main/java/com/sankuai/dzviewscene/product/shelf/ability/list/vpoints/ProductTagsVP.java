package com.sankuai.dzviewscene.product.shelf.ability.list.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.list.ShelfProductListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by float on 2021/8/21.
 */
@VPoint(name = "商品标签", description = "商品标签",code = ProductTagsVP.CODE, ability = ShelfProductListBuilder.CODE)
public abstract class ProductTagsVP<T> extends PmfVPoint<List<String>, ProductTagsVP.Param, T> {

    public static final String CODE = "ProductTagsVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        // 当前卡片中的商品
        private ProductM productM;
    }

}
