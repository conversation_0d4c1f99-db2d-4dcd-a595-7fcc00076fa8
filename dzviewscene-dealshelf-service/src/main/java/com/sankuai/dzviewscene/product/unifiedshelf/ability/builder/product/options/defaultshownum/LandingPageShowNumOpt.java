package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.defaultshownum;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaDefaultShowNumVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import lombok.Data;

@VPointOption(name = "商品区-落地页pagesize商品展示数量",
        description = "商品区-落地页pagesize商品展示数量",
        code = LandingPageShowNumOpt.CODE)
public class LandingPageShowNumOpt extends ProductAreaDefaultShowNumVP<LandingPageShowNumOpt.Config> {

    public static final String CODE = "landingPageShowNumOpt";

    @Override
    public Integer compute(ActivityCxt context, Param param, Config config) {
        if(ParamsUtil.judgeDealShelfHasPage(context)) {
            return config.getPageSize() <= 0 ? ParamsUtil.getIntSafely(context, PmfConstants.Params.pageSize) : config.getPageSize();
        }
        return config.getDefaultShowNum();
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 默认展示商品数，无规则或规则未命中时默认该数量
         */
        private int defaultShowNum = 2;

        /**
         * 配置规则pageSize展示商品数
         */
        private int pageSize;
    }
}
