package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@VPointOption(name = "游戏币-ProductTags",
        description = "支持预售",
        code = "GameCoinItemProductTagsOpt")
public class GameCoinItemProductTagsOpt extends ItemProductTagsVP<GameCoinItemProductTagsOpt.Config> {

    @Override
    public List<String> compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        // 爆品专用标题（config 有开关）
        if (ProductMAttrUtils.hotSpu(productM) && config.isEnableHotSpuSpecialTag()) {
            return ProductMAttrUtils.getHotSpuSpecialProductTags(productM);
        }

        //预售优先级最高
        if (PreSaleUtils.isPreSaleDeal(productM)) {
            return getPreSaleProductTags(productM, config);
        }
        //限时秒杀，彩虹活动秒杀，无营销活动秒杀
        if(RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(productM)){
            return getSecKillProductTags(productM, config);
        }
        //其他的tag
        return productM.getProductTags();
    }

    private List<String> getSecKillProductTags(ProductM productM, GameCoinItemProductTagsOpt.Config config){
        ProductSaleM productSaleM = productM.getSale();
        if(null == productSaleM || StringUtils.isEmpty(config.getSecKillTemplate())){
            return productM.getProductTags();
        }
        int sales = productSaleM.getSale();
        String priceLabelDesc = "";
        if (sales >= 100) {
            int firstOfSales = Integer.parseInt(String.valueOf(sales).substring(0, 1));
            if (sales < 1000) {
                priceLabelDesc = "热销" + firstOfSales + "百"+"·";
            } else if (sales < 10000) {
                priceLabelDesc = "热销" + firstOfSales + "千"+"·";
            } else {
                String stringSales = String.valueOf(sales);
                priceLabelDesc = "爆卖" + stringSales.substring(0, stringSales.length() - 4) + "万"+"·";
            }
        }
        return Lists.newArrayList(String.format(config.getSecKillTemplate(), priceLabelDesc));
    }


    private List<String> getPreSaleProductTags(ProductM productM, GameCoinItemProductTagsOpt.Config config) {
        String preSaleDate = productM.getAttr(config.getPreSaleDateAttrKey());
        if (StringUtils.isEmpty(config.getPreSaleTemplate()) || StringUtils.isEmpty(preSaleDate)) {
            return productM.getProductTags();
        }
        return Lists.newArrayList(String.format(config.getPreSaleTemplate(), preSaleDate));
    }


    @VPointCfg
    @Data
    public static class Config {
    /**
     * 预售时间Key
     */
    private String preSaleDateAttrKey = "preSaleStartDate";

    private String preSaleTemplate = "%s后可用";

    private String secKillTemplate = "%s好评如潮·退款无忧";

    /**
     * 启用爆品专用标签
     */
    private boolean enableHotSpuSpecialTag;
    }
}
