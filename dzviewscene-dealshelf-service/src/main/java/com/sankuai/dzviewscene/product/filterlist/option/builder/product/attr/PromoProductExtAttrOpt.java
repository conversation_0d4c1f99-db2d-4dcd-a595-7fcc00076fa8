package com.sankuai.dzviewscene.product.filterlist.option.builder.product.attr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductExtAttrVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;

import java.util.Objects;

/**
 * @description :
 * @date : 2024/11/11
 */
@VPointOption(name = "优惠商品类型扩展信息实现",
        description = "保存商品的类型数据，是否是团单或者是次卡等商品类型，以及商品的门店折扣价",
        code = "PromoProductExtAttrOpt")
public class PromoProductExtAttrOpt extends ProductExtAttrVP<PromoProductExtAttrOpt.Config> {
    private static final String DEAL_PRODUCT_TYPE = "{\"productType\":1,\"basePrice\":\"%s\",\"tradeType\":\"%d\"}"; // 团购
    private static final String TIME_CARD_PRODUCT_TYPE = "{\"productType\":5,\"basePrice\":\"%s\"}"; // 次卡
    private static final String UNKNOWN_PRODUCT_TYPE = "{\"productType\":0,\"basePrice\":\"%s\"}"; // 未知

    @Override
    public String compute(ActivityCxt context, Param param, Config unused) {
        ProductM productM = param.getProductM();
        if (Objects.isNull(productM)) {
            return null;
        }

        switch (productM.getProductType()) {
            case 5:
                return String.format(TIME_CARD_PRODUCT_TYPE, productM.getBasePriceTag());
            case 1:
            default:
                return String.format(DEAL_PRODUCT_TYPE, productM.getBasePriceTag(), productM.getTradeType());  //由于优惠码货架默认未知的类型的时候就为团单
        }
    }

    @Data
    @VPointCfg
    public static class Config {

    }
}
