package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.shelf.utils.DzTagStyleWrapUtils;
import com.sankuai.dzviewscene.product.shelf.utils.PriceDisplayUtils;
import com.sankuai.dzviewscene.product.shelf.utils.RainbowSecKillUtils;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dzviewscene.product.utils.ProductMPromoInfoUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@VPointOption(name = "丽人-三美货架新样式价格底部标签",
        description = "含特价团购活动标签、优惠标签、全网低价标签、近XX天最低价标签",
        code = "BeautyShelfNewStyleItemPriceBottomTagsOpt"
)
public class BeautyShelfNewStyleItemPriceBottomTagsOpt extends ItemPriceBottomTagsVP<BeautyShelfNewStyleItemPriceBottomTagsOpt.Config> {

    private static final String BEAUTY_CARD_COUPON_CONFIG = "com.sankuai.merchantcard.dzcard.supply.beautycard.coupon.config";
    private static final String PRICE_POWER_TAG_ATTR = "highestPriorityPricePowerTagAttr";

    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Config config) {
        //优惠标签
        DzTagVO promoTag = buildPromoTag(param, config);
        DzTagStyleWrapUtils.overridePromoStyle(promoTag);
        List<DzTagVO> tagVOS = new ArrayList<>();
        addTagIfNeed(tagVOS, promoTag);

        DzTagVO pricePowerTag = buildGuaranteeOrPricePowerTag(context, param, config);
        addTagIfNeed(tagVOS, pricePowerTag);
        return tagVOS;
    }

    private DzTagVO buildGuaranteeOrPricePowerTag(ActivityCxt context, Param param, Config config){
        //买贵必赔标签 > 价格力标签
        DzTagVO guaranteeTag = PriceDisplayUtils.buildPriceGuaranteeTag(param.getProductM());
        if(guaranteeTag != null){
            return guaranteeTag;
        }
        //价格力标签
        List<DouHuM> douHuMList = context.getSource(ShelfDouHuFetcher.CODE);
        Optional<Boolean> showPricePowerTag = (Optional<Boolean>) DouHuUtils.getConfigByDouHu(douHuMList, config.getShowPricePowerTagABMap());
        if(showPricePowerTag.isPresent() && showPricePowerTag.get()){
            return getPricePowerTag(param.getProductM(), param.getPlatform());
        }
        return null;
    }

    private DzTagVO getPricePowerTag(ProductM productM, int platform) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.BeautyShelfNewStyleItemPriceBottomTagsOpt.getPricePowerTag(ProductM,int)");
        String pricePowerTag = productM.getAttr(PRICE_POWER_TAG_ATTR);
        if (StringUtils.isBlank(pricePowerTag)) {
            return null;
        }
        DzTagVO dzTagVO = DzPromoUtils.buildBasicDzTagVOWithColor(platform, pricePowerTag,
                ColorUtils.colorFF4B10, ColorUtils.colorFF4B10, null, ColorUtils.colorFF6633, ColorUtils.colorFFF2EE);
        if (Objects.nonNull(dzTagVO) && PlatformUtil.isMT(platform)) {
            dzTagVO.setBorderRadius(3);
        }
        return dzTagVO;
    }

    private void addTagIfNeed(List<DzTagVO> tagVOS, DzTagVO tagVO) {
        if (tagVO != null) {
            tagVOS.add(tagVO);
        }
    }

    /**
     * 优惠感知统一拉齐逻辑
     * 预售 > 秒杀 > 会员 > 美团补贴 > 新客 > 其他
     *
     * @param param
     * @param config
     * @return
     */
    private DzTagVO buildPromoTag(Param param, Config config) {
        if(Objects.isNull(param) || Objects.isNull(param.getProductM())
                || CollectionUtils.isEmpty(param.getProductM().getPromoPrices())){
            return null;
        }
        Map<Integer, ProductPromoPriceM> promoPriceMMap = ProductMPromoInfoUtils.getPromoTypeAndPriceMap(param.getProductM().getPromoPrices());
        ProductPromoPriceM preSalePromoPrice = null;
        DzTagVO dzTagVO = null;
        // 预售>新会员>商家会员>丽人会员> 秒杀 >  美团补贴 > 新客，https://km.sankuai.com/collabpage/2054276023
        // 预售优先级最高
        if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_Member.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_Member.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale_NewUser.getCode())) {
            preSalePromoPrice = promoPriceMMap.get(PromoTagTypeEnum.PreSale_NewUser.getCode());
        } else if (promoPriceMMap.containsKey(PromoTagTypeEnum.PreSale.getCode())) {
            preSalePromoPrice = promoPriceMMap.get((PromoTagTypeEnum.PreSale.getCode()));
        }

        // 秒杀其次（非商品侧处理）
        if (Objects.isNull(preSalePromoPrice) && RainbowSecKillUtils.isRainbowSecKillDealFromRainbow(param.getProductM())) {
            DzTagVO secKillDzTag = ProductMPromoInfoUtils.getSecKillPromoPriceM(param.getProductM().getPromoPrices(), param.getProductM().getMarketPrice(), param.getSalePrice(), param.getPlatform(), config.getPopType());
            if (Objects.nonNull(secKillDzTag)) {
                secKillDzTag.setPrePic(new DzPictureComponentVO(
                        PlatformUtil.isMT(param.getPlatform()) ? ProductMPromoInfoUtils.MT_SEC_KILL_TAG_URL : ProductMPromoInfoUtils.DP_SEC_KILL_TAG_URL,
                        PlatformUtil.isMT(param.getPlatform()) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio()));
                secKillDzTag.setAfterPic(this.buildAfterPic(param.getPlatform()));
                secKillDzTag.setBorderRadius(PlatformUtil.isMT(param.getPlatform()) ? 3 : 1);
                return secKillDzTag;
            }
        }

        // 判断是否为会员专属
        boolean isMemberExclusive = MerchantMemberPromoUtils.isMemberExclusive(param.getProductM());
        if (isMemberExclusive) {
            return ProductMPromoInfoUtils.buildMemberExclusiveTag(param.getProductM(), param.getPlatform(), param.getSalePrice(),
                    param.getProductM().getPromo(PromoTypeEnum.DIRECT_PROMO.getType()), config.getPopType());
        }

        //商家会员价优惠
        //包含商家会员优惠
        boolean hasMerchantMemberPromo = MerchantMemberPromoUtils.hasMerchantMemberPromoByPromoDetailList(param.getProductM().getPromoPrices());
        if(null == preSalePromoPrice && hasMerchantMemberPromo){
            MerchantMemberProductPromoData merchantMemberPromo = MerchantMemberPromoUtils.getMerchantMemberPromo(param.getProductM());
            if(merchantMemberPromo.getProductPromoPrice() != null){
                return ProductMPromoInfoUtils.buildMerchantMemberPromoTag(param.getProductM(), param.getPlatform(), param.getSalePrice(), merchantMemberPromo, config.getPopType());
            }
        }

        //丽人会员
        ProductPromoPriceM productPromoPriceM = param.getProductM().getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (Objects.isNull(preSalePromoPrice) && productPromoPriceM != null && isBeautyMember(param, productPromoPriceM.getPromoItemList())) {
            boolean isMt = PlatformUtil.isMT(param.getPlatform());
            dzTagVO = DzPromoUtils.buildBasicDzTagVOWithColor(param.getPlatform(), productPromoPriceM.getPromoTag(),
                    ColorUtils.color8C5819, null, null, "#8E3C12", "#FFEDDE");
            String prePic = isMt ? config.getMtMemberPrePic() : config.getDpMemberPrePic();
            double prePicAspectRadio = isMt ? config.getMtMemberPriceBottomTagPrePicAspectRadio() : config.getDpMemberPriceBottomTagPrePicAspectRadio();
            dzTagVO.setPrePic(new DzPictureComponentVO(prePic, prePicAspectRadio));
            dzTagVO.setAfterPic(new DzPictureComponentVO("https://p0.meituan.net/travelcube/b5ef0237b2cb19d166956ce499b6f0bb495.png", 0.875));
            dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(productPromoPriceM, config.getPopType()));
            dzTagVO.setBorderRadius(isMt ? 3 : 1);
            return dzTagVO;
        }

        ProductPromoPriceM otherPromoPrice = this.getNoMemberPromoM(param.getProductM());
        if (Objects.isNull(otherPromoPrice)) {
            return null;
        }
        ProductPromoPriceM promoPriceM = Objects.nonNull(preSalePromoPrice) ? preSalePromoPrice : otherPromoPrice;
        dzTagVO = this.buildPromoTagVo(param, promoPriceM);
        if (Objects.isNull(dzTagVO)) {
            return null;
        }
        dzTagVO.setPrePic(new DzPictureComponentVO(promoPriceM.getIcon(),
                PlatformUtil.isMT(param.getPlatform()) ? config.getMtPriceBottomTagPrePicAspectRadio() : config.getDpPriceBottomTagPrePicAspectRadio()));
        dzTagVO.setAfterPic(this.buildAfterPic(param.getPlatform()));
        dzTagVO.setPromoDetail(DzPromoUtils.buildPromoDetail(promoPriceM, config.getPopType()));
        dzTagVO.setBorderRadius(PlatformUtil.isMT(param.getPlatform()) ? 3 : 1);
        dzTagVO.setText(dzTagVO.getName());
        return dzTagVO;
    }

    /**
     * 构建 AfterPic
     *
     * @param platform
     */
    private DzPictureComponentVO buildAfterPic(int platform) {
        if (PlatformUtil.isMT(platform)) {
            return new DzPictureComponentVO("https://p0.meituan.net/travelcube/ac0b1b7e016f85fcf9b8784d34d1bc14439.png", 1);
        } else {
            return new DzPictureComponentVO("https://p1.meituan.net/travelcube/4b5a342d1d0c2ae89ea51d8a2a6f7d75459.png", 1);
        }
    }

    /**
     * 获取非会员的标签
     *
     * @param productM
     * @return
     */
    private ProductPromoPriceM getNoMemberPromoM(ProductM productM) {
        List<ProductPromoPriceM> noMemberPriceM = productM.getPromoPrices().stream()
                .filter(a -> Objects.nonNull(a.getPromoTagType())
                        // 过滤掉无效值
                        && !a.getPromoTagType().equals(0)
                        // 过滤掉会员
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                        // 过滤掉没有标签
                        && !a.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noMemberPriceM)) {
            return null;
        }
        return noMemberPriceM.get(0);
    }

    /**
     * 构建优惠感知版本的priceBottomTag，有特别的样式逻辑
     *
     * @return
     */
    private DzTagVO buildPromoTagVo(Param param, ProductPromoPriceM productPromoPriceM) {
        DzTagVO basicTagVo = DzPromoUtils.buildBasicDzTagVOWithColor(param.getPlatform(), productPromoPriceM.getPromoTag(),
                ColorUtils.colorFF5500, ColorUtils.colorFFD6BE, null, ColorUtils.colorFF6633, ColorUtils.colorFFF2EE);
        // 点评侧的「新客特惠」、「特惠促销」样式不一样
        if (Objects.nonNull(basicTagVo) && !PlatformUtil.isMT(param.getPlatform()) && Objects.nonNull(productPromoPriceM.getPromoTagType()) &&
                (productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.NewUser.getCode()) || productPromoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Other.getCode()))) {
            basicTagVo.setHasBorder(true);
            basicTagVo.setBorderColor(ColorUtils.colorFFCFBF);
            basicTagVo.setBackground(null);
        }
        return basicTagVo;
    }

    private boolean isBeautyMember(Param param, List<PromoItemM> promoItemMList) {
        if (CollectionUtils.isEmpty(promoItemMList)) {
            return false;
        }
        String json = Lion.getStringValue(BEAUTY_CARD_COUPON_CONFIG);
        List<BeautyCardCouponDTO> beautyCardCouponDTOList = JSON.parseArray(json, BeautyCardCouponDTO.class);
        if (CollectionUtils.isEmpty(beautyCardCouponDTOList)) {
            return false;
        }
        Date now = new Date();
        Set<String> beautyMemberCouponGroupIds = beautyCardCouponDTOList.stream()
                .filter(dto -> !dto.isParamIllegal())
                .filter(dto -> dto.isValid(now))
                .map(dto -> PlatformUtil.isMT(param.getPlatform()) ? dto.getMtCouponGroupId() : dto.getDpCouponGroupId())
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return promoItemMList.stream()
                .filter(promoItemM -> promoItemM.getPromoTypeCode() == com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum.COUPON.getType())
                .anyMatch(promoItemM -> beautyMemberCouponGroupIds.contains(String.valueOf(promoItemM.getPromoId())));
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 弹窗样式类型，默认是1
         */
        private int popType = 1;
        /**
         * 是否隐藏只包含团购优惠的优惠
         */
        @Deprecated
        private boolean hideOnlyDealPromo = false;
        /**
         * 点评活动角标
         */
        @Deprecated
        private String dpActivityPic;
        /**
         * 美团活动角标
         */
        @Deprecated
        private String mtActivityPic;
        /**
         * 活动角标宽高比
         */
        @Deprecated
        private double activityPicAspectRadio;
        /**
         * 特价活动角标宽高比
         */
        @Deprecated
        private double specialPriceActivityPicAspectRadio;
        /**
         * 点评后置pic
         */
        @Deprecated
        private String dpAfterPic;
        /**
         * 美团后置pic
         */
        @Deprecated
        private String mtAfterPic;
        /**
         * 后置pic高度
         */
        @Deprecated
        private int afterPicHight;
        /**
         * 新客优惠标签
         */
        @Deprecated
        private String newUserPromoTagFormat;
        /**
         * 优惠标签
         */
        @Deprecated
        private String promoTagFormat;

        /**
         * 是否不展示前置标签，true:不展示，false:展示，默认是展示
         */
        @Deprecated
        private boolean noShowPrePic;

        /**
         * 如果前置标签无效，不展示标签信息
         */
        @Deprecated
        private boolean noShowTagIfInvalidPrePic;

        /**
         * 是否启动预售标签逻辑
         * 扩充到其他类目后，该配置可下线作为通用流程
         */
        @Deprecated
        private boolean enablePreSale = false;

        /**
         * 为空则没有边框
         */
        @Deprecated
        private String preSaleBorderColor;

        /**
         * 预售的文字颜色
         */
        @Deprecated
        private String preSaleTextColor;

        /**
         * 预售的背景色
         */
        @Deprecated
        private String preSaleBackground;

        /**
         * 超值预售图片
         */
        @Deprecated
        private String prePicUrl;

        @Deprecated
        private int prePicHeight;

        @Deprecated
        private double prePicAspectRadio;

        /**
         * 美团侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtPriceBottomTagPrePicAspectRadio = 3.25;
        /**
         * 点评侧 团购货架 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpPriceBottomTagPrePicAspectRadio = 3.25;

        /**
         * 美团侧 团购货架 会员 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double mtMemberPriceBottomTagPrePicAspectRadio = 2.435;
        /**
         * 点评侧 团购货架 会员 优惠感知 PriceBottomTag PrePic 的宽高比
         */
        private Double dpMemberPriceBottomTagPrePicAspectRadio = 2.435;

        /**
         * 点评侧 会员PrePic图片
         */
        private String dpMemberPrePic;

        /**
         * 美团侧 会员PrePic图片
         */
        private String mtMemberPrePic;

        /**
         * 价格力实验AB配置，实验完可拿掉
         */
        private Map<String,Boolean> showPricePowerTagABMap;

    }

    @Data
    private static class BeautyCardCouponDTO implements Serializable {

        /**
         * 点评券批次id
         */
        private String dpCouponGroupId;

        /**
         * 美团券批次id
         */
        private String mtCouponGroupId;

        /**
         * 券有效期，单位：天
         */
        private int couponValidDays;

        private Date validStartTime;

        private Date validEndTime;

        public boolean isParamIllegal() {
            return StringUtils.isAnyBlank(dpCouponGroupId, mtCouponGroupId) || couponValidDays <= 0;
        }

        public boolean isValid(Date now) {
            if (validStartTime != null && validStartTime.after(now)) {
                return false;
            }
            if (validEndTime != null && validEndTime.before(now)) {
                return false;
            }
            return true;
        }

    }

}

