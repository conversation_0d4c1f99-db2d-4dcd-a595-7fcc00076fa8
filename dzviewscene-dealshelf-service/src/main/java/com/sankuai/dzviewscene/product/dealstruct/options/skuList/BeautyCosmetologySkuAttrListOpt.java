package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.dzviewscene.product.dealstruct.ability.utils.Constants.DEFAULT_STEP_TYPE;

/**
 * <AUTHOR>
 * 美容美体sku属性列表变化点
 * 适用于当团单内的服务项目为【皮肤管理-4038】、【SPA按摩-2104496】、【养生调理-2104491】、【美胸美臀-805】、【瘦身塑形-803】、【脱毛-804】、【其他-801】时
 * 根据配置取货属性值
 */
@VPointOption(name = "美容美体sku属性列表变化点", description = "美容美体sku属性列表变化点，展示配置的sku属性", code = BeautyCosmetologySkuAttrListOpt.CODE)
public class BeautyCosmetologySkuAttrListOpt extends SkuAttrListVP<BeautyCosmetologySkuAttrListOpt.Config> {

    public static final String CODE = "BeautyCosmetologySkuAttrListOpt";

    private static final String APPLICABLE_PARTS = "适用部位";

    private static final String OPTION_TEMPLATE = " %s选%s";

    private static final String PRODUCT = "产品";

    private static final String EQUIPMENT = "仪器";

    private static final String DESC = "说明";

    private static final String PRODUCT_AND_EQUIPMENT = "产品/仪器";

    private static final String ZERO_STEP_TIME_SHOW_DOC = "不计入总时长";

    private static final String STEP_TIME_FORMAT = "%s分钟";


    /**
     * 【皮肤管理】服务项目Id
     */
    private static final long SKIN_MANAGEMENT_CATEGORY_ID = 4038L;

    /**
     * 【脱毛】服务项目Id
     */
    private static final long DEPILATION_CATEGORY_ID = 804L;
    /**
     * 【其他】服务项目Id
     */
    private static final long OTHERS_CATEGORY_ID = 801L;
    /**
     * 【spa按摩】服务项目Id
     */
    private static final long SPA_MASSAGE_CATEGORY_ID = 2104496L;
    /**
     * 【养生调理】服务项目Id
     */
    private static final long HEALTH_PRESERVATION = 2104491L;


    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, BeautyCosmetologySkuAttrListOpt.Config config) {
        //团单属性
        SkuItemDto skuItemDto = param.getSkuItemDto();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems()) || config == null || MapUtils.isEmpty(config.getCategory2SkuDisplayModelList())) {
            return null;
        }
        List<SkuAttrItemDto> skuAttrItems = skuItemDto.getAttrItems();
        long productCategory = param.getDealAttrs() == null ? 0 : param.getSkuItemDto().getProductCategory();
        List<SkuDisplayModel> skuDisplayModels = config.getCategory2SkuDisplayModelList().get(productCategory);
        if (CollectionUtils.isEmpty(skuDisplayModels)) {
            return null;
        }
        boolean isStandProduct = isStandardProduct(param.getDealAttrs());
        return skuDisplayModels.stream()
                .flatMap(skuDisplayModel -> {
                    if (skuDisplayModel == null || StringUtils.isEmpty(skuDisplayModel.getSkuAttrName()) || StringUtils.isEmpty(skuDisplayModel.getSkuTitle())) {
                        return null;
                    }
                    //服务流程
                    if (StringUtils.equals(skuDisplayModel.getSkuAttrName(), "serviceProcess") || StringUtils.equals(skuDisplayModel.getSkuAttrName(), "servicestep")) {
                        List<DealSkuItemVO> dealSkuItems = buildServiceStepSkuItem(skuDisplayModel.getSkuTitle(), skuAttrItems, productCategory, skuDisplayModel.getSkuAttrName(), isStandProduct, config);
                        return CollectionUtils.isNotEmpty(dealSkuItems) ? dealSkuItems.stream() : null;
                    }
                    //适用部位
                    if (StringUtils.equals(skuDisplayModel.getSkuAttrName(), "bodyname")) {
                        return Stream.of(buildApplicablePartsDealSkuItem(skuAttrItems, productCategory));
                    }
                    String skuAttrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, skuDisplayModel.getSkuAttrName());
                    //服务时长
                    if (StringUtils.equals(skuDisplayModel.getSkuAttrName(), "duration")) {
                        skuAttrValue = getNormalizedStepTime(skuAttrValue);
                    }
                    //适合肤质
                    if (StringUtils.equals(skuDisplayModel.getSkuAttrName(), "applySkin")) {
                        skuAttrValue = getSuitableSkinQualityAttrValue(skuDisplayModel, skuAttrItems);
                        skuAttrValue = safeStringTrim(skuAttrValue);
                    }
                    if (StringUtils.isEmpty(skuAttrValue)) {
                        return null;
                    }
                    //无特殊逻辑的货属性直接取值
                    return Stream.of(buildDealSkuItemVO(skuDisplayModel.getSkuTitle(), skuAttrValue, null, 0));
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public String safeStringTrim(String skuAttrValue){
        if (StringUtils.isNotEmpty(skuAttrValue)){
            return skuAttrValue.trim();
        }
        return skuAttrValue;
    }
    private String getSuitableSkinQualityAttrValue(SkuDisplayModel skuDisplayModel, List<SkuAttrItemDto> skuAttrItems) {
        String suitableSkinQuality = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "applySkin");
        String suitableBodyParts = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "bodyname");
        if (StringUtils.equals(suitableSkinQuality, "适用所有肤质")) {
            String configAttrValue = getAttrValueWithConfig(skuDisplayModel, skuAttrItems);
            return StringUtils.isEmpty(suitableBodyParts) ? configAttrValue : configAttrValue + "    " + suitableBodyParts;
        }
        String suitablePartSkinQuality = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "skinType");
        return StringUtils.isEmpty(suitableBodyParts) ? suitablePartSkinQuality : suitablePartSkinQuality + "    " + suitableBodyParts;
    }

    private String getAttrValueWithConfig(SkuDisplayModel skuDisplayModel, List<SkuAttrItemDto> skuAttrItems){
        // 将货的属“skuAttrName” 的原来值skuAttrValue  替换为 skuAttrReplaceValue
        if (StringUtils.equals(skuDisplayModel.getSkuAttrValue(), DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, skuDisplayModel.getSkuAttrName()))){
            return skuDisplayModel.getSkuAttrReplaceValue();
        }
        return DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, skuDisplayModel.getSkuAttrName());
    }

    /**
     * 判断该团单是否是标品
     *
     * @param dealAttrs 团单行业属性列表
     * @return
     */
    private boolean isStandardProduct(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        String standardProductAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "standardDealGroupKey");
        return StringUtils.isNotEmpty(standardProductAttrValue);
    }


    private List<DealSkuItemVO> buildServiceStepSkuItem(String disPlayTitle, List<SkuAttrItemDto> skuAttrItems, long productCategory, String attrName, boolean isStandardProduct, Config config) {
        String serviceStep = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, attrName);
        if (StringUtils.isEmpty(serviceStep)) {
            return null;
        }
        List<SkuAttrAttrItemVO> serviceInfoList;
        //其他-801
        if (productCategory == OTHERS_CATEGORY_ID) {
            List<String> serviceStepInfo = JsonCodec.converseList(serviceStep, String.class);
            if (CollectionUtils.isEmpty(serviceStepInfo)) {
                return null;
            }
            serviceInfoList = serviceStepInfo
                    .stream()
                    .filter(StringUtils::isNotEmpty)
                    .map(service -> {
                        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
                        skuAttrAttrItemVO.setName(service);
                        return skuAttrAttrItemVO;
                    })
                    .collect(Collectors.toList());
        } else {
            List<ServiceStepModel> serviceStepModels = JsonCodec.decode(serviceStep, new TypeReference<List<ServiceStepModel>>() {
            });
            if (CollectionUtils.isEmpty(serviceStepModels)) {
                return null;
            }
            serviceInfoList = serviceStepModels
                    .stream()
                    .map(serviceStepModel -> buildSkuAttrAttrItemVO(serviceStepModel, productCategory))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        String standardServiceProcessInfo = config.getStandardServiceProcessInfo();
        int stepType = config.getStepType() > 0 ? config.getStepType() : DEFAULT_STEP_TYPE;
        if (CollectionUtils.isNotEmpty(serviceInfoList) && isStandardProduct && StringUtils.isNotEmpty(standardServiceProcessInfo)) {
            //标准化团单需要展示服务步骤说明
            DealSkuItemVO stepNum = buildDealSkuItemVO(disPlayTitle, standardServiceProcessInfo, null, 0);
            DealSkuItemVO serviceInfo = buildDealSkuItemVO(StringUtils.EMPTY, StringUtils.EMPTY, serviceInfoList, stepType);
            return Lists.newArrayList(stepNum, serviceInfo);
        }
        if (CollectionUtils.isNotEmpty(serviceInfoList)) {
            return Lists.newArrayList(buildDealSkuItemVO(disPlayTitle, null, serviceInfoList, stepType));
        }
        return null;
    }

    private SkuAttrAttrItemVO buildSkuAttrAttrItemVO(ServiceStepModel serviceStepModel, long productCategory) {
        if (serviceStepModel == null) {
            return null;
        }
        SkuAttrAttrItemVO skuAttrAttrItemVO = new SkuAttrAttrItemVO();
        skuAttrAttrItemVO.setName(getSkuAttrAttrName(serviceStepModel, productCategory));
        String stepTime = StringUtils.isNotEmpty(serviceStepModel.getStepTakeTime()) ? serviceStepModel.getStepTakeTime() : serviceStepModel.getStepTime();
        skuAttrAttrItemVO.setInfo(Lists.newArrayList(getNormalizedStepTime(stepTime)));
        List<CommonAttrVO> commonAttrs = new ArrayList<>();
        String product = serviceStepModel.getProduct();
        String equipment = serviceStepModel.getEquipment();
        if (productCategory == SPA_MASSAGE_CATEGORY_ID || productCategory == HEALTH_PRESERVATION) {
            addToListIfNotNull(commonAttrs, buildCommonAttrVO(PRODUCT_AND_EQUIPMENT, contactProductAndEquipment(product, equipment)));
        } else {
            addToListIfNotNull(commonAttrs, buildCommonAttrVO(PRODUCT, product));
            addToListIfNotNull(commonAttrs, buildCommonAttrVO(EQUIPMENT, equipment));
        }
        addToListIfNotNull(commonAttrs, buildCommonAttrVO(DESC, serviceStepModel.getStepDesc()));
        skuAttrAttrItemVO.setValues(commonAttrs);
        return skuAttrAttrItemVO;
    }

    private String getSkuAttrAttrName(ServiceStepModel serviceStepModel, long productCategory) {
        String stepType = serviceStepModel.getStepType();
        String stepName = serviceStepModel.getStepName();
        if (StringUtils.isNotEmpty(serviceStepModel.getSubStepName())) {
            stepName = serviceStepModel.getSubStepName();
        }
        //养生调理和spa按摩
        if (productCategory == HEALTH_PRESERVATION || productCategory == SPA_MASSAGE_CATEGORY_ID) {
            if (StringUtils.equals(stepName, "其他")) {
                return serviceStepModel.getStepDesc();
            }
            if (StringUtils.equals(stepName, "按摩推拿（不使用精油）")) {
                stepName = "按摩推拿";
            }
            return StringUtils.isEmpty(serviceStepModel.getBodyPart()) ? stepName : serviceStepModel.getBodyPart() + stepName;
        }
        if (productCategory == SKIN_MANAGEMENT_CATEGORY_ID) {
            return stepName;
        }
        return StringUtils.isEmpty(stepName) ? stepType : stepType + "：" + stepName;

    }

    private String contactProductAndEquipment(String product, String equipment) {
        if (StringUtils.isEmpty(product) && StringUtils.isEmpty(equipment)) {
            return null;
        }
        if (StringUtils.isEmpty(product) || StringUtils.isEmpty(equipment)) {
            return StringUtils.isEmpty(product) ? equipment : product;
        }
        return product + "，" + equipment;
    }

    private <T> void addToListIfNotNull(List<T> list, T item) {
        if (list == null || item == null) {
            return;
        }
        list.add(item);
    }

    private CommonAttrVO buildCommonAttrVO(String name, String value) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(value)) {
            return null;
        }
        CommonAttrVO commonAttrVO = new CommonAttrVO();
        commonAttrVO.setName(name);
        commonAttrVO.setValue(value);
        return commonAttrVO;
    }

    private DealSkuItemVO buildApplicablePartsDealSkuItem(List<SkuAttrItemDto> skuAttrItems, long productCategory) {
        //适用部位
        String bodyName = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "bodyname");
        if (StringUtils.isEmpty(bodyName)) {
            return null;
        }
        if (productCategory == DEPILATION_CATEGORY_ID) {
            String bodyOptionCount = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItems, "bodyOptionCount");
            String option = StringUtils.isEmpty(bodyOptionCount) ? "" : String.format(OPTION_TEMPLATE, bodyName.split("、").length, bodyOptionCount);
            return buildDealSkuItemVO(APPLICABLE_PARTS, bodyName.replace("、", "/") + option, null, 0);
        }
        return buildDealSkuItemVO(APPLICABLE_PARTS, bodyName, null, 0);
    }

    /**
     * 如果stepTime为0则展示"不计入总时长"，否则展示"%s分钟"
     *
     * @param
     * @return
     */
    private String getNormalizedStepTime(String stepTime) {
        if (StringUtils.isEmpty(stepTime)) {
            return null;
        }
        if (stepTime.contains("分钟")) {
            return stepTime;
        }
        if (Integer.toString(0).equals(stepTime)) {
            return ZERO_STEP_TIME_SHOW_DOC;
        }
        return String.format(STEP_TIME_FORMAT, stepTime);
    }

    private DealSkuItemVO buildDealSkuItemVO(String name, String value, List<SkuAttrAttrItemVO> valueAttrs, int type) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setType(type);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setValueAttrs(valueAttrs);
        return dealSkuItemVO;
    }

    @Data
    private static class ServiceStepModel {
        //使用仪器
        private String equipment;

        //使用产品
        private String product;

        //步骤名称
        private String stepName;

        //步骤耗时
        private String stepTakeTime;

        //步骤类型
        private String stepType;

        //步骤名称
        private String subStepName;

        //步骤说明
        private String stepDesc;

        //步骤耗时 （养生调理/spa按摩/皮肤管理）
        private String stepTime;

        //针对部位
        private String bodyPart;

    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 货分类和展示固定文案-货属性名称映射关系
         */
        Map<Long, List<SkuDisplayModel>> category2SkuDisplayModelList;

        /**
         * 标准团单服务步骤说明
         */
        String standardServiceProcessInfo;

        /**
         * 服务步骤或服务流程的展示类型
         */
        int stepType;

    }

    @Data
    private static class SkuDisplayModel {
        //货属性展示标题
        private String skuTitle;

        //货属性名称
        private String skuAttrName;
        //货属性值
        private String skuAttrValue;
        //货属性替换值 ：将货属性值替换为该值
        private String skuAttrReplaceValue;
    }
}
