package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Optional;

public class FilterUtils {

    /**
     * 找二级筛选的父节点，如果本身是一级筛选，返回0
     * @param filterBtnMList
     * @param filterId
     * @return
     */
    public static long findFilterParentId(List<FilterBtnM> filterBtnMList, long filterId) {
        if (CollectionUtils.isEmpty(filterBtnMList)) {
            return 0L;
        }
        for (FilterBtnM filterBtnM : filterBtnMList) {
            Optional<FilterBtnM> hitBtn = filterBtnM.getChildren().stream().filter(btn -> btn.getFilterId() == filterId).findFirst();
            if (hitBtn.isPresent()) {
                return filterBtnM.getFilterId();
            }
        }
        return 0L;
    }
}
