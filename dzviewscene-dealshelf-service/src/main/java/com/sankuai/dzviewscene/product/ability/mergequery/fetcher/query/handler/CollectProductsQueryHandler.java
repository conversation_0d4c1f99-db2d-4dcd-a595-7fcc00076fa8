package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.QueryConstants;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductIdM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class CollectProductsQueryHandler implements QueryHandler {

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityCxt ctx, String groupName,
                                                  Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.CollectProductsQueryHandler.query(com.sankuai.athena.viewscene.framework.ActivityCxt,java.lang.String,java.util.Map)");
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setTotal(loadTotal(ctx));
        productGroupM.setHasNext(false);
        productGroupM.setProducts(buildProducts(groupName, ctx.getParameters(), params));
        return CompletableFuture.completedFuture(productGroupM);
    }

    private List<ProductM> buildProducts(String groupName,
                                         Map<String, Object> ctxParams,
                                         Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.CollectProductsQueryHandler.buildProducts(java.lang.String,java.util.Map,java.util.Map)");
        List<String> productIds = (List<String>) ParamsUtil.getValue(ctxParams, PmfConstants.Params.collBizIds, null);
        if (CollectionUtils.isEmpty(productIds)) {
            return Lists.newArrayList();
        }
        return productIds.stream().map(id -> {
            ProductM productM = new ProductM();
            productM.setGroupName(groupName);
            productM.setId(buildProductId(id, params));
            return productM;
        }).collect(Collectors.toList());
    }


    private ProductIdM buildProductId(String id, Map<String, Object> groupParams) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.CollectProductsQueryHandler.buildProductId(java.lang.String,java.util.Map)");
        ProductIdM productIdM = new ProductIdM();
        productIdM.setId(Long.parseLong(id));
        productIdM.setType(ParamsUtil.getIntSafely(groupParams, QueryConstants.Params.productType));
        return productIdM;
    }

    private int loadTotal(ActivityCxt ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler.CollectProductsQueryHandler.loadTotal(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        List<String> productIds = (List<String>) ParamsUtil.getValue(ctx, PmfConstants.Params.collBizIds, null);
        if (CollectionUtils.isEmpty(productIds)) {
            return 0;
        }
        return productIds.size();
    }
}
