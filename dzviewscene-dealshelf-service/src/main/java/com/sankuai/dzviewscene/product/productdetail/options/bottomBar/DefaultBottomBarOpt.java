package com.sankuai.dzviewscene.product.productdetail.options.bottomBar;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.productdetail.ability.bottomBar.vpoints.BottomBarSingleButtonVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;


@VPointOption(name = "默认-底部bar选项", description = "适用于通过商品价格+配置化按钮名称构造底部bar的场景", code = DefaultBottomBarOpt.CODE, isDefault = true)
public class DefaultBottomBarOpt extends BottomBarSingleButtonVP<DefaultBottomBarOpt.Config> {

    public static final String CODE = "DefaultBottomBarOpt";

    private static final int AVAILABLE = 1;


    private static final int DISABLE = 0;

    @Override
    public ButtonVO compute(ActivityCxt activityCxt, BottomBarSingleButtonVP.Param param, DefaultBottomBarOpt.Config cfg) {
        ProductM productM = param.getProductMs().get(0);
        ButtonVO buttonVO = new ButtonVO();
        buttonVO.setSalePrice(productM.getBasePriceTag());
        buttonVO.setButtonName(BooleanUtils.isTrue(productM.getAvailable())?cfg.getButtonName():cfg.getDisableButtonName());
        buttonVO.setButtonStatus(BooleanUtils.isTrue(productM.getAvailable())? AVAILABLE : DISABLE);
        buttonVO.setDisableTips(BooleanUtils.isTrue(productM.getAvailable())? StringUtils.EMPTY : cfg.getDisableTips());
        buttonVO.setButtonUrl(productM.getOrderUrl());
        return buttonVO;
    }


    @Data
    @VPointCfg
    public static class Config {
        private String buttonName;

        private String disableTips;

        private String disableButtonName;
    }
}