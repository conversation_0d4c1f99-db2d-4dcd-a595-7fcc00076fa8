package com.sankuai.dzviewscene.product.dealstruct.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuGroupTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemFilterVP;
import lombok.Data;
import org.codehaus.plexus.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/2/10 4:58 下午
 */
@VPointOption(name = "Sku元素过滤能力默认变化点", description = "Sku元素过滤能力默认变化点",code = DefaultSkuItemFilterVPO.CODE, isDefault = true)
public class DefaultSkuItemFilterVPO extends SkuItemFilterVP<DefaultSkuItemFilterVPO.Config> {

    public static final String CODE = "DefaultSkuItemFilterVPO";

    @Override
    public Boolean compute(ActivityCxt context, Param param, Config config) {
        return true;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
