package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.moretext.DefaultProductAreaMoreTextOpt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;

@VPoint(name = "商品区-查看更多-展示文案", description = "商品区-查看更多-展示文案", code = ProductAreaMoreTextVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class ProductAreaMoreTextVP<T> extends PmfVPoint<String, ProductAreaMoreTextVP.Param, T> {

    public static final String CODE = "ProductAreaMoreTextVP";

    private static final String DEFAULT_REMAIN_TEXT_FORMAT = "更多%d个团购";

    private static final String DEFAULT_TOTAL_TEXT_FORMAT = "全部%d个团购";

    public String getRemainText(int totalNum, int defaultShowNum, String configFormat) {
        String textFormat = StringUtils.isNotEmpty(configFormat) ? configFormat : DEFAULT_REMAIN_TEXT_FORMAT;
        return String.format(textFormat, totalNum - defaultShowNum);
    }

    public String getTotalText(int totalNum, String configFormat) {
        String textFormat = StringUtils.isNotEmpty(configFormat) ? configFormat : DEFAULT_TOTAL_TEXT_FORMAT;
        return String.format(textFormat, totalNum);
    }

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 当前商品数
         */
        private int itemAreaItemCnt;

        /**
         * 商品总数
         */
        private int totalCount;

        /**
         * 商品区商品
         */
        private List<ProductM> floorProductM;

        /**
         * 商品区默认的展示个数
         */
        private int defaultShowNum;

        private long filterId;
    }
}