package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "sku价格默认变化点", description = "sku价格默认变化点",code = DefaultSkuPriceOpt.CODE, isDefault = true)
public class DefaultSkuPriceOpt extends SkuPriceVP<DefaultSkuPriceOpt.Config> {

    public static final String CODE = "DefaultSkuPriceOpt";

    private static final String DEFAULT_FORMAT = "%s元";

    private static final String NO_SHOW_PRICE_FLAG = "不展示价格标识";

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (param.getPrice() == null || NO_SHOW_PRICE_FLAG.equals(config.getFormat())) {
            return null;
        }
        String format = StringUtils.isEmpty(config.getFormat()) ? DEFAULT_FORMAT : config.getFormat();
        return String.format(format, param.getPrice().stripTrailingZeros().toPlainString());
    }

    @Data
    @VPointCfg
    public static class Config {
        private String format;
    }
}
