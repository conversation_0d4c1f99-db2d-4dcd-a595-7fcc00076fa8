package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/25
 */
public class DzTagStyleWrapUtils {

    /**
     * 根据图片链接覆盖优惠标签样式，以下为作用和注意事项
     * 1. 根据图片链接，修改右侧标签的边框颜色、文字颜色、箭头图片、控制是否有间隙
     * 2. 如果不匹配，则保持原默认值，不做修改
     *
     * @param dzTagVO
     */
    public static void overridePromoStyle(DzTagVO dzTagVO) {
        if (dzTagVO == null) {
            return;
        }
        overridePromoStyleByPic(dzTagVO);
    }

    private static void overridePromoStyleByPic(DzTagVO dzTagVO) {
        if (dzTagVO.getPrePic() == null || StringUtils.isEmpty(dzTagVO.getPrePic().getPicUrl())) {
            return;
        }
        overrideTagByCfg(dzTagVO, getTagStyleCfgByPic(dzTagVO.getPrePic().getPicUrl()));
    }


    private static TagStyleCfg getTagStyleCfgByPic(String pic) {
        String styleCfgStr = Lion.getString("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.shelf.union.promo.style");
        if (StringUtils.isEmpty(styleCfgStr)) {
            return null;
        }
        Config config = JsonCodec.decode(styleCfgStr, Config.class);
        if (config == null || MapUtils.isEmpty(config.getUrl2TagStyleCfgMap())) {
            return null;
        }
        return config.getUrl2TagStyleCfgMap().get(pic);
    }

    private static void overrideTagByCfg(DzTagVO dzTagVO, TagStyleCfg styleCfg) {
        if (dzTagVO == null || styleCfg == null) {
            return;
        }
        if (StringUtils.isNotEmpty(styleCfg.getTextColor())) {
            dzTagVO.setTextColor(styleCfg.getTextColor());
        }
        if (StringUtils.isNotEmpty(styleCfg.getBorderColor())) {
            dzTagVO.setBorderColor(styleCfg.getBorderColor());
        }
        if (styleCfg.getPrePic() != null) {
            dzTagVO.setPrePic(styleCfg.getPrePic());
        }
        if (styleCfg.getAfterPic() != null) {
            dzTagVO.setAfterPic(styleCfg.getAfterPic());
        }
        if (styleCfg.isHasBorder()) {
            dzTagVO.setHasBorder(styleCfg.isHasBorder());
        }
        if (styleCfg.getBackground() != null) {
            dzTagVO.setBackground(styleCfg.getBackground());
        }
        dzTagVO.setNoGapBetweenPicText(styleCfg.isNoGapBetweenPicText());
     }

    @Data
    public static class Config {
        /**
         * key-url
         * value-标签覆盖的样式，有效值才会进行覆盖
         */
        private Map<String, TagStyleCfg> url2TagStyleCfgMap;
    }

    @Data
    public static class TagStyleCfg {
        /**
         * 文字颜色
         */
        private String textColor;

        /**
         * 边框颜色
         */
        private String borderColor;

        /**
         * 前置填充图片
         */
        private DzPictureComponentVO prePic;

        /**
         * 后置填充图片
         */
        private DzPictureComponentVO afterPic;

        /**
         * 是否无间距，true 无间距
         * 参考：2023.04 美团侧因为换无间距的图片了，所以可以填 true
         */
        private boolean noGapBetweenPicText;

        private boolean hasBorder;

        private String background;
    }
}
