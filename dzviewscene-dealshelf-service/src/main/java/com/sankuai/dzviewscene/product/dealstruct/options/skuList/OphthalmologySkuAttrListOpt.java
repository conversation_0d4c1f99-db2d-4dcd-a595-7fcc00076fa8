package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuAttrListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.CommonAttrVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemConfig;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.MedicalItemProductTagsOpt;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/12 5:29 下午
 */
@VPointOption(name = "眼科sku属性列表变化点", description = "眼科sku属性列表变化点，根据配置展示sku属性", code = OphthalmologySkuAttrListOpt.CODE)
public class OphthalmologySkuAttrListOpt extends SkuAttrListVP<OphthalmologySkuAttrListOpt.Config> {

    public static final String CODE = "OphthalmologySkuAttrListOpt";
    private static final String meanKey = "意义";
    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.Ophthalmology_checkItemExtConfig", defaultValue = "{}")
    private ProjectExtConfig projectExtConfig;

    @Override
    public List<DealSkuItemVO> compute(ActivityCxt context, Param param, Config config) {
        if (config == null
                || param == null
                || CollectionUtils.isEmpty(config.skuAttrDisplayRules)) {
            return null;
        }
        return buildDealSkuItems(config, param.getSkuItemDto(), param.getDealAttrs());
    }

    private List<DealSkuItemVO> buildDealSkuItems(Config config, SkuItemDto skuItemDto, List<AttrM> dealAttrs) {
        List<SkuAttrDisplayRuleCfg> skuAttrDisplayRules = config.getSkuAttrDisplayRules();
        if (CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        for (SkuAttrDisplayRuleCfg skuAttrDisplayRule : skuAttrDisplayRules) {
            if (skuAttrDisplayRule.getRouteCondition() == null || CollectionUtils.isEmpty(skuAttrDisplayRule.getDisplayAttrRules())) {
                continue;
            }
            boolean satisfyAllAttrKeyValue = ProductMAttrUtils.satisfyAllAttrKeyValue(dealAttrs, skuAttrDisplayRule.getRouteCondition().getSatisfyAllAttrKeyValuesMap());
            boolean satisfyAllSkuAttrKeyValue = ProductMAttrUtils.satisfyAllSkuAttrKeyValue(skuItemDto, skuAttrDisplayRule.getRouteCondition().getSatisfySkuAllAttrKeyValuesMap());
            if (satisfyAllAttrKeyValue && satisfyAllSkuAttrKeyValue) {
                return buildDealSkuItemsByConfig(dealAttrs, skuItemDto.getAttrItems(), skuAttrDisplayRule.getDisplayAttrRules());
            }
        }
        return dealSkuItemVOS;
    }

    private List<DealSkuItemVO> buildDealSkuItemsByConfig(List<AttrM> dealAttrs, List<SkuAttrItemDto> attrItems, List<DisplayAttrRuleCfg> displayAttrRules) {
        List<DealSkuItemVO> dealSkuItemVOS = new ArrayList<>();
        displayAttrRules.forEach(attrRule -> {
            List<DealSkuItemVO> list = convertItemVO(attrRule, dealAttrs, attrItems);
            if (CollectionUtils.isNotEmpty(list)) {
                dealSkuItemVOS.addAll(list);
            }
        });
        return dealSkuItemVOS;
    }

    private List<DealSkuItemVO> convertItemVO(DisplayAttrRuleCfg attrRule, List<AttrM> dealAttrs, List<SkuAttrItemDto> attrItems) {
        if (attrRule.getType() == 2) {
            return convertProjectItemVO(attrRule, dealAttrs, attrItems);
        }
        return convertTextItemVO(attrRule, dealAttrs, attrItems);
    }

    private List<DealSkuItemVO> convertTextItemVO(DisplayAttrRuleCfg attrRule, List<AttrM> dealAttrs, List<SkuAttrItemDto> attrItems) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(attrRule.getAttrTitle());
        String attrValue = getAttrValue(attrRule, dealAttrs, attrItems);
        String nextAttrValue = getNextAttrValue(attrRule, dealAttrs, attrItems);
        if (StringUtils.isEmpty(attrValue) && StringUtils.isEmpty(nextAttrValue)) {
            return null;
        }
        String middleSymbol = StringUtils.isEmpty(attrRule.getMiddleSymbol()) ? "" : attrRule.getMiddleSymbol();
        String suffix = StringUtils.isEmpty(attrRule.getSuffix()) ? "" : attrRule.getSuffix();
        attrValue = attrValue + middleSymbol + nextAttrValue + suffix;
        dealSkuItemVO.setValue(attrValue);
        return Lists.newArrayList(dealSkuItemVO);
    }

    private List<DealSkuItemVO> convertProjectItemVO(DisplayAttrRuleCfg attrRule, List<AttrM> dealAttrs, List<SkuAttrItemDto> attrItems) {
        List<DealSkuItemVO> list = Lists.newArrayList();
        if (attrRule.getProjectDisplayRule() == null) {
            return null;
        }
        List<String> checkItemSort = projectExtConfig.getCheckItemSortList();
        Map<String, String> meanMap = Optional.ofNullable(projectExtConfig.getMeanMap()).orElse(Maps.newHashMap());
        ProjectDisplayRule projectRule = attrRule.getProjectDisplayRule();
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(attrRule.getAttrTitle());
        String projectValue = attrItems.stream().filter(r -> r.getAttrName().equals(attrRule.getAttrKey())).map(r -> r.getAttrValue()).findFirst().orElse(null);
        if (StringUtils.isEmpty(projectValue)) {
            return null;
        }
        List<Map> projectValueList = JsonCodec.converseList(projectValue, Map.class);
        int count = projectValueList.size();
        if (count <= 0) {
            return null;
        }
        String projectCountTitle = projectRule.getTitleTemplate().replace("count", String.valueOf(count));
        dealSkuItemVO.setValue(projectCountTitle);
        list.add(dealSkuItemVO);
        //--检查项目
        List<Map<String, String>> processMap = projectValueList.stream().map(m -> {
            Map<String, String> nm = Maps.newHashMap();
            for (Object key : m.keySet()) {
                Object v = m.get(key);
                nm.put(key.toString(), v == null ? "" : v.toString());
            }
            return nm;
        }).collect(Collectors.toList());

        DealSkuItemVO processItemVO = new DealSkuItemVO();
        List<SkuAttrAttrItemVO> valueAttrList = processMap.stream().map(map -> {
            SkuAttrAttrItemVO attrAttrItemVO = new SkuAttrAttrItemVO();
            attrAttrItemVO.setName(map.get(projectRule.getAttrAttrNameKey()));
            if (CollectionUtils.isNotEmpty(projectRule.getAttrAttrInfoKey())) {
                attrAttrItemVO.setInfo(projectRule.getAttrAttrInfoKey().stream()
                        .map(r -> map.getOrDefault(r, ""))
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(projectRule.getAttrAttrCommonKey())) {
                List<CommonAttrVO> commonAttrVOS =
                        projectRule.getAttrAttrCommonKey().stream()
                                .map(r -> {
                                    CommonAttrVO commonAttrVO = new CommonAttrVO();
                                    String name = r.getName();
                                    String value = "";
                                    //特殊逻辑
                                    if (r.getKey().equals(meanKey)) {
                                        value = meanMap.getOrDefault(attrAttrItemVO.getName(), "");
                                    } else {
                                        value = map.get(r.getKey());
                                    }
                                    commonAttrVO.setName(name);
                                    commonAttrVO.setValue(value);
                                    return commonAttrVO;
                                })
                                .collect(Collectors.toList());

                attrAttrItemVO.setValues(commonAttrVOS);
            }
            return attrAttrItemVO;
        }).collect(Collectors.toList());
        processItemVO.setValueAttrs(valueAttrList);
        processItemVO.setType(attrRule.getType());
        if (MapUtils.isNotEmpty(attrRule.config) && attrRule.config.containsKey("limit")) {
            int limit = Integer.parseInt(attrRule.config.getOrDefault("limit", "0"));
            processItemVO.setConfig(new DealSkuItemConfig(limit));
        }
        //processItemVO里valueAttrs排序
        sortCheckItem(checkItemSort, processItemVO.getValueAttrs());
        list.add(processItemVO);

        return list;
    }

    private void sortCheckItem(List<String> sortList, List<SkuAttrAttrItemVO> valueAttrs) {
        if (CollectionUtils.isEmpty(valueAttrs) || CollectionUtils.isEmpty(sortList)) return;
        valueAttrs.sort(Comparator.comparingInt(l -> sortList.indexOf(l.getName())));
    }

    private String getAttrValue(DisplayAttrRuleCfg ruleCfg, List<AttrM> dealAttrs, List<SkuAttrItemDto> attrItems) {
        String dealAttrKey = ruleCfg.getDealAttrKey();
        String skuAttrKey = ruleCfg.getAttrKey();
        String attrValue = null;
        if (StringUtils.isNotEmpty(dealAttrKey)) {
            attrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, dealAttrKey);
        }
        if (StringUtils.isEmpty(attrValue) && StringUtils.isNotEmpty(skuAttrKey)) {
            attrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, skuAttrKey);
        }
        //别名配置为空，或没有配置，则直接展示
        String rvalue = null;
        if (MapUtils.isEmpty(ruleCfg.attrValueAliasMap) || !ruleCfg.getAttrValueAliasMap().keySet().contains(attrValue)) {
            rvalue = attrValue;
        } else {
            rvalue = ruleCfg.getAttrValueAliasMap().get(attrValue);
        }
        return StringUtils.isEmpty(rvalue) ? StringUtils.EMPTY : rvalue;
    }

    private String getNextAttrValue(DisplayAttrRuleCfg ruleCfg, List<AttrM> dealAttrs, List<SkuAttrItemDto> attrItems) {
        String skuAttrKey = ruleCfg.getNextAttrKey();
        String attrValue = null;
        if (StringUtils.isEmpty(attrValue) && StringUtils.isNotEmpty(skuAttrKey)) {
            attrValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(attrItems, skuAttrKey);
        }
        //别名配置为空，或没有配置，则直接展示
        String rvalue = null;
        if (MapUtils.isEmpty(ruleCfg.attrValueAliasMap) || !ruleCfg.getAttrValueAliasMap().keySet().contains(attrValue)) {
            rvalue = attrValue;
        } else {
            rvalue = ruleCfg.getAttrValueAliasMap().get(attrValue);
        }
        return StringUtils.isEmpty(rvalue) ? StringUtils.EMPTY : rvalue;
    }

    @Data
    @VPointCfg
    public static class Config {
        //sku属性构造模型列表
        private List<SkuAttrDisplayRuleCfg> skuAttrDisplayRules;
    }

    @Data
    private static class SkuAttrDisplayRuleCfg {
        //路由条件
        private RouteConditionCfg routeCondition;
        //sku属性规则配置
        private List<DisplayAttrRuleCfg> displayAttrRules;
    }

    @Data
    private static class DisplayAttrRuleCfg {
        //sku属性展示标题
        private String attrTitle;
        //属性key
        private String attrKey;
        //行业属性key
        private String dealAttrKey;
        //属性value与别名映射
        private Map<String, String> attrValueAliasMap;
        //类型 0-文案 1-流程类型(按照processDisplayRule展示)
        private int type = 0;
        //
        private ProjectDisplayRule projectDisplayRule;

        private Map<String, String> config;

        private String suffix;

        private String nextAttrKey;

        private String middleSymbol;
    }

    @Data
    private static class RouteConditionCfg {
        //满足所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfyAllAttrKeyValuesMap;
        //满足服务项目Sku所有属性key及属性值的映射，当该字段为空时不需要校验
        private Map<String, List<String>> satisfySkuAllAttrKeyValuesMap;
    }

    @Data
    private static class ProjectDisplayRule {
        private String titleTemplate = "共%count%项";
        //info数据的skuKey 支持多个
        private List<String> attrAttrInfoKey;
        private String attrAttrNameKey;
        //AttrAttr的commonValue的key
        private List<CommonKey> attrAttrCommonKey;
    }

    @Data
    private static class CommonKey {
        private String name;
        private String key;
    }

    @Data
    private static class ProjectExtConfig {
        private List<String> checkItemSortList;

        private Map<String, String> meanMap;
    }
}
