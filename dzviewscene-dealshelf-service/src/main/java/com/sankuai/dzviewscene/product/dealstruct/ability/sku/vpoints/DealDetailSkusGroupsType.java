package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/1 11:23 上午
 */
@Deprecated
@VPoint(name = "团购详情sku组列表type", description = "团购详情sku组列表type，支持配置", code = DealDetailSkusGroupsType.CODE, ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class DealDetailSkusGroupsType <T> extends PmfVPoint<String, DealDetailSkusGroupsType.Param, T> {

    public static final String CODE = "DealDetailSkusGroupsType";

    @Data
    @Builder
    @VPointParam
    public static class Param {
    }

}
