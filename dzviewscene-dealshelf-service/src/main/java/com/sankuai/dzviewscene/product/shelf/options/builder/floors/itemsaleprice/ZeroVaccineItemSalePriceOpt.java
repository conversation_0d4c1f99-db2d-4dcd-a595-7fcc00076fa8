package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsaleprice;

import com.dianping.lion.client.Lion;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import org.apache.commons.lang.StringUtils;

@VPointOption(name = "0元预约商家售价价格", description = "只针对tradeType=18的商品", code = "ZeroVaccineItemSalePriceOpt")
public class ZeroVaccineItemSalePriceOpt extends ItemSalePriceVP<Void> {
    /*
     * key:"retailPriceStyle"
     * value:"1"
     * 1：正常展示
     * 2：隐藏
     * 3：起价
     */
    private static final String RETAIL_PRICE_STYLE = "retailPriceStyle";

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        ProductM productM = param.getProductM();
        String retailPriceStyle = getExtAttrValueByName(productM, RETAIL_PRICE_STYLE);
        //正常展示和起价吐出团购价格,retailPriceStyle为null也正常吐出价格
        if (StringUtils.isEmpty(retailPriceStyle) || ("1").equals(retailPriceStyle) || ("3").equals(retailPriceStyle)) {
            return productM.getBasePriceTag();
        }
        //前端根据空值判断PriceTips
        return FloorsBuilderExtAdapter.EMPTY_VALUE;
    }

    private String getExtAttrValueByName(ProductM productM, String name) {
        if (productM == null || productM.getExtAttrs() == null) {
            return null;
        }
        return productM.getExtAttrs().stream().filter(attrM -> attrM != null && name.equals(attrM.getName()))
                .findFirst().map(AttrM::getValue).orElse(null);
    }
}
