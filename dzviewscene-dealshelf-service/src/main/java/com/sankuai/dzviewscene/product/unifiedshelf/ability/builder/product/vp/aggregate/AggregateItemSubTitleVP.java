package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductHierarchyNodeM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

@VPoint(name = "聚合商卡-副标题", description = "聚合商卡-副标题", code = AggregateItemSubTitleVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class AggregateItemSubTitleVP<T> extends PmfVPoint<ItemSubTitleVO, AggregateItemSubTitleVP.Param, T> {

    public static final String CODE = "AggregateItemSubTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {

        private ProductHierarchyNodeM nodeM;

        private Map<String, ProductM> productMMap;
    }
}
