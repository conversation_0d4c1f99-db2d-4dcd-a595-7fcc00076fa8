package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTagsVP;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29 12:06 下午
 */
@VPointOption(name = "商品-ProductTags",
        description = "默认透传主题",
        code = "DefaultProductTags",
        isDefault = true)
public class DefaultProductTags extends ProductTagsVP<DefaultProductTags.Config> {

    @Override
    public List<String> compute(ActivityCxt activityCxt, Param param, DefaultProductTags.Config config) {
        return param.getProductM().getProductTags();
    }

    @VPointCfg
    @Data
    public static class Config {
    }
}
