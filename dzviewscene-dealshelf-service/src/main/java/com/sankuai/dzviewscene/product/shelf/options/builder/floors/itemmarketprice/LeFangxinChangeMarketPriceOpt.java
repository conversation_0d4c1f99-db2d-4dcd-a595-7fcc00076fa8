package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemmarketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemMarketPriceVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;

/**
 * @author: lidu
 * @date: 2024/07/17
 **/
@VPointOption(name = "美团放心改标签不展示市场价",
        description = "美团放心改标签不展示市场价，其他情况默认展示",
        code = "LeFangxinChangeMarketPriceOpt")
public class LeFangxinChangeMarketPriceOpt extends ItemMarketPriceVP<Void> {

    private static final String IS_FANGXIN_CHANGE = "isFangxinChange";

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        if(isFangxinChange(param)) {
            return FloorsBuilderExtAdapter.EMPTY_VALUE;
        }
        return param.getProductM().getMarketPrice();
    }

    /**
     * 判断商品是否为放心改。
     *
     * @param param 参数
     * @return 如果是放心改返回true，否则返回false
     */
    private boolean isFangxinChange(Param param) {
        String value = param.getProductM().getAttr(IS_FANGXIN_CHANGE);
        return "true".equalsIgnoreCase(value);
    }
}

