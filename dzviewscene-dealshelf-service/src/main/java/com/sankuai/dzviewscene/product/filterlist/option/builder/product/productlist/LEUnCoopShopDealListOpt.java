package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.spuproduct.enums.SpuAttrEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.model.LeUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.LeUnCoopShopUniverseInfoOpt;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.leads.process.thrift.dto.leads.LeadsCountReqDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * LE非合作商户的团购列表组装，强业务定制
 */
@VPointOption(name = "LE非合作商户的团购列表组装",
        description = "LE非合作商户的团购列表组装",
        code = "LEUnCoopShopDealListOpt")
public class LEUnCoopShopDealListOpt extends ProductListVP<LEUnCoopShopDealListOpt.Config> {

    private static final int DEFAULT_PAGE_SIZE = 10;

    private static final List<String> WED_PHOTO_UNCOOP_SCENE_CODE_LIST = Lists
            .newArrayList("wedphoto_uncoopshop_deal_spu_list", "wedphoto_uncoopshop_unified_deal_spu_list");

    private static final List<String> PHOTO_UNCOOP_SCENE_CODE_LIST = Lists.newArrayList("photo_uncoolshop_deal_list",
            "photo_uncoolshop_unified_deal_list");

    public static final double ZERO = 0.001;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        if (CollectionUtils.isEmpty(productMS)) {
            return productMS;
        }
        //将商品还原回group模式
        Map<String, List<ProductM>> groupName2Products = productMS.stream()
                .filter(productM -> StringUtils.isNotBlank(productM.getAttr(DealListBuilder.PRODUCT_ATTR_GROUP_NAME)))
                .collect(Collectors.groupingBy(productM -> productM.getAttr(DealListBuilder.PRODUCT_ATTR_GROUP_NAME)));
        if (MapUtils.isEmpty(groupName2Products)) {
            return productMS;
        }
        List<ProductM> result = new ArrayList<>();
        // 取第一组商品
        result.addAll(MapUtils.getObject(groupName2Products, config.getFirstShowGroupName(), Lists.newArrayList()));
        //摄影置顶标品距离过滤，至多取两个
        addPhotoFilter(result, context, config);
        // 取第二组商品，并且增加距离过滤
        add2ResultWhenNotExist(result, filterAndBuildSecondGroup(context, groupName2Products.get(config.getSecondShowGroupName()), config));
        // 取第三组商品
        add2ResultWhenNotExist(result, getGroupProducts(groupName2Products.get(config.getThirdShowGroupName()), context, config));
        LeUncoopShopShelfAttrM uncoopShopShelfAttrM = context.getParam(LeUnCoopShopUniverseInfoOpt.CODE);
        // 截取
        int pageSize = getPageSize(uncoopShopShelfAttrM, context, config);
        // 为标品加上堆头标题和cardKey
        addTopTitleAndCardKey(context, uncoopShopShelfAttrM, result, config);
        return result.stream().limit(pageSize).collect(Collectors.toList());
    }

    public void addPhotoFilter(List<ProductM> result, ActivityCxt context, Config config) {
        if (!isPhotoUncoolShopDealList(context) || config == null) {
            return;
        }
        List<ProductM> filteredProducts = filterProductsByDistance(result, config.getPhotoHeadDistanceLimit());
        List<ProductM> sortedAndLimitedProducts = sortAndLimitProducts(filteredProducts, config.getPhotoHeadLimit());
        result.clear();
        result.addAll(sortedAndLimitedProducts);
    }

    private boolean isPhotoUncoolShopDealList(ActivityCxt context) {
        String sceneCode = context.getParam(ShelfActivityConstants.Params.sceneCode);
        return PHOTO_UNCOOP_SCENE_CODE_LIST.contains(sceneCode);
    }

    private List<ProductM> filterProductsByDistance(List<ProductM> products, double limit) {
        return products.stream()
                .filter(productM -> isValidShop(productM) && getDistanceNum(productM) <= limit && StringUtils.isNotEmpty(productM.getBasePriceTag()))
                .collect(Collectors.toList());
    }

    private boolean isValidShop(ProductM productM) {
        return productM != null && CollectionUtils.isNotEmpty(productM.getShopMs()) && productM.getShopMs().get(0) != null;
    }

    private List<ProductM> sortAndLimitProducts(List<ProductM> products, int limit) {
        return products.stream()
                .sorted(Comparator.comparing(t->new BigDecimal(t.getBasePriceTag())))
                .limit(limit)
                .collect(Collectors.toList());
    }

    private void add2ResultWhenNotExist(List<ProductM> result, List<ProductM> needAddProducts) {
        if (CollectionUtils.isEmpty(needAddProducts)) {
            return;
        }
        for (ProductM productM : needAddProducts) {
            if (findProductMById(result, productM.getProductId(), productM.getProductType()) != null) {
                continue;
            }
            result.add(productM);
        }
    }

    private ProductM findProductMById(List<ProductM> result, int productId, int productType) {
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.stream()
                .filter(productM -> productM.getProductId() == productId && productM.getProductType() == productType)
                .findFirst().orElse(null);
    }

    private List<ProductM> filterAndBuildSecondGroup(ActivityCxt context, List<ProductM> productMS, Config config) {
        if (productMS == null) {
            return new ArrayList<>();
        }
        Integer distanceLimit = getDistanceLimitFromHaiMa(context);
        if (distanceLimit == null || distanceLimit < 0) {
            return productMS;
        }
        List<ProductM> result = productMS.stream()
                .filter(productM -> {
                    if (productM == null || CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
                        return false;
                    }
                    return getDistanceNum(productM) <= distanceLimit;
                })
                .collect(Collectors.toList());

        // 当前仅婚摄行业需要过滤已留资商品
        if (WED_PHOTO_UNCOOP_SCENE_CODE_LIST.contains(context.getSceneCode())) {
            return filterLeadsProduct(result, context, config);
        }
        // 其他场景直接返回商品
        return result;
    }

    private double getDistanceNum(ProductM productM) {
        if (productM.getShopMs().get(0).getDistanceNum() < ZERO) {
            return productM.getObjAttrWithDefault(SpuAttrEnum.ATTR_NEAREST_SHOP_DISTANCE.getKey(), 0.0);
        }
        return productM.getShopMs().get(0).getDistanceNum();
    }

    private Integer getDistanceLimitFromHaiMa(ActivityCxt context) {
        LeUncoopShopShelfAttrM uncoopShopShelfAttrM = context.getParam(LeUnCoopShopUniverseInfoOpt.CODE);
        if (uncoopShopShelfAttrM == null) {
            return null;
        }
        return uncoopShopShelfAttrM.getSlopeCoverDistance();
    }

    private void addTopTitleAndCardKey(ActivityCxt context, LeUncoopShopShelfAttrM uncoopShopShelfAttrM, List<ProductM> productMS, Config config) {
        if (productMS == null) {
            return;
        }
        String topAreaTitle = null;
        int cardIdx = 1;
        String specialCardKey = config.getProductAttrKeyForSpecialCard();
        if (uncoopShopShelfAttrM != null) {
            topAreaTitle = uncoopShopShelfAttrM.getRecommendTopTitle();
        }

        // 为标品添加标题，并为相邻同一组商品添加specialCardKey
        for (int i = 0; i < productMS.size(); i++) {
            ProductM product = productMS.get(i);
            // 为特点商品添加堆头标题
            if (validateAddTopAreaTitle(context, product)) {
                addAttr(product, config.getProductAttrKeyForTopGroupTitle(), topAreaTitle);
            }
            // 为商品添加specialCardKey，用于前端标识同一组相邻商品
            addAttr(product, config.getProductAttrKeyForSpecialCard(), specialCardKey + cardIdx);
            if (i + 1 < productMS.size() && product.getProductType() != productMS.get(i + 1).getProductType()) {
                cardIdx++;
            }
        }
    }

    private boolean validateAddTopAreaTitle(ActivityCxt context, ProductM product) {
        if (product == null) {
            return false;
        }
        // 如果是标品则需要添加堆头标题
        return product.getProductType() == ProductTypeEnum.GENERAL_SPU.getType();
    }

    private void addAttr(ProductM productM, String attrKey, String attrValue) {
        if (productM == null || StringUtils.isAnyBlank(attrKey, attrValue)) {
            return;
        }
        productM.setAttr(attrKey, attrValue);
    }

    private List<ProductM> getGroupProducts(List<ProductM> productMS, ActivityCxt context, Config config) {
        if (productMS == null) {
            return Lists.newArrayList();
        }
        // 当前仅婚摄行业需要过滤已留资商品
        if (WED_PHOTO_UNCOOP_SCENE_CODE_LIST.contains(context.getSceneCode())) {
            return filterLeadsProduct(productMS, context, config);
        }
        // 其他场景直接返回商品
        return productMS;
    }

    private int getPageSize(LeUncoopShopShelfAttrM uncoopShopShelfAttrM, ActivityCxt context, Config config) {
        int pageSize = 0;
        // 先尝试从海马配置中取
        if (uncoopShopShelfAttrM != null && uncoopShopShelfAttrM.getMaxShowNum() != null) {
            pageSize = uncoopShopShelfAttrM.getMaxShowNum();
        } else {
            // 取不到则从config配置中取，再取不到则从入参中取
            pageSize = config.getLimit() != null ? config.getLimit() : ParamsUtil.getIntSafely(context, PmfConstants.Params.pageSize);
        }
        if (pageSize < 1) {
            pageSize = DEFAULT_PAGE_SIZE;
        }
        return pageSize;
    }

    private List<ProductM> filterLeadsProduct(List<ProductM> productMS, ActivityCxt context, Config config) {
        if (CollectionUtils.isEmpty(productMS) || config.getLeadsFilterDays() == null || config.getLeadsFilterDays() <= 0)  {
            return productMS;
        }
        List<ProductM> result = Lists.newArrayList();
        int platform = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        for (ProductM productM : productMS) {
            if (productM == null || CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
                continue;
            }
            Long shopId = getShopId(productM);
            // 获取shopId
            CompletableFuture<Long> mtShopIdFuture = CompletableFuture.completedFuture(shopId);
            Long userId = Optional.ofNullable((Long) context.getParam(ShelfActivityConstants.Params.mtUserId)).orElse(0L);;
            if (!PlatformUtil.isMT(platform)) {
                mtShopIdFuture = compositeAtomService.getMtByDpPoiIdL(shopId).thenApply(id -> Optional.ofNullable(id).orElse(0L));
                userId = Optional.ofNullable((Long) context.getParam(ShelfActivityConstants.Params.dpUserId)).orElse(0L);
            }
            // 获取手机信息
            CompletableFuture<String> mobileFuture = compositeAtomService.queryMobile(userId, platform);
            // 设置时间
            LocalDateTime currentTime = LocalDateTime.now();
            Long endDate = currentTime.toInstant(ZoneOffset.UTC).toEpochMilli();
            Long startDate = currentTime.minusDays(config.getLeadsFilterDays()).toInstant(ZoneOffset.UTC).toEpochMilli();
            // 获取当前用户在商户的留资情况
            CompletableFuture<Integer> leadsCountFuture = mtShopIdFuture.thenCombine(mobileFuture, (mtShopId, mobile) -> {
                if (mtShopId == null || mtShopId <= 0 || StringUtils.isBlank(mobile)) {
                    return null;
                }
                LeadsCountReqDTO reqDTO = buildLeadsCountReq(Lists.newArrayList(mtShopId), mobile, startDate, endDate);
                return compositeAtomService.queryLeadsCount(reqDTO).join();
            });
            Integer leadsCount = leadsCountFuture.join();
            // 仅当前商品有明确留资数量时，才会将其过滤
            if (leadsCount != null && leadsCount > 0) {
                continue;
            }
            result.add(productM);
        }
        return result;
    }

    private long getShopId(ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return 0;
        }
        if (productM.getShopMs().get(0).getLongShopId() != 0) {
            return productM.getShopMs().get(0).getLongShopId();
        }
        return productM.getShopMs().get(0).getShopId();
    }

    private LeadsCountReqDTO buildLeadsCountReq(List<Long> mtShopIds, String mobile, Long startDate, Long endDate) {
        LeadsCountReqDTO reqDTO = new LeadsCountReqDTO();
        reqDTO.setMtShopIdList(mtShopIds);
        reqDTO.setPhone(mobile);
        reqDTO.setStartDate(startDate);
        reqDTO.setEndDate(endDate);
        return reqDTO;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 第一组商品的groupName，该组商品会展示在列表的顶部，并且附带特殊样式
         */
        private String firstShowGroupName = "置顶";

        /**
         * 第二组商品的groupName
         */
        private String secondShowGroupName = "广告";

        /**
         * 第三组商品的groupName
         */
        private String thirdShowGroupName = "推荐";

        /**
         * 置顶商品组的标题key
         */
        private String productAttrKeyForTopGroupTitle = "topAreaTitle";

        /**
         * 置顶商品组的副标题key
         */
        private String productAttrKeyForTopGroupSubTitle = "topAreaSubTitle";

        /**
         * 特殊卡商品组的key
         */
        private String productAttrKeyForSpecialCard = "specialCard";

        /**
         * 商品总个数
         */
        private Integer limit;

        /**
         * 留资过滤天数
         */
        private Integer leadsFilterDays;

        private int photoHeadDistanceLimit=5000;

        private int photoHeadLimit=2;
    }

}
