package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.multisource;

import com.dianping.cat.Cat;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.utils.PlayActivityUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 从团单属性中获取值
 * 支持 aviator 表达式进行计算
 *
 * <AUTHOR>
 * @date 2022/3/18
 */
@Component
public class DealAttrSourceTagBuilder implements SingleSourceTagBuilder<DealAttrSourceTagBuilder.Config> {

    public static final String CODE = "DealAttr";

    @Override
    public Map<String, String> buildTags(ItemProductTagsVP.Param param, List<Config> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return new HashMap<>();
        }
        Map<String, String> result = new HashMap<>(configs.size());
        for (Config config : configs) {
            String tagValue = null;
            if (StringUtils.isNotEmpty(config.getAttrKey())) {
                //单 key 计算
                tagValue = getTagValueFromSingleAttr(config, param.getProductM());
            }
            if (CollectionUtils.isNotEmpty(config.getAttrKeys())) {
                //多 key 计算
                tagValue = getTagValueFromMultiAttr(config, param.getProductM());
            }
            if (StringUtils.isNotEmpty(tagValue)) {
                result.put(config.getKey(), tagValue);
            }
        }
        return result;
    }

    private String getTagValueFromSingleAttr(Config config, ProductM productM) {
        String rawValue = productM.getAttr(config.getAttrKey());
        //静态的
        if (StringUtils.isEmpty(rawValue) || StringUtils.isEmpty(config.getExp())) {
            return rawValue;
        }
        //动态的
        Expression expression = AviatorEvaluator.getInstance().compile(config.getExp(), true);
        Map<String, Object> env = new HashMap<>(2);
        env.put("value", rawValue);
        Object result = expression.execute(env);
        return result == null ? null : result.toString();
    }

    /**
     * 多 key 的拼接
     *
     * @param config
     * @param productM
     * @return
     */
    private String getTagValueFromMultiAttr(Config config, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.multisource.DealAttrSourceTagBuilder.getTagValueFromMultiAttr(DealAttrSourceTagBuilder$Config,ProductM)");
        if (CollectionUtils.isEmpty(config.getAttrKeys())) {
            return null;
        }
        Expression expression = AviatorEvaluator.getInstance().compile(config.getExp(), true);
        Map<String, Object> env = new HashMap<>();
        for (String attrKey : config.getAttrKeys()) {
            env.put(attrKey, productM.getAttr(attrKey));
        }
        Object result = expression.execute(env);
        return result == null ? null : result.toString();
    }

    @Override
    public String getName() {
        return CODE;
    }

    @Data
    public static class Config extends SingleSourceTagConfig {
        /**
         * 团单属性的属性 Key
         */
        private String attrKey;

        /**
         * 团单属性的属性 Key 列表，和单 key 互斥
         */
        private List<String> attrKeys;

        /**
         * 可选：aviator 表达式 进行计算，如果不填写默认直接取 attrValue
         * 入参：attrKey 对应的 attrValue
         */
        private String exp;
    }
}
