package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemwarmup;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemWarmUpVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.WarmUpItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.WarmUpVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@VPointOption(
        name = "商场预热样式",
        description = "商场预热样式",
        code = "ShoppingMallItemWarmUpOpt")
public class ShoppingMallItemWarmUpOpt extends ItemWarmUpVP<Void> {

    @Override
    public WarmUpVO compute(ActivityCxt context, Param param, Void unused) {
        ProductM productM = param.getProductM();
        WarmUpStageEnum.WarmUpStageResult warmUpStageResult = WarmUpStageEnum.getWarmUpStageResult(productM);
        if (!warmUpStageResult.isValidWarmUpStage()) {
            return null;
        }
        switch (warmUpStageResult.getStage()) {
            case ONLY_WARM_UP_PRESALE:
                return buildOnlyWarmUpPreSale(productM);
            case ONLY_WARM_UP_SALE:
                return buildOnlyWarmUpSale(productM);
            case NOT_STARTED_TIME_STOCK:
            case ONLY_TIME_STOCK_PRESALE:
                return buildNotStartedTimeStock(warmUpStageResult, productM);
            case IN_PROCESS_TIME_STOCK:
                return buildInProcessTimeStock(warmUpStageResult, productM);
            case CURRENT_TIME_END_HAVE_NEXT:
            case CURRENT_STOCK_END_HAVE_NEXT:
                return buildCurrentTimeStockEnd(warmUpStageResult, productM);
            default:
            case NO_STOCK:
            case NOT_WARM_UP_AND_TIME_STOCK:
            case NO_NEXT_TIME_STOCK:
                return null;
        }
    }

    //到达释放库存时间点，释放量有余量
    private WarmUpVO buildInProcessTimeStock(WarmUpStageEnum.WarmUpStageResult warmUpStageResult, ProductM productM) {
        WarmUpVO warmUp = new WarmUpVO();
        WarmUpItemVO end = buildWarmUpItem("距结束", null, productM.getOrderUrl(), "已抢光",
                warmUpStageResult.getDealTimeStock().getCurrentTimeStockItem().getEndTime());
        warmUp.setEndItem(end);
        return warmUp;
    }

    //本场已抢光
    private WarmUpVO buildCurrentTimeStockEnd(WarmUpStageEnum.WarmUpStageResult warmUpStageResult, ProductM productM) {
        WarmUpVO warmUp = new WarmUpVO();
        WarmUpItemVO startItem = buildWarmUpItem(null, "后下一场", productM.getOrderUrl(), "立即抢",
                warmUpStageResult.getDealTimeStock().getNextTimeStockItem().getStartTime());
        WarmUpItemVO endItem = buildWarmUpItem("距结束", null, productM.getOrderUrl(), "已抢光",
                warmUpStageResult.getDealTimeStock().getNextTimeStockItem().getEndTime());
        warmUp.setStartItem(startItem);
        warmUp.setEndItem(endItem);
        return warmUp;
    }

    //仅预热-预热阶段
    private WarmUpVO buildOnlyWarmUpPreSale(ProductM productM) {
        WarmUpVO warmUp = new WarmUpVO();
        WarmUpItemVO startItem = buildWarmUpItem(null, "后开抢", productM.getOrderUrl(), "立即抢", productM.getBeginDate());
        WarmUpItemVO endItem = buildWarmUpItem("距结束", null, productM.getOrderUrl(), "抢购结束", productM.getEndDate());
        warmUp.setStartItem(startItem);
        warmUp.setEndItem(endItem);
        return warmUp;
    }

    //未到释放点
    private WarmUpVO buildNotStartedTimeStock(WarmUpStageEnum.WarmUpStageResult warmUpStageResult, ProductM productM) {
        WarmUpStageEnum.DealTimeStockModel dealTimeStock = warmUpStageResult.getDealTimeStock();
        WarmUpVO warmUp = new WarmUpVO();
        WarmUpItemVO startItem = buildWarmUpItem(null, "后开抢", productM.getOrderUrl(), "立即抢",
                dealTimeStock.getNextTimeStockItem().getStartTime());
        WarmUpItemVO endItem = buildWarmUpItem("距结束", null, productM.getOrderUrl(), "已抢光",
                dealTimeStock.getNextTimeStockItem().getEndTime());
        warmUp.setStartItem(startItem);
        warmUp.setEndItem(endItem);
        return warmUp;
    }

    //仅预热-售卖阶段
    private WarmUpVO buildOnlyWarmUpSale(ProductM productM) {
        WarmUpVO warmUp = new WarmUpVO();
        WarmUpItemVO endItem = buildWarmUpItem("距结束", null, productM.getOrderUrl(), "抢购结束", productM.getEndDate());
        warmUp.setEndItem(endItem);
        return warmUp;
    }

    private WarmUpItemVO buildWarmUpItem(String timePrefix, String timeSuffix, String jumpUrl, String nextBtnName,
                                         Long timestamp) {
        WarmUpItemVO warmUpItem = new WarmUpItemVO();
        warmUpItem.setTimeSuffix(timeSuffix);
        warmUpItem.setTimePrefix(timePrefix);
        warmUpItem.setJumpUrl(jumpUrl);
        warmUpItem.setNextButtonName(nextBtnName);
        warmUpItem.setTimestamp(timestamp);
        return warmUpItem;
    }
}
