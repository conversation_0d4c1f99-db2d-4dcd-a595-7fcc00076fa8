package com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import lombok.Data;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> Created on 2022/05/09
 */
@Ability(code = DealMultiQueryFetcher.CODE,
        name = "团单信息统一融合查询能力",
        description = "包含团单召回、填充、排序的融合查询能力。\n" +
                "入参主要为外部传参(平台、商户ID)与能力配置信息。\n" +
                "能力为多种能力的集成，召回门店下的多组商品。2.多组商品填充能力，可根据配置的填充类型选择填充能力，根据填充参数决定填充内容。3.可选排序能力，可根据配置的排序ID，商品组下的商品进行排序。\n" +
                "返回标准的商品组模型集合，以商品组名为Key，商品组模型为Model的Map形式。",
        activities = {DealFilterListActivity.CODE},
        dependency = {FilterFirstFetcher.CODE}
)
public class DealMultiQueryFetcher extends PmfAbility<Map<String, ProductGroupM>, DealMultiQueryFetcher.Request, DealMultiQueryFetcher.Config> {

    public static final String CODE = "DealMultiQueryFetcher";

    @Resource
    private MultiGroupMergeQueryFetcher multiGroupMergeQueryFetcher;

    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityCxt activityCxt, Request request, Config config) {
        //1.增加配置化参数
        List<String> groupNames = config.getGroupNames();
        Map<String, Map<String, Object>> groupParams = config.getGroupParams();
        activityCxt.addParam(QueryFetcher.Params.groupNames, groupNames);
        activityCxt.addParam(QueryFetcher.Params.groupParams, groupParams);
        //2.调用原统一融合查询能力
        return multiGroupMergeQueryFetcher.build(ActivityCtxtUtils.toActivityContext(activityCxt));
    }

    @AbilityCfg
    @Data
    public static class Config {
        /**
         * 【一级参数, List<String>型, 必填】自定义组名
         * {@link QueryFetcher.Params#groupNames}
         */
        private List<String> groupNames;

        /**
         * 【一级参数, Map<String, Object>型, 必填】指定每组商品召回参数
         * {@link QueryFetcher.Params#groupParams}
         */
        private Map<String, Map<String, Object>> groupParams;
    }

    @AbilityRequest
    @Data
    public static class Request {
        private int platform;
    }
}
