package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.sankuai.athena.viewscene.framework.ActivityRequest;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterProductListVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.DealDetailStructModuleVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

import static com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils.*;

public class AntiCrawlerUtils2 {

    public static void antiCrawlerPrice4List(Object join, ActivityRequest request) {
        try {
            if (!Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.anti.crawler.hide.dealfilterlist", false)) {
                return;
            }
            if (join == null) {
                return;
            }
            long userId;
            if (PlatformUtil.isMT(ParamsUtil.getIntSafely(request.getParams(), ShelfActivityConstants.Params.platform))) {
                userId = ParamsUtil.getLongSafely(request.getParams(), ShelfActivityConstants.Params.mtUserId);
            } else {
                userId = ParamsUtil.getLongSafely(request.getParams(), ShelfActivityConstants.Params.dpUserId);
            }
            if (userId > 0) {
                Cat.logEvent("AntiCrawlerUtils2", "break-dealfilterlist");
                return;
            }
            if (join instanceof DzFilterProductListVO) {
                Cat.logEvent("AntiCrawlerUtils2", "hit-dealfilterlist");
                List<DzProductVO> products = ((DzFilterProductListVO) join).getProducts();
                if (CollectionUtils.isNotEmpty(products)) {
                    for (DzProductVO product : products) {
                        if (product == null) {
                            continue;
                        }
                        product.setSalePrice(null);
                        product.setSalePriceDesc(null);
                        product.setMarketPrice(null);
                        product.setMarketPriceDesc(null);
                        product.setVipPrice(null);
                        product.setPinPrice(null);
                        product.setCardPrice(null);
                        product.setPromoVOList(null);
                        product.setPriceBottomPromoTagList(Lists.newArrayList());
                        product.setExtAttrsStr(null);
                    }
                }
            }
        } catch (Exception e) {
            Cat.logError("AntiCrawlerUtils2", e);
        }
    }

    // 反爬去除价格
    public static void antiCrawlerPrice4Detail(Object join, com.sankuai.dzviewscene.shelf.framework.ActivityRequest request) {
        try {
            if (!Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.anti.crawler.hide.dealmodule", false)) {
                return;
            }
            if (join == null) {
                return;
            }
            long userId;
            if (PlatformUtil.isMT(ParamsUtil.getIntSafely(request.getParams(), ShelfActivityConstants.Params.platform))) {
                userId = ParamsUtil.getLongSafely(request.getParams(), ShelfActivityConstants.Params.mtUserId);
            } else {
                userId = ParamsUtil.getLongSafely(request.getParams(), ShelfActivityConstants.Params.dpUserId);
            }
            if (userId > 0) {
                Cat.logEvent("AntiCrawlerUtils2", "break-dealmodule");
                return;
            }
            // 如果是交易快照，则不去除价格
            if (AntiCrawlerUtils.isTradeSnapshot(request.getParams())) {
                return;
            }
            if (join instanceof DealModuleDetailVO) {
                Cat.logEvent("AntiCrawlerUtils2", "hit-dealmodule");
                List<DealDetailModuleVO> moduleList = ((DealModuleDetailVO) join).getModuleList();
                if (CollectionUtils.isNotEmpty(moduleList)) {
                    for (DealDetailModuleVO dealDetailModuleVO : moduleList) {
                        if (dealDetailModuleVO == null || dealDetailModuleVO.getPriceModel() == null) {
                            continue;
                        }
                        dealDetailModuleVO.getPriceModel().setOriginalPrice(null);
                        dealDetailModuleVO.getPriceModel().setSalePrice(null);
                        dealDetailModuleVO.getPriceModel().setSalePriceTitle(null);
                        dealDetailModuleVO.getPriceModel().setOriginalPriceTitle(null);

                        if (CollectionUtils.isNotEmpty(dealDetailModuleVO.getSkuGroupsModel1())) {
                            for (DealSkuGroupModuleVO dealSkuGroupModuleVO : dealDetailModuleVO.getSkuGroupsModel1()) {
                                if (CollectionUtils.isNotEmpty(dealSkuGroupModuleVO.getDealSkuList())) {
                                    for (DealSkuVO dealSkuVO : dealSkuGroupModuleVO.getDealSkuList()) {
                                        dealSkuVO.setPrice(null);
                                        dealSkuVO.setOriginalPrice(null);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Cat.logError("AntiCrawlerUtils2", e);
        }
    }

    // 反爬去除价格
    public static void antiCrawlerPrice4struct(Object object, com.sankuai.dzviewscene.shelf.framework.ActivityRequest request) {
        try {
            if (!Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", "com.sankuai.dzviewscene.dealshelf.anti.crawler.hide.dealstruct", false)) {
                return;
            }
            if (object == null) {
                return;
            }
            long userId;
            if (PlatformUtil.isMT(ParamsUtil.getIntSafely(request.getParams(), ShelfActivityConstants.Params.platform))) {
                userId = ParamsUtil.getLongSafely(request.getParams(), ShelfActivityConstants.Params.mtUserId);
            } else {
                userId = ParamsUtil.getLongSafely(request.getParams(), ShelfActivityConstants.Params.dpUserId);
            }
            if (userId > 0) {
                return;
            }
            Cat.logEvent("AntiCrawlerUtils2", "break-dealstruct");
            if (object instanceof DealDetailStructModuleVO) {
                Cat.logEvent("AntiCrawlerUtils2", "hit-dealstruct");
                DealDetailStructModuleVO dealDetailStructModuleVO = (DealDetailStructModuleVO) object;

                hiddenProductKeyMustDealSkuStructInfoVO(dealDetailStructModuleVO.getMustGroups());

                hiddenProductKeyOptionalDealSkuStructInfoVO(dealDetailStructModuleVO.getOptionalGroups());

                hiddenProductKeyDealSkuStructExtraInfoDoVO(dealDetailStructModuleVO.getExtraInfos());

                dealDetailStructModuleVO.setPrice(null);
                dealDetailStructModuleVO.setMarketPrice(null);
            }
        } catch (Exception e) {
            Cat.logError("AntiCrawlerUtils2", e);
        }
    }

}
