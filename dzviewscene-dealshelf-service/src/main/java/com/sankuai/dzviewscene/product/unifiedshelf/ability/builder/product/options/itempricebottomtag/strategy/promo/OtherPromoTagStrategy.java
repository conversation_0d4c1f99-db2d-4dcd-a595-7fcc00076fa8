package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.promo;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.UnifiedShelfPromoUtils;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.product.utils.PriceUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Component
public class OtherPromoTagStrategy extends AbstractPriceBottomPromoTagBuildStrategy {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.price.promo.icon.text", defaultValue = "{}")
    private Map<String, String> promoType2IconNameMap;

    @Override
    public String getName() {
        return "其他优惠";
    }

    @Override
    public String getStrategyDesc() {
        return "由报价控制的优惠标签，返回最优标签，包括变美神券，美团补贴，新客特惠等";
    }

    @Override
    public ShelfTagVO buildTag(PriceBottomTagBuildReq req) {
        ProductPromoPriceM productPromoPriceM = PriceUtils.getUserHasPromoPrice(req.getProductM(), req.getCardM());
        if (!isValidOtherPromo(productPromoPriceM)) {
            return null;
        }
        //黑名单判断
        if (isHitTagBlackShop(req, productPromoPriceM)) {
            return null;
        }
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setName(productPromoPriceM.getPromoTag());
        shelfTagVO.setText(buildCommonTagText(productPromoPriceM.getPromoTag(), getPromoTag(productPromoPriceM)));
        shelfTagVO.setPromoDetail(UnifiedShelfPromoUtils.buildPromoDetail(productPromoPriceM));
        addAfterPic(shelfTagVO, getCommonAfterPic(req.getPlatform()));
        return shelfTagVO;
    }

    public boolean isValidOtherPromo(ProductPromoPriceM promoPriceM) {
        return Objects.nonNull(promoPriceM)
                && Objects.nonNull(promoPriceM.getPromoTagType())
                // 过滤掉无效值
                && !promoPriceM.getPromoTagType().equals(0)
                // 过滤掉会员
                && !promoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Member.getCode())
                && !promoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Merchant_Member.getCode())
                // 过滤掉没有标签
                && !promoPriceM.getPromoTagType().equals(PromoTagTypeEnum.Default.getCode())
                // 过滤掉使用了卡会员优惠的Promo
                && !CardPromoUtils.CARD_PROMOS.contains(promoPriceM.getPromoType());
    }

    private boolean isHitTagBlackShop(PriceBottomTagBuildReq req, ProductPromoPriceM productPromoPriceM) {
        long dpShopId = ParamsUtil.getLongSafely(req.getContext().getParameters(), ShelfActivityConstants.Params.dpPoiIdL);
        if (Objects.isNull(req.getCfg()) || MapUtils.isEmpty(req.getCfg().getTagBlackDpShopIds())
                || dpShopId <= 0) {
            return false;
        }
        List<Long> blackShopIds = req.getCfg().getTagBlackDpShopIds().get(productPromoPriceM.getPromoTagType());
        if (CollectionUtils.isNotEmpty(blackShopIds) && blackShopIds.contains(dpShopId)) {
            return true;
        }
        return false;
    }

    private String getPromoTag(ProductPromoPriceM productPromoPriceM) {
        if (MapUtils.isEmpty(promoType2IconNameMap)) {
            return null;
        }
        if (StringUtils.isEmpty(productPromoPriceM.getIcon())) {
            return null;
        }
        //icon对应为文案
        return promoType2IconNameMap.get(String.valueOf(productPromoPriceM.getPromoTagType()));
    }

}
