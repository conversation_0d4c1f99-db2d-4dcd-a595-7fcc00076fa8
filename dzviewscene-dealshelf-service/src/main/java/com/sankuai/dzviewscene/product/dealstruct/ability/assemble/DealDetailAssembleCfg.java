package com.sankuai.dzviewscene.product.dealstruct.ability.assemble;

import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;
import lombok.Data;

import java.util.List;

/**
 * created by zhangzhiyuan04 in 2021/12/8
 */
@AbilityCfg
@Data
public class DealDetailAssembleCfg {

    private List<ModuleItem> moduleList;

    private List<ModuleItem> newModuleList;

    private String dpExpId;

    private String mtExpId;

    private List<String> hitSks;

}
