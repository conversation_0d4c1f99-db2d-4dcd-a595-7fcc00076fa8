package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.router.DealCategoryRouter;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModel;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2023/8/24 3:38 下午
 */

@Ability(code = DealStyleSwitchAbility.CODE,
        name = "团详样式路由能力",
        description = "决定走bp的团详套餐还是走业务的套餐逻辑",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailDouhuFetcher.CODE, DealDetailFetcher.CODE
        }
)
public class DealStyleSwitchAbility extends PmfAbility<DealModuleDetailVO, Void, DealStyleSwitchCfg> {

    public static final String CODE = "dealStyleSwitchAbility";

    protected static String getModuleKeyByPlatform(int platform) {
        return PlatformUtil.isMT(platform) ? "dealdetail_gc_packagedetail" : "tuandeal_gc_packagedetail";
    }

    @Resource
    private List<DealCategoryRouter> dealCategoryRouters;

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt activityCxt, Void unused, DealStyleSwitchCfg abilityCfg) {
        List<DouhuResultModel> douHuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        //获取团单所有信息数据，团详一般只有一个
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        int dealCategory = ParamsUtil.getIntSafely(activityCxt, PmfConstants.Params.dealCategoryId);
        int platform = ParamsUtil.getIntSafely(activityCxt, PmfConstants.Params.platform);
        String mpSource = ParamsUtil.getStringSafely(activityCxt, PmfConstants.Params.mpSource);
        DealCategoryRouter.Param param = DealCategoryRouter.Param.builder()
                .dealCategoryId(dealCategory)
                .douHuResultModels(douHuResultModels)
                .dealDetailInfoModel(dealDetailInfoModel)
                .platform(platform)
                .mpSource(mpSource)
                .moduleKey(getModuleKeyByPlatform(platform))
                .build();
        if (CollectionUtils.isEmpty(dealCategoryRouters)) {
            return CompletableFuture.completedFuture(null);
        }
        SwitchModel switchModel = dealCategoryRouters.stream()
                .filter(router -> router.identify(abilityCfg, dealCategory))
                .map(router -> router.compute(activityCxt, param, abilityCfg))
                .filter(Objects::nonNull)
                .findFirst().orElse(new SwitchModel());
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        dealModuleDetailVO.setSwitchModel(switchModel);
        return CompletableFuture.completedFuture(dealModuleDetailVO);
    }
}
