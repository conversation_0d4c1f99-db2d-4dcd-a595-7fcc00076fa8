package com.sankuai.dzviewscene.product.factory;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleItem;
import com.sankuai.dzviewscene.product.factory.impl.DefaultCategoryStrategyImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: created by hang.yu on 2023/10/16 11:22
 */
@Component
public class DealCategoryFactory {

    @Resource
    private Map<String, DealCategoryStrategy> dealCategoryStrategyMap;

    @Resource
    private DefaultCategoryStrategyImpl defaultCategoryStrategy;

    public List<ModuleItem> getModuleList(ActivityCxt activityCxt, DealDetailAssembleCfg assembleCfg) {
        Integer dealCategoryId = (Integer) activityCxt.getParameters().get("dealCategoryId");
        String serviceType = (String) activityCxt.getParameters().getOrDefault("serviceType", StringUtils.EMPTY);
        DealCategoryEnum dealCategoryEnum = DealCategoryEnum.getEnumByDealCategoryId(dealCategoryId, serviceType);
        if (dealCategoryEnum != null) {
            DealCategoryStrategy dealCategoryStrategy = dealCategoryStrategyMap.get(dealCategoryEnum.getStrategyName());
            if (dealCategoryStrategy != null) {
                return dealCategoryStrategy.getModuleList(activityCxt, assembleCfg);
            }
        }
        return defaultCategoryStrategy.getModuleList(activityCxt, assembleCfg);
    }

}
