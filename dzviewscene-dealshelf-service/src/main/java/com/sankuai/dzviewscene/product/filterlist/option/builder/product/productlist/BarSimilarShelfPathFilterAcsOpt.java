package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 货架路径
 * 召回规则：
 * 当团单三级分类为“酒水小食套餐”、“餐食”、“酒水”时，召回门店内除了团单分类为“门票入场券”外的其余团单
 * 当团单三级分类为“门票入场券”时，召回门店内全部团单
 *
 * <p>
 * 排序规则：
 * 首先展示当前团单，其他团单排序规则如下，
 * 当团单三级分类为“酒水小食套餐”时，优先级为：适用人数>团单差价
 * 适用人数：优先推荐适用人数相同的套餐，其次推荐适用人数不同或者无适用人数的团单
 * 团单差价：在适用人数基础上，按团单间差价排序（相似团单价格-本团单价格），二者差价的绝对值越小，排序优先级越高。
 * 当团单三级分类为其他时，优先级按团单间差价排序（相似团单价格-本团单价格），二者差价的绝对值越小，排序优先级越高。
 *
 * <AUTHOR>
 * @since 2023/10/16 16:20
 * 类名有点过时，以注释为准
 */
@VPointOption(name = "酒吧-相似团购",
        description = "酒吧-相似团购-价格和人数排序",
        code = "BarSimilarShelfPathFilterAcsOpt")
public class BarSimilarShelfPathFilterAcsOpt extends ProductListVP<BarSimilarShelfPathFilterAcsOpt.Config> {

    private static final String SERVICE_TYPE = "service_type";

    private static final String PACKAGE_NUMBER_OF_PKG = "number_of_pkg";

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        List<ProductM> productMS = param.getProductMS();
        //获取当前团单ID
        int entityId = ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId);
        int firstTopIndex = findTopProductIndex(productMS, entityId);
        if (firstTopIndex < 0) {
            return null;
        }
        ProductM topProduct = productMS.get(firstTopIndex);
        List<ProductM> collect = productMS.stream()
                .filter(productM -> filterRecall(productM, topProduct, productMS))
                .filter(productM -> StringUtils.isNotEmpty(productM.getTitle()))
                .sorted(Comparator.comparing((ProductM product) -> getOrderValue(product, topProduct))
                        .thenComparing((ProductM product) -> getProductPrice(product, topProduct)))
                .limit(config.getLimit())
                .collect(Collectors.toList());
        return topAndFilterResult(collect, ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId));
    }

    private List<ProductM> topAndFilterResult(List<ProductM> result, int entityId) {
        if (CollectionUtils.isEmpty(result) || result.size() <= 1) {
            return null;
        }
        int finalTopIndex = findTopProductIndex(result, entityId);
        if (finalTopIndex < 0) {
            return null;
        }
        Collections.swap(result, 0, finalTopIndex);
        return result;
    }

    private int findTopProductIndex(List<ProductM> productMS, int entityId) {
        int topIndex = -1;
        for (int index = 0; index < productMS.size(); index++) {
            if (productMS.get(index).getProductId() != entityId) {
                continue;
            }
            topIndex = index;
            break;
        }
        return topIndex;
    }

    private boolean filterRecall(ProductM productM, ProductM currentProduct, List<ProductM> productMS) {
        String serviceType = productM.getAttr(SERVICE_TYPE);
        String currentServiceType = currentProduct.getAttr(SERVICE_TYPE);
        if (StringUtils.equals(currentServiceType, "门票入场券")) {
            return true;
        } else if (productMS.size() == 1) {
            return false;
        } else {
            return !StringUtils.equals(serviceType, "门票入场券");
        }
    }

    /**
     * 非当前团单或适用人数相同则为-1,交换
     * 当团单三级分类为“酒水小食套餐”时，优先级为：适用人数>团单差价
     **/
    private int getOrderValue(ProductM productM, ProductM topProduct) {
        String numberOfPkg = productM.getAttr(PACKAGE_NUMBER_OF_PKG);
        String topNumberOfPkg = topProduct.getAttr(PACKAGE_NUMBER_OF_PKG);
        if (StringUtils.isEmpty(numberOfPkg) || StringUtils.isEmpty(topNumberOfPkg)) {
            return 0;
        }
        if (StringUtils.equals(numberOfPkg, topNumberOfPkg)) {
            return -1;
        } else {
            return 0;
        }
    }

    //价格差
    private Double getProductPrice(ProductM productM, ProductM topProduct) {
        String diff = getSalePrice(productM).subtract(getSalePrice(topProduct)).stripTrailingZeros().toPlainString();
        return Math.abs(Double.parseDouble(diff));
    }

    private BigDecimal getSalePrice(ProductM productM) {
        //立减
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        BigDecimal salePrice = Optional.ofNullable(productPromoPriceM)
                .map(ProductPromoPriceM::getPromoPrice)
                .filter(price -> Objects.nonNull(price))
                .filter(price -> price.compareTo(BigDecimal.ZERO) > 0)
                .orElse(productM.getBasePrice());
        return salePrice == null ? BigDecimal.ZERO : salePrice;
    }

    @VPointCfg
    @Data
    public static class Config {
        private int limit = 5;
    }
}
