package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.docProcessing.DocBuilderUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/15 11:09 上午
 */
@VPointOption(name = "通过脚本表达式构造sku列表变化点", description = "通过脚本表达式构造sku列表变化点（在sku列表后置处理环节）",code = SkuListBuiltByScriptExpressionOpt.CODE, isDefault = false)
public class SkuListBuiltByScriptExpressionOpt extends SkuListAfterProcessingVP<SkuListBuiltByScriptExpressionOpt.Config> {

    public static final String CODE = "SkuListBuiltByScriptExpressionOpt";

    @Override
    public List<DealSkuVO> compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(config.getSkuBuildModels())) {
            return null;
        }
        return config.getSkuBuildModels().stream().map(skuConfig -> buildDealSkuVoByConfig(skuConfig, param.getSkuItems(), param.getDealAttrs(), context)).filter(sku -> sku != null).collect(Collectors.toList());
    }

    /**
     * 利用 团单属性 和 第一个must服务项目数据 为资源，通过脚本表达式构造DealSkuVO列表
     *@param
     *@return
     */
    private DealSkuVO buildDealSkuVoByConfig(SkuBuildModel skuModel, List<SkuItemDto> skuItemDtos, List<AttrM> dealAttrs, ActivityCxt context) {
        if (skuModel == null) {
            return null;
        }
        //获取构造sku列表所需要的资源：团单属性 和 第一个must服务项目数据
        Map<String, Object> resource = extractResourceFromFirstSkuInfoAndDealAttrs(skuItemDtos, dealAttrs);
        //构造DealSkuVO
        return buildSku(resource, skuModel, context);
    }

    /**
     * 通过脚本表达式和资源构造DealSkuVO
     *@param
     *@return
     */
    private DealSkuVO buildSku(Map<String, Object> resource, SkuBuildModel skuModel, ActivityCxt context) {
        if (skuModel == null) {
            return null;
        }
        //构造标题
        String title = DocBuilderUtils.getStringValueByScriptExpression(resource, skuModel.getSkuTitleBuildModels(), context);
        //构造份数
        String copies = DocBuilderUtils.getStringValueByScriptExpression(resource, skuModel.getSkuCopiesBuildModels(), context);
        //构造价格
        String price = DocBuilderUtils.getStringValueByScriptExpression(resource, skuModel.getSkuPriceBuildModels(), context);
        //构造属性
        List<DealSkuItemVO> dealSkuItemVOS = getDealSkuItemVOList(skuModel.getSkuAttrBuildModelList(), resource, context);
        return buildDealSkuVO(skuModel.getIcon(), title, copies, price, dealSkuItemVOS);
    }

    /**
     * 构造sku属性列表
     *@param
     *@return
     */
    private List<DealSkuItemVO> getDealSkuItemVOList(List<SkuAttrModel> skuAttrBuildModelList, Map<String, Object> resource, ActivityCxt context) {
        if (CollectionUtils.isEmpty(skuAttrBuildModelList)) {
            return null;
        }
        List<DealSkuItemVO> dealSkuItemVOS = skuAttrBuildModelList.stream().map(skuAttr -> getDealSkuItemVO(resource, skuAttr, context)).filter(skuAttr -> skuAttr != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return null;
        }
        return dealSkuItemVOS;
    }

    private DealSkuVO buildDealSkuVO(String icon, String title, String copies, String price, List<DealSkuItemVO> dealSkuItemVOS) {
        if (StringUtils.isEmpty(title)) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setPrice(price);
        dealSkuVO.setCopies(copies);
        dealSkuVO.setIcon(icon);
        dealSkuVO.setItems(dealSkuItemVOS);
        return dealSkuVO;
    }

    /**
     * 根据sku属性脚本表达式模型构造sku属性vo
     *@param
     *@return
     */
    private DealSkuItemVO getDealSkuItemVO(Map<String, Object> resource, SkuAttrModel skuAttrModel, ActivityCxt context) {
        if (skuAttrModel == null) {
            return null;
        }
        String skuAttrValue = DocBuilderUtils.getStringValueByScriptExpression(resource, skuAttrModel.getSkuAttrBuildModelList(), context);
        String skuAttrTitle = DocBuilderUtils.getStringValueByScriptExpression(resource, skuAttrModel.getSkuAttrTitleBuildModels(), context);
        return buildDealSkuItemVO(skuAttrTitle, skuAttrValue);
    }

    private DealSkuItemVO buildDealSkuItemVO(String skuAttrTitle, String skuAttrValue) {
        if (skuAttrValue == null && skuAttrTitle == null) {
            return null;
        }
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setName(skuAttrTitle);
        dealSkuItemVO.setValue(skuAttrValue);
        return dealSkuItemVO;
    }

    /**
     * 从第一个must服务项目信息和团单属性中获取构造结果所需的资源
     *@param
     *@return
     */
    private Map<String, Object> extractResourceFromFirstSkuInfoAndDealAttrs(List<SkuItemDto> skuItemDtos, List<AttrM> dealAttrs) {
        Map<String, Object> resource = new HashMap<>();
        addResourceFromSkuItemDto(CollectUtils.firstValue(skuItemDtos), resource);
        addResourceFromDealAttrs(dealAttrs, resource);
        return resource;
    }

    /**
     * 从团单属性中获取构造资源
     *@param
     *@return
     */
    private void addResourceFromDealAttrs(List<AttrM> dealAttrs, Map<String, Object> resource) {
        if (CollectionUtils.isEmpty(dealAttrs) || resource == null) {
            return;
        }
        Map<String, Object> resourceFromDealAttr = dealAttrs.stream().collect(HashMap::new, (map, attr) -> map.put(attr.getName(), attr.getValue()), HashMap::putAll);
        if (MapUtils.isEmpty(resourceFromDealAttr)) {
            return;
        }
        resource.putAll(resourceFromDealAttr);
    }

    /**
     * 从服务项目信息中获取构造资源
     *@param
     *@return
     */
    private void addResourceFromSkuItemDto(SkuItemDto skuItemDto, Map<String, Object> resource) {
        if (resource == null || skuItemDto == null) {
            return;
        }
        resource.put("productCategory", skuItemDto.getProductCategory());
        resource.put("skuName", skuItemDto.getName());
        resource.put("skuCopies", skuItemDto.getCopies());
        resource.put("skuPrice", skuItemDto.getMarketPrice());
        if (CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return;
        }
        Map<String, Object> skuAttrs = skuItemDto.getAttrItems().stream().collect(HashMap::new, (map, skuAttr) -> map.put(skuAttr.getAttrName(), skuAttr.getAttrValue()), HashMap::putAll);
        if (MapUtils.isEmpty(skuAttrs)) {
            return;
        }
        resource.putAll(skuAttrs);
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<SkuBuildModel> skuBuildModels;
    }

    @Data
    private static class SkuBuildModel {
        //icon配置
        private String icon;
        //sku标题构造配置
        private List<DocBuilderUtils.BuildModel> skuTitleBuildModels;
        //sku份数构造配置
        private List<DocBuilderUtils.BuildModel> skuCopiesBuildModels;
        //sku价格构造配置
        private List<DocBuilderUtils.BuildModel> skuPriceBuildModels;
        //sku属性构造配置
        private List<SkuAttrModel> skuAttrBuildModelList;
    }

    @Data
    private static class SkuAttrModel {
        //sku属性展示标题
        private List<DocBuilderUtils.BuildModel> skuAttrTitleBuildModels;
        //sku属性构造配置
        private List<DocBuilderUtils.BuildModel> skuAttrBuildModelList;
    }
}
