package com.sankuai.dzviewscene.product.dealstruct.ability.utils;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectGroupDTO;
import com.sankuai.general.product.query.center.client.dto.standardServiceProject.StandardServiceProjectItemDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/25 9:21 下午
 */
public class DealDetailUtils {

    public static final String SEPERATOR = ",";

    public static String getSkuCategoryBySkuCategoryId(long productCategory, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel categoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && category.getProductCategoryId() == productCategory).findFirst().orElse(null);
        if (categoryModel == null) {
            return null;
        }
        return categoryModel.getCnName();
    }

    public static String getSkuAttrValueBySkuAttrName(List<SkuAttrItemDto> skuAttrItemDtos, String skuAttrName) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || StringUtils.isEmpty(skuAttrName)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtos.stream().filter(attr -> skuAttrName.equals(attr.getAttrName())).findFirst().orElse(null);
        if (skuAttrItemDto == null) {
            return null;
        }
        return skuAttrItemDto.getAttrValue();
    }

    public static String getRawSkuAttrValue(List<SkuAttrItemDto> skuAttrItemDtos, String skuAttrName) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || StringUtils.isEmpty(skuAttrName)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtos.stream().filter(attr -> skuAttrName.equals(attr.getAttrName())).findFirst().orElse(null);
        if (skuAttrItemDto == null) {
            return null;
        }
        return skuAttrItemDto.getRawAttrValue();
    }

    public static String getAndTrimSkuAttrValue(List<SkuAttrItemDto> skuAttrItemDtos, String skuAttrName) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || StringUtils.isEmpty(skuAttrName)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtos.stream().filter(attr -> skuAttrName.equals(attr.getAttrName())).findFirst().orElse(null);
        if (skuAttrItemDto == null) {
            return null;
        }
        return StringUtils.isEmpty(skuAttrItemDto.getAttrValue()) ? StringUtils.EMPTY : skuAttrItemDto.getAttrValue().trim();
    }

    public static Map<String, DealSkuItemInfoModel> getRecommendTag(String recommendTagList) {
        Map<String, DealSkuItemInfoModel> map = Maps.newHashMap();
        if (StringUtils.isEmpty(recommendTagList)) {
            return map;
        }
        List<DealSkuItemInfoModel> decode = JsonCodec.decode(recommendTagList, new TypeReference<List<DealSkuItemInfoModel>>() {});
        if (CollectionUtils.isEmpty(decode)) {
            return map;
        }
        for (DealSkuItemInfoModel dealSkuItemInfoModel : decode) {
            map.put(dealSkuItemInfoModel.getSkuName(), dealSkuItemInfoModel);
        }
        return map;
    }

    public static <T> T getSkuAttrVal(List<SkuAttrItemDto> skuAttrItemDtos, String skuAttrName, TypeReference<T> typeReference) {
        String val = getSkuAttrValueBySkuAttrName(skuAttrItemDtos, skuAttrName);
        if (StringUtils.isEmpty(val)) {
            return null;
        }
        return JsonCodec.decode(val, typeReference);
    }

    public static <T> T getAttrVal(List<AttrM> dealAttrs, String attrName,TypeReference<T> typeReference) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(attrName)) {
            return null;
        }
        AttrM attrM = dealAttrs.stream().filter(attr -> attrName.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null) {
            return null;
        }
        String val = attrM.getValue();
        if (StringUtils.isBlank(val)) {
            return null;
        }
        return JsonCodec.decode(val,typeReference);
    }

    public static int getSkuAttrIntVal(List<SkuAttrItemDto> skuAttrItemDtos, String skuAttrName) {
        String val = getSkuAttrValueBySkuAttrName(skuAttrItemDtos, skuAttrName);
        return NumberUtils.toInt(val);
    }

    /**
     * 从DealDetailInfoModel中提取第一个must组的第一个sku的属性列表
     *
     * @param
     * @return
     */
    public static List<SkuAttrItemDto> extractFirstMustSkuAttrListFromDealDetailInfoModel(DealDetailInfoModel dealDetailInfoModel) {
        SkuItemDto skuItemDto = extractFirstMustSkuFromDealDetailInfoModel(dealDetailInfoModel);
        return skuItemDto == null ? new ArrayList<>() : skuItemDto.getAttrItems();
    }

    /**
     * 从DealDetailInfoModel中提取第一个must组的第一个sku
     *
     * @param
     * @return
     */
    public static SkuItemDto extractFirstMustSkuFromDealDetailInfoModel(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null || dealDetailInfoModel.getDealDetailDtoModel() == null || dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        MustSkuItemsGroupDto mustSkuItemsGroupDto = CollectUtils.firstValue(dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups());
        if (mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        return CollectUtils.firstValue(mustSkuItemsGroupDto.getSkuItems());
    }

    /**
     * 从DealDetailInfoModel中提取dealAttrs属性
     *
     * @param
     * @return
     */
    public static List<AttrM> extractDealAttrsFromDealDetailInfoModel(DealDetailInfoModel dealDetailInfoModel) {
        if (Objects.isNull(dealDetailInfoModel) || CollectionUtils.isEmpty(dealDetailInfoModel.getDealAttrs())) {
            return Lists.newArrayList();
        }
        return dealDetailInfoModel.getDealAttrs();
    }
    /**
     * 通过sku属性名获取sku属性中文名
     *
     * @param
     * @return
     */
    public static String getSkuAttrChnNameBySkuAttrName(List<SkuAttrItemDto> skuAttrItemDtos, String skuAttrName) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || StringUtils.isEmpty(skuAttrName)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtos.stream().filter(attr -> skuAttrName.equals(attr.getAttrName())).findFirst().orElse(null);
        if (skuAttrItemDto == null) {
            return null;
        }
        return skuAttrItemDto.getChnName();
    }

    /**
     * 获取团单第一个必选组下第一个服务项目的属性列表
     *
     * @param
     * @return
     */
    public static List<SkuAttrItemDto> getFirstMustGroupFirstSkuAttrList(DealDetailDtoModel dealDetailDtoModel) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups())) {
            return new ArrayList<>();
        }
        MustSkuItemsGroupDto mustSkuItemsGroupDto = CollectUtils.firstValue(dealDetailDtoModel.getSkuUniStructuredDto().getMustGroups());
        if (mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return new ArrayList<>();
        }
        SkuItemDto skuItemDto = CollectUtils.firstValue(mustSkuItemsGroupDto.getSkuItems());
        if (skuItemDto == null) {
            return new ArrayList<>();
        }
        return skuItemDto.getAttrItems();
    }


    public static String getSkuAttrValueBySkuAttrChnName(List<SkuAttrItemDto> skuAttrItemDtos, String skuAttrChnName) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos) || StringUtils.isEmpty(skuAttrChnName)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtos.stream().filter(attr -> skuAttrChnName.equals(attr.getChnName())).findFirst().orElse(null);
        if (skuAttrItemDto == null) {
            return null;
        }
        return skuAttrItemDto.getAttrValue();
    }

    public static String getSkuAttrModelValueByAttrName(List<SkuAttrModel> skuAttrModels, String skuAttrChnName) {
        if (CollectionUtils.isEmpty(skuAttrModels) || StringUtils.isEmpty(skuAttrChnName)) {
            return null;
        }
        SkuAttrModel skuAttrModel = skuAttrModels.stream().filter(attr -> skuAttrChnName.equals(attr.getName())).findFirst().orElse(null);
        if (skuAttrModel == null || skuAttrModel.getValue() == null) {
            return null;
        }
        return skuAttrModel.getValue().getDoc();
    }

    public static String getSkuAttrModelValueByAttrNameNew(List<SkuAttrModel> skuAttrModels, String skuAttrName) {
        if (CollectionUtils.isEmpty(skuAttrModels) || StringUtils.isEmpty(skuAttrName)) {
            return null;
        }
        SkuAttrModel skuAttrModel = skuAttrModels.stream().filter(attr -> skuAttrName.equals(attr.getAttrName())).findFirst().orElse(null);
        if (skuAttrModel == null || skuAttrModel.getValue() == null) {
            return null;
        }
        return skuAttrModel.getValue().getDoc();
    }

    public static List<String> getAttrValueByAttrName(List<AttrM> dealAttrs, String name) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(name)) {
            return null;
        }
        AttrM attrM = dealAttrs.stream().filter(attr -> name.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null) {
            return null;
        }
        return Lists.newArrayList(attrM.getValue().split(SEPERATOR));
    }

    public static List<String> getAttrValueByAttrNameWithoutSplit(List<AttrM> dealAttrs, String name) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(name)) {
            return null;
        }
        AttrM attrM = dealAttrs.stream().filter(attr -> name.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null) {
            return null;
        }
        return Lists.newArrayList(attrM.getValue());
    }

    
    public static List<String> parseAttrValueByAttrName(List<AttrM> dealAttrs, String name) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(name)) {
            return null;
        }
        String attrValueJson = getAttrSingleValueByAttrName(dealAttrs, name);
        if (StringUtils.isBlank(attrValueJson)) {
            return null;
        }
        try {
            if (attrValueJson.startsWith("[") && attrValueJson.endsWith("]")) {
                return JSON.parseArray(attrValueJson, String.class);
            }
            return Lists.newArrayList(attrValueJson.split(SEPERATOR));
        } catch (Exception e) {
            Cat.logError("parseAttrValueByAttrName err", e);
            return null;
        }
    }

    public static String getAttrSingleValueByAttrName(List<AttrM> dealAttrs, String name) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(name)) {
            return null;
        }
        AttrM attrM = dealAttrs.stream().filter(attr -> name.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null) {
            return null;
        }
        return attrM.getValue();
    }

    public static List<SkuItemDto> extractMustSkuFromDealDetailInfoModel(DealDetailInfoModel dealDetailInfoModel) {
        if (dealDetailInfoModel == null || dealDetailInfoModel.getDealDetailDtoModel() == null || dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null || CollectionUtils.isEmpty(dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        MustSkuItemsGroupDto mustSkuItemsGroupDto = CollectUtils.firstValue(dealDetailInfoModel.getDealDetailDtoModel().getSkuUniStructuredDto().getMustGroups());
        if (mustSkuItemsGroupDto == null || CollectionUtils.isEmpty(mustSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        return mustSkuItemsGroupDto.getSkuItems();
    }

    /**
     * 从sku中读取第一个attr的值
     * @param standardServiceProjectDTO 标准服务项目，优先从这个取
     * @param dealDetailDtoModel 服务项目，如果标准服务项目为空，则从这个读取
     * @param attrName
     * @return
     */
    public static String getFirstSkuAttrModelValueByAttrName(StandardServiceProjectDTO standardServiceProjectDTO,
                                                             DealDetailDtoModel dealDetailDtoModel, String attrName) {
        if (standardServiceProjectDTO != null) {
            return getFirstSkuAttrValueFromStandardServiceProject(standardServiceProjectDTO, attrName);
        }
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        List<SkuItemDto> totalItems = readTotalUnitSkuItems(dealDetailDtoModel.getSkuUniStructuredDto());
        if (CollectionUtils.isEmpty(totalItems)) {
            return null;
        }
        return totalItems.stream()
                .filter(item -> item != null && CollectionUtils.isNotEmpty(item.getAttrItems()))
                .map(item -> item.getAttrItems())
                .flatMap(Collection::stream)
                .filter(attr -> attrName.equals(attr.getAttrName()))
                .map(attr -> attr.getAttrValue())// 如果有attrValues，那么第一个肯定不为空
                .findFirst().orElse(null);
    }

    @Nullable
    private static String getFirstSkuAttrValueFromStandardServiceProject(StandardServiceProjectDTO standardServiceProjectDTO, String attrName) {
        List<StandardServiceProjectItemDTO> totalItems = new ArrayList<>();
        addStandardSkuItems(standardServiceProjectDTO.getMustGroups(), totalItems);
        addStandardSkuItems(standardServiceProjectDTO.getOptionalGroups(), totalItems);
        if (CollectionUtils.isEmpty(totalItems)) {
            return null;
        }
        return totalItems.stream()
                .filter(item -> item.getStandardAttribute() != null && CollectionUtils.isNotEmpty(item.getStandardAttribute().getAttrs()))
                .map(item -> item.getStandardAttribute().getAttrs())
                .flatMap(Collection::stream)
                .filter(attr -> attrName.equals(attr.getAttrName()) && CollectionUtils.isNotEmpty(attr.getAttrValues()))
                .map(attr -> attr.getAttrValues().get(0).getSimpleValues().get(0))// 如果有attrValues，那么第一个肯定不为空
                .findFirst().orElse(null);
    }

    private static List<SkuItemDto> readTotalUnitSkuItems(DealDetailSkuUniStructuredDto skuUniStructuredDto) {
        List<SkuItemDto> totalItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(skuUniStructuredDto.getMustGroups())) {
            List<SkuItemDto> items = skuUniStructuredDto.getMustGroups().stream()
                    .filter(group -> group != null && CollectionUtils.isNotEmpty(group.getSkuItems()))
                    .map(MustSkuItemsGroupDto::getSkuItems)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(items)) {
                totalItems.addAll(items);
            }
        }
        if (CollectionUtils.isNotEmpty(skuUniStructuredDto.getOptionalGroups())) {
            List<SkuItemDto> items = skuUniStructuredDto.getOptionalGroups().stream()
                    .filter(group -> group != null && CollectionUtils.isNotEmpty(group.getSkuItems()))
                    .map(OptionalSkuItemsGroupDto::getSkuItems)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(items)) {
                totalItems.addAll(items);
            }
        }
        return totalItems;
    }

    private static void addStandardSkuItems(List<StandardServiceProjectGroupDTO> groups, List<StandardServiceProjectItemDTO> totalItems) {
        if (CollectionUtils.isNotEmpty(groups)) {
            List<StandardServiceProjectItemDTO> items = groups.stream().map(StandardServiceProjectGroupDTO::getServiceProjectItems).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(items)) {
                totalItems.addAll(items);
            }
        }
    }

    public static boolean isWearableNail(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        // 判断dealAttrs中name等于service_type的属性是否等于"穿戴甲"
        String serviceType = getAttrSingleValueByAttrName(dealAttrs, "service_type");
        return Objects.equals(serviceType, "穿戴甲") && containAttrWithName(dealAttrs, "tag_unifyProduct");
    }

    /**
     * 判断DealGroupDTO的Attrs里是否包含name为key的元素
     *
     * @param dealAttrs 团单属性列表
     * @param attrKey 要检查的属性名
     * @return 如果存在返回true，否则返回false
     */
    private static boolean containAttrWithName(List<AttrM> dealAttrs, String attrKey) {
        if (CollectionUtils.isEmpty(dealAttrs) || attrKey == null) {
            return false;
        }
        return dealAttrs.stream().anyMatch(attr -> attrKey.equals(attr.getName()));
    }

    public static boolean isCanWearAtStore(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return false;
        }
        // 判断dealAttrs中name等于isFreeWearingAtStore的属性是否等于"true"
        String isFreeWearingAtStore = getAttrSingleValueByAttrName(dealAttrs, "isFreeWearingAtStore");
        return "true".equals(isFreeWearingAtStore);
    }

    public static SkuItemDto findFirstSkuItemWhoHasAttr(DealDetailDtoModel dealDetailDtoModel, String attrName) {
        if (dealDetailDtoModel == null || dealDetailDtoModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        List<SkuItemDto> totalItems = readTotalUnitSkuItems(dealDetailDtoModel.getSkuUniStructuredDto());
        if (CollectionUtils.isEmpty(totalItems)) {
            return null;
        }
        return totalItems.stream()
                .filter(item -> StringUtils.isNotBlank(getSkuAttrValueBySkuAttrName(item.getAttrItems(), attrName)))
                .findFirst().orElse(null);
    }

    /**
     * 拼接团单名称(拿到Attrs中新参数进行判断)
     *
     * @param dealAttrs
     * @return
     */
    public static List<String> splicedName(List<AttrM> dealAttrs) {
        String classPeopleRestriction = findAttrValue(dealAttrs, "class_people_restriction");
        String availablePeopleNum = findAttrValue(dealAttrs, "availablePeopleNum");
        String bigNum = findAttrValue(dealAttrs, "peoplenum_of_bigclass");
        String smallNum = findAttrValue(dealAttrs, "peoplenum_of_smallclass");

        // 检查 classPeopleRestriction 是否有效
        if (StringUtils.isBlank(classPeopleRestriction)) {
            return Collections.emptyList();
        }

        // 根据 classPeopleRestriction 的值返回相应的结果
        if ("是".equals(classPeopleRestriction)) {
            return StringUtils.isBlank(availablePeopleNum) ?
                    Collections.emptyList() :
                    Collections.singletonList(availablePeopleNum + "人");
        } else if ("否".equals(classPeopleRestriction)) {
            if (StringUtils.isBlank(bigNum) || StringUtils.isBlank(smallNum)) {
                return Collections.emptyList();
            }
            return Collections.singletonList(smallNum + "-" + bigNum + "人");
        }
        // 确保返回空列表而不是 null
        return Collections.emptyList();
    }

    /**
     * 从Attr中读取数据
     *
     * @param dealAttrs
     * @param name
     * @return
     */
    public static String findAttrValue(List<AttrM> dealAttrs, String name) {
        String str = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, name);
        if (StringUtils.isBlank(str)) {
            return "";
        }
        return str;
    }


    @Data
    public static class DealSkuItemInfoModel {
        private String skuName;
        private String productUrl;
        private String productTag;
    }

}
