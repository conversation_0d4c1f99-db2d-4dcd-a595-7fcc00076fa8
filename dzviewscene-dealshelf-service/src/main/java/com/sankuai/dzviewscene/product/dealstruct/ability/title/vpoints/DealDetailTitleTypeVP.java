package com.sankuai.dzviewscene.product.dealstruct.ability.title.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.title.DealDetailTitleBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/30 9:21 下午
 */
@VPoint(name = "团购详情标题type", description = "团购详情标题type，支持配置", code = DealDetailTitleTypeVP.CODE, ability = DealDetailTitleBuilder.CODE)
public abstract class DealDetailTitleTypeVP<T> extends PmfVPoint<String, DealDetailTitleTypeVP.Param, T> {

    public static final String CODE = "DealDetailTitleTypeVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
    }

}
