package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempurchase;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPurchaseVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/8/23
 */
@VPointOption(name = "摄影商品购买信息，通过配置来启动某些通用逻辑",
        description = "",
        code = "PhotographyItemPurchaseOpt",
        isDefault = true)
public class PhotographyItemPurchaseOpt extends ItemPurchaseVP<PhotographyItemPurchaseOpt.Config> {

    @Override
    public RichLabelVO compute(ActivityCxt activityCxt, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (!config.isShowPreSaleActivity() && PreSaleUtils.isPreSaleDeal(productM)) {
            return null;
        }
        if (StringUtils.isNotEmpty(productM.getPurchase())) {
            RichLabelVO richLabelVO = new RichLabelVO();
            richLabelVO.setText(productM.getPurchase());
            return richLabelVO;
        }
        return null;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 是否展示预售活动
         */
        private boolean isShowPreSaleActivity = false;

    }
}
