package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.postbuildhandler;

import cn.hutool.core.util.NumberUtil;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dp.arts.common.util.Sets;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.*;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.UserAfterPayPaddingHandler;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.PriceAboveTagsUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.ProductAreaPostBuildHandlerVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemTitleVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.*;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@VPointOption(
        name = "足疗spu聚合处理",
        description = "将同spu团单聚合成item，用于足疗场景",
        code = MassageSpuAggregateHandlerOpt.CODE)
public class MassageSpuAggregateHandlerOpt extends ProductAreaPostBuildHandlerVP<MassageSpuAggregateHandlerOpt.Config> {

    public static final String CODE = "MassageSpuAggregateHandlerOpt";

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.spu.shelf.black.shop", defaultValue = "[]")
    public static List<Long> BLACK_SHOP_IDS = Lists.newArrayList();

    private static final String MASSAGE_SPU_PRODUCT_TAGS = "massageSpuProductTags";

    private static Pattern TITLE_SUFFIX_PATTERN = Pattern.compile("(.*?)｜\\d+分钟[\\u4e00-\\u9fa5]*(?:足疗|按摩|推拿|精油SPA|淋巴净排|艾灸（手工悬灸）|艾灸（盒灸）|艾灸（仪器灸）|艾灸（铺灸）|正骨|推拿（含正骨）|采耳)$");

    private static final String MASSAGE_SPU_TITLE_ICON = "https://p0.meituan.net/ingee/bbb9fd3fe047e32e4372d7fed35767d5255.png";

    //样式1:聚合为SPU卡片标题
    private static final String Style1 = "1";

    //样式2:聚合为SPU卡片信息
    private static final String Style2 = "2";

    //样式3:替换副标题
    private static final String Style3 = "3";

    @Override
    public Void compute(ActivityCxt context, Param param, Config config) {
        if (CollectionUtils.isEmpty(param.getShelfProductAreaVO().getItems()) || MapUtils.isEmpty(config.getDouHu2Style())) {
            return null;
        }
        //如果已经聚合过了就不走下面逻辑了
        if (hasAggregate(context, param)) {
            return null;
        }
        int clientType = ParamsUtil.getIntSafely(context, ShelfActivityConstants.Params.userAgent);
        if (!PlatformUtil.isApp(clientType) || !hasSpu(param.getProducts())) {
            return null;
        }
        List<DouHuM> douHuMS = context.getParam(ShelfActivityConstants.Params.douHus);
        String style = config.getDouHu2Style().entrySet().stream().filter(entry -> DouHuUtils.hitAnySk(douHuMS, entry.getKey())).map(Map.Entry::getValue).findFirst().orElse(null);
        if (Style1.equals(style)) {
            handleSpuStyle1(context, config, param.getShelfProductAreaVO(), param.getProducts());
        } else if (Style2.equals(style)) {
            handleSpuStyle2(context, config, param.getShelfProductAreaVO(), param.getProducts());
        } else if (Style3.equals(style)) {
            handleSpuStyle3(context, param.getShelfProductAreaVO(), param.getProducts());
        }
        appendAfterPayProductTag(param, config, context, style);
        return null;
    }

    /**
     * 方案1，聚合为标题卡片
     *
     * @param context
     * @param shelfProductAreaVO
     * @param products
     */
    private void handleSpuStyle1(ActivityCxt context, Config config, ShelfProductAreaVO shelfProductAreaVO, List<ProductM> products) {
        shelfProductAreaVO.setItems(buildSpuAggregateItems(context, config, products, shelfProductAreaVO.getItems(), Style1));
    }

    /**
     * 方案2，聚合为SPU卡片
     *
     * @param context
     * @param shelfProductAreaVO
     * @param products
     */
    private void handleSpuStyle2(ActivityCxt context, Config config, ShelfProductAreaVO shelfProductAreaVO, List<ProductM> products) {
        //商品总数
        int totalNum = shelfProductAreaVO.getItems().size();
        shelfProductAreaVO.setItems(buildSpuAggregateItems(context, config, products, shelfProductAreaVO.getItems(), Style2));
        shelfProductAreaVO.setMoreText(buildMoreText(shelfProductAreaVO, totalNum, config));
    }

    private boolean hasAggregate(ActivityCxt context, Param param) {
        return param.getShelfProductAreaVO().getItems().stream().anyMatch(v -> CollectionUtils.isNotEmpty(v.getSubItems()) && v.getSubItems().size() > 1);
    }

    /**
     * 方案3，SPU副标题
     *
     * @param context
     * @param shelfProductAreaVO
     * @param products
     */
    private void handleSpuStyle3(ActivityCxt context, ShelfProductAreaVO shelfProductAreaVO, List<ProductM> products) {
        //团购id-ProductM
        Map<Long, ProductM> productId2M = products.stream().filter(v -> v.getProductType() == ProductTypeEnum.DEAL.getType())
                .collect(Collectors.toMap(v -> (long) v.getProductId(), Function.identity(), (v1, v2) -> v1));
        for (ShelfItemVO shelfItemVO : shelfProductAreaVO.getItems()) {
            ProductM productM = productId2M.get(shelfItemVO.getItemId());
            if (shelfItemVO.getItemType() != ProductTypeEnum.DEAL.getType() ||
                    productM == null || StringUtils.isEmpty(productM.getSptSpuId())) {
                continue;
            }
            String spuTitleTags = productM.getAttr(MASSAGE_SPU_PRODUCT_TAGS);
            if (StringUtils.isNotBlank(spuTitleTags)) {
                List<String> productTags = JsonCodec.decode(spuTitleTags, new TypeReference<List<String>>() {
                });
                shelfItemVO.setProductTags(UnifiedShelfItemSubTitleVP.build4Default(productTags));
                UnifiedShelfItemSubTitleVP.tryQueryHighLight(context, shelfItemVO.getProductTags(), false);
            }
        }
    }

    private String buildMoreText(ShelfProductAreaVO shelfProductAreaVO, int totalNum, Config config) {
        int showNum = 0;
        int realNum = 0;
        for (ShelfItemVO shelfItemVO : shelfProductAreaVO.getItems()) {
            if (shelfItemVO.getShowType() == ItemShowTypeEnum.AGGREGATE_CARD.getType()) {
                showNum += getChildShowNum(shelfItemVO, config);
                realNum += CollectionUtils.isEmpty(shelfItemVO.getSubItems()) ? 0 : shelfItemVO.getSubItems().size();
            } else {
                showNum ++;
                realNum ++;
            }
            if (showNum >= shelfProductAreaVO.getDefaultShowNum()) {
                break;
            }
        }
        return realNum < totalNum ? String.format("更多%d个团购", totalNum - realNum) : null;
    }

    private int getChildShowNum(ShelfItemVO shelfItemVO, Config config){
        if(CollectionUtils.isEmpty(shelfItemVO.getSubItems())){
            return 0;
        }
        if(config.getChildrenDefaultShowNum() > 0){
            return Math.min(shelfItemVO.getSubItems().size(), config.getChildrenDefaultShowNum());
        }
        return shelfItemVO.getSubItems().size();
    }

    private List<ShelfItemVO> buildSpuAggregateItems(ActivityCxt context, Config config, List<ProductM> products, List<ShelfItemVO> shelfItemVOS, String style) {
        //团购id-ProductM
        Map<Long, ProductM> productId2M = products.stream().filter(v -> v.getProductType() == ProductTypeEnum.DEAL.getType())
                .collect(Collectors.toMap(v -> (long) v.getProductId(), Function.identity(), (v1, v2) -> v1));
        //聚合spu
        Map<String, List<ItemVO>> spuId2Items = Maps.newHashMap();
        Map<String, DealProductSpuDTO> spuId2Info = Maps.newHashMap();
        for(ShelfItemVO shelfItemVO : shelfItemVOS) {
            if (shelfItemVO.getItemType() != ProductTypeEnum.DEAL.getType()
                    || !productId2M.containsKey(shelfItemVO.getItemId())) {
                continue;
            }
            ProductM productM = productId2M.get(shelfItemVO.getItemId());
            DealProductSpuDTO spuInfo = getRelateSpuDTO(productM);
            if (spuInfo == null) {
                continue;
            }
            spuId2Items.computeIfAbsent(productM.getSptSpuId(), k -> Lists.newArrayList())
                    .add(buildItemVO(productM, shelfItemVO));
            spuId2Info.computeIfAbsent(productM.getSptSpuId(), k -> spuInfo);
        }
        return aggregateItemVOS(context, config, shelfItemVOS, style, productId2M, spuId2Items, spuId2Info);
    }

    @NotNull
    private List<ShelfItemVO> aggregateItemVOS(ActivityCxt context, Config config,List<ShelfItemVO> shelfItemVOS, String style, Map<Long, ProductM> productId2M,
                                               Map<String, List<ItemVO>> spuId2Items, Map<String, DealProductSpuDTO> spuId2Info) {
        List<ShelfItemVO> result = Lists.newArrayList();
        //位次索引
        int index = 0;
        //已处理spuId
        Set<String> processedSpuIds = Sets.newHashSet();
        //按原顺序构造列表
        for (ShelfItemVO shelfItemVO : shelfItemVOS) {
            ProductM productM = productId2M.get(shelfItemVO.getItemId());
            //无关联spu团单直接添加到结果列表
            if (shelfItemVO.getItemType() != ProductTypeEnum.DEAL.getType() || productM == null
                    || StringUtils.isEmpty(productM.getSptSpuId()) || spuId2Info.get(productM.getSptSpuId()) == null
                    || CollectionUtils.isEmpty(spuId2Items.get(productM.getSptSpuId()))) {
                result.add(shelfItemVO);
                index++; // 增加位次索引
                continue;
            }
            //已处理的spu团单跳过
            if (processedSpuIds.contains(productM.getSptSpuId())) {
                continue;
            }
            DealProductSpuDTO spuInfo = spuId2Info.get(productM.getSptSpuId());
            List<ItemVO> itemVOS = spuId2Items.get(productM.getSptSpuId());
            ShelfItemVO aggregateItem = Style2.equals(style)
                    ? aggregateStyle2(context, config, spuInfo, itemVOS, index)
                    : aggregateStyle1(context, spuInfo, itemVOS, index);
            result.add(aggregateItem);
            index++; // 增加位次索引
            processedSpuIds.add(productM.getSptSpuId());
        }
        return result;
    }

    private ShelfItemVO aggregateStyle1(ActivityCxt context, DealProductSpuDTO spuInfo, List<ItemVO> relateItems, int index) {
        ShelfItemVO shelfItemVO = new ShelfItemVO();
        shelfItemVO.setItemId(spuInfo.getSpuId());
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setTitleTag(buildTitleTag(spuInfo.getSpuName()));
        List<ShelfItemVO> subItems = relateItems.stream().map(v -> buildShelfItemVO(context, v)).collect(Collectors.toList());
        shelfItemVO.setSubItems(subItems);
        shelfItemVO.setShowType(ItemShowTypeEnum.AGGREGATE_CARD.getType());
        shelfItemVO.setLabs(buildLabs(shelfItemVO, spuInfo.getSpuName(), index));
        return shelfItemVO;
    }

    private ShelfItemVO aggregateStyle2(ActivityCxt context, Config config, DealProductSpuDTO spuInfo, List<ItemVO> relateItems, int index) {
        ShelfItemVO shelfItemVO = buildSpuItem(context, config, spuInfo, relateItems, index);
        if (relateItems.size() == 1) {
            //单SPU团单，拼接spu标题
            if (!hitBlackShop(context) && CollectionUtils.isNotEmpty(shelfItemVO.getSubItems().get(0).getTitle())) {
                shelfItemVO.getSubItems().get(0).getTitle().add(0, buildText(spuInfo.getSpuName() + "｜"));
            }
            return shelfItemVO;
        }else{
            //多SPU团单，隐藏头图
            shelfItemVO.getSubItems().forEach(v -> {
                if (v.getHeadPic() != null) {
                    v.getHeadPic().setPic(null);
                    v.getHeadPic().setFloatTags(null);
                }
            });
        }
        return shelfItemVO;
    }

    private DealProductSpuDTO getRelateSpuDTO(ProductM productM) {
        if (StringUtils.isEmpty(productM.getSptSpuId()) || CollectionUtils.isEmpty(productM.getSpuMList())) {
            return null;
        }
        return productM.getSpuMList().stream().filter(v -> productM.getSptSpuId().equals(String.valueOf(v.getSpuId()))
            && StringUtils.isNotBlank(v.getSpuName())).findFirst().orElse(null);
    }

    private ShelfItemVO buildSpuItem(ActivityCxt context, Config config, DealProductSpuDTO spuInfo, List<ItemVO> relateItems, int index) {
        ShelfItemVO shelfItemVO = new ShelfItemVO();
        shelfItemVO.setItemId(spuInfo.getSpuId());
        shelfItemVO.setItemType(ProductTypeEnum.SPT_SPU.getType());
        shelfItemVO.setTitle(Lists.newArrayList(buildText(spuInfo.getSpuName())));
        shelfItemVO.setHeadPic(spuHeadPic(relateItems));
        shelfItemVO.setProductTags(spuSubTitle(relateItems));
        shelfItemVO.setSalePrice(spuSalePrice(relateItems));
        shelfItemVO.setSalePriceSuffix("起");
        shelfItemVO.setButtonCarouselMsg(spuSaleMsg(relateItems));
        shelfItemVO.setSpecialTags(spuSpecialTags(relateItems));
        List<ShelfItemVO> subItems = relateItems.stream().map(v -> buildShelfItemVO(context, v)).collect(Collectors.toList());
        shelfItemVO.setSubItems(subItems);
        shelfItemVO.setDefaultShowNum(config.getChildrenDefaultShowNum());
        shelfItemVO.setMoreText(buildChildrenMoreText(config, relateItems));
        shelfItemVO.setShowType(ItemShowTypeEnum.AGGREGATE_CARD.getType());
        shelfItemVO.setLabs(buildLabs(shelfItemVO, spuInfo.getSpuName(), index));
        return shelfItemVO;
    }

    private String buildChildrenMoreText(Config config, List<ItemVO> relateItems) {
        if (relateItems.size() <= config.getChildrenDefaultShowNum() || StringUtils.isEmpty(config.getChildrenMoreText())) {
            return null;
        }
        return String.format(config.getChildrenMoreText(), relateItems.size());
    }

    private String buildLabs(ShelfItemVO shelfItemVO, String title, int index) {
        Map<String, Object> oceanMap = Maps.newHashMap();
        oceanMap.put("index", index);
        //1单SPU 2多SPU
        oceanMap.put("type", shelfItemVO.getSubItems().size() > 1 ? 2 : 1);
        oceanMap.put("title", title);
        return JsonCodec.encode(oceanMap);
    }

    private ShelfItemVO buildShelfItemVO(ActivityCxt context, ItemVO itemVO) {
        ShelfItemVO shelfItemVO = itemVO.getShelfItemVO();
        //隐藏副标题
        shelfItemVO.setProductTags(null);
        //标题替换原始标题
        String originTitle = parseOriginTitle(itemVO.getProduct());
        if (StringUtils.isNotEmpty(originTitle)) {
            shelfItemVO.setTitle(UnifiedShelfItemTitleVP.build4TryHighlightText(context, originTitle, false));
        }
        return shelfItemVO;
    }

    private String parseOriginTitle(ProductM product) {
        if (StringUtils.isEmpty(product.getTitle())) {
            return null;
        }
        try {
            Matcher matcher = TITLE_SUFFIX_PATTERN.matcher(product.getTitle());
            if (!matcher.find()) {
                return null;
            }
            return matcher.group(1);
        } catch (Exception e) {
            //do nothing
            return null;
        }
    }

    private StyleTextModel buildText(String text) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(text);
        styleTextModel.setStyle(TextStyleEnum.TEXT_GRAY.getType());
        return styleTextModel;
    }

    private IconRichLabelModel buildTitleTag(String spuName) {
        IconRichLabelModel iconRichLabelModel = new IconRichLabelModel();
        PictureModel pictureModel = new PictureModel();
        pictureModel.setPicUrl(MASSAGE_SPU_TITLE_ICON);
        iconRichLabelModel.setIcon(pictureModel);
        RichLabelModel richLabelModel = new RichLabelModel();
        richLabelModel.setText(spuName);
        richLabelModel.setMultiText(Lists.newArrayList(spuName));
        iconRichLabelModel.setText(richLabelModel);
        iconRichLabelModel.setType(IconRichLabelTypeEnum.ICON_TEXT.getType());
        return iconRichLabelModel;
    }

    private ItemVO buildItemVO(ProductM product, ShelfItemVO shelfItemVO) {
        ItemVO itemVO = new ItemVO();
        itemVO.setProduct(product);
        itemVO.setShelfItemVO(shelfItemVO);
        return itemVO;
    }

    /**
     * spu卡片头图，选择销量最高团单头图
     *
     * @param relateItems
     * @return
     */
    private PicAreaVO spuHeadPic(List<ItemVO> relateItems) {
        PictureModel picModel = relateItems.stream()
                .max(Comparator.comparing(item -> Optional.ofNullable(item.getProduct().getSale())
                        .map(ProductSaleM::getSale)
                        .orElse(0)))
                .map(item -> item.getShelfItemVO().getHeadPic() != null ? item.getShelfItemVO().getHeadPic().getPic() : null)
                .orElse(null);
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(picModel);
        return picAreaVO;
    }

    /**
     * spu卡片推荐理由，选择团单销量最高推荐理由
     *
     * @param relateItems
     * @return
     */
    private ItemSpecialTagVO spuSpecialTags(List<ItemVO> relateItems) {
        List<String> recommendTags = relateItems.stream()
                .max(Comparator.comparing(item -> Optional.ofNullable(item.getProduct().getSale())
                        .map(ProductSaleM::getSale)
                        .orElse(0)))
                .map(item -> PriceAboveTagsUtils.getRecommendTag(item.getProduct()))
                .orElse(null);
        if (CollectionUtils.isEmpty(recommendTags)) {
            return null;
        }
        ShelfTagVO recommend = buildShelfTagVO("“" + recommendTags.get(0) + "”", ColorUtils.colorF4F4F4, ColorUtils.color666666);
        ItemSpecialTagVO itemSpecialTagVO = new ItemSpecialTagVO();
        itemSpecialTagVO.setTags(Lists.newArrayList(recommend));
        return itemSpecialTagVO;
    }

    private ShelfTagVO buildShelfTagVO(String recommendTag, String background, String textColor) {
        RichLabelModel richLabelText = new RichLabelModel();
        richLabelText.setText(recommendTag);
        richLabelText.setMultiText(Lists.newArrayList(recommendTag));
        richLabelText.setTextColor(textColor);
        richLabelText.setBackgroundColor(background);
        richLabelText.setStyle(RichLabelStyleEnum.BUBBLE.getType());
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setText(richLabelText);
        return shelfTagVO;
    }

    /**
     * spu卡片副标题
     *
     * @param relateItems
     * @return
     */
    private ItemSubTitleVO spuSubTitle(List<ItemVO> relateItems) {
        ItemSubTitleVO itemSubTitleVO = new ItemSubTitleVO();
        itemSubTitleVO.setTags(Lists.newArrayList(buildText(String.format("可选%d个套餐", relateItems.size()))));
        return itemSubTitleVO;
    }

    /**
     * spu卡片销量，累计销量
     *
     * @param relateItems
     * @return
     */
    private List<CarouselMsg> spuSaleMsg(List<ItemVO> relateItems) {
        int sales = relateItems.stream().map(v -> v.getProduct().getSale()).filter(Objects::nonNull).mapToInt(ProductSaleM::getSale).sum();
        if (sales <= 0) {
            return null;
        }
        CarouselMsg saleMsg = new CarouselMsg();
        saleMsg.setText(buildText(sectionSales(sales)));
        saleMsg.setType(CarouselMsgTypeEnum.SALE_MSG.getType());
        return Lists.newArrayList(saleMsg);
    }

    /**
     * spu卡片价格，选择团单最低价格
     *
     * @param relateItems
     * @return
     */
    private String spuSalePrice(List<ItemVO> relateItems) {
        return relateItems.stream().map(v -> v.getShelfItemVO().getSalePrice()).filter(Objects::nonNull)
                .min(Comparator.comparing(BigDecimal::new)).orElse("0");
    }

    private boolean hasSpu(List<ProductM> products) {
        return products.stream().anyMatch(v -> StringUtils.isNotEmpty(v.getSptSpuId()));
    }

    private boolean hitBlackShop(ActivityCxt context) {
        long dpShopId = ParamsUtil.getLongSafely(context, ShelfActivityConstants.Params.dpPoiIdL);
        if (CollectionUtils.isNotEmpty(BLACK_SHOP_IDS) && BLACK_SHOP_IDS.contains(dpShopId)) {
            return true;
        }
        //配置-1，全量下掉
        if (CollectionUtils.isNotEmpty(BLACK_SHOP_IDS) && BLACK_SHOP_IDS.contains(-1L)) {
            return true;
        }
        return false;
    }

    private String sectionSales(int saleNum) {
        if (saleNum < 50) {
            return String.format("年售%s", saleNum);
        }
        if (saleNum < 100) {
            return String.format("年售%s+", saleNum / 10 * 10);
        }
        if (saleNum < 1000) {
            return String.format("年售%s+", saleNum / 100 * 100);
        }
        if (saleNum < 10000) {
            return String.format("年售%s+", saleNum / 1000 * 1000);
        }
        return String.format("年售%s万+", NumberUtil.div(saleNum, 10000, 1, RoundingMode.DOWN));
    }

    public void appendAfterPayProductTag(Param param, Config config, ActivityCxt context, String style) {
        List<DouHuM> douHuMList = context.getParam(ShelfActivityConstants.Params.douHus);
        if (!UserAfterPayPaddingHandler.getUserExposure(context) || !DouHuUtils.hitAnySk(douHuMList, config.getAfterPayExps())) {
            return;
        }
        List<ProductM> products = param.getProducts();
        Map<Long, ProductM> productId2M = products.stream().filter(v -> v.getProductType() == ProductTypeEnum.DEAL.getType())
                .collect(Collectors.toMap(v -> (long) v.getProductId(), Function.identity(), (v1, v2) -> v1));
        ShelfProductAreaVO shelfProductAreaVO = param.getShelfProductAreaVO();
        for (ShelfItemVO shelfItemVO : shelfProductAreaVO.getItems()) {
            if (Style3.equals(style)) {
                ProductM productM = productId2M.get(shelfItemVO.getItemId());
                String spuTitleTags = productM.getAttr(MASSAGE_SPU_PRODUCT_TAGS);
                if (StringUtils.isNotEmpty(spuTitleTags)) {
                    toAppend(config, shelfItemVO, productId2M);
                }
                continue;
            }
            List<ShelfItemVO> subItems = shelfItemVO.getSubItems();
            if (CollectionUtils.isNotEmpty(subItems)) {
                subItems.forEach(subItem -> toAppend(config, subItem, productId2M));
            }
        }
    }

    private void toAppend(Config config, ShelfItemVO shelfItemVO, Map<Long, ProductM> productId2M) {
        ProductM productM = productId2M.get(shelfItemVO.getItemId());
        if ((productM.getProductType() == ProductTypeEnum.TIME_CARD.getType() || productM.isTimesDeal()) && ProductMAttrUtils.isAfterPayProduct(productM)) {
            ItemSubTitleVO itemSubTitleVO = appendAfterPaySpuTag(shelfItemVO.getProductTags(), config);
            shelfItemVO.setProductTags(itemSubTitleVO);
        }
    }

    public ItemSubTitleVO appendAfterPaySpuTag(ItemSubTitleVO productTags, Config config) {

        StyleTextModel afterPayTag = buildPreAfterPayTag(config);
        if (productTags == null || CollectionUtils.isEmpty(productTags.getTags())) {
            productTags = new ItemSubTitleVO();
            productTags.setJoinType(0);
            productTags.setTags(Lists.newArrayList(afterPayTag));
        } else {
            List<StyleTextModel> tags = productTags.getTags();
            tags.add(0, afterPayTag);
            productTags.setTags(tags);
        }
        return productTags;
    }

    public StyleTextModel buildPreAfterPayTag(Config config) {
        StyleTextModel styleTextModel = new StyleTextModel();
        styleTextModel.setText(config.getPreAfterTag());
        styleTextModel.setStyle(TextStyleEnum.TEXT_GREEN.getType());
        return styleTextModel;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 斗槲实验结果对应样式
         * key：实验结果
         * value：样式, 1-只聚合为标题，2-聚合SPU卡片信息
         */
        private Map<String, String> douHu2Style;

        private Map<Integer, DealProductSpuDTO> mockSpu;

        /**
         * 子项默认展示数
         */
        private int childrenDefaultShowNum = 2;

        /**
         * 子项更多文案，%d个数
         */
        private String childrenMoreText = "全部%d个套餐";

        /**
         * 先用后付副标题
         */
        private String preAfterTag;

        private List<String> afterPayExps;
    }

    @Data
    public static class ItemVO {

        private ProductM product;

        private ShelfItemVO shelfItemVO;
    }
}
