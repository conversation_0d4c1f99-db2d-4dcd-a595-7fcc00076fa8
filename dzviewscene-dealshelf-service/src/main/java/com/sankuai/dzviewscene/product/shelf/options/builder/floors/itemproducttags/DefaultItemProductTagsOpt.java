package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import java.util.List;
/**
 * <AUTHOR>
 * @date 2022/2/9
 */
@VPointOption(name = "商品副标题构造-默认逻辑",
        description = "取 ProductM 的 ProductTags",
        code = "DefaultItemProductTagsOpt",
        isDefault = true)
public class DefaultItemProductTagsOpt extends ItemProductTagsVP<Void> {
    @Override
    public List<String> compute(ActivityCxt context, Param param, Void config) {
        return param.getProductM().getProductTags();
    }
}