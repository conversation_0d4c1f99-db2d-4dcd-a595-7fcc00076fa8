package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.bean.copy.processor.internal.util.Collections;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListAfterProcessingVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/8 5:29 下午
 */
@VPointOption(name = "配镜sku列表后置处理变化点", description = "配镜sku列表后置处理变化点", code = GlassesSkuListAfterProcessingOpt.CODE, isDefault = false)
public class GlassesSkuListAfterProcessingOpt extends SkuListAfterProcessingVP<GlassesSkuListAfterProcessingOpt.Config> {

    public static final String CODE = "GlassesSkuListAfterProcessingOpt";

    @Override
    public List<DealSkuVO> compute(ActivityCxt context, Param param, Config config) {
        List<DealSkuVO> dealSkuVOS = param.getDealSkuVOS();
        if (CollectionUtils.isNotEmpty(dealSkuVOS)) {
            dealSkuVOS.stream().forEach(dealSkuVO -> {
                if (CollectionUtils.isEmpty(dealSkuVO.getItems())) {
                    return;
                }
                buildJingPianDealSkuItemVOS(dealSkuVO);
                buildJingKuangDealSkuItemVOS(dealSkuVO);
                buildPeiJianDealSkuItemVOS(dealSkuVO);
                buildZengZhiFuWuDealSkuItemVOS(dealSkuVO);
            });
        }
        return dealSkuVOS;
    }

    private void buildJingPianDealSkuItemVOS(DealSkuVO dealSkuVO) {
        //镜片
        List<DealSkuItemVO> dealSkuItemVOS = dealSkuVO.getItems()
                .stream().filter(dealSkuItemVO -> dealSkuItemVO.getName().equals("镜片"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return;
        }
        if (dealSkuItemVOS.size() > 1) {
            for (int i = 0; i < dealSkuItemVOS.size(); i++) {
                if (i == 0) {
                    dealSkuItemVOS.get(i).setName("镜片（" + dealSkuItemVOS.size() + "选1）");
                    continue;
                }
                dealSkuItemVOS.get(i).setName("");
            }
        }
    }

    private void buildJingKuangDealSkuItemVOS(DealSkuVO dealSkuVO) {
        //镜框
        List<DealSkuItemVO> dealSkuItemVOS = dealSkuVO.getItems()
                .stream().filter(dealSkuItemVO -> dealSkuItemVO.getName().equals("镜框"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return;
        }
        if (dealSkuItemVOS.size() > 1) {
            for (int i = 0; i < dealSkuItemVOS.size(); i++) {
                if (i == 0) {
                    continue;
                }
                dealSkuItemVOS.get(i).setName("");
            }
        }
    }

    private void buildPeiJianDealSkuItemVOS(DealSkuVO dealSkuVO) {
        //镜框
        List<DealSkuItemVO> dealSkuItemVOS = dealSkuVO.getItems()
                .stream().filter(dealSkuItemVO -> dealSkuItemVO.getName().equals("配件"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return;
        }
        if (dealSkuItemVOS.size() > 1) {
            for (int i = 0; i < dealSkuItemVOS.size(); i++) {
                if (i == 0) {
                    continue;
                }
                dealSkuItemVOS.get(i).setName("");
            }
        }
    }

    private void buildZengZhiFuWuDealSkuItemVOS(DealSkuVO dealSkuVO) {
        //镜框
        List<DealSkuItemVO> dealSkuItemVOS = dealSkuVO.getItems()
                .stream().filter(dealSkuItemVO -> dealSkuItemVO.getName().equals("增值服务"))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dealSkuItemVOS)) {
            return;
        }
        if (dealSkuItemVOS.size() > 1) {
            for (int i = 0; i < dealSkuItemVOS.size(); i++) {
                if (i == 0) {
                    continue;
                }
                dealSkuItemVOS.get(i).setName("");
            }
        }
    }


    @Data
    @VPointCfg
    public static class Config {
        private String supportingProjectSkuName;
        private String supportingProjectSkuCountFormat;
    }

    @Data
    static private class VaccineSupportingProjecItemModel {
        private String count;
        private String projectName;
    }
}
