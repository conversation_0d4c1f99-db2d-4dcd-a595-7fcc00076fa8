package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuTitleVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@VPointOption(name = "ktv项目标题变化点", description = "ktv项目标题变化点，根据ktv团单三级分类来拼接标题", code = KtvSkuTitleOpt.CODE)
public class KtvSkuTitleOpt extends SkuTitleVP<KtvSkuTitleOpt.Config> {

    public static final String CODE = "KtvSkuTitleOpt";

    /**
     * 拼接项目属性 = 新标题
     */
    private static final String DIRECT_JOIN_ATTR_STRATEGY = "directJoinAttr";

    private static Map<String, KtvSkuTitleOpt.TitleBuildStrategy> buildStrategyMap = new HashMap<>();

    static {
        buildStrategyMap.put(DIRECT_JOIN_ATTR_STRATEGY, getDirectJoinAttrStrategy());
    }

    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        if (validateParams(param, config)) {
            // 不需要特殊的标题拼接策略，直接返回项目名
            return param.getSkuTitle();
        }
        String strategy = config.getTitleStrategys().get(param.getSkuItemDto().getProductCategory());
        TitleBuildStrategy titleBuildStrategy = buildStrategyMap.get(strategy);
        if (strategy == null) {
            //构造策略不存在，则使用默认的
            return param.getSkuTitle();
        }
        String title = titleBuildStrategy.build(param, config);
        return StringUtils.isNotEmpty(title) ? title : param.getSkuTitle();
    }

    private boolean validateParams(Param param, Config config) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.dealstruct.options.skuList.KtvSkuTitleOpt.validateParams(SkuTitleVP$Param,KtvSkuTitleOpt$Config)");
        return config == null ||
                MapUtils.isEmpty(config.getTitleStrategys()) ||
                param.getSkuItemDto() == null ||
                !config.getTitleStrategys().containsKey(param.getSkuItemDto().getProductCategory()) ||
                MapUtils.isEmpty(config.getAttrs()) ||
                CollectionUtils.isEmpty(config.getAttrs().get(param.getSkuItemDto().getProductCategory()));
    }

    @FunctionalInterface
    public interface TitleBuildStrategy {
        /**
         * 构造项目标题
         *
         * @param param
         * @param config
         * @return
         */
        String build(KtvSkuTitleOpt.Param param, KtvSkuTitleOpt.Config config);
    }

    /**
     * 拼接属性替换标题
     */
    private static KtvSkuTitleOpt.TitleBuildStrategy getDirectJoinAttrStrategy() {
        return (param, config) -> {
            List<String> attrs = config.getAttrs().get(param.getSkuItemDto().getProductCategory());
            return attrs.stream().map(attr -> DealDetailUtils.getSkuAttrValueBySkuAttrName(param.getSkuItemDto().getAttrItems(), attr))
                    .filter(StringUtils::isNotEmpty).collect(Collectors.joining());
        };
    }

    @Data
    @VPointCfg
    public static class Config {
        /**
         * 服务项目Id:标题拼接策略
         */
        private Map<Long, String> titleStrategys;
        /**
         * 服务项目Id:待拼接的属性列表，有序
         */
        private Map<Long, List<String>> attrs;
    }
}
