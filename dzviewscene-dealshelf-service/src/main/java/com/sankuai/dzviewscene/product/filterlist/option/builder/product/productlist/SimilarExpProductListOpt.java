package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductListVP;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/31 5:08 PM
 */
@VPointOption(name = "按实验策略分流",
        description = "按实验策略分流",
        code = "SimilarExpProductListOpt")
public class SimilarExpProductListOpt extends ProductListVP<SimilarExpProductListOpt.Config> {

    @Override
    public List<ProductM> compute(ActivityCxt context, Param param, Config config) {
        int curId = ParamsUtil.getIntSafely(context, PmfConstants.Params.entityId);
        List<ProductM> productMS = topCurProduct(param.getProductMS(), curId);
        int getLimitByDouHuM = getLimitByDouHuM(param.getDouHuMS(), config);
        if (getLimitByDouHuM <= 0) {
            return productMS;
        }
        return productMS.stream().filter(productM -> StringUtils.isNotEmpty(productM.getTitle())).limit(getLimitByDouHuM).collect(Collectors.toList());
    }

    private List<ProductM> topCurProduct(List<ProductM> productMS, int curId) {
        List<ProductM> collect = new ArrayList<>();
        ProductM curProduct = null;
        for (ProductM p : productMS) {
            if (p.getProductId() == curId) {
                curProduct = p;
                continue;
            }
            collect.add(p);
        }
        if (curProduct != null) {
            collect.add(0, curProduct);
        }
        return collect;
    }

    private int getLimitByDouHuM(List<DouHuM> douHuMS, Config config) {
        if (CollectionUtils.isEmpty(douHuMS) || MapUtils.isEmpty(config.getExpSk2Limit())) {
            return -1;
        }
        return douHuMS.stream()
                .map(DouHuM::getSk)
                .filter(Objects::nonNull)
                .map(sk -> config.getExpSk2Limit().get(sk))
                .filter(Objects::nonNull).findFirst().orElse(-1);
    }

    @VPointCfg
    @Data
    public static class Config {
        private Map<String, Integer> expSk2Limit;
    }
}
