package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductRichTagsVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.ColorUtils;
import com.sankuai.dzviewscene.productshelf.vu.utils.FrontSizeUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@VPointOption(name = "种植牙-RichTags形式的副标题构造",
        description = "种植牙富文本副标题",
        code = "ImplantToothProductRichTagsOpt")
public class ImplantToothProductRichTagsOpt extends ItemProductRichTagsVP<ImplantToothProductRichTagsOpt.Config> {

    private static final String IMPLANT_TOOTH_CHECK = "种植牙方案设计";

    private static final String IMPLANT_TOOTH_POSITIVE = "种植牙治疗";

    private static final String cssBorderRadius = "#F6F6F6";

    private static final String cssPadding = "0,4,0,4";

    private static final String fontWeight = "400";

    /**
     * 种植牙检查单服务亮点
     */
    private static final String implant_advantage1 = "implant_advantage1";

    /**
     * 种植牙正价单 放心种植
     */
    private static final String reassuringDentalImplant = "reassuringDentalImplant";

    /**
     * 种植牙正价单 服务亮点
     */
    private static final String ServiceHighlights = "ServiceHighlights";

    @Override
    public List<RichLabelVO> compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        List<RichLabelVO> richLabelVOS = new ArrayList<>();
        //种植牙检查单 "serviceTypeId":140042  种植牙方案设计
        if (StringUtils.equals(ProductMAttrUtils.getServiceType(productM), IMPLANT_TOOTH_CHECK)) {
            List<String> advantage1List = ProductMAttrUtils.getAttrValueList(productM, implant_advantage1);
            if (CollectionUtils.isEmpty(advantage1List)) {
                return richLabelVOS;
            }
            richLabelVOS.addAll(advantage1List.stream().map(advantage ->
                    new RichLabelVO(FrontSizeUtils.front10, ColorUtils.color777777, advantage)).collect(Collectors.toList()));
            return richLabelVOS;
        }
        //种植牙正价单 serviceTypeId:126050  种植牙治疗
        if (StringUtils.equals(ProductMAttrUtils.getServiceType(productM), IMPLANT_TOOTH_POSITIVE)) {
            String performanceGuarantee = ProductMAttrUtils.getAttrValue(productM, reassuringDentalImplant);
            String splitStr = config == null ? Strings.EMPTY : config.getSplitStr();
            if (StringUtils.isNotEmpty(performanceGuarantee)) {
                RichLabelVO richLabelVO = new RichLabelVO(FrontSizeUtils.front10, ColorUtils.color111111,
                        splitStr + performanceGuarantee);
                richLabelVO.setPreIcon(config == null ? null : config.getPositivePreIcon());
                richLabelVO.setPreIconAspectRadio(config == null ? 0 : config.getPreIconAspectRadio());
                richLabelVO.setBackgroundColor(config == null ? ColorUtils.colorEBF6FF : config.getPreIconBackGroundColor());
                richLabelVO.setCssBorderRadius(config == null ? cssBorderRadius : config.getCssBorderRadius());
                richLabelVO.setCssPadding(config == null ? cssPadding : config.getCssPadding());
                richLabelVO.setFontWeight(config == null ? fontWeight : config.getFontWeight());
                richLabelVOS.add(richLabelVO);
            }
            List<String> serviceHighlightsList = ProductMAttrUtils.getAttrValueList(productM, ServiceHighlights);
            if (CollectionUtils.isEmpty(serviceHighlightsList)) {
                return richLabelVOS;
            }
            richLabelVOS.addAll(serviceHighlightsList.stream().map(advantage ->
                    new RichLabelVO(FrontSizeUtils.front10, ColorUtils.color777777, advantage)).collect(Collectors.toList()));
            return richLabelVOS;
        }
        return richLabelVOS;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 放心种植正价单标签前缀图片
         */
        private String positivePreIcon = "https://p0.meituan.net/dealproduct/92c5339e946b3b9c4f9f19dbce3d98c42119.png";

        private String splitStr;

        private String preIconBackGroundColor = "#EBF6FF";

        private String cssBorderRadius = "#F6F6F6";

        private String cssPadding = "0,4,0,4";

        private String fontWeight = "400";

        private double preIconAspectRadio = 5.5;
    }
}
