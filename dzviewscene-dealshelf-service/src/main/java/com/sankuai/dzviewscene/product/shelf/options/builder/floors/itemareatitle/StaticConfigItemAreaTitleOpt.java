package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemareatitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemAreaTitleVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.TitleComponentVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/27
 */
@VPointOption(name = "静态配置标题区",
        description = "",
        code = "StaticConfigItemAreaTitleOpt")
public class StaticConfigItemAreaTitleOpt extends ItemAreaTitleVP<StaticConfigItemAreaTitleOpt.Config> {

    @Override
    public TitleComponentVO compute(ActivityCxt activityCxt, Param param, Config config) {
        //配置了分组标题
        if (MapUtils.isNotEmpty(config.getGroupName2TitleCfgMap()) && StringUtils.isNotEmpty(param.getGroupName())) {
            TitleCfg titleCfg = config.getGroupName2TitleCfgMap().get(param.getGroupName());
            if (titleCfg != null) {
                return titleCfg2TitleCmp(titleCfg);
            }
        }
        //配置了筛选项对应商品组标题
        if(MapUtils.isNotEmpty(config.getFilterName2GroupTitleCfgMap())){
            String filterName = getFirstLevelFilterName(param.getFilterM(), param.getFilterId());
            Map<String, TitleCfg> groupName2Cfg = config.getFilterName2GroupTitleCfgMap().get(filterName);
            if(MapUtils.isNotEmpty(groupName2Cfg) && groupName2Cfg.get(param.getGroupName()) != null){
                return titleCfg2TitleCmp(groupName2Cfg.get(param.getGroupName()));
            }
        }
        return null;
    }

    private TitleComponentVO titleCfg2TitleCmp(TitleCfg titleCfg) {
        TitleComponentVO titleComponentVO = new TitleComponentVO();
        titleComponentVO.setTitle(titleCfg.getTitle());
        titleComponentVO.setIcon(titleCfg.getIcon());
        titleComponentVO.setSubTitle(titleCfg.getSubTitle());
        return titleComponentVO;
    }

    /**
     * 根据筛选项ID获取一级筛选名称
     *
     * @param filterM
     * @param filterId
     * @return
     */
    private String getFirstLevelFilterName(FilterM filterM, long filterId) {
        if (filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return null;
        }
        for (FilterBtnM firstLevelBtn : filterM.getFilters()) {
            if (firstLevelBtn.getFilterId() == filterId) {
                return firstLevelBtn.getTitle();
            }
        }
        return null;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * key 为 groupName；
         * value 为标题配置
         */
        private Map<String, TitleCfg> groupName2TitleCfgMap;

        /**
         * key 为 filterName；
         * value 为商品组与标题配置映射，key为组名，value为标题配置
         */
        private Map<String, Map<String, TitleCfg>> filterName2GroupTitleCfgMap;
    }

    @Data
    public static class TitleCfg {
        /**
         * 标题
         */
        private String title;

        /**
         * 副标题
         */
        private String subTitle;

        /**
         * 图片url
         */
        private String icon;
    }
}
