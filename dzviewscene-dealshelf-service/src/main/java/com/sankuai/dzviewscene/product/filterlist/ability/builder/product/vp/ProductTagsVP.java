package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29 11:30 上午
 */
@VPoint(name = "商品-ProductTags", description = "商品-ProductTags", code = ProductTagsVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductTagsVP<T> extends PmfVPoint<List<String>, ProductTagsVP.Param, T> {

    public static final String CODE = "ProductTagsVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}
