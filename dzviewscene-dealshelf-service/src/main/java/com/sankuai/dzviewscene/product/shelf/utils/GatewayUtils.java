package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.gateway.common.utils.GatewayHelper;
import com.dianping.lion.client.Lion;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;

public class GatewayUtils {

    private static final String IGNORE_NULL_SWITCH = "com.sankuai.dzviewscene.dealshelf.gateway.response.ignore.null.switch";

    /**
     * 返回结果序列化忽略null字段
     */
    public static void ignoreNullField(int clientType){
        //序列化忽略null字段开关
        if(Lion.getBoolean("com.sankuai.dzviewscene.dealshelf", IGNORE_NULL_SWITCH, false)){
            return;
        }
        //app内
        if(PlatformUtil.isApp(clientType)){
            GatewayHelper.setSerializeSkipNull(true);
        }
    }
}
