package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemmarketprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemMarketPriceVP;

@VPointOption(name = "默认-返回市场价",
        description = "直接返回市场价",
        code = DefaultMarketPriceOpt.CODE,
        isDefault = true)
public class DefaultMarketPriceOpt extends UnifiedShelfItemMarketPriceVP<Void> {

    public static final String CODE = "DefaultMarketPriceOpt";

    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        return param.getProductM().getMarketPrice();
    }
}