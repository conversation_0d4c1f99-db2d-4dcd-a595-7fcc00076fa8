package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create.BarMustGroupSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create.BarOptionalGroupDrinksAndMealsSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create.BarOptionalGroupDrinksSkuListCreator;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate.create.BarOptionalGroupMealsSkuListCreator;

import java.util.ArrayList;
import java.util.List;

/**
 * 酒吧sku列表构造器工厂
 * <AUTHOR>
 * @date 2023/2/9 1:26 下午
 */
public class BarSkuListCreatorFactory {

    private static List<AbstractBarSkuListCreator> barSkuListBuilderList = new ArrayList<>();

    static {
        barSkuListBuilderList.add(new BarMustGroupSkuListCreator());
        barSkuListBuilderList.add(new BarOptionalGroupDrinksSkuListCreator());
        barSkuListBuilderList.add(new BarOptionalGroupMealsSkuListCreator());
        barSkuListBuilderList.add(new BarOptionalGroupDrinksAndMealsSkuListCreator());
    }

    public static AbstractBarSkuListCreator creadSkuListBuilder(boolean isMustSku, List<SkuItemDto> skuList, BarDealDetailSkuListModuleOpt.Config config) {
        for(AbstractBarSkuListCreator barSkuListBuilder : barSkuListBuilderList) {
            if (barSkuListBuilder.ideantify(isMustSku, skuList, config)) {
                return barSkuListBuilder;
            }
        }
        return null;
    }
}
