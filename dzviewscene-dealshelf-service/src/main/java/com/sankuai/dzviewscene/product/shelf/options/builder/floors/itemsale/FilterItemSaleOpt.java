package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsale;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSaleVP;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/12/6 11:10
 */
@VPointOption(
        name = "根据条件过滤团单销量文案",
        description = "根据条件过滤团单销量文案",
        code = "FilterItemSaleOpt")
public class FilterItemSaleOpt extends ItemSaleVP<FilterItemSaleOpt.Config> {
    @Override
    public String compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (Objects.isNull(productM.getSale()) || StringUtils.isBlank(productM.getSale().getSaleTag())) {
            return StringUtils.EMPTY;
        }
        if (config.filterZeroSale && productM.getSale().getSale() <= 0) {
            return StringUtils.EMPTY;
        }
        // 爆品团单+且销量小于等于，则不返回
        if (Objects.nonNull(config.getFilterHotDealSaleBySaleNum()) && ProductMAttrUtils.hotSpu(productM)
                && productM.getSale().getSale() <= config.getFilterHotDealSaleBySaleNum()) {
            return StringUtils.EMPTY;
        }
        return productM.getSale().getSaleTag();
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 过滤销量为0的销量标签
         */
        private boolean filterZeroSale = false;
        /**
         * 根据销量过滤爆品的销量
         */
        private Integer filterHotDealSaleBySaleNum;
    }
}
