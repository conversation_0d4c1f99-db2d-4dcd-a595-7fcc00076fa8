package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/25 16:18
 */
@VPoint(name = "商品-价格上方标签", description = "位于价格上方的一行标签", code = ItemPriceAboveTagsVP.CODE, ability = FloorsBuilder.CODE)
public abstract class ItemPriceAboveTagsVP<T> extends PmfVPoint<List<DzTagVO>, ItemPriceAboveTagsVP.Param, T> {
    public static final String CODE = "ItemPriceAboveTagsVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private int platform;

        private List<DouHuM> douHuList;

        private CardM cardM;

        private String salePrice;

        private List<DzTagVO> priceBottomTags;
    }
}
