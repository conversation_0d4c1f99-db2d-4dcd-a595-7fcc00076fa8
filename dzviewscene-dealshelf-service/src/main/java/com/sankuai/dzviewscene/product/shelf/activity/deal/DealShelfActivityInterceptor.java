package com.sankuai.dzviewscene.product.shelf.activity.deal;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.sankuai.athena.viewscene.framework.InterceptorContext;
import com.sankuai.athena.viewscene.framework.annotation.ActivityInterceptor;
import com.sankuai.athena.viewscene.framework.core.IActivityInterceptor;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.productshelf.vu.enums.RequestTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@ActivityInterceptor(name = "团单货架活动打点拦截器", code = "activity_deal_shelf_interceptor", activity = DealShelfActivity.CODE)
public class DealShelfActivityInterceptor implements IActivityInterceptor<DzShelfResponseVO> {

    private static final String REFRESH_PREFIX = "refresh.";

    private static final String FIRST_LOAD = "DEAL_SHELF_FIRST_LOAD_APP";

    @Override
    public void beforeExecute(InterceptorContext<DzShelfResponseVO> interceptorContext) {

    }

    @Override
    public void complete(InterceptorContext<DzShelfResponseVO> interceptorContext, DzShelfResponseVO result) {
        try {
            Cat.logMetricForCount(interceptorContext.getActivityCode(), buildMetricTags(interceptorContext, result));
            catWithCategory(interceptorContext, result);
            logMetricForRefreshCount(interceptorContext, result);
        } catch (Exception e) {/*静默*/}
    }

    private void catWithCategory(InterceptorContext<DzShelfResponseVO> interceptorContext, DzShelfResponseVO result){
        String requestType = ParamsUtil.getStringSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.requestType);
        long startTime = ParamsUtil.getLongSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.startTime);
        ShopM shopM = (ShopM)interceptorContext.getParameters().get(ShelfActivityConstants.Ctx.ctxShop);
        int userAgent = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
        if(startTime <= 0 || shopM == null) {
            return;
        }
        //记录app内首屏请求耗时
        if(RequestTypeEnum.API_DEAL_SHELF.getType().equals(requestType) && PlatformUtil.isApp(userAgent) ){
            Cat.newCompletedTransactionWithDuration(FIRST_LOAD, String.valueOf(shopM.getShopType()), System.currentTimeMillis()- startTime);
        }
    }

    private void logMetricForRefreshCount(InterceptorContext<DzShelfResponseVO> interceptorContext, DzShelfResponseVO result) {
        String refreshTag = ParamsUtil.getStringSafely(interceptorContext.getParameters(), ShelfActivityConstants.Params.refreshTag);
        if (StringUtils.isBlank(refreshTag)) {
            return;
        }
        Cat.logMetricForCount(REFRESH_PREFIX + interceptorContext.getActivityCode(), buildRefreshMetricTags(interceptorContext, result, refreshTag));
    }

    private Map<String, String> buildMetricTags(InterceptorContext<DzShelfResponseVO> interceptorContext, DzShelfResponseVO dzShelfResponseVO) {
        DzShelfResponseVO finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : dzShelfResponseVO;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("hasFilters", Boolean.toString(ModelUtils.hasFilters(finalResult)));
            put("hasProducts", Boolean.toString(!ModelUtils.hasNoProducts(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if(StringUtils.isNotEmpty(clientTypeMsg)){
                put("clientType", clientTypeMsg);
            }
        }};
    }

    private Map<String, String> buildRefreshMetricTags(InterceptorContext<DzShelfResponseVO> interceptorContext, DzShelfResponseVO dzShelfResponseVO, String refreshTag) {
        DzShelfResponseVO finalResult = interceptorContext.getDefaultResult() != null ? interceptorContext.getDefaultResult() : dzShelfResponseVO;
        return new HashMap<String, String>() {{
            put("sceneCode", interceptorContext.getSceneCode());
            put("refreshTag", refreshTag);
            put("hasProducts", Boolean.toString(!ModelUtils.hasNoProducts(finalResult)));
            int clientType = ParamsUtil.getIntSafely(interceptorContext.getParameters(), PmfConstants.Params.userAgent);
            String clientTypeMsg = VCClientTypeEnum.getClientTypeMsg(clientType);
            if(StringUtils.isNotEmpty(clientTypeMsg)){
                put("clientType", clientTypeMsg);
            }
        }};
    }
}
