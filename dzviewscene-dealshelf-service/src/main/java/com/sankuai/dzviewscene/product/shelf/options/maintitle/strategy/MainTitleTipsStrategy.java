/*
 * Create Author  : liyanmin
 * Create Date    : 2024-04-22
 * Project        :
 * File Name      : ButtonCarouselMsgStrategy.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.shelf.options.maintitle.strategy;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonCarouselMsgVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.maintitle.vp.DealMainTitleVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;

import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-04-22
 * @since dzviewscene-dealshelf-home 1.0
 */
public interface MainTitleTipsStrategy {

    String getName();
    
    List<IconRichLabelVO> build(ActivityCxt ctx, TipsStrategyConfig tipsStrategyConfig,List<String> hasDataTipsStrategies);


}
