package com.sankuai.dzviewscene.product.dealstruct.options;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dp.arts.utils.recycler.Recycler;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.vpoints.SkuItemAttrVP;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuAttrValueModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/27 10:16 下午
 */
@VPointOption(name = "团购详情货attr列表配置变化点", description = "团购详情货attr列表配置变化点", code = ConfiguredSkuItemAttrItemsVPO.CODE, isDefault = false)
public class ConfiguredSkuItemAttrItemsVPO extends SkuItemAttrVP<ConfiguredSkuItemAttrItemsVPO.Config> {

    public static final String CODE = "ConfiguredSkuItemAttrItemsVPO";

    @Override
    public List<SkuAttrModel> compute(ActivityCxt context, Param param, Config config) {
        SkuItemDto skuItemDto = param.getSkuItemDto();
        List<ProductSkuCategoryModel> productCategories = param.getProductCategories();
        if (skuItemDto == null || CollectionUtils.isEmpty(skuItemDto.getAttrItems()) || config == null) {
            return null;
        }
        String skuCategory = getSkuCategory(productCategories, skuItemDto.getProductCategory());
        List<SkuAttrsConfigModel> skuAttrModels = getSkuAttrModels(skuCategory, config);
        return getSkuAttrModel(skuAttrModels, skuItemDto.getAttrItems());
    }

    private List<SkuAttrModel> getSkuAttrModel(List<SkuAttrsConfigModel> skuAttrModels, List<SkuAttrItemDto> attrItems) {
        if (CollectionUtils.isEmpty(skuAttrModels) || CollectionUtils.isEmpty(attrItems)) {
            return null;
        }
        Map<SkuAttrModel, Integer> skuAttr2PriorityMap = attrItems.stream().collect(HashMap::new, (map, attr) -> {
            SkuAttrsConfigModel skuAttrsConfigModel = getAttrConfigModel(attr, skuAttrModels);
            int attrPriority = getAttrPriority(skuAttrsConfigModel);
            String attrTitle = getAttrTitle(skuAttrsConfigModel);
            String attrValue = getAttrValue(attr, skuAttrsConfigModel);
            SkuAttrModel skuAttrModel = buildSkuAttrModel(attrTitle, attrValue);
            if (skuAttrModel == null) {
                return;
            }
            map.put(skuAttrModel, attrPriority);
        }, HashMap::putAll);
        if (MapUtils.isEmpty(skuAttr2PriorityMap)) {
            return null;
        }
        return skuAttr2PriorityMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private SkuAttrModel buildSkuAttrModel(String attrTitle, String attrValue) {
        if (StringUtils.isEmpty(attrTitle) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        SkuAttrModel skuAttrModel = new SkuAttrModel();
        skuAttrModel.setName(attrTitle);
        skuAttrModel.setValue(buildSkuAttrValueModel(attrValue));
        return skuAttrModel;
    }

    private SkuAttrValueModel buildSkuAttrValueModel(String attrValue) {
        if (StringUtils.isEmpty(attrValue)) {
            return null;
        }
        SkuAttrValueModel skuAttrValueModel = new SkuAttrValueModel();
        skuAttrValueModel.setDoc(attrValue);
        return skuAttrValueModel;
    }

    private String getAttrValue(SkuAttrItemDto attrDto, SkuAttrsConfigModel skuAttrsConfigModel) {
        if (attrDto == null || skuAttrsConfigModel == null) {
            return null;
        }
        if (MapUtils.isEmpty(skuAttrsConfigModel.getAttrValue2AttrShowValueMap()) || !skuAttrsConfigModel.getAttrValue2AttrShowValueMap().containsKey(attrDto.getAttrValue())) {
            return attrDto.getAttrValue();
        }
        return skuAttrsConfigModel.getAttrValue2AttrShowValueMap().get(attrDto.getAttrValue());
    }

    private SkuAttrsConfigModel getAttrConfigModel(SkuAttrItemDto attrDto, List<SkuAttrsConfigModel> skuAttrModels) {
        if (CollectionUtils.isEmpty(skuAttrModels) || attrDto == null || StringUtils.isEmpty(attrDto.getAttrName())) {
            return null;
        }
        return skuAttrModels.stream().filter(attrModel -> attrDto.getAttrName().equals(attrModel.getAttrName())).findFirst().orElse(null);
    }

    private String getAttrTitle(SkuAttrsConfigModel skuAttrsConfigModel) {
        if (skuAttrsConfigModel == null) {
            return null;
        }
        return skuAttrsConfigModel.getAttrTitle();
    }

    private int getAttrPriority(SkuAttrsConfigModel skuAttrsConfigModel) {
        if (skuAttrsConfigModel == null) {
            return Integer.MAX_VALUE;
        }
        return skuAttrsConfigModel.getPriority();
    }

    private List<SkuAttrsConfigModel> getSkuAttrModels(String category, Config config) {
        if(config == null || StringUtils.isEmpty(category)) {
            return null;
        }
        List<SkuCategoryAttrsModel> skuCategoryAttrsModels= config.getSkuCategoryAttrsModels();
        if (CollectionUtils.isEmpty(skuCategoryAttrsModels)) {
            return config.getDefaultSkuAttrsConfigModels();
        }
        SkuCategoryAttrsModel skuCategoryAttrsModel = skuCategoryAttrsModels.stream().filter(model -> category.equals(model.getCategory())).findFirst().orElse(null);
        if (skuCategoryAttrsModel != null) {
            return skuCategoryAttrsModel.getAttrsConfigModels();
        }
        return config.getDefaultSkuAttrsConfigModels();
    }

    private String getSkuCategory(List<ProductSkuCategoryModel> productCategories, long skuCategoryId) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel categoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() == skuCategoryId).findFirst().orElse(null);
        if (categoryModel == null) {
            return null;
        }
        return categoryModel.getCnName();
    }

    @Data
    @VPointCfg
    public static class Config {
        private List<SkuCategoryAttrsModel> skuCategoryAttrsModels;
        private List<SkuAttrsConfigModel> defaultSkuAttrsConfigModels;
    }

    @Data
    private static class SkuCategoryAttrsModel {
        //优先级
        private int priority;
        //货类别
        private String category;
        //该类别下所展示的属性配置信息
        private List<SkuAttrsConfigModel> attrsConfigModels;
    }

    @Data
    private static class SkuAttrsConfigModel {
        //优先级
        private int priority;
        //该属性展示的标题
        private String attrTitle;
        //所取属性的属性名
        private String attrName;
        //属性值和展示值映射
        private Map<String, String> attrValue2AttrShowValueMap;
    }
}
