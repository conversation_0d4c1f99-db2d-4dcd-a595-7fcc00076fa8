package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/11/26 3:12 下午
 */
@Deprecated
@VPoint(name = "团购详情sku货列表组名变化点", description = "团购详情sku货列表组名变化点，支持配置",code = DealDetailSkusGroupTitleVP.CODE, ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class DealDetailSkusGroupTitleVP<T> extends PmfVPoint<String, DealDetailSkusGroupTitleVP.Param, T> {

    public static final String CODE = "DealDetailSkusGroupTitleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private int totalNum;
        private int optionalNum;
        private boolean isMustGroup;
    }

}
