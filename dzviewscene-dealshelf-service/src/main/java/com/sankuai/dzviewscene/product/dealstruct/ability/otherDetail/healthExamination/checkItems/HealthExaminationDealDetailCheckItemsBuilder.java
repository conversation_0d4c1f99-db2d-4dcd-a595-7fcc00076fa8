package com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems;

import com.dianping.deal.struct.query.api.entity.dto.*;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.DealAttrVOListModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.otherDetail.healthExamination.checkItems.vpoints.HealthExaminationCheckItemListVP;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailConstants;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.healthExamination.*;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/17 2:30 下午
 */
@Ability(code = HealthExaminationDealDetailCheckItemsBuilder.CODE,
        name = "体检团购详情模块检查项列表构造能力",
        description = "体检团购详情模块检查项列表构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealAttrVOListModuleBuilder.CODE
        }
)
public class HealthExaminationDealDetailCheckItemsBuilder extends PmfAbility<DealModuleDetailVO, HealthExaminationDealDetailCheckItemsParam, HealthExaminationDealDetailCheckItemsCfg> {

    public static final String CODE = "healthExaminationDealDetailCheckItemsBuilder";

    //增值服务sku类别
    private static final long VALUE_ADDED_SERVICE_PRODUCT_CATE = 4070L;

    private String SERVICE_TYPE_ATTR_NAME = "service_type";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<DealModuleDetailVO> build(ActivityCxt ctx, HealthExaminationDealDetailCheckItemsParam param, HealthExaminationDealDetailCheckItemsCfg cfg) {
        //1.获取构造团详所需的团单基本信息
        DealDetailInfoModel dealDetailBasicInfo = getDealDetailBasicInfo(ctx);
        if (dealDetailBasicInfo == null) {
            CompletableFuture.completedFuture(null);
        }
        //2.获取含有落地页数据的检查项筛选列表（用在老的前端包上展示）
        CompletableFuture<List<FilterItemListVO>> filterItemListVOsFuture = getCheckItemFilterListWithTertiarySignificance(ctx, dealDetailBasicInfo, cfg);
        //3.获取不含落地页数据的检查项列表（用在新的前端包上展示）
        CompletableFuture<HealthExaminationItemsGroupVO> checkItemListGroupFuture = getCheckItemListGroup(ctx, dealDetailBasicInfo);
        //4.组装结果
        return filterItemListVOsFuture.thenCombine(checkItemListGroupFuture, (filterItemListVOs, checkItemListGroup) -> buildDealModuleDetailVO(filterItemListVOs, checkItemListGroup, dealDetailBasicInfo.getDesc(), dealDetailBasicInfo.getDealAttrs()));
    }

    /**
     * 获取不含检查意义的检查项列表
     *@param
     *@return
     */
    private CompletableFuture<HealthExaminationItemsGroupVO> getCheckItemListGroup(ActivityCxt ctx, DealDetailInfoModel dealDetailBasicInfo) {
        HealthExaminationCheckItemListVP<?> healthExaminationCheckItemListVP = findVPoint(ctx, HealthExaminationCheckItemListVP.CODE);
        HealthExaminationItemsGroupVO healthExaminationItemsGroupVO = healthExaminationCheckItemListVP.execute(ctx, HealthExaminationCheckItemListVP.Param.builder().dealDetailBasicInfo(dealDetailBasicInfo).build());
        return CompletableFuture.completedFuture(healthExaminationItemsGroupVO);
    }

    /**
     * 构造含有检查意义的检查项筛选列表
     *@param
     *@return
     */
    private CompletableFuture<List<FilterItemListVO>> getCheckItemFilterListWithTertiarySignificance(ActivityCxt ctx, DealDetailInfoModel dealDetailBasicInfo, HealthExaminationDealDetailCheckItemsCfg cfg) {
        //1.获取构造检查意义所需的海马配置信息
        CompletableFuture<HaimaResponse> future = getCheckItemAndSignificanceHaimaResponse(ctx);
        //2.构造检查项筛选列表
        return future.thenApply(checkItemAndSignificance -> getFilterItemListVOs(dealDetailBasicInfo, cfg, checkItemAndSignificance, ParamsUtil.getIntSafely(ctx, DealDetailConstants.Params.platform)));
    }

    /**
     * 获取海马配置
     *@param
     *@return
     */
    private CompletableFuture<HaimaResponse> getCheckItemAndSignificanceHaimaResponse(ActivityCxt ctx) {
        HaimaRequest haimaRequest = buildHaiMaRequest(ctx);
        return compositeAtomService.getHaiMaResponse(haimaRequest);
    }

    /**
     * 根据团单基本信息DealDetailInfoModel获取FilterItemListVO列表
     *@param
     *@return
     */
    private List<FilterItemListVO> getFilterItemListVOs(DealDetailInfoModel dealDetailBasicInfo, HealthExaminationDealDetailCheckItemsCfg cfg, HaimaResponse checkItemAndSignificanceHaimaResponse, int platform) {
        if (dealDetailBasicInfo == null) {
            return null;
        }
        List<FilterItemListVO> filterItemListVOS = new ArrayList<>();
        //添加全部可享FilterTabModel
        FilterItemListVO mustFilterItemListVO = getMustSkusFilterItemListVO(dealDetailBasicInfo, cfg, checkItemAndSignificanceHaimaResponse, platform);
        filterItemListVOS.add(mustFilterItemListVO);
        //添加部分可选FilterTabModel
        FilterItemListVO optionalFilterItemListVO = getOptionalSkusFilterItemListVO(dealDetailBasicInfo, cfg, checkItemAndSignificanceHaimaResponse, mustFilterItemListVO == null, platform, dealDetailBasicInfo.getDealId());
        filterItemListVOS.add(optionalFilterItemListVO);
        //添加增值服务ilterTabModel
        FilterItemListVO valueAddedFilterItemListVO = getValueAddedSkusFilterItemListVO(dealDetailBasicInfo, cfg, mustFilterItemListVO == null && optionalFilterItemListVO == null, platform, dealDetailBasicInfo.getDealId());
        filterItemListVOS.add(valueAddedFilterItemListVO);
        //过滤null值
        List<FilterItemListVO> resultFilterItemListVOs = filterItemListVOS.stream().filter(model -> model != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultFilterItemListVOs)) {
            return null;
        }
        return resultFilterItemListVOs;
    }

    /**
     * 获取检查意义和对应的检查项列表map
     *@param secondaryCheckItem 二级检查项
     *@param checkItems 三级检查项列表
     *@param haimaResponse 检查项和检查意义海马配置
     *@return 检查意义和对应的检查项列表map
     */
    private Map<String, List<String>> getCheckItemSignificance2CheckItemsMap(String secondaryCheckItem, List<String> checkItems, HaimaResponse haimaResponse) {
        if(haimaResponse == null || !haimaResponse.isSuccess() || CollectionUtils.isEmpty(haimaResponse.getData()) || CollectionUtils.isEmpty(checkItems)) {
            return new HashMap<>();
        }
        return getCheckItemSignificance2CheckItemsMap(haimaResponse.getData(), secondaryCheckItem, checkItems);
    }

    private Map<String, List<String>> getCheckItemSignificance2CheckItemsMap(List<HaimaConfig> configs, String secondaryCheckItem, List<String> checkItems) {
        if (CollectionUtils.isEmpty(configs) || StringUtils.isEmpty(secondaryCheckItem) || CollectionUtils.isEmpty(checkItems)) {
            return new HashMap<>();
        }
        //根据二级检查项获取对应的海马配置
        List<HaimaContent> haimaContents = getCheckItemHaimaContentList(configs, secondaryCheckItem);
        //获取三级检查项和检查意义map配置
        Map<String, List<String>> checkItemSignificance2CheckItemListMap = getCheckItemSignificance2CheckItemListMap(haimaContents);
        //对checkItems按照海马配置的顺序进行排序
        checkItems = checkItems.stream().sorted(Comparator.comparingInt(o -> getPriorityOfCheckItem(haimaContents, o))).collect(Collectors.toList());
        //获取检查意义和对应的检查项列表map
        return getCheckItemSignificance2CheckItemsMap(checkItemSignificance2CheckItemListMap, checkItems);
    }

    private Map<String, List<String>> getCheckItemSignificance2CheckItemsMap(Map<String, List<String>> checkItemSignificance2CheckItemListMapConfig, List<String> checkItems) {
        if (checkItemSignificance2CheckItemListMapConfig == null || CollectionUtils.isEmpty(checkItems)) {
            return new HashMap<>();
        }
        return checkItems.stream().collect(LinkedHashMap::new, (map, checkItem) -> {
            Map.Entry<String, List<String>> entry = checkItemSignificance2CheckItemListMapConfig.entrySet().stream().filter(e -> e.getValue().contains(checkItem)).findFirst().orElse(null);
            String significance = entry == null ? StringUtils.EMPTY : entry.getKey();
            if (!map.containsKey(significance)) {
                map.put(significance, Lists.newArrayList(checkItem));
                return;
            }
            List<String> checkItemList = map.get(significance);
            if (CollectionUtils.isEmpty(checkItemList)) {
                map.put(significance, Lists.newArrayList(checkItem));
                return;
            }
            checkItemList.add(checkItem);
            map.put(significance, checkItemList);
        }, LinkedHashMap::putAll);
    }

    /**
     * 根据二级检查项名称获取对应配置的三级检查项意义到三级检查项列表map
     *@param
     *@return
     */
    private Map<String, List<String>> getCheckItemSignificance2CheckItemListMap(List<HaimaContent> haimaContents) {
        return haimaContents.stream().collect(HashMap::new, (map, content) -> {
            //获取检查意义
            String checkSignificance = content.getContentString("checkSignificance");
            //获取检查项列表
            List<String> checkItemNames = getCheckItemNamesFronHaimaContent(content);
            if (StringUtils.isEmpty(checkSignificance) || CollectionUtils.isEmpty(checkItemNames)) {
                return;
            }
            map.put(checkSignificance, checkItemNames);
        }, HashMap::putAll);
    }

    private List<HaimaContent> getCheckItemHaimaContentList(List<HaimaConfig> configs, String secondaryCheckItem) {
        if (CollectionUtils.isEmpty(configs)) {
            return new ArrayList<>();
        }
        HaimaConfig haimaConfig = configs.stream()
                .filter(config -> secondaryCheckItem.equals(config.getExtString("checkItem")))
                .findFirst().orElse(null);
        if (haimaConfig == null || haimaConfig.getContents() == null) {
            return new ArrayList<>();
        }
        return haimaConfig.getContents();
    }

    /**
     * 获取三级检查项优先级
     *@param
     *@return
     */
    private int getPriorityOfCheckItem(List<HaimaContent> contents, String checkItemName) {
        if (CollectionUtils.isEmpty(contents) || StringUtils.isEmpty(checkItemName)) {
            return Integer.MAX_VALUE;
        }
        List<String> checkItemNameList = contents.stream().flatMap(content -> getCheckItemNamesFronHaimaContent(content).stream()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkItemNameList) || !checkItemNameList.contains(checkItemName)) {
            return Integer.MAX_VALUE;
        }
        return checkItemNameList.indexOf(checkItemName);
    }

    /**
     * 获取二级检查项下三级检查项列表
     *@param
     *@return
     */
    private List<String> getCheckItemNamesFronHaimaContent(HaimaContent haimaContent) {
        if (haimaContent == null || StringUtils.isEmpty(haimaContent.getContentString("childCheckItem"))) {
            return new ArrayList<>();
        }
        List<String> checkItemNames = JsonCodec.converseList(haimaContent.getContentString("childCheckItem"), String.class);
        if (checkItemNames == null) {
            return new ArrayList<>();
        }
        return checkItemNames;
    }

    private DealDetailInfoModel getDealDetailBasicInfo(ActivityCxt activityCxt) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        return CollectUtils.firstValue(dealDetailInfoModels);
    }

    /**
     * 从配置上获取tab name对应的tab id
     *@param
     *@return
     */
    private int getTabIdByTabName(HealthExaminationDealDetailCheckItemsCfg healthExaminationDealDetailCheckItemsCfg, String tabName) {
        if (healthExaminationDealDetailCheckItemsCfg == null || MapUtils.isEmpty(healthExaminationDealDetailCheckItemsCfg.getTabName2TabIdMap()) || StringUtils.isEmpty(tabName)) {
            return 0;
        }
        Integer tabId = healthExaminationDealDetailCheckItemsCfg.getTabName2TabIdMap().get(tabName);
        if (tabId == null) {
            return 0;
        }
        return tabId;
    }

    private DealModuleDetailVO buildDealModuleDetailVO(List<FilterItemListVO> resultFilterItemListVOs, HealthExaminationItemsGroupVO healthExaminationItemsGroupVO, String desc, List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(resultFilterItemListVOs) && healthExaminationItemsGroupVO == null) {
            return null;
        }
        HealthExaminationDealDetailVO healthExaminationDealDetailVO = new HealthExaminationDealDetailVO();
        //添加落地页筛选检查项列表
        healthExaminationDealDetailVO.setFilterItemListVOs(resultFilterItemListVOs);
        //添加检查项列表
        healthExaminationDealDetailVO.setExaminationItemsGroup(healthExaminationItemsGroupVO);
        //添加商家描述信息
        healthExaminationDealDetailVO.setDesc(desc);
        //添加团单属性信息
        healthExaminationDealDetailVO.setDealAttrs(buildDealAttributes(dealAttrs));
        //构造结果
        DealModuleDetailVO dealModuleDetailVO = new DealModuleDetailVO();
        dealModuleDetailVO.setHealthExaminationDealDetailVO(healthExaminationDealDetailVO);
        return dealModuleDetailVO;
    }

    /**
     * 构造团单属性模块
     *@param
     *@return
     */
    private List<DealDetailStructAttrModuleVO> buildDealAttributes(List<AttrM> dealAttrs) {
        if (CollectionUtils.isEmpty(dealAttrs)) {
            return null;
        }
        List<DealDetailStructAttrModuleVO> attrModuleVOS = new ArrayList<>();
        //添加适用人群
        addSuitablePeople(attrModuleVOS, dealAttrs);
        //添加服务亮点
        addServiceHighlights(attrModuleVOS, dealAttrs);
        if (CollectionUtils.isEmpty(attrModuleVOS)) {
            return null;
        }
        return attrModuleVOS;
    }

    /**
     * 添加服务亮点属性
     *@param
     *@return
     */
    private void addServiceHighlights(List<DealDetailStructAttrModuleVO> attrModuleVOS, List<AttrM> dealAttrs) {
        List<String> highlights = new ArrayList<>();
        //添加出报告时效
        addReportTime(highlights, dealAttrs);
        //添加亮点属性
        addHighlights(highlights, dealAttrs);
        if (CollectionUtils.isEmpty(highlights)) {
            return;
        }
        attrModuleVOS.add(buildDealDetailStructAttrModuleVO("服务亮点", StringUtils.join(highlights, "、")));
    }

    /**
     * 添加出报告时效
     *@param
     *@return
     */
    private void addReportTime(List<String> highlights, List<AttrM> dealAttrs) {
        if (!ObjectUtils.equals(DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "service_type"), "入职体检")) {
            return;
        }
        double reportDayNum = getReportDayNum(dealAttrs);
        if (reportDayNum < 0) {
            return;
        }
        if (reportDayNum < 1) {
            highlights.add("当日出报告");
            return;
        }
        if (reportDayNum < 2) {
            highlights.add("次日出报告");
            return;
        }
        if (reportDayNum < 3) {
            highlights.add("3日内出报告");
            return;
        }
        if (reportDayNum < 5) {
            highlights.add("3-5个工作日出报告");
            return;
        }
        if (reportDayNum < 7) {
            highlights.add("5-7个工作日出报告");
            return;
        }
        if (reportDayNum < 7.000001) {
            highlights.add("一周出报告");
            return;
        }
    }

    /**
     * 添加亮点属性
     *@param
     *@return
     */
    private void addHighlights(List<String> highlights, List<AttrM> dealAttrs) {
        List<String> highlightList = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "px_additional_service");
        if (CollectionUtils.isEmpty(highlightList)) {
            return;
        }
        highlights.addAll(highlightList);
    }

    /**
     * 获取出报告所需的天数
     *@param
     *@return
     */
    private double getReportDayNum(List<AttrM> dealAttrs) {
        String reportTimeAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "physical_examination_get_result_time");
        if (StringUtils.isEmpty(reportTimeAttrValue)) {
            return -1;
        }
        reportTimeAttrValue = reportTimeAttrValue.replaceAll("体检报告可于体检结束后", StringUtils.EMPTY).replaceAll("后自取", StringUtils.EMPTY);
        if (reportTimeAttrValue.contains("周")) {
            return  NumberUtils.objToDouble(reportTimeAttrValue.replaceAll("周", StringUtils.EMPTY)) * 7;
        }
        if (reportTimeAttrValue.contains("个工作日")) {
            return  NumberUtils.objToDouble(reportTimeAttrValue.replaceAll("个工作日", StringUtils.EMPTY));
        }
        return -1;
    }

    /**
     * 添加适用人群属性
     *@param
     *@return
     */
    private void addSuitablePeople(List<DealDetailStructAttrModuleVO> attrModuleVOS, List<AttrM> dealAttrs) {
        List<String> suitablePeople = new ArrayList<>();
        addSuitableAge(suitablePeople, dealAttrs);
        addSuitableGender(suitablePeople, dealAttrs);
        addSuitableCrowd(suitablePeople, dealAttrs);
        if (CollectionUtils.isEmpty(suitablePeople)) {
            return;
        }
        attrModuleVOS.add(buildDealDetailStructAttrModuleVO("适用人群", StringUtils.join(suitablePeople, "、")));
    }

    private void addSuitableCrowd(List<String> suitablePeople, List<AttrM> dealAttrs) {
        List<String> suitableCrowd = DealDetailUtils.getAttrValueByAttrName(dealAttrs, "suitable_crowd");
        if (CollectionUtils.isEmpty(suitableCrowd)) {
            return;
        }
        suitableCrowd = suitableCrowd.stream()
                .map(crowd -> StringUtils.isNotEmpty(crowd) ? crowd + "人群" : null)
                .filter(crowd -> StringUtils.isNotEmpty(crowd))
                .collect(Collectors.toList());
        suitablePeople.addAll(suitableCrowd);
    }

    private void addSuitableGender(List<String> suitablePeople, List<AttrM> dealAttrs) {
        String suitableGenderAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "checkup_sex");
        String suitableGender = ObjectUtils.equals(suitableGenderAttrValue, "通用") ? "男女通用" : suitableGenderAttrValue;
        if (StringUtils.isEmpty(suitableGender)) {
            return;
        }
        String maritalStatus = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "marital_status");
        if (StringUtils.isNotEmpty(maritalStatus)) {
            suitableGender = suitableGender + maritalStatus;
        }
        suitablePeople.add(suitableGender);
    }

    private void addSuitableAge(List<String> suitablePeople, List<AttrM> dealAttrs) {
        String suitableAgeAttrValue = DealDetailUtils.getAttrSingleValueByAttrName(dealAttrs, "px_suitable_age");
        if (StringUtils.isEmpty(suitableAgeAttrValue)) {
            return;
        }
        String suitableAge = Lists.newArrayList("青年", "中年", "老年").stream()
                .filter(age -> suitableAgeAttrValue.contains(age))
                .map(age -> age.replaceAll("年", StringUtils.EMPTY))
                .reduce(String::concat)
                .map(age -> "青中老".equals(age) ? "全年龄（18岁以上）" : age + "年")
                .orElse(null);
        if (StringUtils.isEmpty(suitableAge)) {
            return;
        }
        suitablePeople.add(suitableAge);
    }

    private DealDetailStructAttrModuleVO buildDealDetailStructAttrModuleVO(String attrName, String attrValue) {
        if (StringUtils.isEmpty(attrName) || StringUtils.isEmpty(attrValue)) {
            return null;
        }
        DealDetailStructAttrModuleVO dealDetailStructAttrModuleVO = new DealDetailStructAttrModuleVO();
        dealDetailStructAttrModuleVO.setAttrName(attrName);
        dealDetailStructAttrModuleVO.setAttrValues(Lists.newArrayList(attrValue));
        return dealDetailStructAttrModuleVO;
    }

    /**
     * 获取全部可享FilterItemListVO
     *@param
     *@return
     */
    private FilterItemListVO getMustSkusFilterItemListVO(DealDetailInfoModel dealDetailBasicInfo, HealthExaminationDealDetailCheckItemsCfg cfg, HaimaResponse haimaResponse, int platform) {
        List<FilterItemListVO> mustChildFilterItemListVO = getChildFilterItemListVOFromMustSkus(dealDetailBasicInfo, cfg, haimaResponse, platform);
        FilterItemListVO filterItemListVO = buildFilterItemListVO(true, getTabIdByTabName(cfg, "全部可享"), "全部可享", mustChildFilterItemListVO, null, platform, dealDetailBasicInfo.getDealId(), cfg);
        setReadMore(filterItemListVO);
        return filterItemListVO;
    }

    /**
     * 将三层筛选树中所有父筛选的查看更多跳链设置为叶子筛选的查看更多跳链
     *@param
     *@return
     */
    private void setReadMore(FilterItemListVO filterItemListVO) {
        if (filterItemListVO == null || CollectionUtils.isEmpty(filterItemListVO.getChildrenFilters())) {
            return;
        }
        //将二级筛选查看更多设置成相应的三级子筛选的查看更多（优先取被选中的三级子筛选，没有被选中的子筛选时取第一个筛选）
        for (FilterItemListVO childFilterItemListVO : filterItemListVO.getChildrenFilters()) {
            if (CollectionUtils.isEmpty(childFilterItemListVO.getChildrenFilters())) {
                continue;
            }
            FilterItemListVO seletedGrandchildFilter = childFilterItemListVO.getChildrenFilters().stream().filter(filter -> filter != null && filter.getIsSelected()).findFirst().orElse(null);
            if (seletedGrandchildFilter == null) {
                seletedGrandchildFilter = CollectUtils.firstValue(childFilterItemListVO.getChildrenFilters());
            }
            childFilterItemListVO.setMore(seletedGrandchildFilter.getMore());
        }
        //将一级筛选查看更多设置成二级子筛选的查看更多（优先取被选中的二级子筛选，没有被选中的子筛选时取第一个筛选）
        FilterItemListVO seletedChildFilter = filterItemListVO.getChildrenFilters().stream().filter(filter -> filter.getIsSelected()).findFirst().orElse(null);
        if (seletedChildFilter == null) {
            seletedChildFilter = CollectUtils.firstValue(filterItemListVO.getChildrenFilters());
        }
        filterItemListVO.setMore(seletedChildFilter.getMore());
    }

    /**
     * 获取增值服务FilterItemListVO
     *@param
     *@return
     */
    public FilterItemListVO getValueAddedSkusFilterItemListVO(DealDetailInfoModel dealDetailBasicInfo, HealthExaminationDealDetailCheckItemsCfg cfg, boolean isSelected, int platform, int dealId) {
        List<SkuItemDto> valueAddedSkuItemList = extractValueAddedSkuList(dealDetailBasicInfo);
        //将sku列表按照sku类别进行分组
        Map<String, List<String>> valueAddedSkuGrandchildTabName2CheckItemNamesMap = getGrandchildTabName2CheckItemNamesMap(valueAddedSkuItemList);
        HealthExaminationItemModelVO healthExaminationItemModelVO = getValueAddedHealthExaminationItemModelVO(valueAddedSkuGrandchildTabName2CheckItemNamesMap);
        if (healthExaminationItemModelVO == null) {
            return null;
        }
        List<HealthExaminationItemGroupModelVO> healthExaminationItemGroupModelVOS = Lists.newArrayList(buildHealthExaminationItemGroupModelVOList("增值服务", Lists.newArrayList(healthExaminationItemModelVO)));
        return buildFilterItemListVO(isSelected, getTabIdByTabName(cfg, "增值服务"), "增值服务", null, healthExaminationItemGroupModelVOS, platform, dealId, cfg);
    }

    private HealthExaminationItemGroupModelVO buildHealthExaminationItemGroupModelVOList(String desc, List<HealthExaminationItemModelVO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        HealthExaminationItemGroupModelVO healthExaminationItemGroupModelVO = new HealthExaminationItemGroupModelVO();
        healthExaminationItemGroupModelVO.setItemList(itemList);
        healthExaminationItemGroupModelVO.setDesc(desc);
        return healthExaminationItemGroupModelVO;
    }

    /**
     * 构造增值服务HealthExaminationItemModelVO
     *@param
     *@return
     */
    public HealthExaminationItemModelVO getValueAddedHealthExaminationItemModelVO(Map<String, List<String>> mustSkuGrandchildTabName2CheckItemNamesMap) {
        if (MapUtils.isEmpty(mustSkuGrandchildTabName2CheckItemNamesMap)) {
            return null;
        }
        HeaderVO headerVO = buildHeaderVO("服务类型", "内容");
        List<HealthExaminationSubitemVO> healthExaminationSubitemVOS = mustSkuGrandchildTabName2CheckItemNamesMap.entrySet()
                .stream()
                .map(entry -> buildValueAddedServiceHealthExaminationSubitemVO(entry.getValue(), entry.getKey()))
                .filter(vo -> vo != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(healthExaminationSubitemVOS)) {
            return null;
        }
        return buildHealthExaminationItemModelVO(headerVO, healthExaminationSubitemVOS, null);
    }

    /**
     * 构造增值服务HealthExaminationSubitemVO
     *@param
     *@return
     */
    private HealthExaminationSubitemVO buildValueAddedServiceHealthExaminationSubitemVO(List<String> checkItems, String tabName) {
        if (CollectionUtils.isEmpty(checkItems)) {
            return null;
        }
        String itemName = String.format("%s（%s项）", tabName, checkItems.size());
        String itemSignificance =  StringUtils.join(checkItems, "、");
        return buildHealthExaminationSubitemVO(itemName, itemSignificance);
    }

    private HealthExaminationItemModelVO buildHealthExaminationItemModelVO(HeaderVO headerVO, List<HealthExaminationSubitemVO> healthExaminationSubitemVOS, String itemName) {
        HealthExaminationItemModelVO healthExaminationItemModelVO = new HealthExaminationItemModelVO();
        healthExaminationItemModelVO.setHeader(headerVO);
        healthExaminationItemModelVO.setSubitemList(healthExaminationSubitemVOS);
        healthExaminationItemModelVO.setItemName(itemName);
        return healthExaminationItemModelVO;
    }

    /**
     * 构造HealthExaminationSubitemVO
     *@param
     *@return
     */
    private HealthExaminationSubitemVO buildHealthExaminationSubitemVO(String itemName, String itemSignificance) {
        HealthExaminationSubitemVO healthExaminationSubitemVO = new HealthExaminationSubitemVO();
        healthExaminationSubitemVO.setItemName(itemName);
        healthExaminationSubitemVO.setItemSignificance(itemSignificance);
        return healthExaminationSubitemVO;
    }

    /**
     * 构造表头
     *@param
     *@return
     */
    private HeaderVO buildHeaderVO(String headerItem1, String headerItem2) {
        HeaderVO headerVO = new HeaderVO();
        List<HeaderItemVO> headerItemVOS = new ArrayList<HeaderItemVO>() {{
            HeaderItemVO headerItemVO1 = new HeaderItemVO();
            headerItemVO1.setKey("itemName");
            headerItemVO1.setData(headerItem1);
            add(headerItemVO1);
            HeaderItemVO headerItemVO2 = new HeaderItemVO();
            headerItemVO2.setKey("itemSignificance");
            headerItemVO2.setData(headerItem2);
            add(headerItemVO2);
        }};
        headerVO.setHeaderItems(headerItemVOS);
        return headerVO;
    }

    /**
     * 获取部分可选FilterItemListVO
     *@param
     *@return
     */
    private FilterItemListVO getOptionalSkusFilterItemListVO(DealDetailInfoModel dealDetailBasicInfo, HealthExaminationDealDetailCheckItemsCfg cfg, HaimaResponse haimaResponse, boolean isSelected, int platform, int dealId) {
        List<OptionalSkuItemsGroupDto> optionalSkuItemsGroupDtos = extractOptionalSkuItemsGroupDto(dealDetailBasicInfo);
        if (CollectionUtils.isEmpty(optionalSkuItemsGroupDtos)) {
            return null;
        }
        List<HealthExaminationItemGroupModelVO> itemGroupList = optionalSkuItemsGroupDtos
                .stream()
                .map(group -> buildOptionalHealthExaminationItemGroupModelVO(group, haimaResponse))
                .filter(group -> group != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(itemGroupList)) {
            return null;
        }
        return buildFilterItemListVO(isSelected, getTabIdByTabName(cfg, "可选项目"), "可选项目", null, itemGroupList, platform, dealId, cfg);
    }

    /**
     * 构造部分可选HealthExaminationItemGroupModelVO
     *@param
     *@return
     */
    private HealthExaminationItemGroupModelVO buildOptionalHealthExaminationItemGroupModelVO(OptionalSkuItemsGroupDto group, HaimaResponse haimaResponse) {
        List<HealthExaminationItemModelVO> healthExaminationItemModelVOS = getHealthExaminationItemModelVOsFromOptionalSkus(group, haimaResponse);
        if (CollectionUtils.isEmpty(healthExaminationItemModelVOS)) {
            return null;
        }
        String desc = String.format("以下可选包%s选%s", healthExaminationItemModelVOS.size(), group.getOptionalCount());
        return buildHealthExaminationItemGroupModelVOList(desc, healthExaminationItemModelVOS);
    }

    private String getServiceType(DealDetailInfoModel dealDetailBasicInfo) {
        if (dealDetailBasicInfo == null || CollectionUtils.isEmpty(dealDetailBasicInfo.getDealAttrs())) {
            return null;
        }
        return DealDetailUtils.getAttrSingleValueByAttrName(dealDetailBasicInfo.getDealAttrs(), SERVICE_TYPE_ATTR_NAME);
    }

    /**
     * 获取ReadMore
     *@param
     *@return
     */
    private ReadMoreVO getReadModeVO(int dealId, int tabId, int platform, HealthExaminationDealDetailCheckItemsCfg cfg) {
        String jumpUrl = PlatformUtil.isMT(platform) ? String.format(cfg.getMtMoreJumpUrlFormat(), dealId, tabId) : String.format(cfg.getDpMoreJumpUrlFormat(), dealId, tabId);
        ReadMoreVO readMoreVO = new ReadMoreVO();
        readMoreVO.setMoreText(cfg.getReadMoreText());
        readMoreVO.setMoreJumpUrl(jumpUrl);
        return readMoreVO;
    }
    /**
     * 获取部分可选的sku列表对应的二级HealthExaminationItemModelVO列表
     *@param
     *@return
     */
    private List<HealthExaminationItemModelVO> getHealthExaminationItemModelVOsFromOptionalSkus(OptionalSkuItemsGroupDto optionalSkuItemsGroupDto, HaimaResponse checkItemAndSignificanceHaimaResponse) {
        if (optionalSkuItemsGroupDto == null || CollectionUtils.isEmpty(optionalSkuItemsGroupDto.getSkuItems())) {
            return null;
        }
        return optionalSkuItemsGroupDto.getSkuItems()
                .stream()
                //剔除增值服务类别的sku
                .filter(sku -> sku != null && sku.getProductCategory() != VALUE_ADDED_SERVICE_PRODUCT_CATE)
                .map(sku -> convertOptionalSku2HealthExaminationItemModelVO(sku, checkItemAndSignificanceHaimaResponse))
                .filter(vo -> vo != null)
                .collect(Collectors.toList());
    }

    /**
     * 将可选项目组的sku转化为HealthExaminationItemModelVO
     *@param
     *@return
     */
    private HealthExaminationItemModelVO convertOptionalSku2HealthExaminationItemModelVO(SkuItemDto skuItemDto, HaimaResponse haimaResponse) {
        if (skuItemDto == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(skuItemDto.getAttrItems())) {
            return buildHealthExaminationItemModelVO(null, null, skuItemDto.getName());
        }
        HeaderVO headerVO = buildHeaderVO("检查项", "检查意义");
        List<HealthExaminationSubitemVO> healthExaminationSubitemVOS = skuItemDto.getAttrItems()
                .stream()
                .map(attr -> convertSkuAttr2HealthExaminationSubitemVO(attr, haimaResponse))
                .filter(vo -> vo != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(healthExaminationSubitemVOS)) {
            return buildHealthExaminationItemModelVO(null, null, skuItemDto.getName());
        }
        int checkItemNum = skuItemDto.getAttrItems().stream()
                .flatMap(attr -> StringUtils.isEmpty(attr.getAttrValue()) ? new ArrayList<String>().stream() : Lists.newArrayList(attr.getAttrValue().split("、")).stream())
                .collect(Collectors.toList()).size();
        return buildHealthExaminationItemModelVO(headerVO, healthExaminationSubitemVOS, String.format("%s（%s项）", skuItemDto.getName(), checkItemNum));
    }

    /**
     * 将部分可选sku属性转换为HealthExaminationSubitemVO
     *@param
     *@return
     */
    private HealthExaminationSubitemVO convertSkuAttr2HealthExaminationSubitemVO(SkuAttrItemDto attr, HaimaResponse haimaResponse) {
        if (StringUtils.isEmpty(attr.getAttrValue())) {
            return null;
        }
        String secondaryCheckItem = attr.getChnName();
        List<String> checkItems = Lists.newArrayList(attr.getAttrValue().split("、"));
        Map<String, List<String>> checkSignificance2CheckItemsMap = getCheckItemSignificance2CheckItemsMap(secondaryCheckItem, checkItems, haimaResponse);
        if (MapUtils.isEmpty(checkSignificance2CheckItemsMap)) {
            return null;
        }
        List<String> checkSignificanceList = checkSignificance2CheckItemsMap.keySet().stream().filter(key -> StringUtils.isNotEmpty(key)).collect(Collectors.toList());
        return buildHealthExaminationSubitemVO(String.format("%s（%s项）", attr.getChnName(), checkItems.size()), StringUtils.join(checkSignificanceList, "\n"));
    }

    /**
     * 获取全部可享sku列表对应的二级FilterItemListVO列表
     *@param
     *@return
     */
    private List<FilterItemListVO> getChildFilterItemListVOFromMustSkus(DealDetailInfoModel dealDetailBasicInfo, HealthExaminationDealDetailCheckItemsCfg cfg, HaimaResponse haimaResponse, int platform) {
        //获取全部可享sku列表
        List<SkuItemDto> skuItemList = extractSkuItemListFromMustGroup(dealDetailBasicInfo);
        //将sku列表按照sku类别进行分组
        Map<String, List<SkuItemDto>> skuGroupMap = groupSkuCtegory2SkuListMap(skuItemList, dealDetailBasicInfo.getProductCategories());
        if (MapUtils.isEmpty(skuGroupMap)) {
            return null;
        }
        //将按照sku类别分组的sku列表组转换为ChildTabItem列表，分组名为ChildTab名，sku列表转换桅ChildTab中三级tab列表
        List<FilterItemListVO> filterItemListVOS = skuGroupMap.entrySet()
                .stream()
                .map(entry -> {
                    List<FilterItemListVO> grandchildTabModels = getGrandchildTabModelsByMustSkuLst(getSelectedThirdTabName(getServiceType(dealDetailBasicInfo), cfg), entry.getValue(), cfg, haimaResponse, platform, dealDetailBasicInfo.getDealId());
                    boolean isSelected = entry.getKey().equals(getSelectedSecondTabName(getServiceType(dealDetailBasicInfo), cfg));
                    return buildFilterItemListVO(isSelected, getTabIdByTabName(cfg, entry.getKey()), entry.getKey(), grandchildTabModels, null, platform, dealDetailBasicInfo.getDealId(), cfg);
                })
                .filter(vo -> vo != null)
                .sorted(Comparator.comparing(o -> getSecondTabPriority(o.getTabName())))
                .collect(Collectors.toList());
        if (!isFirstTabSelected(getServiceType(dealDetailBasicInfo), cfg, skuGroupMap)) {
            return filterItemListVOS;
        }
        setFirstTabSelectedStatus(filterItemListVOS);
        return filterItemListVOS;
    }

    /**
     * 判断是否需要选中第一个筛选：当筛选组中不含有默认选中的tab名时，默认选中每层中第一个筛选
     *@param
     *@return
     */
    private boolean isFirstTabSelected(String serviceType, HealthExaminationDealDetailCheckItemsCfg cfg, Map<String, List<SkuItemDto>> skuGroupMap) {
        String selectedThirdTabName = getSelectedThirdTabName(serviceType, cfg);
        String selectedSecondTabName = getSelectedSecondTabName(serviceType, cfg);
        if (StringUtils.isEmpty(selectedSecondTabName) || StringUtils.isEmpty(selectedThirdTabName) || MapUtils.isEmpty(skuGroupMap)) {
            return true;
        }
        List<SkuAttrItemDto> skuAttrItemDtos = skuGroupMap.entrySet()
                .stream()
                .filter(entry -> selectedSecondTabName.equals(entry.getKey()))
                .flatMap(entry -> entry.getValue().stream())
                .flatMap(sku -> {
                    if (sku.getAttrItems() == null) {
                        return new ArrayList<SkuAttrItemDto>().stream();
                    }
                    return sku.getAttrItems().stream();
                }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return true;
        }
        return skuAttrItemDtos.stream().filter(skuAttr -> selectedThirdTabName.equals(skuAttr.getChnName())).findFirst().orElse(null) == null;
    }

    private String getSelectedSecondTabName(String serviceType, HealthExaminationDealDetailCheckItemsCfg cfg) {
        HealthExaminationDealDetailCheckItemsCfg.SelectedFilterTabConfigModel selectedFilterTabConfigModel = getSelectedFilterTabConfigModel(serviceType, cfg);
        return selectedFilterTabConfigModel != null ? selectedFilterTabConfigModel.getSecondTabName() : null;
    }

    private String getSelectedThirdTabName(String serviceType, HealthExaminationDealDetailCheckItemsCfg cfg) {
        HealthExaminationDealDetailCheckItemsCfg.SelectedFilterTabConfigModel selectedFilterTabConfigModel = getSelectedFilterTabConfigModel(serviceType, cfg);
        return selectedFilterTabConfigModel != null ? selectedFilterTabConfigModel.getThirdTabName() : null;
    }

    private HealthExaminationDealDetailCheckItemsCfg.SelectedFilterTabConfigModel getSelectedFilterTabConfigModel(String serviceType, HealthExaminationDealDetailCheckItemsCfg cfg) {
        if (StringUtils.isEmpty(serviceType) || cfg == null || CollectionUtils.isEmpty(cfg.getSelectedFilterTabConfigModels())) {
            return null;
        }
        return cfg.getSelectedFilterTabConfigModels().stream().filter(model -> serviceType.equals(model.getServiceType())).findFirst().orElse(null);
    }

    private int getSecondTabPriority(String secondTabName) {
        List<String> secondTabNameList = Lists.newArrayList("基础检查", "实验室检查", "仪器检查");
        if (!secondTabNameList.contains(secondTabName)) {
            return Integer.MAX_VALUE;
        }
        return secondTabNameList.indexOf(secondTabName);
    }

    private void setFirstTabSelectedStatus(List<FilterItemListVO> filterItemListVOs) {
        if (CollectionUtils.isEmpty(filterItemListVOs) || filterItemListVOs.get(0) == null) {
            return;
        }
        filterItemListVOs.get(0).setIsSelected(true);
        if (CollectionUtils.isEmpty(filterItemListVOs.get(0).getChildrenFilters()) || filterItemListVOs.get(0).getChildrenFilters().get(0) == null) {
            return;
        }
        filterItemListVOs.get(0).getChildrenFilters().get(0).setIsSelected(true);
    }

    /**
     * 将全部可享sku列表或增值服务sku列表转化为三级FilterItemListVO列表
     *@param
     *@return
     */
    private List<FilterItemListVO> getGrandchildTabModelsByMustSkuLst(String selectedThirdTabName, List<SkuItemDto> skuList, HealthExaminationDealDetailCheckItemsCfg cfg, HaimaResponse haimaResponse, int platform, int dealId) {
        Map<String, List<String>> grandchildTabName2CheckItemsMap = getGrandchildTabName2CheckItemNamesMap(skuList);
        if (MapUtils.isEmpty(grandchildTabName2CheckItemsMap)) {
            return null;
        }
        Map<FilterItemListVO, Integer> filterItemListVO2PriorityMap = grandchildTabName2CheckItemsMap.entrySet()
                .stream()
                .collect(HashMap::new, (map, entry) -> {
                    //构造tab名
                    String tabName = String.format("%s（%s项）", entry.getKey(), entry.getValue().size());
                    //构造检查项目表格组
                    List<HealthExaminationItemGroupModelVO> healthExaminationItemGroupModelVOS = buildMustSkusItemGroups(tabName, entry.getKey(), entry.getValue(), haimaResponse);//Lists.newArrayList(buildHealthExaminationItemGroupModelVOList(null, Lists.newArrayList(healthExaminationItemModelVO)));
                    //确认选中状态
                    boolean isSelected = StringUtils.isNotEmpty(selectedThirdTabName) && selectedThirdTabName.equals(entry.getKey());
                    //构造FilterItemListVO
                    FilterItemListVO filterItemListVO = buildFilterItemListVO(isSelected, getTabIdByTabName(cfg, entry.getKey()), tabName, null, healthExaminationItemGroupModelVOS, platform, dealId, cfg);
                    if (filterItemListVO == null) {
                        return;
                    }
                    //获取优先级进行后续排序
                    map.put(filterItemListVO, getThirdTabPriority(haimaResponse, entry.getKey()));
                }, HashMap::putAll);
        if (MapUtils.isEmpty(filterItemListVO2PriorityMap)) {
            return null;
        }
        return filterItemListVO2PriorityMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    /**
     * 构造全部可享sku检查项目组表格列表
     *@param
     *@return
     */
    private List<HealthExaminationItemGroupModelVO> buildMustSkusItemGroups(String groupName, String grandchildTab, List<String> checkItemList, HaimaResponse haimaResponse) {
        //构造表头
        HeaderVO headerVO = buildHeaderVO("检查项", "检查意义");
        //构造表行元素列表
        List<HealthExaminationSubitemVO> healthExaminationSubitemVOS = getHealthExaminationSubitemVOList(grandchildTab, checkItemList, haimaResponse);
        if (CollectionUtils.isEmpty(healthExaminationSubitemVOS)) {
            return null;
        }
        //构造表格
        HealthExaminationItemModelVO healthExaminationItemModelVO = buildHealthExaminationItemModelVO(headerVO, healthExaminationSubitemVOS, groupName);
        //构造表格组，全部可享表格组中只有一张表格
        return Lists.newArrayList(buildHealthExaminationItemGroupModelVOList(null, Lists.newArrayList(healthExaminationItemModelVO)));
    }

    private int getThirdTabPriority(HaimaResponse haimaResponse, String thirdTabName) {
        if (haimaResponse == null || CollectionUtils.isEmpty(haimaResponse.getData())) {
            return Integer.MAX_VALUE;
        }
        //根据二级检查项获取对应的海马配置
        List<String> checkItemList = haimaResponse.getData().stream().map(config -> config.getExtString("checkItem")).filter(checkItem -> checkItem != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkItemList) || !checkItemList.contains(thirdTabName)) {
            return Integer.MAX_VALUE;
        }
        return checkItemList.indexOf(thirdTabName);
    }

    /**
     * 根据二级检查项和三级检查项列表获取HealthExaminationSubitemVO列表
     *@param
     *@return
     */
    private List<HealthExaminationSubitemVO> getHealthExaminationSubitemVOList(String secondaryCheckItem, List<String> checkItems, HaimaResponse haimaResponse) {
        Map<String, List<String>> significance2CheckItemsMap = getCheckItemSignificance2CheckItemsMap(secondaryCheckItem, checkItems, haimaResponse);
        if (MapUtils.isEmpty(significance2CheckItemsMap)) {
            return null;
        }
        //检查意义不为空
       List<HealthExaminationSubitemVO> voWithoutEmptySignificance = significance2CheckItemsMap.entrySet()
                .stream()
                .filter(e -> StringUtils.isNotEmpty(e.getKey()))
                .map(e -> {
                    String itemName = StringUtils.join(e.getValue(), "、");
                    return buildHealthExaminationSubitemVO(String.format("%s（%s项）", itemName, e.getValue().size()), e.getKey());
                })
                .collect(Collectors.toList());
        //检查意义为空
        List<HealthExaminationSubitemVO> voWithEmptySignificance = significance2CheckItemsMap.entrySet()
                .stream()
                .filter(e -> StringUtils.isEmpty(e.getKey()))
                .flatMap(e -> e.getValue().stream())
                .map(item -> buildHealthExaminationSubitemVO(String.format("%s（%s项）", item, 1), StringUtils.EMPTY))
                .collect(Collectors.toList());
        voWithoutEmptySignificance.addAll(voWithEmptySignificance);
        return voWithoutEmptySignificance;
    }

    public FilterItemListVO buildFilterItemListVO(boolean isSelected, int tabId, String tabTitle, List<FilterItemListVO> childrenFilters, List<HealthExaminationItemGroupModelVO> itemGroupList, int platform, int dealId, HealthExaminationDealDetailCheckItemsCfg cfg) {
        if (CollectionUtils.isEmpty(childrenFilters) && CollectionUtils.isEmpty(itemGroupList)) {
            return null;
        }
        FilterItemListVO filterItemListVO = new FilterItemListVO();
        filterItemListVO.setTabName(tabTitle);
        filterItemListVO.setTabId(tabId);
        filterItemListVO.setItemGroupList(itemGroupList);
        filterItemListVO.setChildrenFilters(childrenFilters);
        filterItemListVO.setMore(getReadModeVO(dealId, tabId, platform, cfg));
        filterItemListVO.setIsSelected(isSelected);
        return filterItemListVO;
    }

    /**
     * 获取三级筛选tab名到检查项列表的Map，用于全部可享sku列表或增值服务sku列表
     *@param
     *@return
     */
    private Map<String, List<String>> getGrandchildTabName2CheckItemNamesMap(List<SkuItemDto> mustSkuList) {
        if (CollectionUtils.isEmpty(mustSkuList)) {
            return new LinkedHashMap<>();
        }
        Map<String, List<String>> grandchildTabName2CheckItemNamesMap = mustSkuList.stream()
                //过滤商家自定义sku
                .filter(sku -> CollectionUtils.isNotEmpty(sku.getAttrItems()))
                //获取所有sku的属性列表
                .flatMap(sku -> sku.getAttrItems().stream())
                //将sku属性列表按照属性名进行归类，同一属性名的属性值合并成List
                .collect(LinkedHashMap::new, (map, skuAttr) -> {
                    if (StringUtils.isEmpty(skuAttr.getChnName()) || StringUtils.isEmpty(skuAttr.getAttrValue())) {
                        return;
                    }
                    //属性值按照分隔符"、"拆分成检查项列表
                    List<String> newCheckItemList = Lists.newArrayList(skuAttr.getAttrValue().split("、"));
                    if (!map.containsKey(skuAttr.getChnName()) || CollectionUtils.isEmpty(map.get(skuAttr.getChnName()))) {
                        map.put(skuAttr.getChnName(), newCheckItemList);
                        return;
                    }
                    List<String> precheckItemList = map.get(skuAttr.getChnName());
                    //同一属性名下的检查项列表去重合并
                    Set<String> checkItemSet = new HashSet<String>() {{
                        addAll(precheckItemList);
                        addAll(newCheckItemList);
                    }};
                    map.put(skuAttr.getChnName(), Lists.newArrayList(checkItemSet));
                }, LinkedHashMap::putAll);
        //商家自定义sku
        List<String> otherSkuNameList = mustSkuList.stream()
                .filter(sku -> CollectionUtils.isEmpty(sku.getAttrItems()))
                .map(sku -> sku.getName())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(otherSkuNameList)) {
            grandchildTabName2CheckItemNamesMap.put("更多", otherSkuNameList);
        }
        return grandchildTabName2CheckItemNamesMap;
    }

    /**
     * 将sku列表按照sku所属类别进行分组
     *@param skuItemDtoList 需要进行分组的sku列表
     *@param productCategories 该团单下所有sku类别的全集，用来获取该sku的类别名
     *@return sku类别名到所属该类别的sku列表对应的map
     */
    private Map<String, List<SkuItemDto>> groupSkuCtegory2SkuListMap(List<SkuItemDto> skuItemDtoList, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(skuItemDtoList)) {
            return new HashMap<>();
        }
        return skuItemDtoList
                .stream()
                .collect(HashMap::new, (map, sku) -> {
                    String skuCategoryName = getSkuCategoryNameById(sku.getProductCategory(), productCategories);
                    //如果map中不含该sku类别的key或者该sku类别的key对应的value值为空列表，则新建一个含有该sku的列表作为该类别名key对应的value值放入map
                    if (!map.containsKey(skuCategoryName) || CollectionUtils.isEmpty(map.get(skuCategoryName))) {
                        map.put(skuCategoryName, Lists.newArrayList(sku));
                        return;
                    }
                    List<SkuItemDto> skuList = map.get(skuCategoryName);
                    //将该sku放入map中该sku类别key对应的sku列表中
                    skuList.add(sku);
                }, HashMap::putAll);
    }

    /**
     * 根据sku类别id获取sku类别名
     *@param productCategories 该团单下sku类别全集
     *@return
     */
    private String getSkuCategoryNameById(long categoryId, List<ProductSkuCategoryModel> productCategories) {
        if (CollectionUtils.isEmpty(productCategories)) {
            return null;
        }
        ProductSkuCategoryModel categoryModel = productCategories.stream().filter(category -> category.getProductCategoryId() != null && category.getProductCategoryId() == categoryId).findFirst().orElse(null);
        return categoryModel == null ? null : categoryModel.getCnName();
    }

    /**
     * 提取增值服务sku列表，从全部可享和部分可享sku列表提取出来的类别为4070的sku的列表
     *@param
     *@return
     */
    private List<SkuItemDto> extractValueAddedSkuList(DealDetailInfoModel dealDetailBasicInfo) {
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = extractDealDetailSkuUniStructuredDtoFromDealDetailInfoModel(dealDetailBasicInfo);
        if (dealDetailSkuUniStructuredDto == null || CollectionUtils.isEmpty(dealDetailSkuUniStructuredDto.getMustGroups())) {
            return new ArrayList<>();
        }
        List<SkuItemDto> skuItemDtos = new ArrayList<>();
        List<SkuItemDto> valueAddedSkusFromMustSkuGroup = dealDetailSkuUniStructuredDto.getMustGroups().stream()
                .flatMap(group -> group.getSkuItems().stream())
                .filter(sku -> sku != null && sku.getProductCategory() == VALUE_ADDED_SERVICE_PRODUCT_CATE)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(valueAddedSkusFromMustSkuGroup)) {
            skuItemDtos.addAll(valueAddedSkusFromMustSkuGroup);
        }
        List<SkuItemDto> valueAddedSkusFromOptionalSkuGroup = dealDetailSkuUniStructuredDto.getOptionalGroups().stream()
                .flatMap(group -> group.getSkuItems().stream())
                .filter(sku -> sku != null && sku.getProductCategory() == VALUE_ADDED_SERVICE_PRODUCT_CATE)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(valueAddedSkusFromOptionalSkuGroup)) {
            skuItemDtos.addAll(valueAddedSkusFromOptionalSkuGroup);
        }
        return skuItemDtos;
    }

    /**
     * 提取全部可享sku列表
     *@param
     *@return
     */
    private List<SkuItemDto> extractSkuItemListFromMustGroup(DealDetailInfoModel dealDetailBasicInfo) {
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = extractDealDetailSkuUniStructuredDtoFromDealDetailInfoModel(dealDetailBasicInfo);
        if (dealDetailSkuUniStructuredDto == null || CollectionUtils.isEmpty(dealDetailSkuUniStructuredDto.getMustGroups())) {
            return new ArrayList<>();
        }
        return dealDetailSkuUniStructuredDto.getMustGroups().stream()
                .flatMap(group -> group.getSkuItems().stream())
                //剔除增值服务类别的sku
                .filter(sku -> sku != null && sku.getProductCategory() != VALUE_ADDED_SERVICE_PRODUCT_CATE)
                .collect(Collectors.toList());
    }

    /**
     * 提取部分可选sku列表
     *@param
     *@return
     */
    private List<OptionalSkuItemsGroupDto> extractOptionalSkuItemsGroupDto(DealDetailInfoModel dealDetailBasicInfo) {
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = extractDealDetailSkuUniStructuredDtoFromDealDetailInfoModel(dealDetailBasicInfo);
        if (dealDetailSkuUniStructuredDto == null) {
            return null;
        }
        return dealDetailSkuUniStructuredDto.getOptionalGroups();
    }

    private DealDetailSkuUniStructuredDto extractDealDetailSkuUniStructuredDtoFromDealDetailInfoModel(DealDetailInfoModel dealDetailBasicInfo) {
        if (dealDetailBasicInfo == null || dealDetailBasicInfo.getDealDetailDtoModel() == null) {
            return null;
        }
        return dealDetailBasicInfo.getDealDetailDtoModel().getSkuUniStructuredDto();
    }

    private HaimaRequest buildHaiMaRequest(ActivityCxt ctx) {
        int platform = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.platform);
        String haimaKey = "health_examination_check_item_significance";
        int cityId = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.dpCityId);
        if (PlatformUtil.isMT(platform)) {
            cityId = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.mtCityId);
        }
        HaimaRequest request = new HaimaRequest();
        // 业务方申请的运营位key
        request.setSceneKey(haimaKey);
        request.setCityId(cityId);
        return request;
    }

}
