package com.sankuai.dzviewscene.product.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

@Getter
@AllArgsConstructor
public enum PromoTagTypeEnum {
    NONE(0, ""),
    CREDIT_USE(1, "支持先用后付"),
    COST_EFFECTIVENESS(2, "性价比高");

    private final int code;
    private final String desc;

    public static PromoTagTypeEnum getByCode(int code) {
        for (PromoTagTypeEnum promoTagTypeEnum : PromoTagTypeEnum.values()) {
            if (promoTagTypeEnum.code == code) {
                return promoTagTypeEnum;
            }
        }
        return NONE;
    }

    public static String getDescByCode(int code) {
        for (PromoTagTypeEnum promoTagTypeEnum : PromoTagTypeEnum.values()) {
            if (promoTagTypeEnum.code == code) {
                return promoTagTypeEnum.desc;
            }
        }
        return StringUtils.EMPTY;
    }
}