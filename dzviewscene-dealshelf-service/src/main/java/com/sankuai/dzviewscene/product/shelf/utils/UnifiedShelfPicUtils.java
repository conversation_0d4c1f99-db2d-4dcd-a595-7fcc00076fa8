/*
 * Create Author  : liya<PERSON><PERSON>
 * Create Date    : 2024-09-13
 * Project        :
 * File Name      : UnifiedShelfPicUtils.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.product.shelf.utils;

import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import org.apache.commons.lang.StringUtils;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-13
 * @since dzviewscene-dealshelf-home 1.0
 */
public class UnifiedShelfPicUtils {

     public static String toHttpsUrl(String key, int width, int height, PictureURLBuilders.ScaleType scaleType) {
         String httpsUrl = "";
         if (!key.contains("?")) {
             httpsUrl = PictureURLBuilders.toHttpsUrl(key, width, height, scaleType);
         }else {
             // 上方工具类无法处理含token的私有桶链接，参考https://km.sankuai.com/page/**********规则自行处理
             String[] split = StringUtils.split(key, "?");
             if (split.length != 2) {
                 return key;
             }
             httpsUrl =  String.format("%s?%s", urlGenerate(split[0], width, height, scaleType), split[1]);
         }
         //s3plus图片去掉watermark参数
         if(httpsUrl != null && httpsUrl.contains("s3plus-img")){
             return httpsUrl.replace("%7Cwatermark%3D0", "");
         }
         return httpsUrl;
    }


    private static String urlGenerate(String key, int width, int height, PictureURLBuilders.ScaleType scaleType) {
        String scaleParams = "";
        if (width != 0) {
            scaleParams = width + "w_";
        }
        if (height != 0) {
            scaleParams = scaleParams + height + "h_";
        }
        if (StringUtils.isBlank(scaleParams)) {
            scaleParams = "watermark=0";
        } else {
            if (scaleType == PictureURLBuilders.ScaleType.Scale) {
                scaleParams = scaleParams + "0e_1l|watermark=0";
            } else {
                scaleParams = scaleParams + "1e_1c_1l|watermark=0";
            }
        }
        return String.format("%s@%s", key, scaleParams);
    }
}
