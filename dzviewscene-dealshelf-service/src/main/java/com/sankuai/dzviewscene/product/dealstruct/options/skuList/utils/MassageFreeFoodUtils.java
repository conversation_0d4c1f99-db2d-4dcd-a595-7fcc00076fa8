package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.BuffetFoodEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.SnackFoodEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.TeaFoodEnum;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class MassageFreeFoodUtils {

    private static final String BUFFET_FOOD = "自助餐畅吃";

    private static final String SNACK_FOOD = "小吃简餐畅吃";

    private static final String TEA_FOOD = "茶点水果";

    private static final String FREE_TEA = "茶点";

    private static final String NOTHING = "无";

    public static final Set<String> FOOD_NEW_DATA = Sets.newHashSet(BUFFET_FOOD, SNACK_FOOD, TEA_FOOD, NOTHING);

    /**
     * 构建免费餐食
     */
    public static DealSkuVO parseFreeFood(SkuItemDto skuItemDto, boolean hitNewIcon) {
        String freeFood = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "freeFood");
        if (BUFFET_FOOD.equals(freeFood)) {
            return buildDealSkuVO(BUFFET_FOOD, getFoodValue(skuItemDto), buildBuffetItems(skuItemDto, hitNewIcon));
        } else if (SNACK_FOOD.equals(freeFood)) {
            return buildDealSkuVO(SNACK_FOOD, getFoodValue(skuItemDto), buildSnackItems(skuItemDto, hitNewIcon));
        } else if (TEA_FOOD.equals(freeFood)) {
            //茶点水果里，如果有水果，展示“茶点水果”；如果没有水果，展示“免费茶点”
            String fruitStr = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "Fruit");
            return buildDealSkuVO(StringUtils.isNotBlank(fruitStr) ? TEA_FOOD : FREE_TEA, getFoodValue(skuItemDto), buildTeaItems(skuItemDto, hitNewIcon));
        }
        return null;
    }

    private static DealSkuVO buildDealSkuVO(String title, String subTitle, List<DealSkuItemVO> items) {
        DealSkuVO dealSkuVO = new DealSkuVO();
        dealSkuVO.setTitle(title);
        dealSkuVO.setSubTitle(subTitle);
        dealSkuVO.setItems(items);
        return dealSkuVO;
    }

    private static List<DealSkuItemVO> buildBuffetItems(SkuItemDto skuItemDto, boolean hitNewIcon) {
        List<DealSkuItemVO> result = new ArrayList<>();
        for (BuffetFoodEnum foodTypeEnum : BuffetFoodEnum.values()) {
            String foodValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), foodTypeEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                result.add(buildDealSkuItemVO(foodTypeEnum.getFoodName(), foodValue, hitNewIcon ? foodTypeEnum.getNewFoodIcon() : foodTypeEnum.getFoodIcon()));
            }
        }
        return result;
    }

    private static List<DealSkuItemVO> buildSnackItems(SkuItemDto skuItemDto, boolean hitNewIcon) {
        List<DealSkuItemVO> result = new ArrayList<>();
        for (SnackFoodEnum foodEnum : SnackFoodEnum.values()) {
            String foodValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), foodEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                result.add(buildDealSkuItemVO(foodEnum.getFoodName(), foodValue, hitNewIcon ? foodEnum.getNewFoodIcon() : foodEnum.getFoodIcon()));
            }
        }
        return result;
    }

    private static List<DealSkuItemVO> buildTeaItems(SkuItemDto skuItemDto, boolean hitNewIcon) {
        List<DealSkuItemVO> result = new ArrayList<>();
        for (TeaFoodEnum foodEnum : TeaFoodEnum.values()) {
            String foodValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), foodEnum.getFoodCode());
            if (StringUtils.isNotBlank(foodValue)) {
                result.add(buildDealSkuItemVO(foodEnum.getFoodName(), foodValue, hitNewIcon ? foodEnum.getNewFoodIcon() : foodEnum.getFoodIcon()));
            }
        }
        return result;
    }

    private static DealSkuItemVO buildDealSkuItemVO(String name, String value, String icon) {
        DealSkuItemVO dealSkuItemVO = new DealSkuItemVO();
        dealSkuItemVO.setType(0);
        dealSkuItemVO.setName(name);
        dealSkuItemVO.setValue(value);
        dealSkuItemVO.setIcon(icon);
        return dealSkuItemVO;
    }

    private static String getFoodValue(SkuItemDto skuItemDto) {
        String foodValue = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuItemDto.getAttrItems(), "MealValue");
        if (StringUtils.isBlank(foodValue)) {
            return null;
        }
        return String.format("价值¥%s", foodValue);
    }

}