package com.sankuai.dzviewscene.product.utils;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * FilterM工具
 * Created by z<PERSON>suping on 2023/1/5.
 */
public class FilterMUtils {

    /**
     * 根据白名单信息判断是否命中筛选项ID和筛选项名称
     * @param filterIdNameMapWhitelist
     * @param filterId
     * @param filterName
     * @return
     */
    public static boolean hitFilterIdOrNameByWhitelist(Map<Long, String> filterIdNameMapWhitelist, long filterId, String filterName) {
        //如果白名单为空，说明不需要校验
        if (MapUtils.isEmpty(filterIdNameMapWhitelist)) {
            return true;
        }
        String filterNameWhitelist = filterIdNameMapWhitelist.get(filterId);
        //没有配置筛选项目名称，则只校验筛选项ID
        if (StringUtils.isEmpty(filterNameWhitelist)) {
            return filterIdNameMapWhitelist.keySet().contains(filterId);
        }
        //筛选项名称为空，不满足要求返回false
        if (StringUtils.isEmpty(filterName)) {
            return false;
        }
        //同时命中筛选项ID和筛选项名称的白名单
        return filterIdNameMapWhitelist.keySet().contains(filterId) && StringUtils.equals(filterNameWhitelist, filterName);
    }
}
