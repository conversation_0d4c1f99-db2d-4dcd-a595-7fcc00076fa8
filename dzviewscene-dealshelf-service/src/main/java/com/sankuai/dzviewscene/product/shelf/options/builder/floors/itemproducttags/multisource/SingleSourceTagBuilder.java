package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.multisource;

import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/18
 */
public interface SingleSourceTagBuilder<Config extends SingleSourceTagConfig> {

    /**
     * @param param
     * @param configs
     * @return key - 传入配置的 key， value - 加工好的 tag
     */
    Map<String, String> buildTags(ItemProductTagsVP.Param param, List<Config> configs);

    /**
     * @return 策略名
     */
    String getName();
}
