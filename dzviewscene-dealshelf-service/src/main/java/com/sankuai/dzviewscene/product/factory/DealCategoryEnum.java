package com.sankuai.dzviewscene.product.factory;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * @author: created by hang.yu on 2023/10/10 16:54
 */
@Getter
@AllArgsConstructor
public enum DealCategoryEnum {

    /**
     * 足浴
     */
    MASSAGE(303, "", "massageCategoryStrategyImpl"),

    /**
     * 洗浴
     */
    BATH(304, "", "bathCategoryStrategyImpl"),

    /**
     * 酒吧
     */
    BAR(312, "","barCategoryStrategyImpl"),

    /**
     * 管道疏通
     */
    PIPE(414, "","pipeCategoryStrategyImpl"),
    WEARABLE_NAIL(502, "穿戴甲", "wearableNailCategoryStrategyImpl"),

    ;

    private final Integer dealCategoryId;
    
    private final String serviceType;

    private final String strategyName;


    public static DealCategoryEnum getEnumByDealCategoryId(Integer dealCategoryId, String serviceType) {
        for (DealCategoryEnum value : DealCategoryEnum.values()) {
            if (value.getDealCategoryId().equals(dealCategoryId)
                    && (StringUtils.isEmpty(value.getServiceType())
                        || value.getServiceType().equals(serviceType))) {
                return value;
            }
        }
        return null;
    }

}
