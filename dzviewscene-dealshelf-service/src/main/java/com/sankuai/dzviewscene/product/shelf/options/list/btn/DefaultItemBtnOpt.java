package com.sankuai.dzviewscene.product.shelf.options.list.btn;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.ItemBtnVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import lombok.Data;

@VPointOption(name = "默认按钮文案",
        description = "默认按钮文案，返回空",
        code = DefaultItemBtnOpt.CODE, isDefault = true)
public class DefaultItemBtnOpt extends ItemBtnVP<DefaultItemBtnOpt.Config> {
    public static final String CODE = "DefaultItemBtnOpt";

    @Override
    public DzSimpleButtonVO compute(ActivityCxt context, Param param, DefaultItemBtnOpt.Config config) {
        return null;
    }

    @Data
    @VPointCfg
    public static class Config {
    }
}
