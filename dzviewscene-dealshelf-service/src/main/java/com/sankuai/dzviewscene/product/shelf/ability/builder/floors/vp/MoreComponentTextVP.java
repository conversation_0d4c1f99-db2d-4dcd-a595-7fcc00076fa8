package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.FloorsBuilder;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/3/10
 */
@VPoint(name = "商品区-查看更多-展示文案", description = "DzMoreComponentVO.text", code = MoreComponentTextVP.CODE, ability = FloorsBuilder.CODE)
public abstract class MoreComponentTextVP<T> extends PmfVPoint<String, MoreComponentTextVP.Param, T> {
    public static final String CODE = "MoreComponentTextVP";
    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 商品区的商品总个数
         */
        private int itemAreaItemCnt;

        /**
         * 商品区无效商品数
         */
        private int unavailableItemCnt;

        /**
         * 商品区默认的展示个数
         */
        private int defaultShowNum;

        private String groupName;

        private long filterId;
    }
}
