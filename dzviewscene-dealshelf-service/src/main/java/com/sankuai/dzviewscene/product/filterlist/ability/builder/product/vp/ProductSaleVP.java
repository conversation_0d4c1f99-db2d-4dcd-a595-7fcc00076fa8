package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@VPoint(name = "销量", description = "销量", code = ProductSaleVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductSaleVP<T> extends PmfVPoint<String, ProductSaleVP.Param, T> {

    public static final String CODE = "ProductSaleVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;
    }
}