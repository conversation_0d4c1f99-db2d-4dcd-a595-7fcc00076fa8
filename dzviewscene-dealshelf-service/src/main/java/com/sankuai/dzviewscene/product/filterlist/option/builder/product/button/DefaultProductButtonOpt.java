package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;

/**
 * <AUTHOR>
 * @since 2023/4/9 21:53
 */
@VPointOption(name = "通用-不设置按钮",
        description = "",
        code = "DefaultProductButtonOpt",
        isDefault = true)
public class DefaultProductButtonOpt extends ProductButtonVP<Void> {
    @Override
    public DzSimpleButtonVO compute(ActivityCxt context, Param param, Void unused) {
        return null;
    }
}
