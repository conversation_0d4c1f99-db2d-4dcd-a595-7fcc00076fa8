package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Ability;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.ModuleStrategy;
import com.sankuai.dzviewscene.product.dealstruct.ability.douhu.DealDetailDouhuFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.*;
import com.sankuai.dzviewscene.product.dealstruct.activity.deal.DealDetailActivity;
import com.sankuai.dzviewscene.product.dealstruct.model.*;
import com.sankuai.dzviewscene.product.dealstruct.vo.*;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.collections.CollectionUtils;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * created by zhangzhiyuan04 in 2021/12/30
 */
@Ability(code = DealDetailSkuListsBuilder.CODE,
        name = "团购详情模块sku列表构造能力",
        description = "团购详情模块sku列表构造能力",
        activities = {
                DealDetailActivity.CODE
        },
        dependency = {
                DealDetailFetcher.CODE,
                DealDetailDouhuFetcher.CODE
        }
)
public class DealDetailSkuListsBuilder extends PmfAbility<List<List<DealSkuGroupModuleVO>>, DealDetailSkuListsParam, DealDetailSkuListsCfg> implements ModuleStrategy {

    public static final String CODE = "dealDetailSkuListsBuilder";

    @Override
    public DealDetailModuleVO buildModelVO(ActivityCxt activityCxt, DealDetailAssembleParam assambleParam, String config) {
        List<List<DealSkuGroupModuleVO>> dealSkuGroupModuleVoLists = activityCxt.getSource(DealDetailSkuListsBuilder.CODE);
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = CollectUtils.firstValue(dealSkuGroupModuleVoLists);
        if (CollectionUtils.isEmpty(dealSkuGroupModuleVOS)) {
            return null;
        }
        DealDetailModuleVO dealDetailModuleVO = new DealDetailModuleVO();
        dealDetailModuleVO.setSkuGroupsModel1(dealSkuGroupModuleVOS);

        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        DealDetailInfoModel dealDetailInfoModel = CollectUtils.firstValue(dealDetailInfoModels);
        // 获取团购次卡标题
        if (TimesDealUtil.isTimesDeal(dealDetailInfoModel)) {
            dealDetailModuleVO.setName(TimesDealUtil.getTimesTitle(dealDetailInfoModel.getDealAttrs()));
        }
        return dealDetailModuleVO;
    }

    @Override
    public CompletableFuture<List<List<DealSkuGroupModuleVO>>> build(ActivityCxt activityCxt, DealDetailSkuListsParam dealDetailSkuListsParam, DealDetailSkuListsCfg dealDetailSkuListsCfg) {
        List<DealDetailInfoModel> dealDetailInfoModels = activityCxt.getSource(DealDetailFetcher.CODE);
        List<DouhuResultModel> douhuResultModels = activityCxt.getSource(DealDetailDouhuFetcher.CODE);
        if (CollectionUtils.isEmpty(dealDetailInfoModels)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<List<DealSkuGroupModuleVO>> collect = dealDetailInfoModels.stream().map(detailModel -> {
            //有效性校验
            if (!isValidDealDetail(detailModel)) {
                return null;
            }
            return buildSkuUniModel(activityCxt, detailModel.getDealDetailDtoModel().getSkuUniStructuredDto(), detailModel.getProductCategories(), detailModel.getDealAttrs(), douhuResultModels);
        }).filter(model -> model != null).collect(Collectors.toList());
        return CompletableFuture.completedFuture(collect);
    }

    private List<DealSkuGroupModuleVO> buildSkuUniModel(ActivityCxt activityCxt, DealDetailSkuUniStructuredDto structuredDto, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        List<DealSkuGroupSequenceModel> dealSkuGroupSequenceModels = new ArrayList<>();
        //通过全部可享sku列表构造sku列表组
        List<MustSkuItemsGroupDto> mustGroups = structuredDto.getMustGroups();
        if (CollectionUtils.isNotEmpty(mustGroups)) {
            List<DealSkuGroupSequenceModel> mustItems = buildMustSkuListGroups(activityCxt, mustGroups, productCategories, dealAttrs, douhuResultModels);
            dealSkuGroupSequenceModels.addAll(mustItems);
        }
        //通过部分可享sku列表构造sku列表组
        List<OptionalSkuItemsGroupDto> optionalGroups = structuredDto.getOptionalGroups();
        if (CollectionUtils.isNotEmpty(optionalGroups)) {
            List<DealSkuGroupSequenceModel> optionalItems = buildOptionalSkuListGroups(activityCxt, optionalGroups, productCategories, dealAttrs, douhuResultModels);
            dealSkuGroupSequenceModels.addAll(optionalItems);

        }
        if (CollectionUtils.isEmpty(dealSkuGroupSequenceModels)) {
            return null;
        }
        //排序
        List<DealSkuGroupModuleVO> dealSkuGroupModuleVOS = dealSkuGroupSequenceModels.stream()
                .filter(model -> model != null) //滤空
                .sorted(Comparator.comparingInt(DealSkuGroupSequenceModel::getPriority)) //排序
                .map(model -> model.getDealSkuGroupModuleVO()) // 提取
                .collect(Collectors.toList());
        //后置处理
        SkuListGroupsAfterProcessingVP<?> skuListGroupsAfterProcessingVP = findVPoint(activityCxt, SkuListGroupsAfterProcessingVP.CODE);
        if (skuListGroupsAfterProcessingVP == null) {
            return dealSkuGroupModuleVOS;
        }
        return skuListGroupsAfterProcessingVP.execute(activityCxt, SkuListGroupsAfterProcessingVP.Param.builder().dealAttrs(dealAttrs).skuGroups(dealSkuGroupModuleVOS).productCategories(productCategories).build());
    }

    private List<DealSkuGroupSequenceModel> buildMustSkuListGroups(ActivityCxt activityCxt, List<MustSkuItemsGroupDto> mustSkuGroups, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        if (CollectionUtils.isEmpty(mustSkuGroups)) {
            return Lists.newArrayList();
        }
        return mustSkuGroups.stream()
                .map(mustSkuGroup -> buildSkuGroup(activityCxt, mustSkuGroup.getSkuItems(), true, CollectionUtils.size(mustSkuGroup.getSkuItems()), productCategories, dealAttrs, douhuResultModels))
                .filter(model -> model != null).collect(Collectors.toList());
    }

    private List<DealSkuGroupSequenceModel> buildOptionalSkuListGroups(ActivityCxt activityCxt, List<OptionalSkuItemsGroupDto> optionalSkuGroups, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        if (CollectionUtils.isEmpty(optionalSkuGroups)) {
            return Lists.newArrayList();
        }
        return optionalSkuGroups.stream()
                .map(optionalSkuGroup -> buildSkuGroup(activityCxt, optionalSkuGroup.getSkuItems(), false, optionalSkuGroup.getOptionalCount(), productCategories, dealAttrs, douhuResultModels))
                .filter(model -> model != null).collect(Collectors.toList());
    }

    private DealSkuGroupSequenceModel buildSkuGroup(ActivityCxt activityCxt, List<SkuItemDto> skuItems, boolean isMustGroup, int optionalNum, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs, List<DouhuResultModel> douhuResultModels) {
        if (CollectionUtils.isEmpty(skuItems)) {
            return null;
        }
        DealSkuGroupSequenceModel dealSkuGroupSequenceModel = new DealSkuGroupSequenceModel();
        //构造sku组优先级序号
        SkuListGroupPriorityVP<?> skuListGroupPriorityVP = findVPoint(activityCxt, SkuListGroupPriorityVP.CODE);
        Integer priorityId = skuListGroupPriorityVP.execute(activityCxt, SkuListGroupPriorityVP.Param.builder().skuItems(skuItems).isMustGroup(isMustGroup).optionalCount(optionalNum).productCategories(productCategories).build());
        dealSkuGroupSequenceModel.setPriority(priorityId == null ? Integer.MAX_VALUE : priorityId);
        DealSkuGroupModuleVO dealSkuGroupModuleVO = new DealSkuGroupModuleVO();
        //构造sku组标题
        SkuListGroupTitleVP<?> skuListGroupTitleVP = findVPoint(activityCxt, SkuListGroupTitleVP.CODE);
        String groupTitle = skuListGroupTitleVP.execute(activityCxt, SkuListGroupTitleVP.Param.builder().isMustGroup(isMustGroup).totalNum(CollectionUtils.size(skuItems)).optionalNum(optionalNum).build());
        dealSkuGroupModuleVO.setTitle(groupTitle);
        //构造sku列表
        List<DealSkuVO> dealSkuVOList = skuItems.stream()
                .map(sku -> buildDealSkuSequenceModel(activityCxt, sku, productCategories, dealAttrs)) //构造
                .filter(model -> model != null) //滤空
                .sorted(Comparator.comparingInt(DealSkuSequenceModel::getSequenceIndex)) //排序
                .map(model -> model.getSkuVO()) //提取
                .collect(Collectors.toList());
        //后置处理
        SkuListAfterProcessingVP<?> skuListAfterProcessingVP = findVPoint(activityCxt, SkuListAfterProcessingVP.CODE);
        if (skuListAfterProcessingVP != null) {
            dealSkuVOList = skuListAfterProcessingVP.execute(activityCxt, SkuListAfterProcessingVP.Param.builder().dealSkuVOS(dealSkuVOList).isMustGroup(isMustGroup).productCategories(productCategories).skuItems(skuItems).dealAttrs(dealAttrs).build());
        }
        dealSkuGroupModuleVO.setDealSkuList(dealSkuVOList);
        dealSkuGroupSequenceModel.setDealSkuGroupModuleVO(dealSkuGroupModuleVO);
        return dealSkuGroupSequenceModel;
    }

    private DealSkuSequenceModel buildDealSkuSequenceModel(ActivityCxt activityCxt, SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs) {
        DealSkuSequenceModel dealSkuSequenceModel = new DealSkuSequenceModel();
        //构造DealSkuVO
        DealSkuVO dealSkuVO = buildDealSkuVO(activityCxt, skuItemDto, productCategories, dealAttrs);
        if (dealSkuVO == null) {
            return null;
        }
        //构造sku优先级
        SkuPriorityVP<?> skuPriorityVP = findVPoint(activityCxt, SkuPriorityVP.CODE);
        Integer priority = skuPriorityVP.execute(activityCxt, SkuPriorityVP.Param.builder().skuItemDto(skuItemDto).productCategories(productCategories).build());
        dealSkuSequenceModel.setSequenceIndex(priority == null ? Integer.MAX_VALUE : priority);
        dealSkuSequenceModel.setSkuVO(dealSkuVO);
        return dealSkuSequenceModel;
    }

    private DealSkuVO buildDealSkuVO(ActivityCxt activityCxt, SkuItemDto skuItemDto, List<ProductSkuCategoryModel> productCategories, List<AttrM> dealAttrs) {
        if (skuItemDto == null) {
            return null;
        }
        DealSkuVO dealSkuVO = new DealSkuVO();
        //icon
        SkuIconVP<?> skuIconVP = findVPoint(activityCxt, SkuIconVP.CODE);
        String skuIcon = skuIconVP.execute(activityCxt, SkuIconVP.Param.builder().skuItemDto(skuItemDto).build());
        dealSkuVO.setIcon(skuIcon);
        //名称
        SkuTitleVP<?> skuTitleVP = findVPoint(activityCxt, SkuTitleVP.CODE);
        String skuTitle = skuTitleVP.execute(activityCxt, SkuTitleVP.Param.builder().skuTitle(skuItemDto.getName()).productCategories(productCategories).skuItemDto(skuItemDto).dealAttrs(dealAttrs).build());
        dealSkuVO.setTitle(skuTitle);
        //份数
        SkuCopiesVP<?> skuCopiesVP = findVPoint(activityCxt, SkuCopiesVP.CODE);
        String skuCopies = skuCopiesVP.execute(activityCxt, SkuCopiesVP.Param.builder().copies(skuItemDto.getCopies()).skuItemDto(skuItemDto).dealAttrs(dealAttrs).build());
        dealSkuVO.setCopies(skuCopies);
        //价格
        SkuPriceVP<?> skuPriceVP = findVPoint(activityCxt, SkuPriceVP.CODE);
        String skuPrice = skuPriceVP.execute(activityCxt, SkuPriceVP.Param.builder().price(skuItemDto.getMarketPrice()).build());
        dealSkuVO.setPrice(skuPrice);
        //属性列表
        SkuAttrListVP<?> skuAttrListVP = findVPoint(activityCxt, SkuAttrListVP.CODE);
        List<DealSkuItemVO> skuAttrList = skuAttrListVP.execute(activityCxt, SkuAttrListVP.Param.builder().skuItemDto(skuItemDto).dealAttrs(dealAttrs).productCategories(productCategories).build());
        dealSkuVO.setItems(skuAttrList);
        return dealSkuVO;
    }

    private boolean isValidDealDetail(DealDetailInfoModel detailModel) {
        if(detailModel == null || detailModel.getDealDetailDtoModel() == null || detailModel.getDealDetailDtoModel().getSkuUniStructuredDto() == null) {
            return false;
        }
        return true;
    }

}
