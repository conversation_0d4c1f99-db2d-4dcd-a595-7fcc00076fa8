package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.shelf.utils.DealSecKillUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.BtnTypeEnum;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfButtonVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemButtonVP;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.BtnNameTypeEnum;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfig;
import com.sankuai.dzviewscene.product.unifiedshelf.utils.ShopCategoryConfigUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.LinkedHashMap;

@VPointOption(name = "配置化的按钮",
        description = "根据配置生成商品按钮",
        code = "UnifiedShelfItemConfigureBtnOpt")
public class UnifiedShelfItemConfigureBtnOpt extends UnifiedShelfItemButtonVP<UnifiedShelfItemConfigureBtnOpt.Config> {

    private static final String DEFAULT_BUTTON_NAME = "抢购";

    @Override
    public ShelfButtonVO compute(ActivityCxt context, Param param, Config config) {
        // 根据类目配置来重写config
        rewriteByShopCategoryConfig(context, config);
        ShelfButtonVO button = new ShelfButtonVO();
        button.setJumpUrl(getJumpUrl(config, param.getProductM(), param.getItemJumpUrl()));
        button.setName(getButtonName(context, param.getProductM(), config));
        button.setType(getType(param.getProductM(), config));
        button.setDisable(getDisable(context, param.getProductM(), config));
        return button;
    }

    private void rewriteByShopCategoryConfig(ActivityCxt context, Config config) {
        ShopCategoryButtonConfig hitConfig = ShopCategoryConfigUtils.getHitConfig(context, config.getBackCategory2cfg(), ShopCategoryButtonConfig.class);
        if (hitConfig != null) {
            if (StringUtils.isNotEmpty(hitConfig.getName())) {
                config.setName(hitConfig.getName());
            }
            config.setEnableOrderUrl(hitConfig.isEnableOrderUrl());
        }
    }

    private boolean getDisable(ActivityCxt context, ProductM productM, Config config) {
        if(config.isDisable()){
            return true;
        }
        if(WarmUpStageEnum.isDuringWarmUpPeriod(productM)){
            return true;
        }
        return false;
    }

    private String getJumpUrl(Config config, ProductM productM, String rawUrl) {
        // 配置了使用提单页链接
        if (config.isEnableOrderUrl() && StringUtils.isNotEmpty(productM.getOrderUrl())) {
            return productM.getOrderUrl();
        }
        return StringUtils.isNotEmpty(config.getJumpUrl()) ? config.getJumpUrl() : rawUrl;
    }

    private int getType(ProductM productM, Config config) {
        //秒杀按钮
        if (DealSecKillUtils.isSecKillDeal(productM)) {
            return BtnTypeEnum.SKILL_BUTTON.getCode();
        }
        //配置了按钮类型
        BtnTypeEnum btnType = BtnTypeEnum.getByCode(config.getType());
        if (btnType != null) {
            return btnType.getCode();
        }
        return BtnTypeEnum.COMMON_BUTTON.getCode();
    }

    private String getButtonName(ActivityCxt context, ProductM productM, Config config) {
        //后续新增buttonName逻辑加到builder里
        String btnName = BtnNameBuilderFactory.getButtonName(context, productM, config.getBtnNameCfg());
        if (StringUtils.isNotEmpty(btnName)) {
            return btnName;
        }
        if (StringUtils.isNotEmpty(config.getName())) {
            return config.getName();
        }
        return DEFAULT_BUTTON_NAME;
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 场景路由配置
         */
        private ShopCategoryConfig backCategory2cfg;

        /**
         * 支持配置按钮类型
         * com.sankuai.dzviewscene.product.unifiedshelf.enums.BtnTypeEnum
         */
        private int type;

        /**
         * 按钮名称
         */
        private String name;

        /**
         * 按钮是否禁用
         */
        private boolean disable = false;

        /**
         * 是否填充为下单链接
         */
        private boolean enableOrderUrl;

        /**
         * 配置跳转链接
         */
        private String jumpUrl;

        /**
         * 配置规则相应按钮名称
         * key为规则条件名，即BtnNameType枚举的code,value为按钮名称，配置为空则使用对应默认名称
         *
         * @see BtnNameTypeEnum
         */
        private LinkedHashMap<String, String> btnNameCfg;

    }

    @Data
    public static class ShopCategoryButtonConfig {

        /**
         * 是否填充为下单链接
         */
        private boolean enableOrderUrl;
        private String name;
    }
}
