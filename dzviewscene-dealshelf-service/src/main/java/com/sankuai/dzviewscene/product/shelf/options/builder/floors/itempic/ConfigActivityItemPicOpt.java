package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPicVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import com.sankuai.dzviewscene.shelf.platform.utils.ProductActivityUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2023/4/18 1:25 PM
 **/
@VPointOption(name = "配置化活动角标头图",
        description = "配置化活动角标头图:按照配置筛设置活动角标,可配置隐藏某类活动角标",
        code = "ConfigActivityItemPicOpt")
public class ConfigActivityItemPicOpt extends ItemPicVP<ConfigActivityItemPicOpt.Config> {

    @Override
    public PicAreaVO compute(ActivityCxt context, Param param, Config config) {
        ProductM productM = param.getProductM();
        if (productM.getPicUrl() == null) return null;
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(toHttpsUrl(getPic(productM, config), config.picWidth, config.picHeight, PictureURLBuilders.ScaleType.Cut));
        dzPictureComponentVO.setAspectRadio(config.headerPicAspectRadio);
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(dzPictureComponentVO);
        List<ProductActivityM> activities = productM.getActivities();
        ProductActivityM filterActivity = filterByScene(activities, config);
        filterActivity = filterHidden(config.getNeedHiddenActivityType(), filterActivity);
        if (filterActivity != null) {
            picAreaVO.setFloatTags(buildTagFromActivityUrl(filterActivity, config, context));
        }
        return picAreaVO;
    }

    /**
     * @param productM
     * @param config 按照商品标签维度配置头图
     * @return
     */
    public String getPic(ProductM productM, Config config) {
        if(ProductMAttrUtils.superSpu(productM)
                && Objects.nonNull(productM.getSpuM()) && StringUtils.isNotBlank(productM.getSpuM().getHeadPic())){
            return productM.getSpuM().getHeadPic();
        }
        if (CollectionUtils.isNotEmpty(productM.getProductTagList()) && MapUtils.isNotEmpty(config.getTagId2HeadPicMap())) {
            List<String> tagIdList = productM.getProductTagList().stream().filter(Objects::nonNull)
                    .map(TagM::getId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            String pic = config.getTagId2HeadPicMap().entrySet().stream()
                    .filter(entry -> tagIdList.contains(entry.getKey())).map(Map.Entry::getValue).findFirst().orElse(null);
            return StringUtils.isBlank(pic) ? productM.getPicUrl() : pic;
        }
        return productM.getPicUrl();
    }

    // filterActivity.getUrl(),filterActivity.getUrlAspectRadio(),config.floatAspectRadio)
    private static List<FloatTagVO> buildTagFromActivityUrl(ProductActivityM filterActivity, Config config,
                                                            ActivityCxt activityCxt) {
        FloatConfig floatConfig = getFloatConfig(filterActivity.getShelfActivityType(), config.getFloatConfigs());


        DzPictureComponentVO floatTagPic = new DzPictureComponentVO();
        floatTagPic.setPicUrl(filterActivity.getUrl());

        floatTagPic.setAspectRadio(config.useFloatConfigs && floatConfig.getAspectRadio() > 0 ?
                floatConfig.getAspectRadio() : filterActivity.getUrlAspectRadio() < 0.01 ? 1 : filterActivity.getUrlAspectRadio());
        floatTagPic.setPicHeight(floatConfig.picHeight);
        //货架新样式，非包边横向拉满角标：新版：4.67，旧版4.4
        handleUnityStyle(config, activityCxt, floatTagPic);
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(floatTagPic);
        floatTagVO.setPosition(floatConfig.position);
        return Lists.newArrayList(floatTagVO);
    }

    private static void handleUnityStyle(Config config, ActivityCxt activityCxt, DzPictureComponentVO floatTagPic) {
        if (Objects.nonNull(config.getSkDefaultIconConfig()) && config.getSkDefaultIconConfig().getIconAspectRadio() > 0) {
            floatTagPic.setAspectRadio((config.getSkDefaultIconConfig().getIconAspectRadio()));
            return;
        }
        Cat.logEvent("ExpHarnessing", "com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.ConfigActivityItemPicOpt.handleUnityStyle");
        List<DouHuM> douHuList = activityCxt.getSource(ShelfDouHuFetcher.CODE);
        Optional<IconConfig> iconConfig = (Optional<IconConfig>) DouHuUtils.getConfigByDouHu(douHuList, config.iconConfigMap);
        if (iconConfig.isPresent()) {
            floatTagPic.setAspectRadio(iconConfig.get().getIconAspectRadio());
        }
    }

    private static FloatConfig getFloatConfig(Integer shelfActivityType, List<FloatConfig> floatConfigs) {
        if (CollectionUtils.isEmpty(floatConfigs) || shelfActivityType == null) {
            return getDefaultFloatConfig();
        }
        FloatConfig config = floatConfigs.stream().filter(floatConfig ->
                Objects.equals(shelfActivityType, floatConfig.getActivityType())).findFirst().orElse(null);
        if (config != null) {
            return config;
        }
        // -1 是配置的默认
        return floatConfigs.stream().filter(floatConfig ->
                Objects.equals(-1, floatConfig.getActivityType())).findFirst().orElse(getDefaultFloatConfig());
    }

    private static FloatConfig getDefaultFloatConfig() {
        FloatConfig config = new FloatConfig();
        config.setActivityType(-1);
        config.setPosition(4);
        config.setAspectRadio(4.4);
        return config;
    }

    private ProductActivityM filterByScene(List<ProductActivityM> activities, Config config) {
        ProductActivityM filterActivity = ProductActivityUtils.filterFirst(activities);
        if (config.isFilterByActivityScene() && CollectionUtils.isNotEmpty(config.getFilterActivitySceneCode())) {
            filterActivity = ProductActivityUtils.filterFirstActivityByScene(activities, config.getFilterActivitySceneCode(), config.isFilterEqualType());
        }
        return filterActivity;
    }

    private ProductActivityM filterHidden(List<Integer> needHiddenActivityType, ProductActivityM activityM) {
        if (activityM == null || StringUtils.isEmpty(activityM.getUrl())) {
            return null;
        }
        if (CollectionUtils.isEmpty(needHiddenActivityType)) {
            return activityM;
        }
        int type = Optional.ofNullable(activityM.getShelfActivityType()).orElse(0);
        return needHiddenActivityType.contains(type) ? null : activityM;
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 标签-头图map
         */
        private Map<String, String> tagId2HeadPicMap;
        private double headerPicAspectRadio = 1;

        private double floatAspectRadio = 1;

        private Map<String, IconConfig> iconConfigMap;

        /**
         * 推全sk默认icon配置
         */
        private IconConfig skDefaultIconConfig;

        private int picWidth = 300;

        private int picHeight = 300;

        /**
         * 是否根据filterActivitySceneCode+filterEqualType筛选活动
         */
        private boolean filterByActivityScene;

        /**
         * 筛选sceneCode的活动
         */
        private List<Integer> filterActivitySceneCode;

        /**
         * 筛选EqualType
         */
        private boolean filterEqualType;

        /**
         * 需要隐藏角标的活动
         */
        private List<Integer> needHiddenActivityType;

        /**
         * 营销标签样式
         */
        private List<FloatConfig> floatConfigs;

        private boolean useFloatConfigs = false;
    }

    @Data
    public static class FloatConfig {
        private int activityType;
        private double aspectRadio;
        private int position;
        private int picHeight;
        private int picWidth;
    }

    @Data
    public static class IconConfig {

        private double iconAspectRadio;
    }
}
