package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemshowtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemShowTypeVP;
import com.sankuai.dzviewscene.product.utils.LifeWashUtils;
import com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums;
import lombok.Data;

import java.util.Map;

/***
 * <AUTHOR>
 */
@VPointOption(name = "换洗节-商品展示样式(showtype=3)",
        description = "支持配置控制换洗节展示样式,当满足换洗节样式时返回换洗节展示样式(showtype=3)，否则返回兜底样式（showtype=1）",
        code = "LifeWashItemShowTypeOpt")
public class LifeWashItemShowTypeOpt extends ItemShowTypeVP<LifeWashItemShowTypeOpt.Config> {

    @Override
    public Integer compute(ActivityCxt activityCxt, Param param, Config config) {
        //1.命中换洗节新样式
        if (LifeWashUtils.hitWashNewShowType(param.getProductM())) {
            return DzItemShowTypeEnums.HUGE_SAVE_MONEY.getType();
        }
        //2.开关关闭或未命中换洗节，走默认展示样式
        return LifeWashUtils.defItemShowTypeOptCompute(activityCxt, param, config);
    }

    @VPointCfg
    @Data
    public static class Config {
        /**
         * 商品显示样式类型
         * {@link com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums}
         */
        private int itemShowType;
        /**
         * 说明：低版本如果传了折行样式，前端货架展示会直接崩溃，所以需要限制低版本；一般这个配置不用改；
         * 展示团单标题折行的点评最低版本
         */
        private String dpLimitVersion = "10.47.0";
        /**
         * 展示团单标题折行的美团最低版本
         */
        private String mtLimitVersion = "11.10.400";
        /**
         * 斗斛实验策略和商品显示样式类型 {@link com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums}映射关系
         */
        private Map<String, Integer> douHuSk2ShowType;
    }


}
