package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/11
 */
@VPointOption(name = "空-返回 null",
        description = "",
        code = "NullItemPriceBottomTagsOpt")
public class NullItemPriceBottomTagsOpt extends ItemPriceBottomTagsVP<Void> {
    @Override
    public List<DzTagVO> compute(ActivityCxt context, Param param, Void config) {
        return null;
    }
}
