package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.postpadding;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.dzviewscene.product.ability.douhu.DouHuAbility;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class FixedPriceSpuPostPaddingHandler implements PostPaddingHandler {

    @Override
    public CompletableFuture<ProductGroupM> postPadding(ActivityCxt ctx,
                                                        ProductGroupM productGroupM,
                                                        Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.postpadding.FixedPriceSpuPostPaddingHandler.postPadding(ActivityCxt,ProductGroupM,Map)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return CompletableFuture.completedFuture(productGroupM);
        }
        if (!checkExpAllowShow(ctx, params)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        try {
            List<Long> productIdOrder = JsonCodec.converseList(ParamsUtil.getStringSafely(params, "productIdOrder"), Long.class);
            List<ProductM> resultProducts = productGroupM.getProducts().stream()
                    .filter(productM -> BooleanUtils.isTrue(productM.getAvailable()))
                    .sorted((product1, product2) -> sort(product1, product2, productIdOrder))
                    .collect(Collectors.toList());
            productGroupM.setProducts(resultProducts);
        } catch (Exception e) {

        }

        return CompletableFuture.completedFuture(productGroupM);
    }

    private boolean checkExpAllowShow(ActivityCxt ctx, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.postpadding.FixedPriceSpuPostPaddingHandler.checkExpAllowShow(ActivityCxt,Map)");
        List<DouHuResponse> douHuResponses = ctx.getSource(DouHuAbility.CODE);
        //为空默认展示
        if (CollectionUtils.isEmpty(douHuResponses)) {
            return true;
        }
        int platform = ParamsUtil.getIntSafely(ctx, PmfConstants.Params.platform);
        String expId = ParamsUtil.getStringSafely(params, platform == VCPlatformEnum.DP.getType() ? PmfConstants.Params.dpExpId : PmfConstants.Params.mtExpId);
        String showStrategy = ParamsUtil.getStringSafely(params, platform == VCPlatformEnum.DP.getType() ?PmfConstants.Params.dpShowStrategy : PmfConstants.Params.mtShowStrategy);
        DouHuResponse douHuResponse = douHuResponses.stream().filter(e -> expId.equals(e.getExpId())).findFirst().orElse(null);
        return douHuResponse == null || showStrategy.equals(douHuResponse.getSk());
    }

    private int sort(ProductM product1, ProductM product2, List<Long> productIdOrder) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.postpadding.FixedPriceSpuPostPaddingHandler.sort(ProductM,ProductM,List)");
        if (!productIdOrder.contains(product1.getId().getId())) {
            return 1;
        }
        if (!productIdOrder.contains(product2.getId().getId())) {
            return -1;
        }
        int index1 = productIdOrder.indexOf(product1.getId().getId());
        int index2 = productIdOrder.indexOf(product2.getId().getId());
        return index1 - index2;
    }

}
