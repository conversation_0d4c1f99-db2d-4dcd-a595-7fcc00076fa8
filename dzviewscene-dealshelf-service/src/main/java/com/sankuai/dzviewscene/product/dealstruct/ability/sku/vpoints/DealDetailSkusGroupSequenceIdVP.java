package com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.DealDetailSkuProductsGroupsBuilder;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/26 5:39 下午
 */
@Deprecated
@VPoint(name = "团购详情sku货列表组顺序号变化点", description = "团购详情sku货列表组顺序号变化点，支持配置",code = DealDetailSkusGroupSequenceIdVP.CODE, ability = DealDetailSkuProductsGroupsBuilder.CODE)
public abstract class DealDetailSkusGroupSequenceIdVP<T> extends PmfVPoint<Integer, DealDetailSkusGroupSequenceIdVP.Param, T> {

    public static final String CODE = "DealDetailSkusGroupSequenceIdVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private List<SkuItemDto> skuItems;
        private boolean isMustGroup;
        private boolean optionalCount;
        private List<ProductSkuCategoryModel> productCategories;
    }

}
