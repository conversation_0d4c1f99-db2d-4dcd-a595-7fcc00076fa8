package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.strategy;

import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;

import java.util.List;

public interface SpecialTagStrategy {

    /**
     * 策略名，需要保证唯一性
     * @return
     */
    String getName();

    /**
     * 策略描述
     * @return
     */
    String getStrategyDesc();

    /**
     * @param req
     * @return 构造的标签
     */
    List<ShelfTagVO> build(SpecialTagBuildReq req);
}
