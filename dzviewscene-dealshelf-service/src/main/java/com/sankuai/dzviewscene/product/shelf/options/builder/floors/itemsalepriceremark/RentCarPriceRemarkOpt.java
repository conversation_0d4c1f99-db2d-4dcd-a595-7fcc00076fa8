package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceremark;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePriceRemarkVP;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@VPointOption(name = "租车货架",
        description = "返回租车时长单位",
        code = "RentCarPriceRemarkOpt")
public class RentCarPriceRemarkOpt extends ItemSalePriceRemarkVP<Void> {
    private static final String RENT_DURATION_KEY = "rent_duration_1512";

    private static final Map<String, String> START_WITH_ONE_UNIT = new HashMap<>();

    static {
        START_WITH_ONE_UNIT.put("1小时", "小时");
        START_WITH_ONE_UNIT.put("1天", "天");
        START_WITH_ONE_UNIT.put("1周", "周");
        START_WITH_ONE_UNIT.put("1月", "月");
        START_WITH_ONE_UNIT.put("1年", "年");
    }

    @Override
    public String compute(ActivityCxt activityCxt, Param param, Void unused) {
        String duration = param.getProductM().getAttr(RENT_DURATION_KEY);
        if (StringUtils.isEmpty(duration)) {
            return null;
        }
        return durationConvert(duration);
    }

    private String durationConvert(String rawDuration) {
        String duration = rawDuration;
        if (START_WITH_ONE_UNIT.containsKey(rawDuration)) {
            duration = START_WITH_ONE_UNIT.get(rawDuration);
        }
        return String.format("/%s", duration);
    }
}
