package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.DealSkuListModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.OverNightModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2023/9/13 17:05
 */
@VPointOption(name = "足疗组合及多选一套餐团详sku列表组变化点V2", description = "足疗组合及多选一套餐团详sku列表组变化点V2", code = MassageCombinationDealModuleV2Opt.CODE)
public class MassageCombinationDealModuleV2Opt extends AbstractFootMessageModuleOpt<MassageCombinationDealModuleV2Opt.Config> {

    public static final String CODE = "MassageCombinationDealModuleV2Opt";

    @Override
    public List<DealDetailSkuListModuleGroupModel> compute(ActivityCxt context, Param param, Config config) {
        List<DealDetailSkuListModuleGroupModel> result = Lists.newArrayList();
        boolean hitNewIcon = hitNewIcon(param.getDouhuResultModels(), config.getExpIds());
        DealDetailInfoModel dealDetailInfoModel = param.getDealDetailInfoModel();
        // 构造服务流程模块
        result.add(buildServiceFlowModule(dealDetailInfoModel));
        // 构造免费服务模块 过夜
        result.add(buildFreeServiceModule(dealDetailInfoModel.getDealAttrs(), hitNewIcon));
        // 构造付费服务模块 过夜
        result.add(buildPayServiceModule(dealDetailInfoModel.getDealAttrs(), hitNewIcon));
        context.addParam(QueryFetcher.Params.massageCombination, true);
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 构造服务流程模块
     */
    private DealDetailSkuListModuleGroupModel buildServiceFlowModule(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuGroupModuleVO> list = buildDealSkuGroupModuleList(dealDetailInfoModel);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        groupModel.setGroupName("服务流程模块");
        groupModel.setGroupTitle(TimesDealUtil.isTimesDeal(dealDetailInfoModel) ? TimesDealUtil.getTimesTitle(dealDetailInfoModel.getDealAttrs()) : null);
        groupModel.setDealSkuGroupModuleVOS(list);
        return groupModel;
    }

    private DealDetailSkuListModuleGroupModel buildFreeServiceModule(List<AttrM> dealAttrs, boolean hitNewIcon) {
        List<DealSkuVO> freeServices = buildFreeServices(dealAttrs, hitNewIcon);
        if (CollectionUtils.isEmpty(freeServices)) {
            return null;
        }
        return buildDealDetailSkuListModuleGroupModel("免费服务模块", "服务加码不加价", freeServices);
    }

    private List<DealSkuVO> buildFreeServices(List<AttrM> dealAttrs, boolean hitNewIcon) {
        List<DealSkuVO> result = Lists.newArrayList();
        DealSkuVO overNight = OverNightModuleUtils.parseFreeOverNightSkuModule(dealAttrs, hitNewIcon);
        if (overNight == null) {
            return result;
        }
        // 过夜服务
        result.add(overNight);
        return result;
    }

    private List<DealSkuGroupModuleVO> buildDealSkuGroupModuleList(DealDetailInfoModel dealDetailInfoModel) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        // 全部可享
        List<SkuItemDto> mustSkuItemList = DealSkuListModuleUtils.extractMustSkuItemList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(mustSkuItemList)) {
            result.add(buildAllAvailableGroup(dealDetailInfoModel, mustSkuItemList));
        }
        // 服务项目m选n
        List<OptionalSkuItemsGroupDto> optionalSkuItemList = DealSkuListModuleUtils.extractOptionalSkuItemsGroupList(dealDetailInfoModel);
        if (CollectionUtils.isNotEmpty(optionalSkuItemList)) {
            result.addAll(buildEitherOrGroupList(dealDetailInfoModel, optionalSkuItemList));
        }
        return result;
    }

    private DealSkuGroupModuleVO buildAllAvailableGroup(DealDetailInfoModel dealDetailInfoModel, List<SkuItemDto> mustSkuItemList) {
        DealSkuGroupModuleVO allAvailableGroup = new DealSkuGroupModuleVO();
        allAvailableGroup.setDealSkuList(buildDealSkuList(mustSkuItemList, dealDetailInfoModel.getProductCategories()));
        return allAvailableGroup;
    }

    private List<DealSkuGroupModuleVO> buildEitherOrGroupList(DealDetailInfoModel dealDetailInfoModel, List<OptionalSkuItemsGroupDto> optionalSkuItemList) {
        List<DealSkuGroupModuleVO> result = Lists.newArrayList();
        for (OptionalSkuItemsGroupDto optionalSkuItemsGroupDto : optionalSkuItemList) {
            List<SkuItemDto> skuItemList = optionalSkuItemsGroupDto.getSkuItems();
            if (CollectionUtils.isNotEmpty(skuItemList)) {
                DealSkuGroupModuleVO eitherOrGroup = new DealSkuGroupModuleVO();
                String title = String.format("以下%d选%d", skuItemList.size(), optionalSkuItemsGroupDto.getOptionalCount());
                eitherOrGroup.setTitle(title);
                eitherOrGroup.setTitleStyle(0);
                eitherOrGroup.setDealSkuList(buildDealSkuList(skuItemList, dealDetailInfoModel.getProductCategories()));
                result.add(eitherOrGroup);
            }
        }
        return result;
    }

    private List<DealSkuVO> buildDealSkuList(List<SkuItemDto> skuItemList, List<ProductSkuCategoryModel> productCategories) {
        List<DealSkuVO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(skuItemList)) {
            return result;
        }
        for (SkuItemDto skuItemDto : skuItemList) {
            DealSkuVO dealSkuVO = new DealSkuVO();
            dealSkuVO.setTitle(getServiceFlowSkuName2(skuItemDto.getAttrItems(), getCombinationServiceType(skuItemDto.getProductCategory(), productCategories)));
            dealSkuVO.setSubTitle(getSkuSubTitle(skuItemDto));
            dealSkuVO.setPrice(skuItemDto.getMarketPrice() == null ? null : "￥" + skuItemDto.getMarketPrice().stripTrailingZeros().toPlainString());
            dealSkuVO.setItems(getServiceItems2(skuItemDto.getAttrItems()));
            result.add(dealSkuVO);
        }
        return result;
    }

    @Data
    @VPointCfg
    public static class Config {

        private List<String> expIds;

    }


}
