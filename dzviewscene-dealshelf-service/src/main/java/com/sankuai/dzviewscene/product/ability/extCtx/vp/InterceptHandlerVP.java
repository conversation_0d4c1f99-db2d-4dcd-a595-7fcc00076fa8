package com.sankuai.dzviewscene.product.ability.extCtx.vp;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.ability.extCtx.PreHandlerContextAbility;
import lombok.Builder;
import lombok.Data;


@VPoint(name = "拦截预处理变化点", description = "拦截预处理变化点点", code = InterceptHandlerVP.CODE, ability = PreHandlerContextAbility.CODE)
public abstract class InterceptHandlerVP<T> extends PmfVPoint<Boolean, InterceptHandlerVP.Param, T> {

    public static final String CODE = "InterceptHandlerVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 上下文信息
         */
        private ActivityCxt ctx;

        private int platform;
    }
}
