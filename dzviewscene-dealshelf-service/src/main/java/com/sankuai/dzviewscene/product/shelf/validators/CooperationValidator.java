package com.sankuai.dzviewscene.product.shelf.validators;


import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.annotation.Validator;
import com.sankuai.athena.viewscene.framework.core.IActivityValidator;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;

import javax.annotation.Resource;

/**
 * created by zhangzhiyuan04 in 2021/8/26
 */
@Validator(code = "cooperationValidator", name = "合作权限校验器", activities = DealShelfActivity.CODE)
public class CooperationValidator implements IActivityValidator {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public boolean isValid(ActivityCxt activityCxt) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.product.shelf.validators.CooperationValidator.isValid(com.sankuai.athena.viewscene.framework.ActivityCxt)");
        // poiMigrate
        if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.CooperationValidator.getScene())) {
            return compositeAtomService.getIsCooperativeResponseL(ParamsUtil.getLongSafely(activityCxt.getParameters(), ShelfActivityConstants.Params.dpPoiIdL)).thenApply(isCooperativeResponse -> {
                if (isCooperativeResponse == null || !isCooperativeResponse.isSuccess() || isCooperativeResponse.getContent() == null) {
                    return false;
                }
                return isCooperativeResponse.getContent();
            }).join();
        } else {
            // 原逻辑
            return compositeAtomService.getIsCooperativeResponse(ParamsUtil.getIntSafely(activityCxt.getParameters(), ShelfActivityConstants.Params.dpPoiId)).thenApply(isCooperativeResponse -> {
                if (isCooperativeResponse == null || !isCooperativeResponse.isSuccess() || isCooperativeResponse.getContent() == null) {
                    return false;
                }
                return isCooperativeResponse.getContent();
            }).join();
        }
    }
}
