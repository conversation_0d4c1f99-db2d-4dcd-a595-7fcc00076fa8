package com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.DealListBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 * @since 2023/4/6 00:07
 */
@VPoint(name = "价格下方标签", description = "价格下方标签", code = ProductPriceBottomTagVP.CODE, ability = DealListBuilder.CODE)
public abstract class ProductPriceBottomTagVP<T> extends PmfVPoint<List<DzTagVO>, ProductPriceBottomTagVP.Param, T> {
    public static final String CODE = "ProductPriceBottomTagVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        /**
         * 到手价
         */
        private String salePrice;

        private CardM cardM;

        private int platform;
    }
}
