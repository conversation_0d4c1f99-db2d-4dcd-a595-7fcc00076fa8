package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.UnifiedShelfFilterBuilder;
import com.sankuai.dzviewscene.product.utils.LionObjectManagerUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@VPoint(name = "筛选-extra", description = "筛选-扩展信息", code = UnifiedShelfExtraVP.CODE, ability = UnifiedShelfFilterBuilder.CODE)
public abstract class UnifiedShelfExtraVP<T> extends PmfVPoint<String, UnifiedShelfExtraVP.Param, T> {

    public static final String CODE = "UnifiedShelfExtraVP";

    @Data
    @Builder
    @VPointParam
    public static class Param {
        /**
         * 筛选按钮
         */
        private FilterBtnM filterBtnM;
    }

    public void extractMustParameters(ActivityCxt context, JSONObject extra) {
        LionObjectManagerUtils.getFilterExtraRequired().forEach(key -> {
            String value = ContextParamBuildUtils.getParamFromExtraMap(context, key, "");
            if (StringUtils.isNotBlank(value)) {
                extra.put(key, value);
            }
        });
    }
}
