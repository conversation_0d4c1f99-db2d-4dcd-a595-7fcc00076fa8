package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemcceanlabs;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemOceanLabsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;

import java.util.HashMap;
import java.util.Map;

@VPointOption(name = "穿戴甲货架埋点", description = "穿戴甲货架埋点", code = "PressOnNailItemOceanLabsOpt")
public class PressOnNailItemOceanLabsOpt extends ItemOceanLabsVP<Void> {
    @Override
    public String compute(ActivityCxt context, Param param, Void unused) {
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("customize_array", Lists.newArrayList(PressOnNailUtils.buildItemOceanMap(context, param.getProductM())));
        paddingDiscountAndComparePriceLab(oceanMap, param.getProductM());
        paddingRankSourceLab(oceanMap, param.getProductM());
        return JsonCodec.encode(oceanMap);
    }
}
