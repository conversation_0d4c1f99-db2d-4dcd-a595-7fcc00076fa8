package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp;

import com.sankuai.athena.viewscene.framework.pmf.annotations.VPoint;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointParam;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfVPoint;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.UnifiedProductAreaBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@VPoint(name = "商品优惠标签", description = "商品优惠标签，在价格行展示", code = UnifiedShelfItemPromoTagVP.CODE, ability = UnifiedProductAreaBuilder.CODE)
public abstract class UnifiedShelfItemPromoTagVP<T> extends PmfVPoint<List<RichLabelModel>, UnifiedShelfItemPromoTagVP.Param, T> {

    public static final String CODE = "UnifiedShelfItemPromoTagVP";


    @Data
    @Builder
    @VPointParam
    public static class Param {
        private ProductM productM;

        private String salePrice;
    }
}
