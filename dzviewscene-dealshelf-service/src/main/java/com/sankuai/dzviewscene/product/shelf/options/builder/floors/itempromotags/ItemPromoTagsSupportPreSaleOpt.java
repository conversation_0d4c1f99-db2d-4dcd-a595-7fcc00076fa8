package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

@VPointOption(name = "返回立减优惠详情,支持预售",
        description = "返回立减优惠详情,支持预售",
        code = "PromoTagsSupportPreSaleOpt")
public class ItemPromoTagsSupportPreSaleOpt extends ItemPromoTagsVP<ItemPromoTagsSupportPreSaleOpt.Config> {
    @Override
    public List<DzPromoVO> compute(ActivityCxt context, Param param, Config config) {
        if (config.isEnablePreSale() && PreSaleUtils.isPreSaleDeal(param.getProductM())) {
            return getPreSalePromo(param, config);
        }
        return getDefPromo(param);
    }

    private List<DzPromoVO> getPreSalePromo(Param param, Config config) {
        if (!config.isPreSaleShowPromo()) {
            return Collections.emptyList();
        }
        ProductM productM = param.getProductM();
        if (CollectionUtils.isEmpty(productM.getPromoPrices())) {
            return Collections.emptyList();
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return Collections.emptyList();
        }
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setName(getPromoTag(config, productM, productPromoPriceM));
        dzPromoVO.setPosition(config.getPosition());
        dzPromoVO.setDetail(DzPromoUtils.buildPromoDetail(productPromoPriceM, DzPromoUtils.POP_TYPE_PRE_SALE));
        if (!config.isPreSaleHasDetail() && PreSaleUtils.isPreSaleDeal(productM)) {
            dzPromoVO.setDetail(null);
        }
        dzPromoVO.setPrePic(getPreSalePic(param.getProductM(), config));
        return Lists.newArrayList(dzPromoVO);
    }

    private DzPictureComponentVO getPreSalePic(ProductM productM, Config config) {
        if (!PreSaleUtils.isPreSaleDeal(productM)) {
            return null;
        }
        if (StringUtils.isBlank(config.getPrePicUrl())) {
            return null;
        }
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setPicUrl(config.getPrePicUrl());
        pic.setPicHeight(config.getPrePicHeight());
        pic.setAspectRadio(config.getPrePicAspectRadio());
        return pic;
    }

    private String getPromoTag(Config config, ProductM productM, ProductPromoPriceM productPromoPriceM) {
        if (config.isEnablePreSale() && PreSaleUtils.isPreSaleDeal(productM) && productPromoPriceM.getTotalPromoPrice() != null) {
            //预售需要自行修改展示优惠标签：提前买省¥xx，后续如果再铺到其他类目，
            if (StringUtils.isBlank(config.getPreSalePromoTemplate())) {
                return productPromoPriceM.getPromoTag();
            }
            return String.format(config.getPreSalePromoTemplate(), productPromoPriceM.getTotalPromoPrice().stripTrailingZeros().toPlainString());
        }
        return productPromoPriceM.getPromoTag();
    }

    @VPointCfg
    @Data
    public static class Config {

        /**
         * 是否启动预售标签逻辑
         * 扩充到其他类目后，该配置可下线作为通用流程
         */
        private boolean enablePreSale = true;

        /**
         * 预售商品的优惠显示模板
         * 如果为空，则使用价格计算服务返回的结果
         */
        private String preSalePromoTemplate;

        /**
         * 预售单是否需要detail
         */
        private boolean preSaleHasDetail = false;

        /**
         * 超值预售图片
         */
        private String prePicUrl = "https://p0.meituan.net/travelcube/ef7b23a4d100f4065dd4ab2544d55e732885.png";

        private int prePicHeight = 32;

        private double prePicAspectRadio = 3.28125;

        /**
         * 预售单显示优惠信息
         * 一般用于部分场景预售单需要放到价格下方展示优惠（false）
         */
        private boolean preSaleShowPromo = true;

        private int position = 2;
    }
}
