package com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;

import java.util.Collections;

@ExtPointInstance(name = "宠物实物电商货架筛选组件扩展点")
public class PetMallFilterFetcherExt extends FilterFetcherExtAdapter {

    @Override
    public void endIntercept(ActivityContext activityContext, String groupName, FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.petmall.filter.PetMallFilterFetcherExt.endIntercept(ActivityContext,String,FilterM)");
        if (CollectionUtils.isEmpty(filterM.getFilters())) {
            return;
        }
        for (FilterBtnM filterBtnM : filterM.getFilters()) {
            if (CollectionUtils.isEmpty(filterBtnM.getChildren())) {
                continue;
            }
            filterBtnM.setFilterId(filterBtnM.getChildren()
                    .stream()
                    .filter(tag -> "全部".equals(tag.getTitle()))
                    .findFirst()
                    .map(FilterBtnM::getFilterId)
                    .orElse(0L));
            filterBtnM.setChildren(Collections.emptyList());
        }
    }

}
