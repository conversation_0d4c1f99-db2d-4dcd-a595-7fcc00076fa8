package com.sankuai.dzviewscene.shelf.platform.detail;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.AbstractSceneIdentifier;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.SceneIdentifier;
import com.sankuai.dzviewscene.shelf.framework.core.IScenePredicate;
import com.sankuai.dzviewscene.shelf.platform.common.scene.PredicateBuilder;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * Created by liweilong06 on 2020/9/28.
 */
@SceneIdentifier(activityCode = ProductDetailActivity.ACTIVITY_PRODUCT_DETAIL_CODE, name = "商品详情场景识别器")
public class ProductDetailActivitySceneIdentifier extends AbstractSceneIdentifier {

    @Resource
    private PredicateBuilder predicateBuilder;

    //中医团单二级类目ID列表
    private static final List<Integer> DENTAL_DEAL_CATEGORY_IDS = Lists.newArrayList(506);

    @Override
    protected void fillPredicates(Map<String, IScenePredicate> scenePredicates) {
        scenePredicates.put("beauty_productdetail_structmodule", activityContext -> false);
        scenePredicates.put("baby_productdetail_hotdeal", activityContext -> false);
        scenePredicates.put("joy_detail_pinpool_bar", activityContext -> false);
        scenePredicates.put("inactive_roleplay_standardproduct_detail", activityContext -> false);
        scenePredicates.put("dental_productdetail_structmodule", activityContext -> isSpecialDeal(activityContext, DENTAL_DEAL_CATEGORY_IDS));
        scenePredicates.put("hair_perm_dye_productdetail_structmodule", activityContext -> false);
        //宠物疫苗团详模块
        scenePredicates.put("pet_vaccine_deal_detail_structmodule", activityContext -> false);
        scenePredicates.putAll(predicateBuilder.build(ProductDetailActivity.ACTIVITY_PRODUCT_DETAIL_CODE));
    }

    private boolean isSpecialDeal(ActivityContext activityContext, List<Integer> dealCategoryIds) {
        int dealCategoryId = ParamsUtil.getIntSafely(activityContext, ProductDetailActivityConstants.Params.dealCategoryId);
        if(dealCategoryIds.contains(dealCategoryId)) {
            return true;
        }
        return false;
    }
}
