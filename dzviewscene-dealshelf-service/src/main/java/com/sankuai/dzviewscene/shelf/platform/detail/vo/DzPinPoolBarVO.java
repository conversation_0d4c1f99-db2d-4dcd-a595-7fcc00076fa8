package com.sankuai.dzviewscene.shelf.platform.detail.vo;

import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 到综拼场小黄条VO
 */
@Data
public class DzPinPoolBarVO implements Serializable {

    /**
     * 当前进度信息
     */
    private List<RichLabelVO> currentProgressDesc;

    private String currentProgressStr;

    /**
     * 跳转文案
     */
    private String jumpText;

    /**
     * 浮层参数
     */
    private Map<String,Object> popParams;
}
