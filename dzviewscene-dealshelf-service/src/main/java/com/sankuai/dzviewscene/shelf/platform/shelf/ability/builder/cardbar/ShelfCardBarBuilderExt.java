package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.cardbar;

import com.sankuai.dzviewscene.shelf.business.shelf.common.context.JoyCardShelfContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/8/26.
 */
@ExtPointInstance(name = "默认卡片构造区扩展点实现")
public class ShelfCardBarBuilderExt implements CardBarBuilderExt {

    @Override
    public String jumpUrl(ActivityContext activityContext) {
        return getJoyCardValue(activityContext, "jumpUrl");
    }

    @Override
    public String title(ActivityContext activityContext) {
        return getJoyCardValue(activityContext, "title");
    }

    @Override
    public String iconUrl(ActivityContext activityContext) {
        return getJoyCardValue(activityContext, "iconUrl");
    }

    private String getJoyCardValue(ActivityContext activityContext, String keyName) {
        CompletableFuture<Map<String, String>> extContextFuture = activityContext.getExtContext(JoyCardShelfContext.CONTEXT_KEY);
        if (extContextFuture == null) {
            return null;
        }
        Map<String, String> extContext = extContextFuture.join();
        if (MapUtils.isEmpty(extContext)) {
            return null;
        }
        return extContext.get(keyName);
    }
}
