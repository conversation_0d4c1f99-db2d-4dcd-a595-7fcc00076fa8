package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.tpfun.product.api.sku.common.enums.ProductStatusEnum;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.dianping.tpfun.product.api.sku.request.QueryShopProductRequest;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 泛商品召回能力, 返回商品模型列表, 商品模型只会填充ID和shopIds, shopIds从参数得来, 比如要召回门店1,2,3的商品, 那么1,2,3会被反向所引导所召回的商品模型中
 * <p>
 * Created by float.lu on 2020/9/1.
 */
@Component
public class ShopGeneralProductHandler implements GroupQueryHandler {
    @Resource
    private CompositeAtomService compositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.allow.queryinvalid", defaultValue = "false")
    private boolean allowQueryInvalid;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityContext activityContext, String groupName, Map<String, Object> params) {
        Integer productType = (Integer) params.get(QueryFetcher.Params.productType);
        Long spuType = (Long) params.get(QueryFetcher.Params.spuType);
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        boolean filterInvalid = checkIsFilterInvalid(params);

        if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.ShopGeneralProductHandler.getScene())) {
            List<Long> shopIds = buildShopIdsPoiMigrate(activityContext, platform, groupName);
            if (CollectionUtils.isEmpty(shopIds)) {
                return CompletableFuture.completedFuture(new ProductGroupM());
            }

            List<Integer> excludeProductIds = (List<Integer>) params.get(QueryFetcher.Params.excludeProductIds);
            return fetchShopProductsCompletableFuturePoiMigrate(shopIds, platform, productType, spuType, filterInvalid).thenApply(shop2Products -> {
                Map<Long, List<Product>> shop2ValidProducts = filterInvalidProductsPoiMigrate(shop2Products);
                if (MapUtils.isEmpty(shop2ValidProducts)) {
                    return null;
                }
                return buildProductGroupPoiMigrate(shop2ValidProducts, excludeProductIds);
            });
        } else {
            // 原逻辑
            List<Integer> shopIds = buildShopIds(activityContext, platform, groupName);
            if (CollectionUtils.isEmpty(shopIds)) {
                return CompletableFuture.completedFuture(new ProductGroupM());
            }

            List<Integer> excludeProductIds = (List<Integer>) params.get(QueryFetcher.Params.excludeProductIds);
            return fetchShopProductsCompletableFuture(shopIds, platform, productType, spuType, filterInvalid).thenApply(shop2Products -> {
                Map<Integer, List<Product>> shop2ValidProducts = filterInvalidProducts(shop2Products);
                if (MapUtils.isEmpty(shop2ValidProducts)) {
                    return null;
                }
                return buildProductGroup(shop2ValidProducts, excludeProductIds);
            });
        }
    }

    private Map<Integer, List<Product>> filterInvalidProducts(Map<Integer, List<Product>> shop2Products) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.filterInvalidProducts(java.util.Map)");
        if (MapUtils.isEmpty(shop2Products)) {
            return Maps.newHashMap();
        }
        Map<Integer, List<Product>> shop2ValidProducts = Maps.newHashMap();
        for (Map.Entry<Integer, List<Product>> entry : shop2Products.entrySet()) {
            List<Product> validProducts = entry.getValue().stream().filter(this::checkIsProductValid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(validProducts)) {
                continue;
            }
            shop2ValidProducts.put(entry.getKey(), validProducts);
        }
        return shop2ValidProducts;
    }

    private Map<Long, List<Product>> filterInvalidProductsPoiMigrate(Map<Long, List<Product>> shop2Products) {
        if (MapUtils.isEmpty(shop2Products)) {
            return Maps.newHashMap();
        }
        Map<Long, List<Product>> shop2ValidProducts = Maps.newHashMap();
        for (Map.Entry<Long, List<Product>> entry : shop2Products.entrySet()) {
            List<Product> validProducts = entry.getValue().stream().filter(this::checkIsProductValid).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(validProducts)) {
                continue;
            }
            shop2ValidProducts.put(entry.getKey(), validProducts);
        }
        return shop2ValidProducts;
    }

    private boolean checkIsProductValid(Product product) {
        Date now = new Date();
        return product != null
                && product.getStatus() == ProductStatusEnum.ONLINE.getCode()
                && product.getEndDate().after(now);
    }

    private boolean checkIsFilterInvalid(Map<String, Object> params) {
        Boolean filterInvalid = (Boolean) params.get(QueryFetcher.Params.filterInvalid);
        if(!allowQueryInvalid || filterInvalid == null){
            return true;
        }
        return filterInvalid;
    }

    private CompletableFuture<Map<Integer, List<Product>>> fetchShopProductsCompletableFuture(List<Integer> shopIds, int platform, Integer productType, Long spuType, boolean filterInvalid) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.fetchShopProductsCompletableFuture(java.util.List,int,java.lang.Integer,java.lang.Long,boolean)");

        //1.区分平台，美团的商户ID要转成点评的商户ID，然后再执行团单召回
        return buildShopId2DpShopId(shopIds, platform).thenCompose(shopId2DpShopId -> {

            //2.根据点评的商户ID召回泛商品
            List<Integer> dpShopIds = new ArrayList<>(shopId2DpShopId.values());
            QueryShopProductRequest queryRequest = buildQueryRequest(dpShopIds, productType, spuType, filterInvalid);
            CompletableFuture<Map<Integer, List<Product>>> dpShop2ProductsFuture = compositeAtomService.mGetBaseProductsByShop(queryRequest);

            //3.返回的点评商户ID再转成美团商户ID，如果是点评的请求，保持原点评ID
            Map<Integer, Integer> dpShopId2shopId = shopId2DpShopId.entrySet().stream()
                    .collect(HashMap::new, (map, entry) -> map.put(entry.getValue(), entry.getKey()), HashMap::putAll);

            //4.还原召回结果的商户ID为美团商户ID
            return dpShop2ProductsFuture.thenCompose(dpShop2Products -> buildShopId2Product(dpShop2Products, dpShopId2shopId));
        });
    }

    private CompletableFuture<Map<Long, List<Product>>> fetchShopProductsCompletableFuturePoiMigrate(List<Long> shopIds, int platform, Integer productType, Long spuType, boolean filterInvalid) {

        //1.区分平台，美团的商户ID要转成点评的商户ID，然后再执行团单召回
        return buildShopId2DpShopIdPoiMigrate(shopIds, platform).thenCompose(shopId2DpShopId -> {

            //2.根据点评的商户ID召回泛商品
            List<Long> dpShopIds = new ArrayList<>(shopId2DpShopId.values());
            QueryShopProductRequest queryRequest = buildQueryRequestPoiMigrate(dpShopIds, productType, spuType, filterInvalid);
            CompletableFuture<Map<Long, List<Product>>> dpShop2ProductsFuture = compositeAtomService.mGetBaseProductsByLongShop(queryRequest);

            //3.返回的点评商户ID再转成美团商户ID，如果是点评的请求，保持原点评ID
            Map<Long, Long> dpShopId2shopId = shopId2DpShopId.entrySet().stream()
                    .collect(HashMap::new, (map, entry) -> map.put(entry.getValue(), entry.getKey()), HashMap::putAll);

            //4.还原召回结果的商户ID为美团商户ID
            return dpShop2ProductsFuture.thenCompose(dpShop2Products -> buildShopId2ProductPoiMigrate(dpShop2Products, dpShopId2shopId));
        });
    }

    private CompletableFuture<Map<Integer, Integer>> buildShopId2DpShopId(List<Integer> shopIds, int platform) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildShopId2DpShopId(java.util.List,int)");
        if (!PlatformUtil.isMT(platform)) {
            // 如果是点评，直接返回shopId -> shopId
            return CompletableFuture.completedFuture(shopIds.stream().collect(Collectors.toMap(shopId -> shopId, shopId -> shopId)));
        } else {
            return getMtShopId2DpShopId(shopIds);
        }
    }
    private CompletableFuture<Map<Long, Long>> buildShopId2DpShopIdPoiMigrate(List<Long> shopIds, int platform) {
        if (!PlatformUtil.isMT(platform)) {
            // 如果是点评，直接返回shopId -> shopId
            return CompletableFuture.completedFuture(shopIds.stream().collect(Collectors.toMap(shopId -> shopId, shopId -> shopId)));
        } else {
            return getMtShopId2DpShopIdPoiMigrate(shopIds);
        }
    }

    private CompletableFuture<Map<Integer, List<Product>>> buildShopId2Product(Map<Integer, List<Product>> dpShop2Products,
                                                                               Map<Integer, Integer> dpShopId2MtShopId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildShopId2Product(java.util.Map,java.util.Map)");
        if (MapUtils.isEmpty(dpShop2Products)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        Map<Integer, List<Product>> shopId2Products = Maps.newHashMap();
        dpShop2Products.forEach((dpShopId, products) -> {
            int shopId = dpShopId2MtShopId.getOrDefault(dpShopId, 0);
            shopId2Products.put(shopId, products);
        });
        return CompletableFuture.completedFuture(shopId2Products);
    }

    private CompletableFuture<Map<Long, List<Product>>> buildShopId2ProductPoiMigrate(Map<Long, List<Product>> dpShop2Products,
                                                                            Map<Long, Long> dpShopId2MtShopId) {
        if (MapUtils.isEmpty(dpShop2Products)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        Map<Long, List<Product>> shopId2Products = Maps.newHashMap();
        dpShop2Products.forEach((dpShopId, products) -> {
            long shopId = dpShopId2MtShopId.getOrDefault(dpShopId, 0L);
            shopId2Products.put(shopId, products);
        });
        return CompletableFuture.completedFuture(shopId2Products);
    }

    private CompletableFuture<Map<Integer, Integer>> getMtShopId2DpShopId(List<Integer> shopIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.getMtShopId2DpShopId(java.util.List)");

        Map<Integer, Integer> mtShopId2DpShopId = Maps.newHashMap();
        CompletableFuture<Map<Integer, List<Integer>>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtIds(shopIds);
        if (dpShopIdsFuture == null) {
            return CompletableFuture.completedFuture(mtShopId2DpShopId);
        }
        return dpShopIdsFuture.thenApply(this::buildMtShopId2DpShopId);
    }

    private CompletableFuture<Map<Long, Long>> getMtShopId2DpShopIdPoiMigrate(List<Long> shopIds) {

        Map<Long, Long> mtShopId2DpShopId = Maps.newHashMap();
        CompletableFuture<Map<Long, List<Long>>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtIdsL(shopIds);
        if (dpShopIdsFuture == null) {
            return CompletableFuture.completedFuture(mtShopId2DpShopId);
        }
        return dpShopIdsFuture.thenApply(this::buildMtShopId2DpShopIdPoiMigrate);
    }


    private Map<Integer, Integer> buildMtShopId2DpShopId(Map<Integer, List<Integer>> mtShopId2DpShopIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildMtShopId2DpShopId(java.util.Map)");
        Map<Integer, Integer> mtShopId2DpShopId = Maps.newHashMap();
        if (MapUtils.isEmpty(mtShopId2DpShopIds)) {
            return mtShopId2DpShopId;
        }
        mtShopId2DpShopIds.forEach((key, value) ->
                mtShopId2DpShopId.put(key, CollectionUtils.isEmpty(value) ? 0 : value.get(0)));
        return mtShopId2DpShopId;
    }

    private Map<Long, Long> buildMtShopId2DpShopIdPoiMigrate(Map<Long, List<Long>> mtShopId2DpShopIds) {
        Map<Long, Long> mtShopId2DpShopId = Maps.newHashMap();
        if (MapUtils.isEmpty(mtShopId2DpShopIds)) {
            return mtShopId2DpShopId;
        }
        mtShopId2DpShopIds.forEach((key, value) ->
                mtShopId2DpShopId.put(key, CollectionUtils.isEmpty(value) ? 0 : value.get(0)));
        return mtShopId2DpShopId;
    }

    private List<Integer> buildShopIds(ActivityContext activityContext, int platform, String groupName) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildShopIds(com.sankuai.dzviewscene.shelf.framework.ActivityContext,int,java.lang.String)");
        // 1. 优先拿商品组指定的多门店参数
        Map<String, List<Integer>> productType2ShopIds = activityContext.getParam(QueryFetcher.Params.groupName2ShopIds);
        if (MapUtils.isNotEmpty(productType2ShopIds) && productType2ShopIds.get(groupName) != null) {
            return productType2ShopIds.get(groupName);
        }
        // 2. 其次拿单门店ID
        Integer shopId = getSingletonShopId(activityContext, platform);
        if (shopId == null) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(shopId);
    }

    private List<Long> buildShopIdsPoiMigrate(ActivityContext activityContext, int platform, String groupName) {
        // 1. 优先拿商品组指定的多门店参数
        Map<String, List<Long>> productType2ShopIds = activityContext.getParam(QueryFetcher.Params.groupName2ShopIds);
        if (MapUtils.isNotEmpty(productType2ShopIds) && productType2ShopIds.get(groupName) != null) {
            return productType2ShopIds.get(groupName);
        }
        // 2. 其次拿单门店ID
        Long shopId = getSingletonShopIdPoiMigrate(activityContext, platform);
        if (shopId == null) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(shopId);
    }

    private Integer getSingletonShopId(ActivityContext activityContext, int platform) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.getSingletonShopId(com.sankuai.dzviewscene.shelf.framework.ActivityContext,int)");
        if (PlatformUtil.isMT(platform)) {
            return activityContext.getParam(ShelfActivityConstants.Params.mtPoiId);
        }
        return activityContext.getParam(ShelfActivityConstants.Params.dpPoiId);
    }

    private Long getSingletonShopIdPoiMigrate(ActivityContext activityContext, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }


    private ProductGroupM buildProductGroup(Map<Integer, List<Product>> shop2Products, List<Integer> excludeProductIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildProductGroup(java.util.Map,java.util.List)");
        if (MapUtils.isEmpty(shop2Products)) {
            return new ProductGroupM();
        }
        List<ProductM> products = buildProductMs(buildProduct2ShopIds(shop2Products), collectAllProducts(shop2Products));
        products = excludeProducts(products, excludeProductIds);
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(products);
        productGroupM.setTotal(CollectionUtils.isEmpty(products) ? 0 : products.size());
        return productGroupM;
    }

    private ProductGroupM buildProductGroupPoiMigrate(Map<Long, List<Product>> shop2Products, List<Integer> excludeProductIds) {
        if (MapUtils.isEmpty(shop2Products)) {
            return new ProductGroupM();
        }
        List<ProductM> products = buildProductMsPoiMigrate(buildProduct2ShopIdsPoiMigrate(shop2Products), collectAllProductsPoiMigrate(shop2Products));
        products = excludeProducts(products, excludeProductIds);
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(products);
        productGroupM.setTotal(CollectionUtils.isEmpty(products) ? 0 : products.size());
        return productGroupM;
    }

    private List<ProductM> excludeProducts(List<ProductM> products, List<Integer> excludeProductIds) {
        if (CollectionUtils.isEmpty(products) || CollectionUtils.isEmpty(excludeProductIds)) {
            return products;
        }
        return products.stream()
                .filter(productM -> !excludeProductIds.contains(productM.getProductId()))
                .collect(Collectors.toList());
    }

    private List<ProductM> buildProductMs
            (Map<Integer, List<Integer>> product2ShopIds, List<Product> products) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildProductMs(java.util.Map,java.util.List)");
        return products.stream()
                .map(product -> buildProductM(product, product2ShopIds.get(product.getId())))
                .collect(Collectors.toList());
    }

    private List<ProductM> buildProductMsPoiMigrate
            (Map<Integer, List<Long>> product2ShopIds, List<Product> products) {
        return products.stream()
                .map(product -> buildProductMPoiMigrate(product, product2ShopIds.get(product.getId())))
                .collect(Collectors.toList());
    }

    private List<Product> collectAllProducts(Map<Integer, List<Product>> shop2Products) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.collectAllProducts(java.util.Map)");
        List<Product> products = new ArrayList<>();
        Set<Integer> productIds = new HashSet<>();
        shop2Products.entrySet()
                .forEach(shop2ProductEntry -> {
                    List<Product> shopProducts = shop2ProductEntry.getValue();
                    if (CollectionUtils.isEmpty(shopProducts)) {
                        return;
                    }
                    shopProducts.forEach(product -> {
                        // 去重
                        if (productIds.contains(product.getId())) {
                            return;
                        }
                        products.add(product);
                        productIds.add(product.getId());
                    });
                });
        return products;
    }

    private List<Product> collectAllProductsPoiMigrate(Map<Long, List<Product>> shop2Products) {
        List<Product> products = new ArrayList<>();
        Set<Integer> productIds = new HashSet<>();
        shop2Products.entrySet()
                .forEach(shop2ProductEntry -> {
                    List<Product> shopProducts = shop2ProductEntry.getValue();
                    if (CollectionUtils.isEmpty(shopProducts)) {
                        return;
                    }
                    shopProducts.forEach(product -> {
                        // 去重
                        if (productIds.contains(product.getId())) {
                            return;
                        }
                        products.add(product);
                        productIds.add(product.getId());
                    });
                });
        return products;
    }

    private Map<Integer, List<Integer>> buildProduct2ShopIds(Map<Integer, List<Product>> shop2Products) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildProduct2ShopIds(java.util.Map)");
        if (MapUtils.isEmpty(shop2Products)) {
            return Maps.newHashMap();
        }
        Map<Integer, List<Integer>> product2ShopIds = new HashMap<>();
        shop2Products.entrySet().forEach(shop2ProductsEntry -> {
            List<Product> products = shop2ProductsEntry.getValue();
            if (CollectionUtils.isEmpty(products)) {
                return;
            }
            int shopId = shop2ProductsEntry.getKey();
            products.forEach(product -> {
                if (product2ShopIds.get(product.getId()) == null) {
                    product2ShopIds.put(product.getId(), new ArrayList<>());
                }
                product2ShopIds.get(product.getId()).add(shopId);
            });
        });
        return product2ShopIds;
    }

    private Map<Integer, List<Long>> buildProduct2ShopIdsPoiMigrate(Map<Long, List<Product>> shop2Products) {
        if (MapUtils.isEmpty(shop2Products)) {
            return Maps.newHashMap();
        }
        Map<Integer, List<Long>> product2ShopIds = new HashMap<>();
        shop2Products.entrySet().forEach(shop2ProductsEntry -> {
            List<Product> products = shop2ProductsEntry.getValue();
            if (CollectionUtils.isEmpty(products)) {
                return;
            }
            long shopId = shop2ProductsEntry.getKey();
            products.forEach(product -> {
                if (product2ShopIds.get(product.getId()) == null) {
                    product2ShopIds.put(product.getId(), new ArrayList<>());
                }
                product2ShopIds.get(product.getId()).add(shopId);
            });
        });
        return product2ShopIds;
    }

    private ProductM buildProductM(Product product, List<Integer> shopIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildProductM(com.dianping.tpfun.product.api.sku.model.Product,java.util.List)");
        ProductM productM = new ProductM();
        productM.setProductId(product.getId());
        productM.setShopIds(shopIds);
        productM.setSpuType(product.getSpuType());
        productM.setExtAttrs(Lists.newArrayList(new AttrM("spuType", String.valueOf(product.getSpuType()))));
        return productM;
    }

    private ProductM buildProductMPoiMigrate(Product product, List<Long> shopIds) {
        ProductM productM = new ProductM();
        productM.setProductId(product.getId());
        productM.setShopIds(shopIds.stream().map(Long::intValue).collect(Collectors.toList()));
        productM.setShopLongIds(shopIds);
        productM.setSpuType(product.getSpuType());
        productM.setExtAttrs(Lists.newArrayList(new AttrM("spuType", String.valueOf(product.getSpuType()))));
        return productM;
    }

    private QueryShopProductRequest buildQueryRequest(List<Integer> shopIds, Integer productType, Long spuType, boolean filterInvalid) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler.buildQueryRequest(java.util.List,java.lang.Integer,java.lang.Long,boolean)");
        QueryShopProductRequest queryRequest = new QueryShopProductRequest();
        queryRequest.setProductType(productType);
        queryRequest.setSpuType(spuType);
        queryRequest.setShopIds(shopIds);
        queryRequest.setFilterInvalid(filterInvalid);
        return queryRequest;
    }

    private QueryShopProductRequest buildQueryRequestPoiMigrate(List<Long> shopIds, Integer productType, Long spuType, boolean filterInvalid) {
        QueryShopProductRequest queryRequest = new QueryShopProductRequest();
        queryRequest.setProductType(productType);
        queryRequest.setSpuType(spuType);
        queryRequest.setShopIds(shopIds.stream().map(Long::intValue).collect(Collectors.toList()));
        queryRequest.setLongShopIds(shopIds);
        queryRequest.setFilterInvalid(filterInvalid);
        return queryRequest;
    }

}
