package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter;

import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;

import java.util.List;

/**
 * Created by float.lu on 2020/8/26.
 */
@ExtPointInstance(name = "默认筛选组件构造器扩展点实现")
public class ShelfFilterBuilderExt implements FilterBuilderExt {


    @Override
    public int showType(ActivityContext activityContext, String groupName) {
        return 0;
    }

    @Override
    public int minShowNum(ActivityContext activityContext, String groupName) {
        return 0;
    }

    @Override
    public int childrenMinShowNum(ActivityContext activityContext, String groupName) {
        return 0;
    }

    @Override
    public String labs(ActivityContext activityContext, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public String labs(ActivityContext activityContext, FilterBtnM filterBtnM, int layer, int index) {
        return null;
    }

    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public RichLabelVO selectedTitleButton(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public RichLabelVO subTitle(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public String extra(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public List<IconRichLabelVO> preFixedBtns(ActivityContext activityContext, String groupName) {
        return null;
    }

    @Override
    public boolean showWithNoProducts(ActivityContext activityContext) {
        return false;
    }

    @Override
    public Boolean selected(ActivityContext activityContext, FilterBtnM filterBtnM) {
        return null;
    }
}
