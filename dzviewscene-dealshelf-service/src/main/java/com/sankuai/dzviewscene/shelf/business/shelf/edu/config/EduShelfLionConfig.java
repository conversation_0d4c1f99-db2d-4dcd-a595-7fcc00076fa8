package com.sankuai.dzviewscene.shelf.business.shelf.edu.config;

import lombok.Data;

import java.util.Map;

/**
 * Title:EduShelfLionConfig
 * Description: 教育配置化信息
 *
 * <AUTHOR>
 * @date 2021/1/22
 */
@Data
public class EduShelfLionConfig {

    /**
     * 货架配置key
     */
    public static final String EDU_SHELF_LION_CONFIG_KEY = "com.sankuai.dzviewscene.productshelf.edu.shelf.config";

    /**
     * 新老货架切换开关
     */
    private boolean eduOldShelfSwitch;
    /**
     * 课程货架最大展示数
     */
    private int maxCourseShowItem;
    /**
     * 课程货架-跳转详情链接的url配置-MT
     */
    private String mtCourseMoreUrlDomain;
    /**
     * 课程货架-跳转详情链接的url配置-DP
     */
    private String dpCourseMoreUrlDomain;
    /**
     * 课程货架-图片width
     */
    private int courseShelfPicWidth;
    /**
     * 课程货架-图片height
     */
    private int courseShelfPicHeight;

    /**
     * 团购货架最大展示数
     */
    private int maxEduDealShowItem;
    /**
     * 团购货架-跳转详情链接的url配置-DP
     */
    private String mtDealMoreUrlDomain;
    /**
     * 团购货架-跳转详情链接的url配置-DP
     */
    private String dpDealMoreUrlDomain;

    /**
     * 货架-跳转新老详情开关
     */
    private boolean moreJumpUrl2New;

    /**
     * 玩乐卡icon
     */
    String playCardIcon;

    /**
     * 玩乐卡icon宽高比
     */
    double playCardIconAspectRadio;

    /**
     * 商家推荐图片
     */
    String shopRecommendPic;

    /**
     * 商家推荐图片比例
     */
    double shopRecommendAspectRadio;

    /**
     * 活动图片的比例
     */
    double activityPicAspectRadio;

    /**
     * 一级筛选栏至少多少个才会展示
     */
    private int filterMinShowNum;
    /**
     * 二级筛选栏至少多少个才会展示
     */
    private int childrenMinShowNum;
    /**
     * 教育课程货架-课详新老链接切换
     * true：老；false：新
     */
    private boolean eduOldCourseDetailSwitch;
    /**
     * mt、dp课详情跳转url的map
     */
    private Map<Integer, String> platformCourseJumpUrlTemplateMap;
}
