package com.sankuai.dzviewscene.shelf.business.shelf.life.builder;

import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.PlatformShelfCacheFilterFetcher.SHELF_TAGS;

/**
 * 生活服务团购货架主标题模块构造扩展点：tag是自动计算的，来自于商品平台
 * Created by 李维隆 on 2020/04/21
 */
@ExtPointInstance(name = "生活服务团购货架主标题模块构造扩展点：tag是自动计算的")
public class LifeDealMainTitleForAutoTagBuilderExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.life.deal.constant.config", defaultValue = "{}")
    public MainTitleConstant mainTitleConstant;

    @Override
    public String title(ActivityContext activityContext) {
        return mainTitleConstant.getTitle();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (PlatformUtil.isMT(platform)) return mainTitleConstant.getMtTitleIcon();
        return mainTitleConstant.getDpTitleIcon();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        CompletableFuture<ShelfGroupM> shelfGroupCompletableFuture = activityContext.getMainData();
        ShelfGroupM shelfGroupM = shelfGroupCompletableFuture.join();
        if (shelfGroupM.getFilterMs() == null || CollectionUtils.isEmpty(shelfGroupM.getFilterMs().values())) {
            return null;
        }
        Map<String, Object> extra = Lists.newArrayList(shelfGroupM.getFilterMs().values()).get(0).getExtra();
        if (MapUtils.isEmpty(extra) || extra.get(SHELF_TAGS) == null) {
            return null;
        }
        return buildTags(extra);
    }

    @NotNull
    private List<IconRichLabelVO> buildTags(Map<String, Object> extra) {
        List<String> shelfTags = (List<String>) extra.get(SHELF_TAGS);
        if (CollectionUtils.isEmpty(shelfTags)) {
            return null;
        }
        String icon = mainTitleConstant.getTitleTagIcon();
        List<IconRichLabelVO> returnValue = new ArrayList<>();
        for (String shelfTag : shelfTags) {
            IconRichLabelVO iconRichLabelVO = new IconRichLabelVO();
            iconRichLabelVO.setIcon(icon);
            iconRichLabelVO.setText(new RichLabelVO(shelfTag));
            returnValue.add(iconRichLabelVO);
        }
        return returnValue;
    }

    @Data
    private static class MainTitleConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
    }
}
