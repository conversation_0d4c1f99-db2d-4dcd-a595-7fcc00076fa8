package com.sankuai.dzviewscene.shelf.platform.shelf.flow;

import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by zhangsuping on 2020/11/16.
 */
@ActivityFlow(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, flowCode = MergeQueryFilterLastShelfFlow.ACTIVITY_FLOW_MERGE_QUERY_FILTER_LAST_SHELF_FLOW, name = "")
public class MergeQueryFilterLastShelfFlow implements IActivityFlow<ShelfGroupM> {

        public static final String ACTIVITY_FLOW_MERGE_QUERY_FILTER_LAST_SHELF_FLOW = "MergeQueryFilterLastShelfFlow";

        @Override
        public CompletableFuture<ShelfGroupM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
            // 1. 查询斗斛
            CompletableFuture<DouHuM> douHuMCompletableFuture = activity.findAbility(ctx, DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE).build(ctx);

            // 2. 融合查询
            CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = activity.findAbility(ctx, MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE).build(ctx);

            // 3. 填充之后生成筛选: 商品组名->生成筛选
            CompletableFuture<Map<String, FilterM>> filterMCompletableFuture = productGroupsCompletableFuture.thenCompose(productGroups -> {
                // 4.1 放入召回结果, 能力实现基于召回结果生成筛选
                ctx.attach(FilterFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
                return activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE).build(ctx);
            });


            return CompletableFuture.allOf(douHuMCompletableFuture, filterMCompletableFuture, productGroupsCompletableFuture).thenApply(v -> {
                ShelfGroupM shelfGroupM = new ShelfGroupM();
                shelfGroupM.addDouHu(douHuMCompletableFuture.join());
                shelfGroupM.setFilterMs(filterMCompletableFuture.join());
                shelfGroupM.setProductGroupMs(productGroupsCompletableFuture.join());
                return shelfGroupM;
            });
        }
    }