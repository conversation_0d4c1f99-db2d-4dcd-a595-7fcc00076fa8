package com.sankuai.dzviewscene.shelf.platform.filterlist;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextBuilder;
import com.sankuai.dzviewscene.shelf.framework.core.IContextBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.ap.internal.util.Collections;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Created by liweilong06 on 2020/11/19.
 */
@ContextBuilder(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, name = "列表页上下文构造器")
public class FilterListActivityContextBuilder implements IContextBuilder{

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public ActivityContext build(ActivityRequest activityRequest) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.build(com.sankuai.dzviewscene.shelf.framework.ActivityRequest)");
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(activityRequest.getParams());

        // poiMigrate
        if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.DealShelfActivityCtxBuilder.getScene())) {
            addPlatformParamPoiMigrate(activityContext);
        } else {
            // 原逻辑
            addPlatformParam(activityContext);
        }
        return activityContext;
    }

    private void addPlatformParam(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.addPlatformParam(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform))) {
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpPoiId));
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId));
        } else {
            // 美团平台
            addMtCityId(activityContext);
            addMtShopIdAndInfo(activityContext);
        }
        addCtxShop(activityContext);
    }

    private void addPlatformParamPoiMigrate(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.addPlatformParamPoiMigrate(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform))) {
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpPoiId));
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId));
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId));
        } else {
            // 美团平台
            addMtCityId(activityContext);
            addMtShopIdAndInfoPoiMigrate(activityContext);
        }
        addCtxShopPoiMigrate(activityContext);
    }

    private void addMtShopIdAndInfo(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.addMtShopIdAndInfo(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int mtPoiId = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtPoiId);
        if (mtPoiId < 1) {
            return;
        }
        CompletableFuture<List<Integer>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtId(mtPoiId);
        dpShopIdsFuture.thenAccept(dpShopIds -> {
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds));
        }).join();
    }

    private void addMtShopIdAndInfoPoiMigrate(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.addMtShopIdAndInfoPoiMigrate(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        Long mtPoiId = PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        if (mtPoiId < 1) {
            return;
        }
        CompletableFuture<List<Long>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtIdL(mtPoiId);
        dpShopIdsFuture.thenAccept(dpShopIds -> {
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds).intValue());
            activityContext.addParam(ShelfActivityConstants.Params.dpPoiIdL, CollectionUtils.isEmpty(dpShopIds) ? 0L : Collections.first(dpShopIds));
        }).join();
    }

    private void addMtCityId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.addMtCityId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int mtCityId = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtCityId);
        if (mtCityId < 1) {
            return;
        }
        CompletableFuture<Integer> dpCityIdFuture = compositeAtomService.getDpCityIdByMt(mtCityId);
        dpCityIdFuture.thenAccept(dpCityId -> {
            activityContext.addParam(ShelfActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
        }).join();
    }

    private void addCtxShop(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.addCtxShop(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int dpPoiId = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpPoiId);
        if (dpPoiId > 0) {
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShop(dpPoiId));
        }
    }

    private void addCtxShopPoiMigrate(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.addCtxShopPoiMigrate(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        Long dpPoiId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        if (dpPoiId > 0) {
            activityContext.addParam(ShelfActivityConstants.Ctx.ctxShop, loadShopPoiMigrate(dpPoiId));
        }
    }

    private ShopM loadShop(int dpShopId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.loadShop(int)");
        if (dpShopId <= 0) {
            return null;
        }
        return compositeAtomService.loadShop(dpShopId).thenApply(shopDTO -> {
            if (shopDTO == null) {
                return null;
            }
            ShopM shopM = new ShopM();
            shopM.setShopId(shopDTO.getShopId() == null ? 0 : shopDTO.getShopId());
            shopM.setShopName(shopDTO.getShopName());
            shopM.setShopType(shopDTO.getShopType() == null ? 0 : shopDTO.getShopType());
            shopM.setCategory(shopDTO.getMainCategoryId() == null ? 0 : shopDTO.getMainCategoryId());
            shopM.setLat(shopDTO.getGlat() == null ? 0 : shopDTO.getGlat());
            shopM.setLng(shopDTO.getGlng() == null ? 0 : shopDTO.getGlng());
            shopM.setCityId(shopDTO.getCityId() == null ? 0 : shopDTO.getCityId());
            return shopM;
        }).join();
    }

    private ShopM loadShopPoiMigrate(long dpShopId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.loadShopPoiMigrate(long)");
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(Lists.newArrayList(dpShopId))).thenApply(dpPoiDTOList -> {
            if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                return null;
            }

            DpPoiDTO dpPoiDTO = Collections.first(dpPoiDTOList);
            ShopM shopM = new ShopM();
            shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId().intValue());
            shopM.setLongShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
            shopM.setShopName(dpPoiDTO.getShopName());
            shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
            shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
            shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
            shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
            shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
            return shopM;
        }).join();
    }

    private DpPoiRequest buildDpPoiRequest(List<Long> dpPoiIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityContextBuilder.buildDpPoiRequest(java.util.List)");
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }
}
