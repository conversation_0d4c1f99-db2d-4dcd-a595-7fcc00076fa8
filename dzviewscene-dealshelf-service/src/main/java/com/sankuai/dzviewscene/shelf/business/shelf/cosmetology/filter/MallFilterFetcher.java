package com.sankuai.dzviewscene.shelf.business.shelf.cosmetology.filter;

import com.dianping.vc.sdk.lang.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.AbstractShelfFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;

import java.util.Objects;


/**
 * <AUTHOR>
 * @since 2023/9/21 10:08
 */
@AbilityInstance(name = "商场筛选取数能力实例")
public class MallFilterFetcher extends AbstractShelfFilterFetcher {
    /**
     * 商场「推荐」TAB页ID
     */
    private static final long RECOMMEND_FILTER_ID = 200129395L;

    @Override
    protected long getSelected(String groupName, FilterM filterM, ActivityContext activityContext) {
        // 选中筛选项，则优先级最高
        long selectedTagId = NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.selectedFilterId) + "", 0);
        if (selectedTagId > 0) {
            return selectedTagId;
        }
        Long mallSelected = getMallSelected(filterM);
        if (Objects.nonNull(mallSelected)) {
            return mallSelected;
        }
        // 兜底，父类默认逻辑
        return super.getSelected(groupName, filterM, activityContext);
    }

    /**
     * 商场筛选项锚定逻辑
     *
     * @param filterM
     * @return
     */
    private Long getMallSelected(FilterM filterM) {
        if (Objects.isNull(filterM) || CollectionUtils.isEmpty(filterM.getFilters())) {
            return null;
        }
        // 若存在「推荐」Tab，直接返回
        if (filterM.getFilters().stream().anyMatch(a -> a.getFilterId() == RECOMMEND_FILTER_ID)) {
            return RECOMMEND_FILTER_ID;
        }
        // 否则返回第一个
        return filterM.getFilters().get(0).getFilterId();
    }
}
