package com.sankuai.dzviewscene.shelf.business.shelf.baby.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductMAttrConstant;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.AbstractDefaultFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import joptsimple.internal.Strings;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.rmi.dgc.Lease;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/3
 */
@ExtPointInstance(name = "亲子-孕婴童摄影特惠拍货架楼层模块构造扩展点")
public class BabyPhotographyTeHuiPaiFloorsBuilderExt extends AbstractDefaultFloorsBuilderExt {
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.baby.photo.tehuipai.constant.config", defaultValue = "{}")
    private BabyPhotographyTeHuiPaiFloorsBuilderExt.FloorConstant floorConstant;

    /**
     * 标签的字数，用于返回判断返回不同长度的背景图
     */
    private static final int LABEL_LENGTH_THREE = 3;
    private static final int LABEL_LENGTH_FOUR = 4;
    private static final int LABEL_LENGTH_FIVE = 5;

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.floorDefaultShowNum(ActivityContext,String,List)");
        //特惠拍最多展示20个
        return 20;
    }

    @Override
    public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentSalePrice(ActivityContext,String,ProductM)");
        String perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPrice(productM);
        if (PerfectActivityBuildUtils.isDuringPerfectActivity(productM) && StringUtils.isNotEmpty(perfectActivityPrice)) {
            return perfectActivityPrice;
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return productM.getBasePriceTag();
        }
        return productPromoPriceM.getPromoPriceTag();
    }

    @Override
    public List<RichLabelVO> itemComponentBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentBottomTags(ActivityContext,String,ProductM)");
        RichLabelVO card = buildRichLabel(productM.getCardPrice());
        RichLabelVO pin = buildRichLabel(productM.getPinPrice());
        List<RichLabelVO> richLabelVOS = new ArrayList<>();
        if (card != null) richLabelVOS.add(card);
        if (pin != null) richLabelVOS.add(pin);
        return richLabelVOS;
    }

    private RichLabelVO buildRichLabel(ProductPriceM productPriceM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.buildRichLabel(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM)");
        if (productPriceM == null) return null;
        RichLabelVO richLabelVO = new RichLabelVO();
        String[] priceDesc = productPriceM.getPriceTag().split("/");
        if (priceDesc.length < 2) {
            //仅有一个价格时的富文本文字信息
            List<BabyPhotographyTeHuiPaiFloorsBuilderExt.RichLabel> richLabelList = Lists.newArrayList(
                    new BabyPhotographyTeHuiPaiFloorsBuilderExt.RichLabel(productPriceM.getPriceDesc(), floorConstant.getColor777777(), floorConstant.getFront11()),
                    new BabyPhotographyTeHuiPaiFloorsBuilderExt.RichLabel(productPriceM.getPriceTag(), floorConstant.getColorFF6633(), floorConstant.getFront11(), floorConstant.getBoldFontWeight()));
            richLabelVO.setText(JsonCodec.encode(richLabelList));
            return richLabelVO;
        }
        List<BabyPhotographyTeHuiPaiFloorsBuilderExt.RichLabel> richLabelList = Lists.newArrayList(
                new BabyPhotographyTeHuiPaiFloorsBuilderExt.RichLabel(productPriceM.getPriceDesc(), floorConstant.getColor777777(), floorConstant.getFront11()),
                new BabyPhotographyTeHuiPaiFloorsBuilderExt.RichLabel(priceDesc[0], floorConstant.getColorFF6633(), floorConstant.getFront11(), floorConstant.getBoldFontWeight()),
                new BabyPhotographyTeHuiPaiFloorsBuilderExt.RichLabel("/" + priceDesc[1], floorConstant.getColorFF6633(), floorConstant.getFront11()));
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return richLabelVO;
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentPic(ActivityContext,String,ProductM)");
        if (StringUtils.isEmpty(productM.getPicUrl())) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        // 图片信息
        addPicUrl(productM, picAreaVO);
        // 图片标签
        addFloatTags(productM, picAreaVO);
        return picAreaVO;
    }

    private void addFloatTags(ProductM productM, PicAreaVO picAreaVO) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.addFloatTags(ProductM,PicAreaVO)");
        String dealActivityStr = productM.getAttr(ProductMAttrConstant.TARGET_DEAL_ACTIVITIES);
        if (StringUtils.isEmpty(dealActivityStr)){
            return;
        }
        List<ProductActivityM> productActivityMList = JsonCodec.decode(dealActivityStr, new TypeReference<List<ProductActivityM>>(){});
        //特惠拍只会有一个
        ProductActivityM dealActivityDTO = productActivityMList.get(0);
        if (dealActivityDTO == null){
            return;
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setPosition(1);
        if (CollectionUtils.isNotEmpty(dealActivityDTO.getUrls())){
            //若为图片
            floatTagVO.setIcon(buildIconDzPictureComponentVO(dealActivityDTO));
        }else if (StringUtils.isNotEmpty(dealActivityDTO.getLable())){
            floatTagVO.setLabel(buildLabelRichLabelVO(dealActivityDTO));
            floatTagVO.setBackgroundImg(getLabelBackgroundImgWithWordCount(dealActivityDTO.getLable()));
        }else {
            return;
        }
        picAreaVO.setFloatTags(Lists.newArrayList(floatTagVO));
    }

    /**
     * 根据标签字数获取不同尺寸的背景图
     * @param label
     * @return
     */
    private String getLabelBackgroundImgWithWordCount(String label){
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.getLabelBackgroundImgWithWordCount(java.lang.String)");
        if (label.length() == LABEL_LENGTH_THREE){
            return "https://p0.meituan.net/scarlett/80e7f1f106cf110aab35e1114c8ffe1a5492.png";
        }else if (label.length() == LABEL_LENGTH_FOUR){
            return "https://p0.meituan.net/scarlett/2ac21e225d01ead3b7afb9269206fbd56830.png";
        }else if (label.length() == LABEL_LENGTH_FIVE){
            return "https://p1.meituan.net/scarlett/d21678755ffa94600997dff91d2c49f28200.png";
        }
        //兜底返回4字
        return "https://p0.meituan.net/scarlett/2ac21e225d01ead3b7afb9269206fbd56830.png";
    }

    private DzPictureComponentVO buildIconDzPictureComponentVO(ProductActivityM dealActivityDTO){
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.buildIconDzPictureComponentVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM)");
        DzPictureComponentVO componentVO = new DzPictureComponentVO();
        componentVO.setPicUrl(dealActivityDTO.getUrl());
        componentVO.setPicHeight(18);
        componentVO.setAspectRadio(floorConstant.getIconAspectRadio());
        return componentVO;
    }

    private RichLabelVO buildLabelRichLabelVO(ProductActivityM dealActivityDTO){
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.buildLabelRichLabelVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM)");
        RichLabelVO labelVO = new RichLabelVO();
        labelVO.setText(dealActivityDTO.getLable());
        labelVO.setTextSize(10);
        labelVO.setTextColor("#FFFFFFFF");
        return labelVO;
    }

    private void addPicUrl(ProductM productM, PicAreaVO picAreaVO) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.addPicUrl(ProductM,PicAreaVO)");
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setAspectRadio(floorConstant.getHeaderPicAspectRadio());
        pic.setPicUrl(PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), floorConstant.getPicWidth(), floorConstant.getPicHeight(), PictureURLBuilders.ScaleType.Cut));
        picAreaVO.setPic(pic);
    }


    @Override
    public List<String> itemComponentProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentProductTags(ActivityContext,String,ProductM,long)");
        return productM.getProductTags();
    }

    @Override
    public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentLabs(ActivityContext,String,ProductM,int)");
        Map<String, Object> labs = new HashMap<>();
        labs.put("poi_id", getShopId(activityContext));
        labs.put("deal_id", productM.getProductId());
        addPromoInfo(labs, itemComponentPromoTags(activityContext, groupName, productM));
        return JsonCodec.encode(labs);
    }

    private Long getShopId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.getShopId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }


    @Override
    public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentPurchase(ActivityContext,String,ProductM)");
        if (StringUtils.isBlank(productM.getPurchase())) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(productM.getPurchase());
        return richLabelVO;
    }

    @Override
    public DzSimpleButtonVO itemComponentButton(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentButton(ActivityContext,String,ProductM)");
        if (productM == null || CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        DzSimpleButtonVO dzSimpleButtonVO = new DzSimpleButtonVO();
        dzSimpleButtonVO.setType(1);
        dzSimpleButtonVO.setName("购买");
        dzSimpleButtonVO.setJumpUrl(productM.getJumpUrl());
        return dzSimpleButtonVO;
    }


    @Override
    public String itemComponentTitle(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentTitle(ActivityContext,String,ProductM,long)");
        return productM.getTitle();
    }

    @Override
    public String itemComponentSale(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentSale(ActivityContext,String,ProductM)");
        if(productM.getSale() == null || StringUtils.isBlank(productM.getSale().getSaleTag())) return Strings.EMPTY;
        return productM.getSale().getSaleTag();
    }

    @Override
    public RichLabelVO activityRemainSecondsLabel(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.activityRemainSecondsLabel(ActivityContext,String,ProductM)");
        RichLabelVO richLabelVO = PerfectActivityBuildUtils.buildPerfectActivityRemainSecondsLabel(productM);
        return richLabelVO;
    }

    @Override
    public List<DzTagVO> itemComponentPriceBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyTeHuiPaiFloorsBuilderExt.itemComponentPriceBottomTags(ActivityContext,String,ProductM)");
        if (CollectionUtils.isEmpty(productM.getCoupons())) {
            return null;
        }
        return productM.getCoupons().stream()
                .filter(productCouponM -> productCouponM != null && StringUtils.isNotEmpty(productCouponM.getCouponTag()))
                .map(productCouponM -> new DzTagVO("#FF6633", false, productCouponM.getCouponTag()))
                .collect(Collectors.toList());
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RichLabel {
        private String text;
        private String textcolor;
        private int textsize;
        private String textstyle;

        public RichLabel() {}

        public RichLabel(String text, String textcolor, int textsize) {
            this.text = text;
            this.textcolor =textcolor;
            this.textsize = textsize;
        }

        public RichLabel(String text, String textcolor, int textsize, String textstyle) {
            this.text = text;
            this.textcolor =textcolor;
            this.textsize = textsize;
            this.textstyle = textstyle;
        }

    }

    @Data
    public static class FloorConstant {
        private int front11;
        private String color777777;
        private String colorFF6633;
        private String boldFontWeight;
        private double headerPicAspectRadio;
        private int picWidth;
        private int picHeight;
        private double iconAspectRadio;
    }

}
