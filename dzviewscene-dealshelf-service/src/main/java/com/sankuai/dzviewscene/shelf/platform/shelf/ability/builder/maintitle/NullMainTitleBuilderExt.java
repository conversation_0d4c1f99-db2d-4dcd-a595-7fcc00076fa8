package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;

import java.util.List;

/**
 * Created by float.lu on 2020/8/26.
 */
@ExtPointInstance(name = "默认主标题区构造扩展点实现")
public class NullMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @Override
    public String jumpUrl(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.NullMainTitleBuilderExt.jumpUrl(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return null;
    }

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.NullMainTitleBuilderExt.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return null;
    }

    @Override
    public String icon(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.NullMainTitleBuilderExt.icon(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return null;
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.NullMainTitleBuilderExt.tags(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return null;
    }
}
