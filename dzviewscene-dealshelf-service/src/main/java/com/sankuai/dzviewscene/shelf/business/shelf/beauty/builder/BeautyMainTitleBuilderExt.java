package com.sankuai.dzviewscene.shelf.business.shelf.beauty.builder;

import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@ExtPointInstance(name = "丽人-团购货架主标题模块构造扩展点")
public class BeautyMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    private static final String BEAUTY_TIME_CARD_GREY_DP_SHOP_ID = "com.sankuai.merchantcard.dzcard.supply.beauty.prepayCard.shelf.dpShopId";

    private static final String BEAUTY_TIME_CARD_GREY_MT_SHOP_ID = "com.sankuai.merchantcard.dzcard.supply.beauty.prepayCard.shelf.mtShopId";

    private static final String DEFAULT_CARD_SAYS_PIC = "https://p0.meituan.net/travelcube/c010dd7305dd787a832b3f01012eb4dd3231.png";

    private static final String RECOMMEND_REASON = "reason";

    private static final String REASON_SPLIT = "#__#__#";

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.beauty.constant.config", defaultValue = "{}")
    public MainTitleConstant mainTitleConstant;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public String title(ActivityContext activityContext) {
        return mainTitleConstant.getTitle();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (VCPlatformEnum.MT.getType() == platform) return mainTitleConstant.getMtTitleIcon();
        return mainTitleConstant.getDpTitleIcon();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        List<IconRichLabelVO> commonTags = getCommonTags();
        List<IconRichLabelVO> cardSaysTags = getCardSaysTags(activityContext);
        if(CollectionUtils.isNotEmpty(cardSaysTags)){
            commonTags.addAll(cardSaysTags);
        }
        return commonTags;
    }

    private  List<IconRichLabelVO> getCommonTags(){
        IconRichLabelVO returnAnyTimeIcon = new IconRichLabelVO();
        String icon = mainTitleConstant.getTitleTagIcon();
        returnAnyTimeIcon.setIcon(icon);
        returnAnyTimeIcon.setText(new RichLabelVO("随时退"));
        IconRichLabelVO returnExpiredIcon = new IconRichLabelVO();
        returnExpiredIcon.setIcon(icon);
        returnExpiredIcon.setText(new RichLabelVO("过期退"));
        return Lists.newArrayList(returnAnyTimeIcon, returnExpiredIcon);
    }

    private List<IconRichLabelVO> getCardSaysTags(ActivityContext activityContext){
        int shelfVersion = ParamsUtil.getIntSafely(activityContext,ShelfActivityConstants.Params.shelfVersion);
        if(shelfVersion <= 100){
            return Lists.newArrayList();
        }
        RecommendDTO recommendDTO = getRecommendData(activityContext);
        List<String> ugcContents = parseRecommendDTO(recommendDTO);
        if(CollectionUtils.isEmpty(ugcContents)){
            return Lists.newArrayList();
        }
        return ugcContents.stream().map(v -> {
            IconRichLabelVO returnExpiredIcon = new IconRichLabelVO();
            returnExpiredIcon.setText(new RichLabelVO(v));
            returnExpiredIcon.setIcon(DEFAULT_CARD_SAYS_PIC);
            returnExpiredIcon.setType(1);
            return returnExpiredIcon;
        }).collect(Collectors.toList());
    }

    private RecommendDTO getRecommendData(ActivityContext activityContext){
        double lat = ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lat);
        double lng = ParamsUtil.getDoubleSafely(activityContext, ShelfActivityConstants.Params.lng);
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        long userId = PlatformUtil.isMT(platform) ? ParamsUtil.getLongSafely(activityContext,ShelfActivityConstants.Params.mtUserId) : ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpUserId);
        long shopId = PlatformUtil.isMT(platform) ? ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.mtPoiIdL) : ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.dpPoiIdL);
        int cityId = PlatformUtil.isMT(platform) ? ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtCityId) : ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId);
        String deviceId = ParamsUtil.getStringSafely(activityContext, ShelfActivityConstants.Params.deviceId);

        //白名单控制下
        List<Long> whiteShopIds = platform == VCPlatformEnum.DP.getType() ? Lion.getList("com.sankuai.merchantcard.dzcard.supply", BEAUTY_TIME_CARD_GREY_DP_SHOP_ID, Long.class)
                : Lion.getList("com.sankuai.merchantcard.dzcard.supply", BEAUTY_TIME_CARD_GREY_MT_SHOP_ID,Long.class) ;
        if(CollectionUtils.isNotEmpty(whiteShopIds) && !whiteShopIds.contains(shopId)){
            return null;
        }

        RecommendParameters recommendParameters = new RecommendParameters();
        recommendParameters.setBizId(475);
        recommendParameters.setPlatformEnum(PlatformUtil.isMT(platform) ? PlatformEnum.MT : PlatformEnum.DP);
        recommendParameters.setCityId(cityId);
        recommendParameters.setOriginUserId(String.valueOf(userId));
        if(PlatformUtil.isMT(platform)){
            recommendParameters.setUuid(deviceId);
        }else{
            recommendParameters.setDpid(deviceId);
        }
        recommendParameters.setLat(lat);
        recommendParameters.setLng(lng);
        recommendParameters.setPageSize(1);
        recommendParameters.setBizParams(buildCardSaysBizParam(shopId));
        RecommendResult<RecommendDTO> recommendResult = compositeAtomService.getRecommendResult(recommendParameters).join();
        if(Objects.isNull(recommendResult) || CollectionUtils.isEmpty(recommendResult.getSortedResult())){
            return null;
        }
        return recommendResult.getSortedResult().get(0);
    }

    private List<String> parseRecommendDTO(RecommendDTO recommendDTO){
        if(Objects.isNull(recommendDTO)){
            return Lists.newArrayList();
        }
        String reason = (String)recommendDTO.getBizData().get(RECOMMEND_REASON);
        if(StringUtils.isEmpty(reason)){
            return Lists.newArrayList();
        }
        return Arrays.asList(reason.split(REASON_SPLIT));
    }

    private Map<String, Object> buildCardSaysBizParam(long shopId){
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("shopIds", String.valueOf(shopId));
        bizParams.put("bizType","002");
        bizParams.put("recReasonContentType", "001");
        bizParams.put("expType","999");
        bizParams.put("flowFlag", "002");
        return bizParams;
    }

    @Data
    private static class MainTitleConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
    }
}
