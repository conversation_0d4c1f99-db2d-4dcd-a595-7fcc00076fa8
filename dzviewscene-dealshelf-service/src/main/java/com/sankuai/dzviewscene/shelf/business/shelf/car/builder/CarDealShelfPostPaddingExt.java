package com.sankuai.dzviewscene.shelf.business.shelf.car.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcherExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import deps.redis.clients.util.CollectionUtils;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/4/15 9:12 下午
 */
@ExtPointInstance(name = "爱车团购货架商品填充后扩展点")
public class CarDealShelfPostPaddingExt extends PaddingFetcherExtAdapter {

    @Override
    public void postPadding(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.builder.CarDealShelfPostPaddingExt.postPadding(ActivityContext,String,ProductGroupM)");
        if(productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return;
        }
        productGroupM.getProducts().forEach(productM -> {
            ProductActivityM productActivityM = CollectUtils.firstValue(productM.getActivities());
            if(productActivityM == null || (StringUtils.isEmpty(productActivityM.getUrl()) && StringUtils.isEmpty(productActivityM.getLable()))) {
                return;
            }
            productM.setAttr("hasActivity", "true");
        });
    }
}
