package com.sankuai.dzviewscene.shelf.platform.shelf;

import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.Activity;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.activity.ActivityBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.cardbar.CardBarBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.monitor.ShelfActivityMonitor;
import com.sankuai.dzviewscene.shelf.platform.shelf.ocean.ShelfCommonOcean;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 货架活动编排, 目的是构造DzShelfResponseVO
 * <p>
 * Created by float.lu on 2020/8/20.
 */
@Activity(code = ShelfActivity.ACTIVITY_SHELF_CODE, name = "标准货架活动")
public class ShelfActivity extends AbstractActivity<DzShelfResponseVO> {

    public static final String ACTIVITY_SHELF_CODE = "ShelfActivity";

    @Resource
    private ShelfActivityMonitor shelfActivityMonitor;

    @Override
    public CompletableFuture<DzShelfResponseVO> execute(ActivityContext activityContext) {

        // 1. 流程路由, 构造货架数据对象, 当前以代码编排流程
        activityContext.setMainData(findActivityFlow(activityContext).execute(this, activityContext));

        // 2. 构造货架活动结果对象
        return shelfActivityMonitor.doMonitor(activityContext, buildDzShelfResponseVO(activityContext));
    }


    private CompletableFuture<DzShelfResponseVO> buildDzShelfResponseVO(ActivityContext activityContext) {

        // 1. 构造打点对象
        CompletableFuture<ShelfOceanVO> shelfOceanCompletableFuture = findAbility(activityContext, OceanBuilder.ABILITY_SHELF_OCEAN_CODE).build(activityContext);

        // 2. 构造筛选组件
        CompletableFuture<Map<String, FilterComponentVO>> filterCompletableVOFuture = findAbility(activityContext, FilterBuilder.ABILITY_FILTER_GEN_CODE).build(activityContext);

        // 3. 构造卡片区
        CompletableFuture<DzShelfCardBarVO> cardBarCompletableFuture = findAbility(activityContext, CardBarBuilder.ABILITY_CARD_VAR_CODE).build(activityContext);

        // 4. 构造标题区
        CompletableFuture<MainTitleComponentVO> mainTitleCompletableFuture = findAbility(activityContext, MainTitleBuilder.ABILITY_MAIN_TITLE_CODE).build(activityContext);

        // 5. 构造ITEMS区
        CompletableFuture<List<FilterBtnIdAndProAreasVO>> shelfFloorsCompletableFuture = findAbility(activityContext, FloorsBuilder.ITEM_AREA_ABILITY_CODE).build(activityContext);

        // 6. 构造活动区
        CompletableFuture<ActivityComponentVO> activityComponentVOFuture = findAbility(activityContext, ActivityBuilder.ABILITY_ACTIVITY_CODE).build(activityContext);

        // 7. 构造货架结果
        return CompletableFuture.allOf(
                shelfOceanCompletableFuture,
                filterCompletableVOFuture,
                cardBarCompletableFuture,
                mainTitleCompletableFuture,
                shelfFloorsCompletableFuture,
                activityComponentVOFuture
        ).thenApply(v -> {
            DzShelfResponseVO dzShelfResponseVO = new DzShelfResponseVO();
            dzShelfResponseVO.setShelfComponent(buildDzShelfComponentVO(activityContext, findFirstFilterComponent(filterCompletableVOFuture.join()), cardBarCompletableFuture.join(), mainTitleCompletableFuture.join(), shelfFloorsCompletableFuture.join(), activityComponentVOFuture.join()));
            dzShelfResponseVO.setOcean(ShelfCommonOcean.paddingCommonOcean(shelfOceanCompletableFuture.join(), dzShelfResponseVO.getShelfComponent(), activityContext));
            return dzShelfResponseVO;
        });
    }

    private DzShelfComponentVO buildDzShelfComponentVO(ActivityContext activityContext, FilterComponentVO filterComponentVO, DzShelfCardBarVO cardBarVO, MainTitleComponentVO mainTitleComponentVO, List<FilterBtnIdAndProAreasVO> shelfFloors, ActivityComponentVO activity) {
        DzShelfComponentVO dzShelfComponentVO = new DzShelfComponentVO();
        removeFilterStyle(filterComponentVO);
        dzShelfComponentVO.setFilter(filterComponentVO);
        dzShelfComponentVO.setCardBar(cardBarVO);
        dzShelfComponentVO.setMainTitle(mainTitleComponentVO);
        dzShelfComponentVO.setFilterIdAndProductAreas(shelfFloors);
        dzShelfComponentVO.setShowType(ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Style.showType));
        dzShelfComponentVO.setSceneCode(getSceneCode(activityContext));
        dzShelfComponentVO.setCategoryId(getShopCategoryId(activityContext));
        dzShelfComponentVO.setShelfInteractType(ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Style.interactType));
        dzShelfComponentVO.setActivity(activity);
        return dzShelfComponentVO;
    }

    private String getSceneCode(ActivityContext activityContext) {
        if (activityContext == null) {
            return null;
        }
        return activityContext.getSceneCode();
    }

    private void removeFilterStyle(FilterComponentVO filterComponentVO) {
        if(filterComponentVO == null || CollectionUtils.isEmpty(filterComponentVO.getFilterBtns())) {
            return;
        }
        removeFilterStyle(filterComponentVO.getFilterBtns());
    }

    private void removeFilterStyle(List<FilterButtonVO> filterBtns) {
        if (CollectionUtils.isEmpty(filterBtns)) {
            return;
        }
        filterBtns.forEach(filter -> {
            if(filter == null) {
                return;
            }
            removeRichLabelVOStyle(filter.getTitle());
            removeRichLabelVOStyle(filter.getSelectedTitle());
            removeFilterStyle(filter.getChildren());
        });
    }

    private void removeRichLabelVOStyle(RichLabelVO richLabelVO) {
        if(richLabelVO == null) {
            return;
        }
        if(StringUtils.isEmpty(richLabelVO.getText()) || richLabelVO.getText().equals("玩美季")) {
            return;
        }
        richLabelVO.setTextSize(0);
        richLabelVO.setTextColor(StringUtils.EMPTY);
        richLabelVO.setFontWeight(StringUtils.EMPTY);
    }

    private int getShopCategoryId(ActivityContext ctx) {
        ShopM shopM = ctx.getParam(ProductDetailActivityConstants.Ctx.ctxShop);
        if (shopM == null) {
            return 0;
        }
        return shopM.getCategory();
    }


    // 目前只支持单层货架
    private FilterComponentVO findFirstFilterComponent(Map<String, FilterComponentVO> groupFilterComponents) {
        if (MapUtils.isEmpty(groupFilterComponents)) {
            return null;
        }
        return groupFilterComponents.values().stream().findFirst().orElse(null);
    }

}
