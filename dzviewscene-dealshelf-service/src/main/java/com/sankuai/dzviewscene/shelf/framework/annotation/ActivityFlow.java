package com.sankuai.dzviewscene.shelf.framework.annotation;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.lang.annotation.*;

/**
 * 流程注解, 一个活动多个流程
 * <p>
 * Created by float on 2020/8/22.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Indexed
@Component
public @interface ActivityFlow {

    /**
     * 所属活动编码
     *
     * @return
     */
    String activityCode();

    /**
     * 流程编号
     *
     * @return
     */
    String flowCode();

    /**
     * 活动流程名
     *
     * @return
     */
    String name();
}
