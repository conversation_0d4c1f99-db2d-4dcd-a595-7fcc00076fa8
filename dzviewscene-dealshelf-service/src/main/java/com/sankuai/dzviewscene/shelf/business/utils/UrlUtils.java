package com.sankuai.dzviewscene.shelf.business.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

public class UrlUtils {

    public static final String DP_APP_SCHEMA = "dianping://web?url=";

    public static final String MT_APP_SCHEMA = "imeituan://www.meituan.com/web?url=";


    public static final String MT_XCX_SCHEMA = "/index/pages/h5/h5?weburl=";

    public static final String MT_XCX_PURCHASE_SCHEMA = "/index/pages/h5/h5?f_openId=1&f_token=1&weburl=";
    public static final String MT_XCX_PURCHASE_PARAMS = "&product=mtwxapp";

    public static final String DP_XCX_SCHEMA = "/pages/webview/webview?url=";
    public static final String DP_XCX_PURCHASE_PARAMS = "&openId=!&token=!&utm_source=dianping-wxapp";


    private static final String PARAM_SPLIT = "&";
    private static final String PARAM_URL_SUFFIX = "?";
    private static final String PARAM_CONTAIN = "=";
    private final static String URL_TEMPLATE = "%s%s%s";


    public static String uriToUrl(int platform , String uri){
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.uriToUrl(int,java.lang.String)");
        String host = VCPlatformEnum.DP.getType() == platform ? Lion.getStringValue("com.sankuai.dzviewscene.productshelf.dp.g.host","") : Lion.getStringValue("com.sankuai.dzviewscene.productshelf.mt.g.host","");
        return host.concat(uri);
    }

    public static String addWxXcxParams (int uaCode,String url) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.addWxXcxParams(int,java.lang.String)");
        if (VCClientTypeEnum.isMtClientTypeByCode(uaCode)) {
            return url + "&product=mtwxapp";
        } else {
            return url + "&utm_source=dianping-wxapp";
        }
    }

    public static String getAPPUrl(int uaCode, String uri) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.getAPPUrl(int,java.lang.String)");
        String url = uriToUrl(getPlatformByUaCode(uaCode), uri);
        return getAPPUrlNoConcat(uaCode,url);
    }

    public static String getAPPUrlNoConcat(int uaCode, String url) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.getAPPUrlNoConcat(int,java.lang.String)");
        if(uaCode <= 0){
            return url;
        }
        if(isRawUrlChannel(uaCode)){
            return url;
        }
        if (isWxXcx(uaCode)) {
            return buildWxXcxUrl(uaCode, url);
        }
        return buildAppUrl(uaCode, url);
    }

    public static String getAPPUrlWithMHost(int uaCode, String url) {
        if(uaCode <= 0){
            return url;
        }
        if(isRawUrlChannel(uaCode)){
            return url;
        }
        if (isWxXcx(uaCode)) {
            return buildWxXcxUrlForPurchase(uaCode, url);
        }
        return buildAppUrl(uaCode, url);
    }
    
    private static String buildAppUrl(int uaCode, String url) {
        if (VCClientTypeEnum.isMtClientTypeByCode(uaCode)) {
            return MT_APP_SCHEMA.concat(urlEncode(url));
        } else {
            return DP_APP_SCHEMA.concat(urlEncode(url));
        }
    }

    public static boolean isWxXcx(int uaCode) {
        return VCClientTypeEnum.DP_XCX.getCode() == uaCode || VCClientTypeEnum.MT_XCX.getCode() == uaCode;
    }

    private static boolean isRawUrlChannel (int uaCode) {
       return VCClientTypeEnum.DP_M.getCode() == uaCode || VCClientTypeEnum.MT_I.getCode() == uaCode ||
               VCClientTypeEnum.DP_WX.getCode() == uaCode || VCClientTypeEnum.MT_WX.getCode() == uaCode;
    }

    private static String buildWxXcxUrlForPurchase(int uaCode, String url) {
        if (VCClientTypeEnum.isMtClientTypeByCode(uaCode)) {
            return MT_XCX_PURCHASE_SCHEMA.concat(urlEncode(url.concat(MT_XCX_PURCHASE_PARAMS)));
        } else {
            return DP_XCX_SCHEMA.concat(urlEncode(url.concat(DP_XCX_PURCHASE_PARAMS)));
        }
    }

    private static String buildWxXcxUrl(int uaCode, String url) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.buildWxXcxUrl(int,java.lang.String)");
        if (VCClientTypeEnum.isMtClientTypeByCode(uaCode)) {
            return MT_XCX_SCHEMA.concat(urlEncode(url));
        } else {
            return DP_XCX_SCHEMA.concat(urlEncode(url));
        }
    }

    private static int getPlatformByUaCode(int uaCode){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.getPlatformByUaCode(int)");
        if(VCClientTypeEnum.isMtClientTypeByCode(uaCode)){
            return VCPlatformEnum.MT.getType();
        }
        return VCPlatformEnum.DP.getType();
    }
    public static String urlEncode(String source) {
        String result = source;
        if (StringUtils.isNotBlank(source)) {
            try {
                result = URLEncoder.encode(source, "UTF8");
            } catch (Exception e) {
                result = StringUtils.EMPTY;
            }
        }
        return result;
    }

    /**
     * url后面拼接k=v的参数
     */
    public static String appendUrl(String url, String key, String value) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.appendUrl(java.lang.String,java.lang.String,java.lang.String)");
        return appendUrl(url, new HashMap<String, String>(1) {{
            put(key, value);
        }});
    }

    /**
     * url后面拼接data里的一组k/v参数
     */
    public static String appendUrl(String url, Map<String, String> data) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.appendUrl(java.lang.String,java.util.Map)");
        String paramStr = buildParam(data);
        // 去掉结尾的&或者?
        String newUrl = cutSuffix(url);
        // url包含?，则使用&拼接新的参数，否则使用?拼接
        return String.format(URL_TEMPLATE, newUrl, newUrl.contains(PARAM_URL_SUFFIX) ? PARAM_SPLIT : PARAM_URL_SUFFIX, paramStr);
    }

    /**
     * 去掉结尾的&或者?
     */
    private static String cutSuffix(String url) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.cutSuffix(java.lang.String)");
        if (StringUtils.isBlank(url)) {
            return StringUtils.EMPTY;
        }
        if (url.endsWith(PARAM_SPLIT) || url.endsWith(PARAM_URL_SUFFIX)) {
            return url.substring(0, url.length() - 1);
        }
        return url;
    }

    /**
     * 构造参数，k1=v1&k2=v2&...形式
     */
    private static String buildParam(Map<String, String> data) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.buildParam(java.util.Map)");
        if (MapUtils.isEmpty(data)) {
            return StringUtils.EMPTY;
        }
        StringBuilder param = new StringBuilder();
        data.forEach((k, v) -> param.append(k).append(PARAM_CONTAIN).append(StringUtils.isBlank(v) ? "" : v).append(PARAM_SPLIT));
        String paramStr = param.toString();
        // 去掉最后的&
        return cutSuffix(paramStr);
    }

    public static String getWebHeader(int platform) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.getWebHeader(int)");
        if (PlatformUtil.isMT(platform)) {
            return MT_APP_SCHEMA;
        }
        return DP_APP_SCHEMA;
    }

    public static String getZjXcxSchemaUrl(int platform, String encodedUrl) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.UrlUtils.getZjXcxSchemaUrl(int,java.lang.String)");
        if (platform == VCPlatformEnum.MT.getType() || VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            return String.format("imeituan://www.meituan.com/mmp?targetPath=%s&appId=366710c93c9446e4", encodedUrl);
        }
        return String.format("dianping://mmp?targetPath=%s&appId=366710c93c9446e4", encodedUrl);
    }
}
