package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCombineBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.combine.CombineItemDTO;
import com.sankuai.general.product.query.center.client.enums.CombineTypeEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 多次卡关联关系召回
 *
 * @author: created by hang.yu on 2024/3/4 10:46
 */
@Component
public class TimesDealRelationHandler implements GroupQueryHandler {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityContext activityContext, String groupName, Map<String, Object> params) {
        // 获取查询的团单
        long entityId = ParamsUtil.getLongSafely(activityContext, ShelfActivityConstants.Params.entityId);
        if (entityId <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        // 调用查询中心接口召回有关联关系的团单id
        return compositeAtomService.queryByDealGroupIds(buildRequest(entityId, activityContext)).thenApply(res -> {
            if (res == null || CollectionUtils.isEmpty(res.getList())) {
                return null;
            }
            DealGroupDTO dealGroupDTO = res.getList().get(0);
            if (CollectionUtils.isEmpty(dealGroupDTO.getCombines())) {
                return null;
            }
            Set<Long> combineDealIds = dealGroupDTO.getCombines().stream().filter(combineDTO -> CollectionUtils.isNotEmpty(combineDTO.getCombineItems()))
                    .flatMap(combineDTO -> combineDTO.getCombineItems().stream()).map(CombineItemDTO::getCombineItemId)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(combineDealIds)) {
                return null;
            }
            combineDealIds.add(entityId);
            return buildProductGroupM(combineDealIds);
        });
    }

    public QueryByDealGroupIdRequest buildRequest(long dealId, ActivityContext activityContext) {
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        IdTypeEnum idTypeEnum = PlatformUtil.isMT(platform) ? IdTypeEnum.MT : IdTypeEnum.DP;
        return QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(Sets.newHashSet(dealId), idTypeEnum)
                .combine(DealGroupCombineBuilder
                        .builder(idTypeEnum)
                        .combineTypeEnum(CombineTypeEnum.COUNT_CARD_RELATION)
                        .shopId(getShopIdFromParams(activityContext)))
                .build();
    }

    public long getShopIdFromParams(ActivityContext activityContext) {
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.mtPoiIdL) + "", 0L);
        }
        return NumberUtils.toLong(activityContext.getParam(ShelfActivityConstants.Params.dpPoiIdL) + "", 0L);
    }

    public ProductGroupM buildProductGroupM(Set<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return null;
        }
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setHasNext(false);
        productGroupM.setTotal(dealIds.size());
        productGroupM.setProducts(dealIds.stream().map(this::buildProductM).collect(Collectors.toList()));
        return productGroupM;
    }


    public ProductM buildProductM(Long dealId) {
        ProductM productM = new ProductM();
        productM.setProductId(dealId.intValue());
        // 多次卡召回标识 用于后续排序、构建数据使用
        productM.setTimesDealQueryFlag(true);
        return productM;
    }

}
