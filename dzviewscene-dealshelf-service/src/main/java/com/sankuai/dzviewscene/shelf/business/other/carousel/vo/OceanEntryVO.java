package com.sankuai.dzviewscene.shelf.business.other.carousel.vo;


import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0x55dd)
public class OceanEntryVO implements Serializable {
    /**
     * 额外数据，json
     */
    @MobileDo.MobileField(key = 0x90b4)
    private String labs;

    /**
     * 打点上报的表名
     */
    @MobileDo.MobileField(key = 0xbffc)
    private String category;

    /**
     * abtest信息
     */
    @MobileDo.MobileField(key = 0xf312)
    private String abtest;

    /**
     * 模块点击打点
     */
    @MobileDo.MobileField(key = 0x9dbb)
    private String bidView;

    /**
     * 模块曝光点
     */
    @MobileDo.MobileField(key = 0xcba3)
    private String bidClick;
}