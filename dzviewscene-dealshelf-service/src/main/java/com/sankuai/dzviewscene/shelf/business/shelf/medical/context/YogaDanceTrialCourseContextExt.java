package com.sankuai.dzviewscene.shelf.business.shelf.medical.context;

import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.DouHuExtContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextExt;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2022/04/26
 */
@ContextExt(name = "YogaDanceTrialCourseContextExt")
public class YogaDanceTrialCourseContextExt implements IContextExt<Object> {

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.yoga.douhu.exp.ids", defaultValue = "[]")
    private List<String> yogaDouHuList;

    @Override
    public String contextKey() {
        return "YogaDanceTrialCourseContextExt";
    }

    @Override
    public CompletableFuture<Object> contextExt(ActivityContext activityContext) {
        //命中斗斛，修改分组查询参数为空召回
        activityContext.addParam(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.deal.toString(), new HashMap<String, Object>() {{
                // 空召回
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.emptyQuery.name());
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.empty.name());
            }});
        }});
        return CompletableFuture.completedFuture(null);
    }
}
