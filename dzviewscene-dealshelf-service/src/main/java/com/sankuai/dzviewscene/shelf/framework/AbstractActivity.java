package com.sankuai.dzviewscene.shelf.framework;

import com.sankuai.dzviewscene.shelf.framework.core.IAbility;
import com.sankuai.dzviewscene.shelf.framework.core.IActivity;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;

import javax.annotation.Resource;

/**
 * 活动抽象实现, 提供能力、流程及活动校验器查找实现,
 * 课件, 活动关联如下扩展点:
 * 1. 校验器扩展(校验器可以通过能力实现, 单独提取概念山更清晰)
 * 2. 上下文扩展
 * 3. 流程扩展
 * 4. 能力及能力扩展点
 * <p>
 * Created by float on 2020/8/22.
 */
public abstract class AbstractActivity<Result> implements IActivity<Result> {

    @Resource
    private ComponentFinder componentFinder;

    /**
     * 根据上下文和流程码, 查找流程实例
     *
     * @param activityContext
     * @param <AbilityFlow>
     * @return
     */
    public <AbilityFlow extends IActivityFlow> AbilityFlow findActivityFlow(ActivityContext activityContext) {
        return (AbilityFlow) componentFinder.findActivityFlow(activityContext);
    }

    /**
     * 根据上下文和能力code查找能力实例
     *
     * @param activityContext
     * @param abilityCode
     * @param <Ability>
     * @return
     */
    public <Ability extends IAbility> Ability findAbility(ActivityContext activityContext, String abilityCode) {
        return (Ability) componentFinder.findAbility(activityContext, abilityCode);
    }


}
