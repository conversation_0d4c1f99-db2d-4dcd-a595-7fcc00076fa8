package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle;

import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.sankuai.dzviewscene.shelf.framework.AbstractAbility;
import com.sankuai.dzviewscene.shelf.framework.annotation.Ability;

/**
 * 主标题构造能力, 无默认实现, 业务可按需实现
 * <p>
 * Created by float on 2020/8/22.
 */
@Ability(code = MainTitleBuilder.ABILITY_MAIN_TITLE_CODE, name = "主标题构造能力")
public abstract class MainTitleBuilder extends AbstractAbility<MainTitleComponentVO, MainTitleBuilderExt> {


    public static final String ABILITY_MAIN_TITLE_CODE = "MainTitleBuilder";

}
