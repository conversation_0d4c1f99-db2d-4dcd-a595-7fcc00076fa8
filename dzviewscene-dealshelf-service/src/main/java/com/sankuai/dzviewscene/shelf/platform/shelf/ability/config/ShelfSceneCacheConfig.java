package com.sankuai.dzviewscene.shelf.platform.shelf.ability.config;

import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * 货架分场景缓存配置
 */
@Data
public class ShelfSceneCacheConfig implements Serializable {

    /**
     * 是否使用筛选缓存
     */
    private boolean useFilterCache;

    /**
     * 是否使用召回缓存
     */
    private boolean useQueryCache;

    /**
     * 召回缓存使用数量大小，最多使用缓存中的前多少个数据
     */
    private int queryUseSize;

    private static final String SCENE_CACHE_CONFIG_KEY_TEMPLATE = "com.sankuai.dzviewscene.productshelf.shelf.cache.config.%s";

    private static final String CONFIG_GROUP_NAME = "cache";

    private static final ShelfSceneCacheConfig defaultConfig = new ShelfSceneCacheConfig();

    public ShelfSceneCacheConfig () {
        useFilterCache = false;
        useQueryCache = false;
        queryUseSize = 20;
    }

    /**
     * 通过SceneCode获取缓存配置
     *
     * @param sceneCode
     * @return
     */
    public static ShelfSceneCacheConfig getCacheConfigBySceneCode(String sceneCode) {
        try {
            if (StringUtils.isEmpty(sceneCode)) {
                return defaultConfig;
            }
            String configKey = String.format(SCENE_CACHE_CONFIG_KEY_TEMPLATE, sceneCode);
            return Lion.getConfigRepository("com.sankuai.dzviewscene.productshelf", CONFIG_GROUP_NAME).getBean(configKey, ShelfSceneCacheConfig.class, defaultConfig);
        } catch (Exception e) {
            Cat.logError("缓存配置解析失败，SceneCode:" + sceneCode, e);
            return defaultConfig;
        }
    }

}
