package com.sankuai.dzviewscene.shelf.platform.common.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by float on 2020/8/22.
 */
@Data
@AllArgsConstructor
public class ProductGroupM {

    /**
     * 该组商品总数
     */
    private int total;


    /**
     * 是否还有商品
     */
    private boolean hasNext;

    /**
     * 商品列表
     */
    private List<ProductM> products;

    /**
     * 统计信息
     */
    private Map<String, Object> statistics;

    /**
     * 商品层级结构信息
     */
    private ProductHierarchyNodeM productHierarchyRoot;

    /**
     * 活动信息
     */
    private ActivityM activity;

    /**
     * 预填充的商品
     */
    private Map<Integer, List<ProductM>> preLoadProducts;

    public ProductGroupM(){}

    public ProductGroupM(int total, List<ProductM> products) {
        this.total = total;
        this.products = products;
    }

}
