package com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.dp.schema.Schemas;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemAreaComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.context.CommunityTab;
import com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.AbstractFloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.hadoop.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhaizhui on 2020/9/10
 */
@ExtPointInstance(name = "小区货架货架楼层构造扩展点")
public class CommunityDealFloorsBuilderExt extends AbstractFloorsBuilderExtAdapter {

    public static final String SHELF_TITLE = "小区附近的上门服务";
    //////////////////////////////////////URL拼接相关常量//////////////////////////////////////
    private static final String DP_OPEN_LOCK = "com.sankuai.dzviewscene.productshelf.dp.openlock.url.pattern";
    private static final String MT_OPEN_LOCK = "com.sankuai.dzviewscene.productshelf.mt.openlock.url.pattern";
    private static final String COMMUNITY_TABS = "com.sankuai.dzviewscene.productshelf.community.tabs";
    private static final String MT_URL_SCHEMA = "imeituan://www.meituan.com/web?url=";
    private static final String DP_URL_SCHEMA = "dianping://web?url=";
    private static final String MT_G_DOMAIN = "com.sankuai.dzviewscene.productshelf.mt.g.domain";
    private static final String DP_M_DOMAIN = "com.sankuai.dzviewscene.productshelf.dp.m.domain";
    private static final String DP_URL_PATTERN = "com.sankuai.dzviewscene.productshelf.dp.community.url.pattern";
    private static final String MT_URL_PATTERN = "com.sankuai.dzviewscene.productshelf.mt.community.url.pattern";
    //////////////////////////////////////TITLE ICON//////////////////////////////////////
    private static final String DP_TITLE_ICON = "https://img.meituan.net/dpmobile/2f0dfa7f86bd5368c665c0a8930afea51056.png";
    private static final String MT_TITLE_ICON = "https://img.meituan.net/dpmobile/55884e5e98d6e356a1ef455d1a1db7381832.png";
    private static final String TAB_NAME_OPEN_LOCK = "开锁换锁";

    private static final Logger LOGGER = LoggerFactory.getLogger(CommunityDealFloorsBuilderExt.class);


    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.floorDefaultShowNum(ActivityContext,String,List)");
        return 3;
    }

    @Override
    public int floorShowType(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.floorShowType(ActivityContext,String,ProductGroupM)");
        return 1;
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, DzItemAreaComponentVO ItemAreaComponentVO, long filterId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.moreComponentText(ActivityContext,String,DzItemAreaComponentVO,long)");
        List<CommunityTab> communityTabs = Lion.getList(COMMUNITY_TABS, CommunityTab.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(communityTabs)) {
            return null;
        }
        Map<Long, CommunityTab> filterId2communityTab = communityTabs.stream().collect(Collectors.toMap(CommunityTab::getFilterId, communityTab -> communityTab));
        if (!filterId2communityTab.containsKey(filterId)) {
            return null;
        }
        return "查看附近" + filterId2communityTab.get(filterId).getTabName() + "门店";
    }

    @Override
    public String moreComponentJumpUrl(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, long filterId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.moreComponentJumpUrl(ActivityContext,String,ProductGroupM,long)");
        List<CommunityTab> communityTabs = Lion.getList(COMMUNITY_TABS, CommunityTab.class, Lists.newArrayList());
        if (CollectionUtils.isEmpty(communityTabs)) {
            return null;
        }

        Map<Long, CommunityTab> filterId2communityTab = communityTabs.stream().collect(Collectors.toMap(CommunityTab::getFilterId, communityTab -> communityTab));
        if (!filterId2communityTab.containsKey(filterId)) {
            return null;
        }

        int dpCityId = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId);
        double lat = activityContext.getParam(ShelfActivityConstants.Params.lat);
        double lng = activityContext.getParam(ShelfActivityConstants.Params.lng);
        String keyWord = filterId2communityTab.get(filterId).getKeyWord();
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        long categoryId = getCategoryId(filterId2communityTab.get(filterId), platform);

        if (platform == VCPlatformEnum.DP.getType()) {
            return buildDPMoreURL(filterId2communityTab.get(filterId), dpCityId, categoryId, keyWord, lat, lng, activityContext);
        }
        return buildMTMoreURL(filterId2communityTab.get(filterId), categoryId, keyWord, lat, lng);
    }

    @Override
    public String titleComponentTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.titleComponentTitle(ActivityContext,String,ProductGroupM)");
        return SHELF_TITLE;
    }


    @Override
    public String titleComponentIcon(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.titleComponentIcon(ActivityContext,String,ProductGroupM)");
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        if (platform == VCPlatformEnum.DP.getType()) {
            return DP_TITLE_ICON;
        }
        return MT_TITLE_ICON;
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.itemComponentPic(ActivityContext,String,ProductM)");
        String headPic = productM.getPicUrl();
        if (StringUtil.isBlank(headPic)) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(headPic);
        picAreaVO.setPic(dzPictureComponentVO);
        return picAreaVO;
    }

    private String buildMTMoreURL(CommunityTab communityTab, long categoryId, String keyWord, double lat, double lng) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.buildMTMoreURL(CommunityTab,long,String,double,double)");
        if (TAB_NAME_OPEN_LOCK.equals(communityTab.getTabName())) {
            return StringUtils.format(Lion.get(MT_OPEN_LOCK), categoryId, lat, lng);
        }
        String encodeKeyWord = encodeZW2UTF8(keyWord);
        return Schemas.forMtWeb().setUrl(Lion.get(MT_G_DOMAIN) + StringUtils.format(Lion.get(MT_URL_PATTERN), categoryId, encodeKeyWord, lat, lng)).build();
    }

    private String buildDPMoreURL(CommunityTab communityTab, int dpCityId, long dpCategoryId, String keyWord, double lat, double lng, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.buildDPMoreURL(CommunityTab,int,long,String,double,double,ActivityContext)");
        String encodeKeyWord = encodeZW2UTF8(keyWord);
        if (TAB_NAME_OPEN_LOCK.equals(communityTab.getTabName())) {
            return StringUtils.format(Lion.get(DP_OPEN_LOCK), dpCityId, dpCategoryId, encodeKeyWord, lat, lng);
        }
        String dpId = activityContext.getParam(ShelfActivityConstants.Params.unionId);
        return Schemas.forDpWeb().setUrl(Lion.get(DP_M_DOMAIN) + StringUtils.format(Lion.get(DP_URL_PATTERN), dpCityId, dpId, lat, lng, dpCategoryId, encodeKeyWord)).build();
    }

    private long getCategoryId(CommunityTab communityTab, int platform) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.getCategoryId(com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.context.CommunityTab,int)");
        if (platform == VCPlatformEnum.DP.getType()) {
            return communityTab.getDpShopCategoryId();
        }
        return communityTab.getMtShopCategoryId();
    }

    private String encodeZW2UTF8(String keyWord) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityDealFloorsBuilderExt.encodeZW2UTF8(java.lang.String)");
        try {
            return URLEncoder.encode(keyWord, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            LOGGER.error("中文encode错误", e);
        }
        return "";
    }
}
