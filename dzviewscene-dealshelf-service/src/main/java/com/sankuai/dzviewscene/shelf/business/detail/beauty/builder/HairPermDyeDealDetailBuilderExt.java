package com.sankuai.dzviewscene.shelf.business.detail.beauty.builder;

import com.dianping.cat.Cat;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.OptionalSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.shelf.utils.AntiCrawlerUtils;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.*;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealDetailModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.DealStructModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.detail.ability.builder.ProductDetailBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/30 3:10 下午
 */
@ExtPointInstance(name = "丽人-烫发染发结构化-团单详情页扩展点实现")
public class HairPermDyeDealDetailBuilderExt implements ProductDetailBuilderExt {

    private static final String DEAL_STRUCT_CONTENTT_ATTR_NAME = "dealStructContent";

    private static final String HAIR_DYE_PROCESS_SKU_ATTR_NAME = "染发工艺";

    private static final String HAIR_DYE_PROCESS_SKU_ATTR_VALUE = "染发";

    private static final String REFERENCE_COLOR_SKU_ATTR_NAME = "参考颜色";

    public static final String DETAIL_INFO = "detailInfo";

    private static final String PRICE_FORMAT = "%s元";

    private static final String SKU_COPIES_FORMAT = "%s份";

    private static final String SKU_PIC_ATTR_VALUE_SEPARATOR = "、";

    private static final String OTHER_SKU_CATEGORY = "自填";

    private static final String ADDITION_PRICE_ATTR_NAME = "additionPrice";

    private static final String EXTRA_HAIR_LIMIT_ATTR_NAME = "extHairLimit";

    private static final String BLEACH_ADDITION_ATTR_NAME = "bleachAddtionPrice";

    private static final String COLOR = "漂色";

    private static final String SUITABLE_HAIR_LENGTH = "适合发长";

    private static final String OPTIONAL_GROUP_DESC_FORMAT = "以下项目%s选%s";

    private static final String HAIR_WITHOUT_LENGTH = "不区分长度";

    private static final String HAIR_UNDER_ARM = "不过肩";

    private static final String HAIR_UNDER_CHEST = "不过胸";

    private static final String HAIR_UNDER_ARM_PRICE_ATTR_NAME = "additionPrice";

    private static final String HAIR_UNDER_CHEST_PRICE_ATTR_NAME = "chestAdditionPrice";

    private static final String PRECOLOR_PERM_ATTR_NAME = "底色染发";

    private static final String PERT_PRECOLOR_PERM_ATTR_VALUE = "不包含全头底色染发";

    private static final String EXTRA_PRICE_ATTR_NAME = "额外费用";

    private static final String CATEGORY_ATTR_NAME = "category1";

    private static final String HAIR_LENGTH_WITHOUT_LIMIT = "不限发长";

    private static final String CONTAIN_HIAR_REMOVE_FREE = "包含免费拆卸";

    private static final String RANGE_LIMITED = "限用范围";

    private static final String NO_LIMIT = "无限制";

    private static final String HIAR_REMOVE_FREE = "免费拆卸";

    private static final String HAIR_EXT_ATTR_NAME = "接发发长";

    private static final String HAIR_REMOVE_ATTR_NAME = "拆卸";

    private static final List<String> BRAND_SKU_ATTR_NAMES = Lists.newArrayList("洗发水品牌", "吹风机品牌");

    private static final String OTHER_BRAND = "其他品牌";

    private static final String HAIR_ADDITIONAL_ITEM_ATTR_NAME = "hair_additional_item";

    private static final String HAIR_ADDITIONAL_ITEM_TITLE = "附赠项目";

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.hair.perm.dye.detail.sku.category.attrs.model.list", defaultValue = "{}")
    private SkuCategoryAttrsModelList skuCategoryAttrsModelList;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.hair.perm.dye.deal.detail.constant.config", defaultValue = "{}")
    private DetailConstant detailConstant;

    @Override
    public Object buildDetail(ActivityContext activityContext, ProductM productM) {
        if (productM == null) {
            return null;
        }
        return buildDealDetailStructModuleVO(productM, activityContext);
    }

    private DealDetailStructModuleVO buildDealDetailStructModuleVO(ProductM productM, ActivityContext activityContext) {
        if (productM == null) {
            return null;
        }
        DealDetailStructModuleVO dealDetailStructModuleVO = new DealDetailStructModuleVO();
        //市场价
        dealDetailStructModuleVO.setMarketPrice(String.format(PRICE_FORMAT, productM.getMarketPrice()));
        //售价
        if (productM.getBasePrice() != null) {
            dealDetailStructModuleVO.setPrice(String.format(PRICE_FORMAT, productM.getBasePrice().stripTrailingZeros().toPlainString()));
        }
        //必选模块
        MustDealSkuStructInfoVO mustDealSkuStructInfoVO = getMustDealSkuStructInfoVO(productM);
        if (mustDealSkuStructInfoVO != null) {
            dealDetailStructModuleVO.setMustGroups(Lists.newArrayList(mustDealSkuStructInfoVO));
        }
        //可选模块
        List<OptionalDealSkuStructInfoVO> optionalDealSkuStructInfoVOs = getOptionalDealSkuStructInfoVOs(productM);
        if (CollectionUtils.isNotEmpty(optionalDealSkuStructInfoVOs)) {
            dealDetailStructModuleVO.setOptionalGroups(optionalDealSkuStructInfoVOs);
        }
        //属性信息
        DealDetailStructAttrVO dealDetailStructAttrVO = getDealDetailStructAttrVO(productM);
        if (dealDetailStructAttrVO != null) {
            dealDetailStructModuleVO.setStructAttrs(Lists.newArrayList(dealDetailStructAttrVO));
        }
        //补充信息
        dealDetailStructModuleVO.setDesc(getSupplement(productM));
        AntiCrawlerUtils.hideProductKeyInfo(dealDetailStructModuleVO, activityContext);
        return dealDetailStructModuleVO;
    }

    private DealDetailStructAttrVO getDealDetailStructAttrVO(ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        List<String> attrValues = getAttrValuesByAttrName(HAIR_ADDITIONAL_ITEM_ATTR_NAME, productM.getExtAttrs());
        if (CollectionUtils.isEmpty(attrValues)) {
            return null;
        }
        DealDetailStructAttrVO dealDetailStructAttrVO = new DealDetailStructAttrVO();
        dealDetailStructAttrVO.setAttrName(HAIR_ADDITIONAL_ITEM_TITLE);
        dealDetailStructAttrVO.setAttrValues(attrValues);
        return dealDetailStructAttrVO;
    }

    private List<String> getAttrValuesByAttrName(String attrName, List<AttrM> extAttrs) {
        if (StringUtils.isEmpty(attrName) || CollectionUtils.isEmpty(extAttrs)) {
            return null;
        }
        AttrM attrM = extAttrs.stream().filter(attr -> attrName.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null || StringUtils.isEmpty(attrM.getValue())) {
            return null;
        }
        return Lists.newArrayList(attrM.getValue().split(","));
    }

    private String getSupplement(ProductM productM) {
        if (productM == null) {
            return null;
        }
        String dealDetailText = productM.getAttr(DETAIL_INFO);
        if (StringUtils.isEmpty(dealDetailText)) {
            return null;
        }
        UniformStructModel uniformStruct = JsonCodec.decode(dealDetailText, UniformStructModel.class);
        if (uniformStruct == null) {
            return null;
        }
        return getRichText(uniformStruct.getContent());
    }

    private String getRichText(List<UniformStructContentModel> structContentModels) {
        if (CollectionUtils.isEmpty(structContentModels)) {
            return null;
        }
        Object struct = structContentModels.stream().filter(model -> StringUtils.isNotEmpty(model.getType()) && model.getType().equals("richtext")).findFirst().orElse(null);
        if (struct == null) {
            return null;
        }
        if (!(struct instanceof UniformStructContentModel)) {
            return null;
        }
        UniformStructContentModel richTextModel = (UniformStructContentModel) struct;
        if (richTextModel == null || richTextModel.getData() == null) {
            return null;
        }
        return richTextModel.getData().toString();
    }

    private MustDealSkuStructInfoVO getMustDealSkuStructInfoVO(ProductM productM) {
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME);
        List<SkuItemDto> skuItemDtos = getMustSkuItemDtoList(dealDetail);
        List<ProductSkuCategoryModel> skuCategoryModels = getProductSkuCategoryModels(dealDetail);
        List<DealSkuStructInfoVO> dealSkuStructInfoVOS = getDealSkuStructInfoVOs(skuItemDtos, skuCategoryModels);
        return buildMustDealSkuStructInfoVO(dealSkuStructInfoVOS);
    }

    private List<OptionalDealSkuStructInfoVO> getOptionalDealSkuStructInfoVOs(ProductM productM) {
        String dealDetail = productM.getAttr(DEAL_STRUCT_CONTENTT_ATTR_NAME);
        List<ProductSkuCategoryModel> skuCategoryModels = getProductSkuCategoryModels(dealDetail);
        List<OptionalSkuItemsGroupDto> optionalSkuItemsGroupDtos = getOptionalSkuItemsGroupDto(dealDetail);
        if (CollectionUtils.isEmpty(optionalSkuItemsGroupDtos)) {
            return null;
        }
        return optionalSkuItemsGroupDtos.stream().map(optionalSkuGroups -> {
            if (optionalSkuGroups == null || CollectionUtils.isEmpty(optionalSkuGroups.getSkuItems())) {
                return null;
            }
            int optionalCount = optionalSkuGroups.getOptionalCount();
            List<DealSkuStructInfoVO> dealSkuStructInfoVOS = getDealSkuStructInfoVOs(optionalSkuGroups.getSkuItems(), skuCategoryModels);
            return buildOptionalDealSkuStructInfoVO(dealSkuStructInfoVOS, optionalCount);
        }).filter(skuListGroup -> skuListGroup != null).collect(Collectors.toList());
    }

    private OptionalDealSkuStructInfoVO buildOptionalDealSkuStructInfoVO(List<DealSkuStructInfoVO> dealSkuStructInfoVOS, int optionalCount) {
        if (CollectionUtils.isEmpty(dealSkuStructInfoVOS)) {
            return null;
        }
        OptionalDealSkuStructInfoVO optionalDealSkuStructInfoVO = new OptionalDealSkuStructInfoVO();
        optionalDealSkuStructInfoVO.setDesc(String.format(OPTIONAL_GROUP_DESC_FORMAT, dealSkuStructInfoVOS.size(), optionalCount));
        optionalDealSkuStructInfoVO.setDealStructInfo(dealSkuStructInfoVOS);
        return optionalDealSkuStructInfoVO;
    }

    private List<DealSkuStructInfoVO> getDealSkuStructInfoVOs(List<SkuItemDto> skuItemDtos, List<ProductSkuCategoryModel> skuCategoryModels) {
        if (CollectionUtils.isEmpty(skuItemDtos)) {
            return null;
        }
        Map<DealSkuStructInfoVO, Integer> dealSkuStructInfoVO2SequenceIdMap = skuItemDtos.stream().collect(HashMap::new, (map, sku) -> {
            String skuCategory = getSkuCategory(sku, skuCategoryModels);
            int skuSequenceId = getSkuSequenceId(skuCategory, sku);
            List<SkuAttrModel> skuAttrModels = getSkuAttrModels(skuCategory);
            List<DealStructVO> dealStructVOS = getDealStructVOs(sku.getAttrItems(), skuAttrModels);
            String skuName = getSkuName(skuCategory, sku);
            DealSkuStructInfoVO dealSkuStructInfoVO = buildDealSkuStructInfoVO(skuName, sku.getMarketPrice(), sku.getCopies(), dealStructVOS);
            if (dealSkuStructInfoVO == null) {
                return;
            }
            map.put(dealSkuStructInfoVO, skuSequenceId);
        }, HashMap::putAll);
        //货按照排列序号进行排序
        return dealSkuStructInfoVO2SequenceIdMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private String getSkuName(String skuCategory, SkuItemDto skuItemDto) {
        if (detailConstant == null || CollectionUtils.isEmpty(detailConstant.getSkuCategoryShowingSkuName()) || !detailConstant.getSkuCategoryShowingSkuName().contains(skuCategory)) {
            return skuCategory;
        }
        return skuItemDto.getName();
    }

    private int getSkuSequenceId(String category, SkuItemDto sku) {
        String sortCategory = getSortCategory(category, sku);
        if (StringUtils.isEmpty(sortCategory)) {
            return Integer.MAX_VALUE;
        }
        if (skuCategoryAttrsModelList == null || CollectionUtils.isEmpty(skuCategoryAttrsModelList.getSkuCategoryAttrsModels())) {
            return Integer.MAX_VALUE;
        }
        List<SkuCategoryAttrsModel> skuCategoryAttrsModels = skuCategoryAttrsModelList.getSkuCategoryAttrsModels();
        List<SkuCategoryAttrsModel> categoryAttrsModels = skuCategoryAttrsModels.stream().filter(model -> sortCategory.equals(model.getCategory())).collect(Collectors.toList());
        SkuCategoryAttrsModel skuCategoryAttrsModel = CollectUtils.firstValue(categoryAttrsModels);
        if (skuCategoryAttrsModel == null) {
            return Integer.MAX_VALUE;
        }
        return skuCategoryAttrsModel.getSequenceId();
    }

    private String getSortCategory(String category, SkuItemDto sku) {
        if (!HAIR_DYE_PROCESS_SKU_ATTR_VALUE.equals(category) || sku == null) {
            return category;
        }
        //如果货类别是染发，则按照货的二级分类（漂色/褪色/染发）进行排序
        String secondCategory = DealDetailUtils.getSkuAttrValueBySkuAttrName(sku.getAttrItems(), CATEGORY_ATTR_NAME);
        if (StringUtils.isEmpty(secondCategory)) {
            return category;
        }
        return secondCategory;
    }

    private List<DealStructVO> getDealStructVOs(List<SkuAttrItemDto> skuAttrItemDtos, List<SkuAttrModel> skuAttrModels) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return null;
        }
        Map<DealStructVO, Integer> dealStructVO2SequenceIdMap = skuAttrItemDtos.stream().collect(HashMap::new, (map, sku) -> {
            int attrSequenceId = getSkuAttrSequenceId(sku.getChnName(), skuAttrModels);
            String skuAttrTitle = getSkuAttrTitle(sku.getChnName(), skuAttrModels);
            if (StringUtils.isEmpty(skuAttrTitle)) {
                return;
            }
            String dealStructVOValue = getDealStructVOValue(skuAttrTitle, sku.getAttrValue(), skuAttrItemDtos);
            DealStructVO dealStructVO = buildDealStructVO(skuAttrTitle, dealStructVOValue);
            if (dealStructVO == null) {
                return;
            }
            map.put(dealStructVO, attrSequenceId);
        }, HashMap::putAll);
        //属性字段按照排列序号进行排序
        return dealStructVO2SequenceIdMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private String getDealStructVOValue(String dealStructVOName, String preValue, List<SkuAttrItemDto> skuAttrItemDtos) {
        if (StringUtils.isEmpty(dealStructVOName)) {
            return null;
        }
        if (HAIR_EXT_ATTR_NAME.equals(dealStructVOName)) {
            return getHairExtensionShowDoc(preValue, skuAttrItemDtos);
        }
        if (HAIR_REMOVE_ATTR_NAME.equals(dealStructVOName)) {
            return getHairRemoveShowDoc(preValue, skuAttrItemDtos);
        }
        String colorExtraPrice = getColorExtraPrice(skuAttrItemDtos);
        if (COLOR.equals(dealStructVOName) && StringUtils.isNotEmpty(colorExtraPrice)) {
            return getHairColorExtraPriceDoc(preValue, colorExtraPrice);
        }
        if (SUITABLE_HAIR_LENGTH.equals(dealStructVOName)) {
            return getSuitableHairLengthShowDoc(preValue, skuAttrItemDtos);
        }
        if (PRECOLOR_PERM_ATTR_NAME.equals(dealStructVOName)) {
            return PERT_PRECOLOR_PERM_ATTR_VALUE.equals(preValue) ? preValue : null;
        }
        if (EXTRA_PRICE_ATTR_NAME.equals(dealStructVOName)) {
            return StringUtils.isEmpty(preValue) ? null : String.format(detailConstant.getPermSkuExtraPriceFormat(), preValue);
        }
        return preValue;
    }

    private String getHairRemoveShowDoc(String hairExtAttrValue, List<SkuAttrItemDto> skuAttrItemDtos) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.HairPermDyeDealDetailBuilderExt.getHairRemoveShowDoc(java.lang.String,java.util.List)");
        if (CONTAIN_HIAR_REMOVE_FREE.equals(hairExtAttrValue)) {
            return HIAR_REMOVE_FREE;
        }
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return hairExtAttrValue;
        }
        SkuAttrItemDto hairAdditionPrice = skuAttrItemDtos.stream().filter(attr -> ADDITION_PRICE_ATTR_NAME.equals(attr.getAttrName())).findFirst().orElse(null);
        if (hairAdditionPrice == null || hairAdditionPrice.getAttrValue() == null) {
            return hairExtAttrValue;
        }
        if (detailConstant == null || StringUtils.isEmpty(detailConstant.getHairRemoveFormat())) {
            return hairExtAttrValue;
        }
        return String.format(detailConstant.getHairRemoveFormat(), hairAdditionPrice.getAttrValue());
    }

    private String getHairExtensionShowDoc(String hairExtAttrValue, List<SkuAttrItemDto> skuAttrItemDtos) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.HairPermDyeDealDetailBuilderExt.getHairExtensionShowDoc(java.lang.String,java.util.List)");
        if (HAIR_LENGTH_WITHOUT_LIMIT.equals(hairExtAttrValue)) {
            return hairExtAttrValue;
        }
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return hairExtAttrValue;
        }
        SkuAttrItemDto hairLengthLimit = skuAttrItemDtos.stream().filter(attr -> EXTRA_HAIR_LIMIT_ATTR_NAME.equals(attr.getAttrName())).findFirst().orElse(null);
        if (hairLengthLimit == null || hairLengthLimit.getAttrValue() == null) {
            return hairExtAttrValue;
        }
        if (detailConstant == null || StringUtils.isEmpty(detailConstant.getHairExtFormat())) {
            return hairExtAttrValue;
        }
        return String.format(detailConstant.getHairExtFormat(), hairExtAttrValue, hairLengthLimit.getAttrValue());
    }

    private String getSuitableHairLengthShowDoc(String suitableHairLength, List<SkuAttrItemDto> skuAttrItemDtos) {
        if (HAIR_WITHOUT_LENGTH.equals(suitableHairLength)) {
            return suitableHairLength;
        }
        if (HAIR_UNDER_ARM.equals(suitableHairLength)) {
            return getSuitableHairLengthShowDoc(skuAttrItemDtos, HAIR_UNDER_ARM, HAIR_UNDER_ARM_PRICE_ATTR_NAME, detailConstant.getHairUnderArmExtraPriceDocFormat());
        }
        if (HAIR_UNDER_CHEST.equals(suitableHairLength)) {
            return getSuitableHairLengthShowDoc(skuAttrItemDtos, HAIR_UNDER_CHEST, HAIR_UNDER_CHEST_PRICE_ATTR_NAME, detailConstant.getHairUnderChestExtraPriceDocFormat());
        }
        return null;
    }

    private String getSuitableHairLengthShowDoc(List<SkuAttrItemDto> skuAttrItemDtos, String hairLengthType, String hairPriceAttrName, String format) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.HairPermDyeDealDetailBuilderExt.getSuitableHairLengthShowDoc(java.util.List,java.lang.String,java.lang.String,java.lang.String)");
        String priceWithLongHair = DealDetailUtils.getSkuAttrValueBySkuAttrName(skuAttrItemDtos, hairPriceAttrName);
        if (StringUtils.isEmpty(priceWithLongHair)) {
            return hairLengthType;
        }
        return String.format(format, priceWithLongHair);
    }

    private String getHairColorExtraPriceDoc(String preValue, String colorExtraPrice) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.HairPermDyeDealDetailBuilderExt.getHairColorExtraPriceDoc(java.lang.String,java.lang.String)");
        if (StringUtils.isEmpty(preValue)) {
            return null;
        }
        if (StringUtils.isEmpty(colorExtraPrice)) {
            return preValue;
        }
        if (colorExtraPrice.contains(detailConstant.getCopiesUnit())) {
            return String.format(detailConstant.getHairColorExtraPriceDocWithoutCopiesUnitFormat(), preValue, colorExtraPrice);
        }
        return String.format(detailConstant.getHairColorExtraPriceDocFormat(), preValue, colorExtraPrice);
    }

    private String getColorExtraPrice(List<SkuAttrItemDto> skuAttrItemDtos) {
        if (CollectionUtils.isEmpty(skuAttrItemDtos)) {
            return null;
        }
        SkuAttrItemDto skuAttrItemDto = skuAttrItemDtos.stream().filter(attr -> BLEACH_ADDITION_ATTR_NAME.equals(attr.getAttrName())).findFirst().orElse(null);
        if (skuAttrItemDto == null) {
            return null;
        }
        return skuAttrItemDto.getAttrValue();
    }

    private DealStructVO buildDealStructVO(String name, String value) {
        if (StringUtils.isEmpty(name) || StringUtils.isEmpty(value)) {
            return null;
        }
        if (BRAND_SKU_ATTR_NAMES.contains(name) && value.equals(OTHER_BRAND)) {
            return null;
        }
        if (name.equals(RANGE_LIMITED) && value.equals(NO_LIMIT)) {
            return null;
        }
        DealStructVO dealStructVO = new DealStructVO();
        dealStructVO.setName(name);
        if (name.equals(HAIR_DYE_PROCESS_SKU_ATTR_NAME) && value.equals(HAIR_DYE_PROCESS_SKU_ATTR_VALUE)) {
            return null;
        }
        if (name.equals(REFERENCE_COLOR_SKU_ATTR_NAME)) {
            dealStructVO.setPicValue(buildPicItemVOs(value));
            return dealStructVO;
        }
        dealStructVO.setValue(value);
        return dealStructVO;
    }

    private List<PicItemVO> buildPicItemVOs(String skuPicAttrValue) {
        if (StringUtils.isEmpty(skuPicAttrValue)) {
            return null;
        }
        List<String> skuPicAttrs = Lists.newArrayList(skuPicAttrValue.split(SKU_PIC_ATTR_VALUE_SEPARATOR));
        if (CollectionUtils.isEmpty(skuPicAttrs)) {
            return null;
        }
        Map<PicItemVO, Integer> sequenceId2PicItemVOMap = skuPicAttrs.stream().collect(HashMap::new, (map, skuPicAttr) -> {
            PicItemVO picItemVO = convertSkuPicAttr2PicItemVO(skuPicAttr);
            int sequenceId = getSkuShowPicSequenceId(picItemVO.getTitle());
            map.put(picItemVO, sequenceId);
        }, HashMap::putAll);
        return sequenceId2PicItemVOMap.entrySet().stream().sorted(Comparator.comparingInt(Map.Entry::getValue)).map(entry -> entry.getKey()).collect(Collectors.toList());
    }

    private int getSkuShowPicSequenceId(String color) {
        if (detailConstant == null || StringUtils.isEmpty(detailConstant.getDyeHairSkuColors())) {
            return Integer.MAX_VALUE;
        }
        if (StringUtils.isEmpty(color)) {
            return Integer.MAX_VALUE;
        }
        List<String> colorList = Lists.newArrayList(detailConstant.getDyeHairSkuColors().split(SKU_PIC_ATTR_VALUE_SEPARATOR));
        if (!colorList.contains(color)) {
            return Integer.MAX_VALUE;
        }
        return Lists.newArrayList(detailConstant.getDyeHairSkuColors().split(SKU_PIC_ATTR_VALUE_SEPARATOR)).indexOf(color);
    }

    private PicItemVO convertSkuPicAttr2PicItemVO(String skuPicAttr) {
        if (StringUtils.isEmpty(skuPicAttr)) {
            return null;
        }
        SkuPicModel skuPicModel = JsonCodec.decode(skuPicAttr, SkuPicModel.class);
        if (skuPicModel == null) {
            return null;
        }
        PicItemVO picItemVO = new PicItemVO();
        picItemVO.setTitle(skuPicModel.getName());
        picItemVO.setUrl(skuPicModel.getUrl());
        return picItemVO;
    }

    private String getSkuAttrTitle(String skuAttrName, List<SkuAttrModel> skuAttrModels) {
        if (CollectionUtils.isEmpty(skuAttrModels) || StringUtils.isEmpty(skuAttrName)) {
            return null;
        }
        List<SkuAttrModel> skuAttrs = skuAttrModels.stream().filter(skuAttrModel -> skuAttrName.equals(skuAttrModel.getAttrName())).collect(Collectors.toList());
        SkuAttrModel skuAttrModel = CollectUtils.firstValue(skuAttrs);
        if (skuAttrModel == null) {
            return null;
        }
        return skuAttrModel.getAttrTitle();
    }

    private int getSkuAttrSequenceId(String skuAttrName, List<SkuAttrModel> skuAttrModels) {
        if (CollectionUtils.isEmpty(skuAttrModels) || StringUtils.isEmpty(skuAttrName)) {
            return Integer.MAX_VALUE;
        }
        List<SkuAttrModel> skuAttrs = skuAttrModels.stream().filter(skuAttrModel -> skuAttrName.equals(skuAttrModel.getAttrName())).collect(Collectors.toList());
        SkuAttrModel skuAttrModel = CollectUtils.firstValue(skuAttrs);
        if (skuAttrModel == null) {
            return Integer.MAX_VALUE;
        }
        return skuAttrModel.getSequenceId();
    }

    private List<SkuAttrModel> getSkuAttrModels(String category) {
        if (skuCategoryAttrsModelList == null || CollectionUtils.isEmpty(skuCategoryAttrsModelList.getSkuCategoryAttrsModels())) {
            return null;
        }
        if (StringUtils.isEmpty(category)) {
            return null;
        }
        List<SkuCategoryAttrsModel> categoryModels = skuCategoryAttrsModelList.getSkuCategoryAttrsModels();
        List<SkuCategoryAttrsModel> categorys = categoryModels.stream().filter(categoryModel -> category.equals(categoryModel.getCategory())).collect(Collectors.toList());
        SkuCategoryAttrsModel skuCategoryAttrsModel = CollectUtils.firstValue(categorys);
        if (skuCategoryAttrsModel != null) {
            return skuCategoryAttrsModel.getAttrList();
        }
        List<SkuCategoryAttrsModel> otherCategorys = categoryModels.stream().filter(categoryModel -> OTHER_SKU_CATEGORY.equals(categoryModel.getCategory())).collect(Collectors.toList());
        SkuCategoryAttrsModel otherSkuCategoryAttrsModel = CollectUtils.firstValue(otherCategorys);
        if (otherSkuCategoryAttrsModel == null) {
            return null;
        }
        return otherSkuCategoryAttrsModel.getAttrList();
    }

    private MustDealSkuStructInfoVO buildMustDealSkuStructInfoVO(List<DealSkuStructInfoVO> dealStructInfo) {
        if (CollectionUtils.isEmpty(dealStructInfo)) {
            return null;
        }
        MustDealSkuStructInfoVO mustDealSkuStructInfoVO = new MustDealSkuStructInfoVO();
        mustDealSkuStructInfoVO.setDealStructInfo(dealStructInfo);
        return mustDealSkuStructInfoVO;
    }

    private String getSkuCategory(SkuItemDto sku, List<ProductSkuCategoryModel> skuCategoryModels) {
        if (sku == null || CollectionUtils.isEmpty(skuCategoryModels)) {
            return null;
        }
        List<ProductSkuCategoryModel> categorys = skuCategoryModels.stream().filter(skuCategory -> skuCategory != null && skuCategory.getProductCategoryId() == sku.getProductCategory()).collect(Collectors.toList());
        ProductSkuCategoryModel categoryModel = CollectUtils.firstValue(categorys);
        if (categoryModel == null) {
            return null;
        }
        return categoryModel.getCnName();
    }

    DealSkuStructInfoVO buildDealSkuStructInfoVO(String title, BigDecimal price, int copies, List<DealStructVO> dealStructVOS) {
        DealSkuStructInfoVO dealSkuStructInfoVO = new DealSkuStructInfoVO();
        dealSkuStructInfoVO.setTitle(title);
        dealSkuStructInfoVO.setPrice(String.format(PRICE_FORMAT, price.stripTrailingZeros().toPlainString()));
        dealSkuStructInfoVO.setCopies(String.format(SKU_COPIES_FORMAT, copies));
        dealSkuStructInfoVO.setItems(dealStructVOS);
        return dealSkuStructInfoVO;
    }

    private List<ProductSkuCategoryModel> getProductSkuCategoryModels(String dealDetail) {
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || CollectionUtils.isEmpty(dealStructModel.getProductCategories())) {
            return null;
        }
        return dealStructModel.getProductCategories();
    }

    private List<SkuItemDto> getMustSkuItemDtoList(String dealDetail) {
        MustSkuItemsGroupDto mustGroup = getMustSkuItemsGroupDto(dealDetail);
        if (mustGroup == null || CollectionUtils.isEmpty(mustGroup.getSkuItems())) {
            return null;
        }
        return mustGroup.getSkuItems();
    }

    private List<OptionalSkuItemsGroupDto> getOptionalSkuItemsGroupDto(String dealDetail) {
        DealDetailModel dealDetailModel = getDealDetailModel(dealDetail);
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailModel.getSkuUniStructuredDto().getOptionalGroups();
    }

    private MustSkuItemsGroupDto getMustSkuItemsGroupDto(String dealDetail) {
        DealDetailModel dealDetailModel = getDealDetailModel(dealDetail);
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        if (CollectionUtils.isEmpty(dealDetailModel.getSkuUniStructuredDto().getMustGroups())) {
            return null;
        }
        MustSkuItemsGroupDto mustGroup = CollectUtils.firstValue(dealDetailModel.getSkuUniStructuredDto().getMustGroups());
        return mustGroup;
    }

    private DealDetailModel getDealDetailModel(String dealDetail) {
        DealStructModel dealStructModel = getDealStructModel(dealDetail);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        DealDetailModel dealDetailModel = JsonCodec.decode(dealStructModel.getStract(), DealDetailModel.class);
        if (dealDetailModel == null || dealDetailModel.getSkuUniStructuredDto() == null) {
            return null;
        }
        return dealDetailModel;
    }

    private DealStructModel getDealStructModel(String dealDetail) {
        if (StringUtils.isEmpty(dealDetail)) {
            return null;
        }
        DealStructModel dealStructModel = JsonCodec.decode(dealDetail, DealStructModel.class);
        if (dealStructModel == null || StringUtils.isEmpty(dealStructModel.getStract())) {
            return null;
        }
        return dealStructModel;
    }

    @Data
    private static class SkuCategoryAttrsModelList {
        private List<SkuCategoryAttrsModel> skuCategoryAttrsModels;
    }

    @Data
    private static class SkuCategoryAttrsModel {
        private int sequenceId;
        private String category;
        private List<SkuAttrModel> attrList;
    }

    @Data
    private static class SkuAttrModel {
        private int sequenceId;
        private String attrTitle;
        private String attrName;
    }

    @Data
    private static class SkuPicModel {
        private String name;
        private String url;
    }

    @Data
    private static class DetailConstant {
        private String dyeHairSkuColors;
        private String hairColorExtraPriceDocFormat;
        private String hairUnderArmExtraPriceDocFormat;
        private String hairUnderChestExtraPriceDocFormat;
        private String hairColorExtraPriceDocWithoutCopiesUnitFormat;
        private String hairLengthExtraPriceDocFormat;
        private String permSkuExtraPriceFormat;
        private String copiesUnit;
        private List<String> skuCategoryShowingSkuName;
        private String hairRemoveFormat;
        private String hairExtFormat;
    }
}
