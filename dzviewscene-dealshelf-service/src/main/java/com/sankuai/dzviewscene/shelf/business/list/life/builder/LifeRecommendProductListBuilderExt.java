package com.sankuai.dzviewscene.shelf.business.list.life.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.list.vo.AttrVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/10/20 2:24 下午
 */
@ExtPointInstance(name = "美团小程序到踪商品列表")
public class LifeRecommendProductListBuilderExt extends ProductListBuilderExtAdapter {


    @Override
    public List<AttrVO> attrs(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.list.life.builder.LifeRecommendProductListBuilderExt.attrs(ActivityContext,ProductM)");
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        return productM.getExtAttrs().stream().map(attrM -> {
            AttrVO attrVO = new AttrVO();
            attrVO.setName(attrM.getName());
            attrVO.setValue(attrM.getValue());
            return attrVO;
        }).collect(Collectors.toList());
    }
}
