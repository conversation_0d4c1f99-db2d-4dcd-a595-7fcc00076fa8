package com.sankuai.dzviewscene.shelf.business.filterlist.home.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist.ProductListBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;

/**
 * <AUTHOR>
 * @date 2021/1/11 8:07 下午
 */
@ExtPointInstance(name = "甲醛商品列表商品VO构造扩展点")
public class MethanalProductListBuilderVOExt extends ProductListBuilderExtAdapter {

    @Override
    public int showType(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.home.builder.MethanalProductListBuilderVOExt.showType(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return ParamsUtil.getIntSafely(activityContext, FilterListActivityConstants.Style.showType);
    }
}
