package com.sankuai.dzviewscene.shelf.framework.annotation;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.lang.annotation.*;

/**
 * 能力注解
 * <p>
 * Created by float.lu on 2020/8/21.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Indexed
@Component
public @interface Ability {

    /**
     * 能力代码
     *
     * @return
     */
    String code();

    /**
     * 能力名
     *
     * @return
     */
    String name();
}
