package com.sankuai.dzviewscene.shelf.business.other.carousel.fetcher;

import com.dianping.cat.Cat;
import com.dianping.joy.booking.api.generalpool.dto.GeneralPoolInfoDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.shelf.business.other.carousel.context.ShopPinSessionContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@SuppressWarnings("unchecked")
@AbilityInstance(name = "拼场场次召回器---只召回最早的")
public class JoyPinSessionQueryFetcher extends QueryFetcher {

    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.other.carousel.fetcher.JoyPinSessionQueryFetcher.build(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<List<GeneralPoolInfoDTO>> poolInfoCf = loadPoolInfoCf(ctx);
        return poolInfoCf
                .thenApply(this::postProcess)
                .thenApply(this::buildProductGroupMap);
    }

    private CompletableFuture<List<GeneralPoolInfoDTO>> loadPoolInfoCf(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.other.carousel.fetcher.JoyPinSessionQueryFetcher.loadPoolInfoCf(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return Optional.ofNullable((CompletableFuture<List<GeneralPoolInfoDTO>>) ctx.getExtContext().get(ShopPinSessionContext.CONTEXT_KEY))
                .orElse(CompletableFuture.completedFuture(Lists.newArrayList()));
    }

    private GeneralPoolInfoDTO postProcess(List<GeneralPoolInfoDTO> generalPoolInfos) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.other.carousel.fetcher.JoyPinSessionQueryFetcher.postProcess(java.util.List)");
        if (CollectionUtils.isEmpty(generalPoolInfos)) {
            return null;
        }
        return generalPoolInfos.parallelStream()
                .min(Comparator.comparing(GeneralPoolInfoDTO::getBookStartTime))
                .orElse(null);
    }

    private Map<String, ProductGroupM> buildProductGroupMap(GeneralPoolInfoDTO earliestPool) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.other.carousel.fetcher.JoyPinSessionQueryFetcher.buildProductGroupMap(com.dianping.joy.booking.api.generalpool.dto.GeneralPoolInfoDTO)");
        if (earliestPool == null) {
            return Maps.newHashMap();
        }
        Map<String, ProductGroupM> resultMap = Maps.newHashMap();
        resultMap.put(ProductEnums.joy_pin.name(), buildProductGroup(earliestPool));
        return resultMap;
    }

    private ProductGroupM buildProductGroup(GeneralPoolInfoDTO earliestPool) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.other.carousel.fetcher.JoyPinSessionQueryFetcher.buildProductGroup(com.dianping.joy.booking.api.generalpool.dto.GeneralPoolInfoDTO)");
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(Lists.newArrayList(buildProduct(earliestPool)));
        return productGroupM;
    }

    private ProductM buildProduct(GeneralPoolInfoDTO poolInfoDTO) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.other.carousel.fetcher.JoyPinSessionQueryFetcher.buildProduct(com.dianping.joy.booking.api.generalpool.dto.GeneralPoolInfoDTO)");
        ProductM productM = new ProductM();
        productM.setProductId(poolInfoDTO.getProductId());
        return productM;
    }
}
