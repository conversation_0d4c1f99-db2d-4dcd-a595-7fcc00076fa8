package com.sankuai.dzviewscene.shelf.platform.filterlist.flow;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityFlow;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.ShowFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 流程步骤:
 * 1.召回
 * 2.填充
 * 3.生成筛选
 *
 * Created by float on 2020/9/21.
 */
@ActivityFlow(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, flowCode = StandardFilterProductsFlow.ACTIVITY_FLOW_FIRST_TAB_WITH_PRODUCTS_CODE, name = "融合标准筛选能力的流程")
public class StandardFilterProductsFlow implements IActivityFlow<FilterProductsM> {

    public static final String ACTIVITY_FLOW_FIRST_TAB_WITH_PRODUCTS_CODE = "FirstTabWithProductsFlow";

    @Override
    public CompletableFuture<FilterProductsM> execute(AbstractActivity<?> activity, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.flow.StandardFilterProductsFlow.execute(AbstractActivity,ActivityContext)");

        // 1. 商品数据
        CompletableFuture<Map<String, ProductGroupM>> productGroupCompletableFuture = activity.findAbility(ctx, MultiGroupQueryFetcher.ABILITY_PRODUCT_QUERY_CODE).build(ctx).thenCompose(productGroups -> {
            ctx.attach(PaddingFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
            return activity.findAbility(ctx, MultiGroupPaddingFetcher.ABILITY_PRODUCT_PADDING_CODE).build(ctx);
        });

        // 2. 展示删选数据
        CompletableFuture<List<FilterM>> showFilterCompletableFuture = productGroupCompletableFuture.thenCompose(productGroups -> {
            ctx.attach(FilterFetcher.Attachments.productGroups, CompletableFuture.completedFuture(productGroups));
            return activity.findAbility(ctx, ShowFilterFetcher.ABILITY_MULTI_FILTER_FETCHER).build(ctx);
        });


        // 4. 组装结果
        return CompletableFuture.allOf(productGroupCompletableFuture, showFilterCompletableFuture).thenApply(v -> {
            FilterProductsM filterProductsM = new FilterProductsM();
            filterProductsM.setProductGroup(CollectUtils.firstValue(productGroupCompletableFuture.join()));
            filterProductsM.setFilters(showFilterCompletableFuture.join());
            return filterProductsM;
        });
    }
}
