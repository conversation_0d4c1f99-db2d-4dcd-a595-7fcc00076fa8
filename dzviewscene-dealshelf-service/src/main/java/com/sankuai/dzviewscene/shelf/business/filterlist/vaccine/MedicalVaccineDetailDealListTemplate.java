package com.sankuai.dzviewscene.shelf.business.filterlist.vaccine;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.builder.MedicalDealBuilderVoExt;
import com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.fetcher.MedicalVaccineDealQueryFetcher;
import com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.filter.MedicalVaccineDetailFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.flow.StandardFilterProductsFlow;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 医疗疫苗详情页团单列表场景
 * Created by zhaizhui on 2020/11/10
 */
@ActivityTemplate(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, sceneCode = "medical_productdetail_deal_vaccine_list", name = "医疗_商品详情页_疫苗团单列表")
public class MedicalVaccineDetailDealListTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.MedicalVaccineDetailDealListTemplate.flow()");
        return StandardFilterProductsFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.MedicalVaccineDetailDealListTemplate.extParams(java.util.Map)");
        // 1. 设置只展示一组商品, 商品组名使用dealGroup
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.deal.name()));
        // 2. 设置商品组召回和填充参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.deal.name(), new HashMap<String, Object>() {{
                // 2.2 设置第一组商品填充参数
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                put(PaddingFetcher.Params.planId, "10000032");
                put("countrywideShop", true);
            }});
        }});
        //3. 设置showType
        exParams.put(ShelfActivityConstants.Style.showType, 3);
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.MedicalVaccineDetailDealListTemplate.extContexts(java.util.List)");
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.MedicalVaccineDetailDealListTemplate.extValidators(java.util.List)");
    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.MedicalVaccineDetailDealListTemplate.extAbilities(java.util.Map)");
        // 1. 商品召回能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MedicalVaccineDealQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.vaccine.MedicalVaccineDetailDealListTemplate.extPoints(java.util.Map)");
        extPoints.put(ProductBuilderVoExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, MedicalDealBuilderVoExt.class);
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, MedicalVaccineDetailFilterFetcherExt.class);
    }
}
