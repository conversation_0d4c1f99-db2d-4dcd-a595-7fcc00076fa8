package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.ShowFilterHandler;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class BackroomThemeZoneShowFilterHandler implements ShowFilterHandler {

    @Override
    public CompletableFuture<FilterM> handle(ActivityContext ctx, FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl.BackroomThemeZoneShowFilterHandler.handle(ActivityContext,FilterM)");
        List<String> needHideFilters = ctx.getParam(QueryFetcher.Params.needHideFilters);
        if (CollectionUtils.isEmpty(needHideFilters)) {
            return CompletableFuture.completedFuture(filterM);
        }

        List<FilterBtnM> filters = filterM.getFilters().stream().filter(filterBtnM -> !needHideFilters.contains(filterBtnM.getTitle())).collect(Collectors.toList());
        filterM.setFilters(filters);
        return CompletableFuture.completedFuture(filterM);
    }
}
