package com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.text.CollationKey;
import java.text.Collator;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title 快照预订筛选能力点
 * @package com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability
 * @date 2021/6/10
 */
@Component
public class SnapshotBookingFilterFetcher extends FilterFetcher {

    private static final String CATEGORY_KEY = "commonProductsCategory";

    @Override
    public CompletableFuture<Map<String, FilterM>> build(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.build(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCf = ctx.getAttachment(FilterFetcher.Attachments.productGroups);
        return productGroupsCf.thenApply(this::batchGetFilters);
    }

    private Map<String, FilterM> batchGetFilters(Map<String, ProductGroupM> productGroups) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.batchGetFilters(java.util.Map)");
        if (MapUtils.isEmpty(productGroups)) {
            return Maps.newHashMap();
        }
        return productGroups.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, this::buildFilterM, (oldKey, newKey) -> newKey));
    }

    private FilterM buildFilterM(Map.Entry<String, ProductGroupM> entry) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.buildFilterM(java.util.Map$Entry)");
        FilterM filterM = new FilterM();
        List<ProductM> productMs = Optional.ofNullable(entry.getValue()).map(ProductGroupM::getProducts).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(productMs)) {
            return null;
        }
        filterM.setFilters(buildTypeFilters(productMs));
        return filterM;
    }

    private List<FilterBtnM> buildTypeFilters(List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.buildTypeFilters(java.util.List)");
        List<CategoryFilterButton> sortedCategoryFilterButtons = buildSortedCategoryFilterButtonList(productMs);
        List<FilterBtnM> resultFilterBtnMList = Lists.newArrayList();
        Map<String, List<ProductM>> snapshotCategoryMap = productMs.parallelStream().collect(Collectors.groupingBy(e -> e.getAttr(CATEGORY_KEY)));

        resultFilterBtnMList.addAll(buildSnapshotCategoryFilterButtons(snapshotCategoryMap, sortedCategoryFilterButtons));
        resultFilterBtnMList.add(0, buildAllFilterBtnM(productMs));
        return resultFilterBtnMList;
    }

    private List<FilterBtnM> buildSnapshotCategoryFilterButtons(Map<String, List<ProductM>> snapshotCategoryMap, List<CategoryFilterButton> sortedCategoryFilterButtons) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.buildSnapshotCategoryFilterButtons(java.util.Map,java.util.List)");
        List<FilterBtnM> filterBtnMs = Lists.newArrayList();
        sortedCategoryFilterButtons.forEach(categoryFilterButton -> filterBtnMs.add(buildSingleFilterBtnM(snapshotCategoryMap, categoryFilterButton)));
        return filterBtnMs;
    }

    private FilterBtnM buildSingleFilterBtnM(Map<String, List<ProductM>> snapshotCategoryMap, CategoryFilterButton categoryFilterButton) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.buildSingleFilterBtnM(Map,SnapshotBookingFilterFetcher$CategoryFilterButton)");
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(categoryFilterButton.getCategoryId());
        filterBtnM.setTitle(categoryFilterButton.getCategoryName());
        filterBtnM.setTotalCount(snapshotCategoryMap.get(categoryFilterButton.getCategoryName()).size());
        filterBtnM.setProducts(buildProducts(snapshotCategoryMap.get(categoryFilterButton.getCategoryName())));
        return filterBtnM;
    }

    private List<ProductM> buildProducts(List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.buildProducts(java.util.List)");
        return productMs.stream().map(product -> {
            ProductM productM = new ProductM();
            BeanUtils.copyProperties(product, productM);
            return productM;
        }).sorted(Comparator
                .comparing(product -> ((ProductM) product).getSale().getSale())
                .reversed()).collect(Collectors.toList());
    }

    private List<CategoryFilterButton> buildSortedCategoryFilterButtonList(List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.buildSortedCategoryFilterButtonList(java.util.List)");
        int categoryId = 2;
        List<String> categoryList = productMs.stream()
                .map(productM -> productM.getAttr(CATEGORY_KEY))
                .distinct()
                .collect(Collectors.toList());
        List<CategoryFilterButton> categoryFilterButtonList = Lists.newArrayList();
        for (String categoryStr : categoryList) {
            categoryFilterButtonList.add(new CategoryFilterButton(categoryId++, categoryStr));
        }
        sortCategoryFilterButtonList(categoryFilterButtonList);
        return categoryFilterButtonList;
    }

    private void sortCategoryFilterButtonList(List<CategoryFilterButton> categoryFilterButtonList) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.sortCategoryFilterButtonList(java.util.List)");
        Collections.sort(categoryFilterButtonList, new Comparator<CategoryFilterButton>() {
            Collator collator = Collator.getInstance(Locale.CHINA);

            @Override
            public int compare(CategoryFilterButton o1, CategoryFilterButton o2) {
                CollationKey key1 = collator.getCollationKey(o1.getCategoryName());
                CollationKey key2 = collator.getCollationKey(o2.getCategoryName());
                return key2.compareTo(key1);
            }
        });
    }

    private FilterBtnM buildAllFilterBtnM(List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.snapshot.ability.SnapshotBookingFilterFetcher.buildAllFilterBtnM(java.util.List)");
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(1);
        filterBtnM.setTitle("全部");
        filterBtnM.setTotalCount(productMs.size());
        filterBtnM.setProducts(productMs.stream().sorted(Comparator
                .comparing(product -> ((ProductM) product).getSale().getSale())
                .reversed()).collect(Collectors.toList()));
        filterBtnM.setSelected(true);
        return filterBtnM;
    }

    @Data
    @AllArgsConstructor
    private static class CategoryFilterButton {

        /**
         * 照片类型ID
         */
        private int categoryId;

        /**
         * 照片类型
         */
        private String categoryName;

    }
}
