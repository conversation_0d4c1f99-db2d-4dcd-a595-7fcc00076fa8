package com.sankuai.dzviewscene.shelf.platform.common.ability.padding;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductMAttrConstant;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/4
 */
@ExtPointInstance(name = "彩虹活动白名单过滤，在白名单内的才展示")
public class DealActivityFilterPaddingFetcherExt implements PaddingFetcherExt {
    /**
     * Key - senseCode
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.deal.activity.filter.config", defaultValue = "{}")
    public Map<String, DealActivityFilterPaddingFetcherExt.FilterDealActivityIdConfig> dealActivityIdConfigMap;

    @Override
    public void postPadding(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.DealActivityFilterPaddingFetcherExt.postPadding(ActivityContext,String,ProductGroupM)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return;
        }
        List<Integer> dealActivityIds = getDealActivityIdsWithPlatform(activityContext);
        if (CollectionUtils.isEmpty(dealActivityIds)){
            productGroupM.setProducts(new ArrayList<>());
            return;
        }
        Iterator<ProductM> iterator = productGroupM.getProducts().iterator();
        while (iterator.hasNext()){
            ProductM currentProductM = iterator.next();
            List<ProductActivityM> filterList = filterWhiteList(currentProductM,dealActivityIds);
            if (CollectionUtils.isEmpty(filterList)){
                iterator.remove();
                continue;
            }
            //参与了指定活动，则把信息填入商品信息中
            currentProductM.setAttr(ProductMAttrConstant.TARGET_DEAL_ACTIVITIES, JsonCodec.encode(filterList));
        }
    }

    /**
     * 筛选出白名单内活动
     * @param currentProductM
     * @param dealActivityIds
     * @return
     */
    private List<ProductActivityM> filterWhiteList(ProductM currentProductM,List<Integer> dealActivityIds){
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.DealActivityFilterPaddingFetcherExt.filterWhiteList(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM,java.util.List)");
        if (currentProductM == null || CollectionUtils.isEmpty(currentProductM.getActivities())){
            return null;
        }
        return currentProductM.getActivities().stream().filter(o->dealActivityIds.contains(o.getPageId())).collect(Collectors.toList());
    }

    /**
     * 获取需要过滤的彩虹活动Id
     * @param ctx
     * @return
     */
    private List<Integer> getDealActivityIdsWithPlatform(ActivityContext ctx){
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.DealActivityFilterPaddingFetcherExt.getDealActivityIdsWithPlatform(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        if (MapUtils.isEmpty(dealActivityIdConfigMap)){
            return null;
        }
        String senseCode = ctx.getParam(ShelfActivityConstants.Params.sceneCode);
        FilterDealActivityIdConfig targetConfig = dealActivityIdConfigMap.get(senseCode);
        if (targetConfig == null){
            return null;
        }
        int platform = NumberUtils.toInt(ctx.getParam(ShelfActivityConstants.Params.platform) + "", 1);
        if (PlatformUtil.isMT(platform)){
            return targetConfig.getMtIds();
        }
        return targetConfig.getDpIds();
    }


    @Data
    public static class FilterDealActivityIdConfig{
        /**
         * 美团活动Id
         */
        private List<Integer> mtIds;

        /**
         * 点评活动Id
         */
        private List<Integer> dpIds;
    }
}
