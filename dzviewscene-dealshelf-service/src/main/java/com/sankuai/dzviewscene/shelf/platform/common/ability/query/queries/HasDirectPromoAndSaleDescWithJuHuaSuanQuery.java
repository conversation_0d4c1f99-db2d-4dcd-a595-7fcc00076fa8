package com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries;

import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.JuHuaSuanUtils;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;

/**
 * 如果有优惠，优先排列；
 * 如果都有优惠或者都没有优惠，按照销量排序
 * 如果都相同，排序不变
 * Created by float.lu on 2020/9/23.
 */
public class HasDirectPromoAndSaleDescWithJuHuaSuanQuery implements SimplexQuery {

    @Override
    public Comparator<ProductM> comparator(QueryContext context) {
        return new Comparator<ProductM>() {
            @Override
            public int compare(ProductM product1, ProductM product2) {
                if (product1 == null || product2 == null) {
                    return 0;
                }
                int juHuaSuanRes = JuHuaSuanUtils.juHuaSuanTopSort().compare(product1, product2);
                if (juHuaSuanRes != 0) {
                    return juHuaSuanRes;
                }
                boolean product1HasPromo = hasDirectPromo(product1);
                boolean product2HasPromo = hasDirectPromo(product2);

                if ((product1HasPromo && product2HasPromo) || (!product1HasPromo && !product2HasPromo)) {
                    return sortBySaleNum(product1, product2);
                }
                if (product1HasPromo) {
                    return -1;
                }
                return 1;
            }
        };
    }

    private boolean hasDirectPromo(ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(productM.getPromoPrices())) {
            return false;
        }
        for (ProductPromoPriceM promoPrice : productM.getPromoPrices()) {
            if (promoPrice != null && promoPrice.getPromoType() == PromoTypeEnum.DIRECT_PROMO.getType()) {
                return true;
            }
        }
        return false;
    }

    private int sortBySaleNum(ProductM product1, ProductM product2) {
        Integer saleNum1 = getSaleNum(product1);
        Integer saleNum2 = getSaleNum(product2);
        return saleNum2.compareTo(saleNum1);
    }

    private Integer getSaleNum(ProductM product) {
        if (product.getSale() == null) {
            return 0;
        }
        return product.getSale().getSale();
    }
}
