package com.sankuai.dzviewscene.shelf.platform.other.ability.builder;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextBuilder;
import com.sankuai.dzviewscene.shelf.framework.core.IContextBuilder;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivity;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import org.apache.commons.collections.CollectionUtils;
import org.mapstruct.ap.internal.util.Collections;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@ContextBuilder(activityCode = OtherActivity.ACTIVITY_CODE, name = "非标准场景上下文构造器")
public class OtherActivityContextBuilder implements IContextBuilder {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public ActivityContext build(ActivityRequest activityRequest) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.build(com.sankuai.dzviewscene.shelf.framework.ActivityRequest)");
        ActivityContext activityContext = new ActivityContext();
        activityContext.setParameters(activityRequest.getParams());
        activityContext.setSceneCode(activityContext.getParam(OtherActivityConstants.Params.sceneCode));
        // poiMigrate
        if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.OtherActivityContextBuilder.getScene())) {
            addPlatformParamPoiMigrate(activityContext);
        } else {
            // 原逻辑
            addPlatformParam(activityContext);
        }
        return activityContext;
    }

    private <T> T getFutureResult(Future<T> future, int time, TimeUnit timeUnit) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.getFutureResult(java.util.concurrent.Future,int,java.util.concurrent.TimeUnit)");
        try {
            return future.get(time, timeUnit);
        } catch (Exception e) {}
        return null;
    }

    private <T> T getDefaultFutureResult(Future<T> future) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.getDefaultFutureResult(java.util.concurrent.Future)");
        return getFutureResult(future, 500, TimeUnit.MILLISECONDS);
    }

    private void addPlatformParam(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.addPlatformParam(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.platform))) {
            ShopM shopM = getDefaultFutureResult(loadShop(ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.dpPoiId)));
            activityContext.addParam(OtherActivityConstants.Ctx.ctxShop, shopM);
            return;
        }
        // 美团平台
        CompletableFuture<List<Integer>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtId(ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.mtPoiId));
        CompletableFuture<Integer> dpCityIdFuture = compositeAtomService.getDpCityIdByMt(ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.mtCityId));
        CompletableFuture.allOf(dpShopIdsFuture, dpCityIdFuture).thenAccept(v -> {
            List<Integer> dpShopIds = dpShopIdsFuture.join();
            Integer dpCityId = dpCityIdFuture.join();
            activityContext.addParam(OtherActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds));
            activityContext.addParam(OtherActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
            ShopM shopM = getDefaultFutureResult(loadShop(ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.dpPoiId)));
            activityContext.addParam(OtherActivityConstants.Ctx.ctxShop, shopM);
        }).join();
    }

    private void addPlatformParamPoiMigrate(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.addPlatformParamPoiMigrate(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        // 点评平台
        if (!PlatformUtil.isMT(ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.platform))) {
            ShopM shopM = getDefaultFutureResult(loadShopPoiMigrate(PoiIdUtil.getDpPoiIdL(activityContext, OtherActivityConstants.Params.dpPoiIdL, OtherActivityConstants.Params.dpPoiId)));
            activityContext.addParam(OtherActivityConstants.Ctx.ctxShop, shopM);
            return;
        }
        // 美团平台
        CompletableFuture<List<Long>> dpShopIdsFuture = compositeAtomService.batchGetDpByMtIdL(PoiIdUtil.getMtPoiIdL(activityContext, OtherActivityConstants.Params.mtPoiIdL, OtherActivityConstants.Params.mtPoiId));
        CompletableFuture<Integer> dpCityIdFuture = compositeAtomService.getDpCityIdByMt(ParamsUtil.getIntSafely(activityContext, OtherActivityConstants.Params.mtCityId));
        CompletableFuture.allOf(dpShopIdsFuture, dpCityIdFuture).thenAccept(v -> {
            List<Long> dpShopIds = dpShopIdsFuture.join();
            Integer dpCityId = dpCityIdFuture.join();
            activityContext.addParam(OtherActivityConstants.Params.dpPoiId, CollectionUtils.isEmpty(dpShopIds) ? 0 : Collections.first(dpShopIds).intValue());
            activityContext.addParam(OtherActivityConstants.Params.dpPoiIdL, CollectionUtils.isEmpty(dpShopIds) ? 0L : Collections.first(dpShopIds));
            activityContext.addParam(OtherActivityConstants.Params.dpCityId, dpCityId == null ? 0 : dpCityId);
            ShopM shopM = getDefaultFutureResult(loadShopPoiMigrate(PoiIdUtil.getDpPoiIdL(activityContext, OtherActivityConstants.Params.dpPoiIdL, OtherActivityConstants.Params.dpPoiId)));
            activityContext.addParam(OtherActivityConstants.Ctx.ctxShop, shopM);
        }).join();
    }

    private CompletableFuture<ShopM> loadShop(int dpShopId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.loadShop(int)");
        if (dpShopId == 0) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.loadShop(dpShopId).thenApply(shopDTO -> {
            if (shopDTO == null) {
                return null;
            }
            ShopM shopM = new ShopM();
            shopM.setShopId(shopDTO.getShopId() == null ? 0 : shopDTO.getShopId());
            shopM.setShopUuid(shopDTO.getShopUuid() == null ? "" : shopDTO.getShopUuid());
            shopM.setShopName(shopDTO.getShopName());
            shopM.setShopType(shopDTO.getShopType() == null ? 0 : shopDTO.getShopType());
            shopM.setCategory(shopDTO.getMainCategoryId() == null ? 0 : shopDTO.getMainCategoryId());
            shopM.setLat(shopDTO.getGlat() == null ? 0 : shopDTO.getGlat());
            shopM.setLng(shopDTO.getGlng() == null ? 0 : shopDTO.getGlng());
            shopM.setCityId(shopDTO.getCityId() == null ? 0 : shopDTO.getCityId());
            shopM.setStatus(shopDTO.getPower() == null ? -1 : shopDTO.getPower());
            return shopM;
        });
    }

    private CompletableFuture<ShopM> loadShopPoiMigrate(long dpShopId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.loadShopPoiMigrate(long)");
        if (dpShopId == 0) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.findShopsByDpShopIds(buildDpPoiRequest(Lists.newArrayList(dpShopId))).thenApply(dpPoiDTOList -> {
            if (CollectionUtils.isEmpty(dpPoiDTOList)) {
                return null;
            }
            DpPoiDTO dpPoiDTO = Collections.first(dpPoiDTOList);
            ShopM shopM = new ShopM();
            shopM.setShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId().intValue());
            shopM.setLongShopId(dpPoiDTO.getShopId() == null ? 0 : dpPoiDTO.getShopId());
            shopM.setShopUuid(dpPoiDTO.getUuid() == null ? "" : dpPoiDTO.getUuid());
            shopM.setShopName(dpPoiDTO.getShopName());
            shopM.setShopType(dpPoiDTO.getShopType() == null ? 0 : dpPoiDTO.getShopType());
            shopM.setCategory(dpPoiDTO.getMainCategoryId() == null ? 0 : dpPoiDTO.getMainCategoryId());
            shopM.setLat(dpPoiDTO.getLat() == null ? 0 : dpPoiDTO.getLat());
            shopM.setLng(dpPoiDTO.getLng() == null ? 0 : dpPoiDTO.getLng());
            shopM.setCityId(dpPoiDTO.getCityId() == null ? 0 : dpPoiDTO.getCityId());
            shopM.setStatus(dpPoiDTO.getPower() == null ? -1 : dpPoiDTO.getPower());
            return shopM;
        });
    }

    private DpPoiRequest buildDpPoiRequest(List<Long> dpPoiIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityContextBuilder.buildDpPoiRequest(java.util.List)");
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }
}