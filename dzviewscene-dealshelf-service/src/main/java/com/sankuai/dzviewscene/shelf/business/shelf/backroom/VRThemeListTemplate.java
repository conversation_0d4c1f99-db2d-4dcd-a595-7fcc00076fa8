package com.sankuai.dzviewscene.shelf.business.shelf.backroom;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.BackroomShelfActivity;
import com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.DefaultThemeListBuilder;
import com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder.ThemeListBuilder;
import com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.context.BackroomRecommendContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.CardHoldStatusContext;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.JoyCardShelfContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-10-20
 */
@ActivityTemplate(activityCode = BackroomShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "joy_poi_vr_booking_shelf_products", name = "VR预订货架主题列表默认模板")
public class VRThemeListTemplate implements IActivityTemplate {

    private static final String PADDING_BATCH_SIZE_CONFIG_KEY = "com.sankuai.dzviewscene.productshelf.backroompadding.batchsize";

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.VRThemeListTemplate.flow()");
        return null;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.VRThemeListTemplate.extParams(java.util.Map)");
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.backroom.name()));
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.backroom.name(), new HashMap<String, Object>() {{
                put(QueryFetcher.Params.productType, 21);
                put(QueryFetcher.Params.filterInvalid, false);
                put(PaddingFetcher.Params.source, 1);
                put(PaddingFetcher.Params.planId, "10100045");
                put(PaddingFetcher.Params.batchSize, Lion.getIntValue(PADDING_BATCH_SIZE_CONFIG_KEY, 0));
            }});
        }});
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.VRThemeListTemplate.extContexts(java.util.List)");
        extContexts.add(BackroomRecommendContextExt.class);
        extContexts.add(CardHoldStatusContext.class);
        extContexts.add(JoyCardShelfContext.class);
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.VRThemeListTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.VRThemeListTemplate.extAbilities(java.util.Map)");
        /*召回能力*/
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        /*填充能力*/
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        /*VO构造能力*/
        abilities.put(ThemeListBuilder.ABILITY_THEME_LIST_CODE, DefaultThemeListBuilder.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.VRThemeListTemplate.extPoints(java.util.Map)");

    }
}