package com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.edu.builder.EduStudyRoomFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFixPriceVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021.01.19
 */
@ExtPointInstance(name = "教培-自习室座位类型-团购列表List构建")
public class EduStudyRoomSeatTypeDealListBuilderExt extends ProductBuilderVoExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.edu.studyroom.constant.config", defaultValue = "{}")
    private FloorConstant floorConstant;

    @Override
    public String picUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.picUrl(ActivityContext,ProductM)");
        if (StringUtils.isEmpty(productM.getPicUrl())) return null;
        return PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), 132, 132, PictureURLBuilders.ScaleType.Cut);
    }

    @Override
    public String salePrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.salePrice(ActivityContext,ProductM)");
        ProductPromoPriceM promoPriceM = getPromoPrice(productM);
        if (promoPriceM != null) {
            return promoPriceM.getPromoPriceTag();
        }
        return productM.getBasePriceTag();
    }

    private ProductPromoPriceM getPromoPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.getPromoPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(productM.getPromoPrice(PromoTypeEnum.DIRECT_PROMO.getType()) != null) {
            return productM.getPromoPrice(PromoTypeEnum.DIRECT_PROMO.getType());
        }
        return productM.getPromoPrice(PromoTypeEnum.BASE_PROMO.getType());
    }


    @Override
    public List<String> promo(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.promo(ActivityContext,ProductM)");
        ProductPromoPriceM promo = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (promo != null) {
            return Lists.newArrayList(promo.getPromoTag());
        }
        promo = productM.getPromo(PromoTypeEnum.BASE_PROMO.getType());
        if (promo != null) {
            return Lists.newArrayList(promo.getDiscountTag());
        }
        return null;
    }

    @Override
    public DzFixPriceVO pinPrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.pinPrice(ActivityContext,ProductM)");
        if (productM == null || productM.getPinPrice() == null) {
            return null;
        }
        ProductPriceM productPriceM = productM.getPinPrice();
        DzFixPriceVO dzFixPriceVO = new DzFixPriceVO();
        dzFixPriceVO.setPriceDesc(productPriceM.getPriceDesc());
        dzFixPriceVO.setPrice(productPriceM.getPriceTag());
        return dzFixPriceVO;
    }

    /**
     * 活动标签
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public List<DzActivityTagVO> activityTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.activityTags(ActivityContext,ProductM)");
        // 优先使用活动
        List<DzActivityTagVO> activityFloatTags = buildActivityFloatTags(productM.getActivities());
        if (CollectionUtils.isNotEmpty(activityFloatTags)) return activityFloatTags;
        // 没有活动显示商家推荐
        String shopRecommend = productM.getAttr("attr_shopRecommend");
        return buildShopRecommendFloatTags(shopRecommend);
    }

    private List<DzActivityTagVO> buildShopRecommendFloatTags(String shopRecommend) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.buildShopRecommendFloatTags(java.lang.String)");
        if (StringUtils.isEmpty(shopRecommend) || "false".equals(shopRecommend)) return new ArrayList<>();
        // 商家推荐使用图片
        return buildActivityTagsAsImg(floorConstant.getShopRecommendPic());
    }

    private List<DzActivityTagVO> buildActivityFloatTags(List<ProductActivityM> activities) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.buildActivityFloatTags(java.util.List)");
        if (CollectionUtils.isEmpty(activities) || activities.get(0) == null) {
            return new ArrayList<>();
        }
        // 优先使用title
        if (StringUtils.isNotEmpty(activities.get(0).getLable())) {
            return buildActivityTagsAsTitle(activities.get(0).getLable());
        }
        // 然后使用url
        if (StringUtils.isNotEmpty(activities.get(0).getUrl())) {
            return buildActivityTagsAsImg(activities.get(0).getUrl());
        }
        return null;
    }

    private List<DzActivityTagVO> buildActivityTagsAsTitle(String title) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.buildActivityTagsAsTitle(java.lang.String)");
        DzActivityTagVO floatTagVO = new DzActivityTagVO();
        floatTagVO.setLabel(title);
        floatTagVO.setPosition(1);
        return Lists.newArrayList(floatTagVO);
    }

    private List<DzActivityTagVO> buildActivityTagsAsImg(String url) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.buildActivityTagsAsImg(java.lang.String)");
        DzActivityTagVO floatTagVO = new DzActivityTagVO();
        floatTagVO.setImgUrl(url);
        floatTagVO.setPosition(1);
        return Lists.newArrayList(floatTagVO);
    }

    @Override
    public String buttonName(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.buttonName(ActivityContext,ProductM)");
        return "购买";
    }


    @Override
    public String buttonJumpUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.edu.builder.EduStudyRoomSeatTypeDealListBuilderExt.buttonJumpUrl(ActivityContext,ProductM)");
        return productM.getJumpUrl();
    }

    @Data
    public static class FloorConstant {
        private String shopRecommendPic;
    }

}
