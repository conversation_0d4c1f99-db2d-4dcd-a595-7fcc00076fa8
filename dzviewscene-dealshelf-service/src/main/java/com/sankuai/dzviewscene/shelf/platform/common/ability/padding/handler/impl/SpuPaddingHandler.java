package com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.shopremote.remote.dto.ShopDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardItemDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductDTO;
import com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductPageDTO;
import com.dianping.tpfun.product.api.sku.aggregate.request.GetStandardProductPriceRequest;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dp.arts.common.util.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.business.utils.UrlUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.GroupPaddingHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.enums.FieldsEnum;
import com.sankuai.swan.udqs.api.QueryData;
import com.sankuai.swan.udqs.api.Result;
import com.sankuai.swan.udqs.api.SwanParam;
import graphql.execution.Async;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class SpuPaddingHandler implements GroupPaddingHandler {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.spu.detailurls.config", defaultValue = "{}")
    private static Map<Long, String> spuTypeToSpuDetailConfig;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.spu.detailurls.unificationUrlSwitch", defaultValue = "false")
    private boolean unificationUrlSwitch;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.spu.detailurls.default", defaultValue = "/ifuse/redirect?scenename=standard_product_detail&spuid=%d")
    private String defaultDetailUrlTemplate;

    /**
     * 商品属性：适用商户数量
     */
    public static final String PRODUCT_KEY_SHOP_NUM = "totalShopNum";

    /**
     * 商品属性：全网底价
     */
    public static final String PRODUCT_KEY_LOWEST_NET_PRICE = "lowestNetPrice";

    /**
     * 门店单批查询个数大小
     */
    private static int SHOP_BATCH_QUERY_SIZE = 50;

    @Resource
    CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityContext activityContext, ProductGroupM productGroupM, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.padding(ActivityContext,ProductGroupM,Map)");
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        List<CompletableFuture<Void>> productFutures = productGroupM.getProducts().stream().map(product -> paddingProduct(product, activityContext, params)).collect(Collectors.toList());
        return Async.each(productFutures).thenApply(aVoid -> productGroupM);
    }

    private CompletableFuture<Void> paddingProduct(ProductM product, ActivityContext ctx, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.paddingProduct(ProductM,ActivityContext,Map)");
        CompletableFuture<Void> paddingBaseInfoFuture = getPaddingBaseInfoFuture(product, ctx, params);
        CompletableFuture<Void> paddingPriceInfoFuture = getPaddingPriceInfoFuture(product, ctx);
        // poiMigrate
        CompletableFuture<Void> paddingShopInfoFuture;
        if (PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.SpuPaddingHandler.getScene())) {
            paddingShopInfoFuture = getPaddingShopInfoFuturePoiMigrate(product, ctx);
        } else {
            // 原逻辑
            paddingShopInfoFuture = getPaddingShopInfoFuture(product, ctx);
        }
        CompletableFuture<Void> paddingSaleInfoFuture = getPaddingSaleInfoFuture(product, ctx);
        return CompletableFuture.allOf(paddingBaseInfoFuture, paddingPriceInfoFuture, paddingSaleInfoFuture, paddingShopInfoFuture);
    }

    private CompletableFuture<Void> getPaddingBaseInfoFuture(ProductM product, ActivityContext ctx, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPaddingBaseInfoFuture(ProductM,ActivityContext,Map)");
        int uaCode = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.userAgent)).orElse(100);
        CompletableFuture<StandardProductPageDTO> standardProductPageFuture = compositeAtomService.getSpuById(product.getProductId());
        return standardProductPageFuture.thenAccept(standardProductPage -> {
            if (standardProductPage == null) {
                return;
            }
            product.setTitle(standardProductPage.getProductTitleInfoDTO().getTitle());
            product.setPicUrl(standardProductPage.getPics().get(0));
            String jumpUrl = getJumpUrlTemplate(standardProductPage);
            if (StringUtils.isNotEmpty(jumpUrl)) {
                product.setJumpUrl(buildDetailUrl(uaCode, String.format(jumpUrl, product.getProductId()), params));
            }
            addProductExtra(product, PRODUCT_KEY_LOWEST_NET_PRICE, standardProductPage.getMinPrice());
        });
    }

    private String getJumpUrlTemplate(StandardProductPageDTO standardProductPage) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getJumpUrlTemplate(com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductPageDTO)");
        String template = spuTypeToSpuDetailConfig.get(Long.parseLong(standardProductPage.getCategoryId()));
        if (StringUtils.isEmpty(template)) {
            template = defaultDetailUrlTemplate;
        }
        return template;
    }

    private CompletableFuture<Void> getPaddingPriceInfoFuture(ProductM product, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPaddingPriceInfoFuture(ProductM,ActivityContext)");
        CompletableFuture<StandardProductDTO> standardProductPriceFuture = compositeAtomService.getSpuPrice(buildGetStandardProductPriceRequest(ctx, product.getProductId()));
        return standardProductPriceFuture.thenAccept(priceInfo -> {
            if (priceInfo == null) {
                return;
            }
            product.setBasePrice(priceInfo.getMinPrice());// 减后价（没有立减返回的是实际售价）
            product.setBasePriceTag(getPriceStr(priceInfo.getMinPrice()));
            product.setMarketPrice(priceInfo.getMarketPrice().stripTrailingZeros().toPlainString());
            ProductPromoPriceM productPromoPrice = new ProductPromoPriceM();
            productPromoPrice.setPromoType(0);
            productPromoPrice.setPromoPrice(priceInfo.getReducePrice());// 新标品没有这个值
            productPromoPrice.setPromoPriceTag(getPriceStr(priceInfo.getMinPrice()));
            productPromoPrice.setPromoTag(priceInfo.getPromoDisplayTag());
            product.setPromoPrices(Lists.newArrayList(productPromoPrice));
        });
    }

    private String getPriceStr(BigDecimal price) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPriceStr(java.math.BigDecimal)");
        if (price != null && price.compareTo(BigDecimal.ZERO) > 0) {
            return price.stripTrailingZeros().toPlainString();
        }
        return null;
    }

    private CompletableFuture<Void> getPaddingShopInfoFuture(ProductM product, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPaddingShopInfoFuture(ProductM,ActivityContext)");
        int dpCityId = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        CompletableFuture<List<ShopDTO>> shopsFuture = compositeAtomService.getSpuShop(product.getProductId(), dpCityId)
                .thenCompose(standardProduct -> getShopDetails(standardProduct, dpCityId));
        return shopsFuture.thenAccept(shops -> {
            if (CollectionUtils.isEmpty(shops)) {
                return;
            }
            product.setShopMs(shops.stream().map(shop -> buildShopMs(shop, ctx)).sorted(Comparator.comparing(ShopM::getDistanceNum)).collect(Collectors.toList()));
            addProductExtra(product, PRODUCT_KEY_SHOP_NUM, CollectionUtils.size(shops) + "");
        });
    }

    private CompletableFuture<Void> getPaddingShopInfoFuturePoiMigrate(ProductM product, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPaddingShopInfoFuturePoiMigrate(ProductM,ActivityContext)");
        int dpCityId = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        CompletableFuture<List<DpPoiDTO>> shopsFuture = compositeAtomService.getSpuShop(product.getProductId(), dpCityId)
                .thenCompose(standardProduct -> getShopDetailsPoiMigrate(standardProduct, dpCityId));
        return shopsFuture.thenAccept(shops -> {
            if (CollectionUtils.isEmpty(shops)) {
                return;
            }
            product.setShopMs(shops.stream().map(shop -> buildShopMsPoiMigrate(shop, ctx)).sorted(Comparator.comparing(ShopM::getDistanceNum)).collect(Collectors.toList()));
            addProductExtra(product, PRODUCT_KEY_SHOP_NUM, CollectionUtils.size(shops) + "");
        });
    }

    private void addProductExtra(ProductM product, String attrKey, String attrValue) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.addProductExtra(ProductM,String,String)");
        if (product.getExtAttrs() == null) {
            product.setExtAttrs(Lists.newArrayList());
        }
        product.getExtAttrs().add(new AttrM(attrKey, attrValue));
    }

    private CompletableFuture<Void> getPaddingSaleInfoFuture(ProductM product, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPaddingSaleInfoFuture(ProductM,ActivityContext)");
        CompletableFuture<Result<QueryData>> spuSaleCountResFuture = compositeAtomService.getSwanDataByKey(1036, "tooth_sell", buildSwanParam(Sets.newHashSet((long) product.getProductId())));
        return spuSaleCountResFuture.thenAccept(result -> {
            if (result == null || !result.isIfSuccess()) {
                return;
            }
            QueryData queryData = result.getData();
            if (queryData == null || CollectionUtils.isEmpty(queryData.getResultSet())) {
                return;
            }
            int saleCount = queryData.getResultSet().stream()
                    .filter(data -> data.containsKey("productgroup_id"))
                    .filter(data -> Integer.parseInt((String) data.get("productgroup_id")) == product.getProductId())
                    .map(data -> ((BigDecimal) data.getOrDefault("orderPk", BigDecimal.ZERO)).intValue())
                    .findFirst()
                    .orElse(0);
            ProductSaleM saleM = new ProductSaleM();
            saleM.setSale(saleCount);
            product.setSale(saleM);
        });
    }

    private ShopM buildShopMs(ShopDTO shop, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.buildShopMs(ShopDTO,ActivityContext)");
        double lat = Optional.ofNullable((Double) ctx.getParam(ShelfActivityConstants.Params.lat)).orElse(0d);
        double lng = Optional.ofNullable((Double) ctx.getParam(ShelfActivityConstants.Params.lng)).orElse(0d);
        ShopM shopM = new ShopM();
        shopM.setShopId(shop.getShopId());
        shopM.setShopName(shop.getShopName());
        shopM.setPic(shop.getDefaultPic());
        shopM.setShopId(shop.getShopId());
        shopM.setShopUuid(shop.getShopUuid());
        shopM.setLat(shop.getGlat());
        shopM.setLng(shop.getGlng());
        shopM.setDistanceNum(getDistance(lng, lat, shop.getGlng(), shop.getGlat()));
        shopM.setAddress(shop.getAddress());
        return shopM;

    }

    private ShopM buildShopMsPoiMigrate(DpPoiDTO dpPoiDTO, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.buildShopMsPoiMigrate(DpPoiDTO,ActivityContext)");
        double lat = Optional.ofNullable((Double) ctx.getParam(ShelfActivityConstants.Params.lat)).orElse(0d);
        double lng = Optional.ofNullable((Double) ctx.getParam(ShelfActivityConstants.Params.lng)).orElse(0d);
        ShopM shopM = new ShopM();
        shopM.setShopId(dpPoiDTO.getShopId().intValue());
        shopM.setShopName(dpPoiDTO.getShopName());
        shopM.setPic(dpPoiDTO.getDefaultPic());
        shopM.setLongShopId(dpPoiDTO.getShopId());
        shopM.setShopUuid(dpPoiDTO.getUuid());
        shopM.setLat(dpPoiDTO.getLat());
        shopM.setLng(dpPoiDTO.getLng());
        shopM.setDistanceNum(getDistance(lng, lat, dpPoiDTO.getLng(), dpPoiDTO.getLat()));
        shopM.setAddress(dpPoiDTO.getAddress());
        return shopM;

    }

    private SwanParam buildSwanParam(Set<Long> spuIds) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.buildSwanParam(java.util.Set)");
        SwanParam swanParam = new SwanParam();
        List<Map<String, Object>> paramsList = spuIds
                .stream()
                .map(this::buildSpuParams)
                .collect(Collectors.toList());
        swanParam.setRequestParams(paramsList);
        return swanParam;
    }

    private Map<String, Object> buildSpuParams(long spuId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.buildSpuParams(long)");
        Map<String, Object> params = Maps.newHashMap();
        params.put("productgroup_id", spuId);
        return params;
    }

    private CompletableFuture<List<ShopDTO>> getShopDetails(StandardProductDTO standardProductDTO, int cityId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getShopDetails(com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductDTO,int)");
        if (standardProductDTO == null || MapUtils.isEmpty(standardProductDTO.getStandardItemPageDTO()) || CollectionUtils.isEmpty(standardProductDTO.getStandardItemPageDTO().get(cityId))) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<Integer> shopIds = standardProductDTO.getStandardItemPageDTO().get(cityId).stream().map(StandardItemDTO::getShopId).collect(Collectors.toList());
        return batchGetShopDetails(shopIds);

    }

    private CompletableFuture<List<DpPoiDTO>> getShopDetailsPoiMigrate(StandardProductDTO standardProductDTO, int cityId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getShopDetailsPoiMigrate(com.dianping.tpfun.product.api.sku.aggregate.dto.StandardProductDTO,int)");
        if (standardProductDTO == null || MapUtils.isEmpty(standardProductDTO.getStandardItemPageDTO()) || CollectionUtils.isEmpty(standardProductDTO.getStandardItemPageDTO().get(cityId))) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<Long> shopIds = standardProductDTO.getStandardItemPageDTO().get(cityId).stream().map(PoiIdUtil::getShopIdL).collect(Collectors.toList());
        return batchGetShopDetailsPoiMigrate(shopIds);

    }

    /**
     * 分页查询门店详情，一页最多50个
     *
     * @return
     */
    private CompletableFuture<List<ShopDTO>> batchGetShopDetails(List<Integer> shopIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.batchGetShopDetails(java.util.List)");
        if (CollectionUtils.isEmpty(shopIds)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<CompletableFuture<List<ShopDTO>>> shopListFutures = Lists.newArrayList();
        for (int i = 0; i <= shopIds.size() / SHOP_BATCH_QUERY_SIZE; i++) {
            shopListFutures.add(compositeAtomService.findShops(shopIds.stream().skip(i * SHOP_BATCH_QUERY_SIZE).limit(SHOP_BATCH_QUERY_SIZE).collect(Collectors.toList())));
        }
        return Async.each(shopListFutures).thenApply(shopLists -> shopLists.stream().flatMap(List::stream).collect(Collectors.toList()));
    }

    private CompletableFuture<List<DpPoiDTO>> batchGetShopDetailsPoiMigrate(List<Long> shopIds) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.batchGetShopDetailsPoiMigrate(java.util.List)");
        if (CollectionUtils.isEmpty(shopIds)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }
        List<CompletableFuture<List<DpPoiDTO>>> shopListFutures = Lists.newArrayList();
        for (int i = 0; i <= shopIds.size() / SHOP_BATCH_QUERY_SIZE; i++) {
            DpPoiRequest dpPoiRequest = buildDpPoiRequest(shopIds.stream().skip(i * SHOP_BATCH_QUERY_SIZE).limit(SHOP_BATCH_QUERY_SIZE).collect(Collectors.toList()));
            shopListFutures.add(compositeAtomService.findShopsByDpShopIds(dpPoiRequest));
        }
        return Async.each(shopListFutures).thenApply(shopLists -> shopLists.stream().flatMap(List::stream).collect(Collectors.toList()));
    }

    private DpPoiRequest buildDpPoiRequest(List<Long> dpPoiIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.buildDpPoiRequest(java.util.List)");
        DpPoiRequest dpPoiRequest = new DpPoiRequest();
        dpPoiRequest.setShopIds(dpPoiIds);
        dpPoiRequest.setFields(Lists.newArrayList(FieldsEnum.SHOP_SERVER.getFields()));
        return dpPoiRequest;
    }

    private GetStandardProductPriceRequest buildGetStandardProductPriceRequest(ActivityContext ctx, int spuId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.buildGetStandardProductPriceRequest(com.sankuai.dzviewscene.shelf.framework.ActivityContext,int)");
        GetStandardProductPriceRequest request = new GetStandardProductPriceRequest();
        int dpCityId = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        int mtCityId = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.mtCityId)).orElse(0);
        long dpUserId = Optional.ofNullable((Long) ctx.getParam(ShelfActivityConstants.Params.dpUserId)).orElse(0L);
        long mtUserId = Optional.ofNullable((Long) ctx.getParam(ShelfActivityConstants.Params.mtUserId)).orElse(0L);
        int platform = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        String deviceId = ctx.getParam(ShelfActivityConstants.Params.deviceId);
        request.setCityId(dpCityId);
        request.setSpuId(spuId);
        request.setUserId(VCPlatformEnum.DP.getType() == platform ? dpUserId : mtUserId);
        request.setDpId(deviceId);
        request.setMtCityId(mtCityId);
        request.setPlatform(getPromoPlatform(platform));
        request.setClientType(getPromoProductType(platform));
        return request;
    }

    private int getPromoPlatform(int platform) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPromoPlatform(int)");
        if (VCPlatformEnum.MT.getType() == platform) {
            return PayPlatform.mt_iphone_native.getCode();
        } else {
            return PayPlatform.dp_iphone_native.getCode();
        }
    }

    private int getPromoProductType(int platform) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getPromoProductType(int)");
        if (VCPlatformEnum.MT.getType() == platform) {
            return ProductType.mt_generalTrade.getValue();
        } else {
            return ProductType.generalTrade.getValue();
        }
    }

    private String buildDetailUrl(int uaCode, String uri, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.buildDetailUrl(int,java.lang.String,java.util.Map)");
        String appSchema = VCClientTypeEnum.isMtClientTypeByCode(uaCode) ? UrlUtils.MT_APP_SCHEMA : UrlUtils.DP_APP_SCHEMA;
        String host = getHost(uaCode);
        String url = appendUtmSource(host.concat(uri), params);
        return appSchema.concat(UrlUtils.urlEncode(url));
    }

    private String appendUtmSource(String url, Map<String, Object> groupParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.appendUtmSource(java.lang.String,java.util.Map)");
        String utmSource = ParamsUtil.getValue(groupParams, PaddingFetcher.Params.source, "").toString();
        if (StringUtils.isBlank(utmSource)) {
            return url;
        }
        return UrlUtils.appendUrl(url, "utm_source", utmSource);
    }

    private String getHost(int uaCode) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getHost(int)");
        if (unificationUrlSwitch || !VCClientTypeEnum.isMtClientTypeByCode(uaCode)) {
            // 统一链接后，美团侧也要跳转点评
            return Lion.getStringValue("com.sankuai.dzviewscene.productshelf.dp.m.host");
        }
        return Lion.getStringValue("com.sankuai.dzviewscene.productshelf.mt.i.host");
    }

    /**
     * 赤道半径
     */
    private static final double EARTH_RADIUS = 6378137;

    private static double rad(double d) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.rad(double)");
        return d * Math.PI / 180.0;
    }

    /**
     * 单位：米
     *
     * @param lon1
     * @param lat1
     * @param lon2
     * @param lat2
     * @return
     */
    private static double getDistance(double lon1, double lat1, double lon2, double lat2) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl.SpuPaddingHandler.getDistance(double,double,double,double)");
        double radLat1 = rad(lat1);
        double radLat2 = rad(lat2);
        double a = radLat1 - radLat2;
        double b = rad(lon1) - rad(lon2);
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
        s = s * EARTH_RADIUS;
        return s;
    }
}


