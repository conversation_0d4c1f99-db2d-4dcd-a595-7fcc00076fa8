package com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;

/**
 * 根据基础价格倒排序
 * Created by float.lu on 2020/9/23.
 */
public class BasePriceDescQuery implements SimplexQuery {

    // TODO: 2020/9/23 换成根据BigDecimal类型价格排序
    @Override
    public Comparator<ProductM> comparator(QueryContext context) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.BasePriceDescQuery.comparator(com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext)");
        return new Comparator<ProductM>() {
            @Override
            public int compare(ProductM product1, ProductM product2) {
                if (product1 == null || product2 == null) {
                    return 0;
                }
                if (product1.getBasePrice() == null || product2.getBasePrice() == null) {
                    return 0;
                }
                return product2.getBasePrice().compareTo(product1.getBasePrice());
            }
        };
    }
}
