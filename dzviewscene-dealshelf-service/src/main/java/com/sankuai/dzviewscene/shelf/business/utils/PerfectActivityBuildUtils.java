package com.sankuai.dzviewscene.shelf.business.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.dp.config.LionObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.productshelf.vu.vo.edudetail.ActivityTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.edudetail.AidDecisionVO;
import com.sankuai.dzviewscene.shelf.business.shelf.defaultshelf.model.activity.perfect.PriceWindowModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/1 7:30 下午
 */
public class PerfectActivityBuildUtils {

    private static LionObject<PerfectActivityConstant> perfectActivityConstantLionObject = LionObject.create("com.sankuai.dzviewscene.productshelf.perfect.activity.constant.config", new TypeReference<PerfectActivityConstant>() {});

    public static DzTagVO buildPerfectDzTagVOs(ProductM productM) {
        if(!isShowPerfectActivity(productM)) {
            return null;
        }
        long startTime = getActivityStartTime(productM);
        long preheatTime = getPreheatActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now > startTime || now < preheatTime) {
            return null;
        }
        DzPromoDetailVO dzPromoDetail = buildDzPromoDetail(productM);
        if(dzPromoDetail == null) {
            return null;
        }
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        if(!hasPerfectActivity(productM)) {
            return null;
        }
        String price = getPerfectActivityPrice(productM);
        if(StringUtils.isEmpty(price)) {
            return null;
        }
        String perfectPriceDoc = getPerfectPriceDoc(perfectActivity);
        String perfectPriceColor = getPerfectPriceColor(perfectActivity);
        return buildDzTagVO(dzPromoDetail, perfectPriceDoc, perfectPriceColor, price);
    }

    public static RichLabelVO buildPerfectActivityRemainSecondsLabel(ProductM productM) {
        if (!isShowPerfectActivity(productM)) {
            return null;
        }
        long preheatTime = getPreheatActivityStartTime(productM);
        long startTime = getActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now < preheatTime || now >= startTime) {
            return null;
        }
        String dateFormat = new SimpleDateFormat("M月d日").format(new Date(startTime));
        String doc = perfectActivityConstantLionObject.getObject().getSecondaryActivityTimeDoc();
        String perfectPriceDoc = String.format(doc, dateFormat);
        String activityTimeColor = perfectActivityConstantLionObject.getObject().getSecondaryActivityTimeColor();
        int activityTimeSize = perfectActivityConstantLionObject.getObject().getSecondaryActivityTimeSize();
        return buildRichLabelVO(perfectPriceDoc, activityTimeColor, activityTimeSize);

    }

    public static AidDecisionVO buildPerfectActivityRemainSecondAidDecisionVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildPerfectActivityRemainSecondAidDecisionVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if (!isShowPerfectActivity(productM)) {
            return null;
        }
        long preheatTime = getPreheatActivityStartTime(productM);
        long startTime = getActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now < preheatTime || now >= startTime) {
            return null;
        }
        String dateFormat = new SimpleDateFormat("M月d日").format(new Date(startTime));
        String doc = perfectActivityConstantLionObject.getObject().getSecondaryActivityTimeDoc();
        String perfectPriceDoc = String.format(doc, dateFormat);
        return build(perfectPriceDoc, 1);
    }

    private static AidDecisionVO build(String name, int type) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.build(java.lang.String,int)");
        AidDecisionVO aidDecisionVO = new AidDecisionVO();
        aidDecisionVO.setName(name);
        aidDecisionVO.setType(type);
        return aidDecisionVO;
    }

    public static boolean hasPerfectActivity(ProductM productM) {
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        if(perfectActivity == null && perfectPreHeatActivity == null) {
            return false;
        }
        return true;
    }

    /**
     * 获取非玩美季活动
     * 如果入参中有一个玩美季活动，则说明无正常活动，所以需返回空列表。
     *
     * @return
     */
    public static List<ProductActivityM> getNormalActivityList(List<ProductActivityM> activityMS) {
        if (CollectionUtils.isEmpty(activityMS)) {
            return Lists.newArrayList();
        }
        List<ProductActivityM> list = Lists.newArrayList();
        for (ProductActivityM activityM : activityMS) {
            if (activityM == null) {
                continue;
            }
            if (isPerfectActivity(activityM) || isPreheatPerfectActivity(activityM)) {
                return Lists.newArrayList();
            }
            list.add(activityM);
        }
        return list;
    }

    private static DzTagVO buildDzTagVO(DzPromoDetailVO dzPromoDetail, String perfectPriceDoc, String perfectPriceColor, String price) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildDzTagVO(com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoDetailVO,java.lang.String,java.lang.String,java.lang.String)");
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setPromoDetail(dzPromoDetail);
        dzTagVO.setText(perfectPriceDoc + price);
        if(StringUtils.isNotEmpty(getPerfectPriceDocWithoutPriceLock())) {
            dzTagVO.setText(String.format(getPerfectPriceDocWithoutPriceLock(), price));
        }
        dzTagVO.setTextColor(perfectPriceColor);
        dzTagVO.setHasBorder(true);
        dzTagVO.setBorderColor(perfectPriceColor);
        return dzTagVO;
    }

    private static String getPerfectPriceColor(ProductActivityM perfectActivity) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getPerfectPriceColor(com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM)");
        if(perfectActivity == null || StringUtils.isEmpty(perfectActivity.getActivityPriceColor())) {
            if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
                return null;
            }
            return perfectActivityConstantLionObject.getObject().getPriceDefaultColor();
        }
        return perfectActivity.getActivityPriceColor();
    }

    private static String getPerfectPriceDocWithoutPriceLock() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getPerfectPriceDocWithoutPriceLock()");
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return null;
        }
        return perfectActivityConstantLionObject.getObject().getPriceDefaultDocWithoutLock();
    }

    private static String getPerfectPriceDoc(ProductActivityM perfectActivity) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getPerfectPriceDoc(com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM)");
        if(perfectActivity == null || StringUtils.isEmpty(perfectActivity.getActivityPriceText())) {
            if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
                return null;
            }
            return perfectActivityConstantLionObject.getObject().getPriceDefaultDoc();
        }
        return perfectActivity.getActivityPriceText();
    }

    private static DzPromoDetailVO buildDzPromoDetail(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildDzPromoDetail(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(!isShowPerfectActivity(productM)) {
            return null;
        }
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return null;
        }
        PriceWindowModel priceWindowModel = perfectActivityConstantLionObject.getObject().getPriceWindowModel();
        if(priceWindowModel == null) {
            return null;
        }
        DzPromoDetailVO dzPromoDetail = new DzPromoDetailVO();
        dzPromoDetail.setPopType(2);
        dzPromoDetail.setTitle(priceWindowModel.getTitle());
        dzPromoDetail.setTotalPromoLab(priceWindowModel.getContent());
        dzPromoDetail.setTotalPromoPrice(priceWindowModel.getPriceDoc());
        return dzPromoDetail;
    }


    public static RichLabelVO buildActivityRemainSecondsLabel(ProductM productM) {
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return null;
        }
        if(!isShowPerfectActivity(productM)) {
            return null;
        }
        long startTime = getActivityStartTime(productM);
        long preheatTime = getPreheatActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now > startTime || now < preheatTime) {
            return null;
        }
        if((startTime - now)/1000 > 3600*24) {
            String prefix = perfectActivityConstantLionObject.getObject().getActivityDatePrefix();
            String suffix = perfectActivityConstantLionObject.getObject().getActivityDateSuffx();
            String activityDateColor = perfectActivityConstantLionObject.getObject().getActivityDateColor();
            int activityDateSize = perfectActivityConstantLionObject.getObject().getActivityDateSize();
            return buildRichLabelVO(prefix + new SimpleDateFormat("M月d日").format(new Date(startTime)) + suffix, activityDateColor, activityDateSize);
        }
        String doc = perfectActivityConstantLionObject.getObject().getActivityTimeDoc();
        String activityTimeColor = perfectActivityConstantLionObject.getObject().getActivityTimeColor();
        int activityTimeSize = perfectActivityConstantLionObject.getObject().getActivityTimeSize();
        return buildRichLabelVO(doc, activityTimeColor, activityTimeSize);
    }

    public static List<FloatTagVO> buildDoubleColumnHeadPicFloatTagVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildDoubleColumnHeadPicFloatTagVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(!isShowPerfectActivity(productM)) {
            return null;
        }
        long startTime = getActivityStartTime(productM);
        long preheatTime = getPreheatActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now < preheatTime) {
            return null;
        }
        if(now < startTime) {
            ProductActivityM preHeatPerfectActivity = extractPerfectPreHeatActivity(productM);
            if(preHeatPerfectActivity == null) {
                return null;
            }
            return buildDoubleColumnFloatTagVOList(preHeatPerfectActivity.getUrl(), true);
        }
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        if(perfectActivity == null) {
            return null;
        }
        return buildDoubleColumnFloatTagVOList(perfectActivity.getUrl(), false);
    }

    private static List<FloatTagVO> buildDoubleColumnFloatTagVOList(String url, boolean isPreHeat) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildDoubleColumnFloatTagVOList(java.lang.String,boolean)");
        if(StringUtils.isEmpty(url)) {
            return null;
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(buildDoubleColumnDzPictureComponentVO(url, isPreHeat));
        floatTagVO.setPosition(FloatTagPositionEnums.LEFT_BOTTOM.getPosition());
        return Lists.newArrayList(floatTagVO);
    }

    private static DzPictureComponentVO buildDoubleColumnDzPictureComponentVO(String url, boolean isPreHeat) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildDoubleColumnDzPictureComponentVO(java.lang.String,boolean)");
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(url);
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return dzPictureComponentVO;
        }
        dzPictureComponentVO.setAspectRadio(perfectActivityConstantLionObject.getObject().getDoubleHeadPicLabelAspectRadio());
        dzPictureComponentVO.setPicHeight(perfectActivityConstantLionObject.getObject().getDoubleHeadPicLabelHeight());
        if(isPreHeat) {
            dzPictureComponentVO.setAspectRadio(perfectActivityConstantLionObject.getObject().getDoublePreheatHeadPicLabelAspectRadio());
            dzPictureComponentVO.setPicHeight(perfectActivityConstantLionObject.getObject().getDoublePreheatHeadPicLabelHeight());
        }
        return dzPictureComponentVO;
    }

    public static List<FloatTagVO> buildHeadPicFloatTagVO(ProductM productM) {
        if(!isShowPerfectActivity(productM)) {
            return null;
        }
        long startTime = getActivityStartTime(productM);
        long preheatTime = getPreheatActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now < preheatTime) {
            return null;
        }
        if(now < startTime) {
            ProductActivityM preHeatPerfectActivity = extractPerfectPreHeatActivity(productM);
            if(preHeatPerfectActivity == null) {
                return null;
            }
            return buildFloatTagVOList(preHeatPerfectActivity.getUrl(), true);
        }
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        if(perfectActivity == null) {
            return null;
        }
        return buildFloatTagVOList(perfectActivity.getUrl(), false);
    }

    public static ActivityTagVO buildHeadPicActivityTagVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildHeadPicActivityTagVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(!isShowPerfectActivity(productM)) {
            return null;
        }
        long startTime = getActivityStartTime(productM);
        long preheatTime = getPreheatActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now < preheatTime) {
            return null;
        }
        if(now < startTime) {
            ProductActivityM preHeatPerfectActivity = extractPerfectPreHeatActivity(productM);
            if(preHeatPerfectActivity == null) {
                return null;
            }
            return buildActivityTagVO(preHeatPerfectActivity.getUrl());
        }
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        if(perfectActivity == null) {
            return null;
        }
        return buildActivityTagVO(perfectActivity.getUrl());
    }

    public static DzActivityTagVO buildHeadPicDzActivityTagVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildHeadPicDzActivityTagVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(!isShowPerfectActivity(productM)) {
            return null;
        }
        long startTime = getActivityStartTime(productM);
        long preheatTime = getPreheatActivityStartTime(productM);
        long now = System.currentTimeMillis();
        if(now < preheatTime) {
            return null;
        }
        if(now < startTime) {
            ProductActivityM preHeatPerfectActivity = extractPerfectPreHeatActivity(productM);
            if(preHeatPerfectActivity == null) {
                return null;
            }
            return buildDzActivityTagVO(preHeatPerfectActivity.getUrl());
        }
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        if(perfectActivity == null) {
            return null;
        }
        return buildDzActivityTagVO(perfectActivity.getUrl());
    }

    private static DzActivityTagVO buildDzActivityTagVO(String url) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildDzActivityTagVO(java.lang.String)");
        DzActivityTagVO dzActivityTagVO = new DzActivityTagVO();
        dzActivityTagVO.setImgUrl(url);
        dzActivityTagVO.setPosition(4);
        return dzActivityTagVO;
    }

    private static ActivityTagVO buildActivityTagVO(String url) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildActivityTagVO(java.lang.String)");
        ActivityTagVO activityTagVO = new ActivityTagVO();
        activityTagVO.setImgUrl(url);
        activityTagVO.setPosition(4);
        return activityTagVO;
    }


    private static List<FloatTagVO> buildFloatTagVOList(String url, boolean isPreHeat) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildFloatTagVOList(java.lang.String,boolean)");
        if(StringUtils.isEmpty(url)) {
            return null;
        }
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(buildDzPictureComponentVO(url, isPreHeat));
        floatTagVO.setPosition(FloatTagPositionEnums.LEFT_BOTTOM.getPosition());
        return Lists.newArrayList(floatTagVO);
    }

    private static DzPictureComponentVO buildDzPictureComponentVO(String url, boolean isPreHeat) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildDzPictureComponentVO(java.lang.String,boolean)");
        DzPictureComponentVO dzPictureComponentVO = new DzPictureComponentVO();
        dzPictureComponentVO.setPicUrl(url);
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return dzPictureComponentVO;
        }
        dzPictureComponentVO.setAspectRadio(perfectActivityConstantLionObject.getObject().getHeadPicLabelAspectRadio());
        dzPictureComponentVO.setPicHeight(perfectActivityConstantLionObject.getObject().getHeadPicLabelHeight());
        if(isPreHeat) {
            dzPictureComponentVO.setAspectRadio(perfectActivityConstantLionObject.getObject().getPreheatHeadPicLabelAspectRadio());
            dzPictureComponentVO.setPicHeight(perfectActivityConstantLionObject.getObject().getPreheatHeadPicLabelHeight());
        }
        return dzPictureComponentVO;
    }

    private static RichLabelVO buildRichLabelVO(String text, String color, int size) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildRichLabelVO(java.lang.String,java.lang.String,int)");
        if(StringUtils.isEmpty(text)) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(text);
        richLabelVO.setTextColor(color);
        richLabelVO.setTextSize(size);
        return richLabelVO;
    }

    private static long getActivityStartTime(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getActivityStartTime(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        if(perfectActivity == null && perfectPreHeatActivity == null) {
            return Long.MAX_VALUE;
        }
        if(perfectActivity == null && perfectPreHeatActivity != null) {
            return perfectPreHeatActivity.getRemainingTime() * 1000;
        }
        return perfectActivity.getActivityBeginTime();
    }

    public static long getActivityStartTimeForShow(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getActivityStartTimeForShow(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(!isShowPerfectActivity(productM)) {
            return 0;
        }
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        if(perfectActivity == null && perfectPreHeatActivity == null) {
            return 0L;
        }
        if(perfectActivity == null && perfectPreHeatActivity != null) {
            return perfectPreHeatActivity.getRemainingTime();
        }
        return perfectActivity.getActivityBeginTime() / 1000;
    }

    private static long getPreheatActivityStartTime(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getPreheatActivityStartTime(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        ProductActivityM prefectActivity = extractPerfectActivity(productM);
        if(perfectPreHeatActivity == null && prefectActivity == null) {
            return Long.MAX_VALUE;
        }
        if(perfectPreHeatActivity == null && prefectActivity != null) {
            return Long.MIN_VALUE;
        }
        return perfectPreHeatActivity.getActivityBeginTime();
    }

    private static long getActivityEndTime(ProductM productM) {
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        if(perfectActivity == null && perfectPreHeatActivity == null) {
            return Long.MIN_VALUE;
        }
        if(perfectActivity == null && perfectPreHeatActivity != null) {
            return Long.MAX_VALUE;
        }
        return perfectActivity.getRemainingTime() * 1000;
    }

    public static String getPerfectActivityPrice(ProductM productM) {
        if(productM == null) {
            return null;
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(8);
        if(productPromoPriceM == null) {
            return null;
        }
        return productPromoPriceM.getPromoPriceTag();
    }

    public static BigDecimal getPerfectActivityPromoPrice(ProductM productM) {
        if(productM == null) {
            return null;
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(8);
        if(productPromoPriceM == null) {
            return null;
        }
        return productPromoPriceM.getPromoPrice();
    }

    public static boolean isDuringPerfectActivity(ProductM productM) {
        if(!isShowPerfectActivity(productM)) {
            return false;
        }
        long startTime = getActivityStartTime(productM);
        long endTime = getActivityEndTime(productM);
        return startTime < System.currentTimeMillis() && System.currentTimeMillis() < endTime;
    }

    public static boolean isShowPerfectActivity(ProductM productM) {
        if(!hasPerfectActivity(productM)) {
            return false;
        }
        long now = System.currentTimeMillis();
        if(getActivityEndTime(productM) < now || getPreheatActivityStartTime(productM) > now) {
            return false;
        }
        return isPerfectActivityPriceLessThanIdlePromoPrice(productM) && isPerfectActivityPricesLessThanDailyPrice(productM);
    }

    public static boolean isPpEffectiveActivity(ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(productM.getActivities())) {
            return false;
        }
        ProductActivityM activityM = productM.getActivities().stream().filter(item -> item.getPpEffectiveTagInfo() != null && item.getPpEffectiveTagInfo().isPpEffective()).findFirst().orElse(null);
        return activityM != null;
    }

    public static List<FloatTagVO> buildPicEffectiveFloatTagVO(ProductM productM) {
        ProductActivityM activityM = productM.getActivities().stream().filter(item -> item.getPpEffectiveTagInfo() != null && item.getPpEffectiveTagInfo().isPpEffective()).findFirst().orElse(null);
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(activityM.getPpEffectiveTagInfo().getPicComponentVO());
        return Lists.newArrayList(floatTagVO);
    }

    public static FloatTagVO buildPreTitleEffectiveFloatTagVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.buildPreTitleEffectiveFloatTagVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        ProductActivityM activityM = productM.getActivities().stream().filter(item -> item.getPpEffectiveTagInfo() != null && item.getPpEffectiveTagInfo().isPpEffective()).findFirst().orElse(null);
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setIcon(activityM.getPpEffectiveTagInfo().getPreTagComponentVO());
        return floatTagVO;
    }

    /**
     * 判断某个团单是否处于玩美季活动预热期
     *@param productM 团单信息
     *@return 返回该团单是否处于玩美季活动预热期
     */
    public static boolean isDuringPerfectActivityPreheatTime(ProductM productM) {
        //如果没有玩美季活动则返回false
        if(!isShowPerfectActivity(productM)) {
            return false;
        }
        //判断当前时间是否早于活动正式开始时间
        return System.currentTimeMillis() < getActivityStartTime(productM);
    }

    public static boolean isShowPerfectStartTime(ProductM productM, String sceneCode) {
        if(!isShowPerfectActivity(productM)) {
            return false;
        }
        if(!isSceneShowingStartTime(sceneCode)) {
            return false;
        }
        long now = System.currentTimeMillis();
        long startTime = getActivityStartTime(productM);
        if((startTime - now)/1000 < 3600*24) {
            return true;
        }
        return false;
    }

    private static boolean isSceneShowingStartTime(String sceneCode) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.isSceneShowingStartTime(java.lang.String)");
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return false;
        }
        List<String> sceneCodeWithStartTimeList = perfectActivityConstantLionObject.getObject().getSceneCodeWithStartTimeList();
        if(CollectionUtils.isEmpty(sceneCodeWithStartTimeList)) {
            return false;
        }
        return sceneCodeWithStartTimeList.contains(sceneCode);
    }

    private static boolean isPerfectActivityPriceLessThanIdlePromoPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.isPerfectActivityPriceLessThanIdlePromoPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(productM == null) {
            return false;
        }
        ProductPromoPriceM idlePromo = productM.getPromo(PromoTypeEnum.IDLE_PROMO.getType());
        ProductPromoPriceM perfectPromo = productM.getPromo(8);
        if(perfectPromo == null || perfectPromo.getPromoPrice() == null) {
            return false;
        }
        if(idlePromo == null || idlePromo.getPromoPrice() == null) {
            return true;
        }
        return idlePromo.getPromoPrice().compareTo(perfectPromo.getPromoPrice()) > 0;
    }

    private static boolean isPerfectActivityPricesLessThanDailyPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.isPerfectActivityPricesLessThanDailyPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        long now = System.currentTimeMillis();
        long startTime = getActivityStartTime(productM);
        if(now > startTime) {
            return true;
        }
        if(productM == null) {
            return false;
        }
        ProductPromoPriceM perfectPromo = productM.getPromo(8);
        BigDecimal dailyPrice = getDailyPrice(productM);
        if(perfectPromo == null || perfectPromo.getPromoPrice() == null) {
            return false;
        }
        if(dailyPrice == null) {
            return true;
        }
        return perfectPromo.getPromoPrice().compareTo(dailyPrice) < 0;

    }

    private static BigDecimal getDailyPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getDailyPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return productM.getBasePrice();
        }
        return productPromoPriceM.getPromoPrice();
    }

    public static ProductActivityM extractPerfectActivity(ProductM productM) {
        if(productM == null || CollectionUtils.isEmpty(productM.getActivities()) || CollectUtils.firstValue(productM.getActivities()) == null) {
            return null;
        }
        //玩美季活动判断
        return productM.getActivities().stream().filter(activity -> isPerfectActivity(activity)).findFirst().orElse(null);
    }

    private static boolean isPerfectActivity(ProductActivityM activityM) {
        if(activityM == null) {
            return false;
        }
        boolean typeSwitch = Lion.getBooleanValue("com.sankuai.dzviewscene.productshelf.perfect.activity.type.switch", false);
        if(typeSwitch) {
            return activityM.getShelfActivityType() != null && activityM.getShelfActivityType() == 1 && !activityM.isPreheat();
        }
        return activityM.getActivityBeginTime() != 0 && !activityM.isPreheat();
    }

    private static boolean isPreheatPerfectActivity(ProductActivityM activityM) {
        if(activityM == null) {
            return false;
        }
        boolean typeSwitch = Lion.getBooleanValue("com.sankuai.dzviewscene.productshelf.perfect.activity.type.switch", false);
        if(typeSwitch) {
            return activityM.getShelfActivityType() != null && activityM.getShelfActivityType() == 1 && activityM.isPreheat();
        }
        return activityM.getActivityBeginTime() != 0 && activityM.isPreheat();
    }


    public static ProductActivityM extractPerfectPreHeatActivity(ProductM productM) {
        if(productM == null || CollectionUtils.isEmpty(productM.getActivities()) || CollectUtils.firstValue(productM.getActivities()) == null) {
            return null;
        }
        //玩美季活动判断
        return productM.getActivities().stream().filter(activity -> isPreheatPerfectActivity(activity)).findFirst().orElse(null);
    }

    public static String getFilterColor() {
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return null;
        }
        return perfectActivityConstantLionObject.getObject().getFilterTabColor();
    }

    public static String getSelectedFilterColor() {
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return null;
        }
        return perfectActivityConstantLionObject.getObject().getSelectedFilterColor();
    }

    public static int getFilterSize() {
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return 0;
        }
        return perfectActivityConstantLionObject.getObject().getFilterTabSize();
    }

    public static int getSelectedFilterSize() {
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return 0;
        }
        return perfectActivityConstantLionObject.getObject().getSelectedFilterSize();
    }

    public static String getActivityName() {
        if(perfectActivityConstantLionObject == null || perfectActivityConstantLionObject.getObject() == null) {
            return null;
        }
        return perfectActivityConstantLionObject.getObject().getActivityName();
    }

    @Data
    private static class PerfectActivityConstant{
        private PriceWindowModel priceWindowModel;
        private String priceDefaultDoc;
        private String priceDefaultColor;
        private String activityDatePrefix;
        private String activityDateSuffx;
        private String activityDateColor;
        private int activityDateSize;
        private String activityTimeDoc;
        private String activityTimeColor;
        private int activityTimeSize;
        private String secondaryActivityTimeDoc;
        private String secondaryActivityTimeColor;
        private int secondaryActivityTimeSize;
        private int headPicLabelHeight;
        private double headPicLabelAspectRadio;
        private int preheatHeadPicLabelHeight;
        private double preheatHeadPicLabelAspectRadio;
        private int doubleHeadPicLabelHeight;
        private double doubleHeadPicLabelAspectRadio;
        private int doublePreheatHeadPicLabelHeight;
        private double doublePreheatHeadPicLabelAspectRadio;
        private String filterTabColor;
        private String selectedFilterColor;
        private int filterTabSize;
        private int selectedFilterSize;
        private String activityName;
        private List<String> sceneCodeWithStartTimeList;
        private String priceDefaultDocWithoutLock;
    }
}

