package com.sankuai.dzviewscene.shelf.business.shelf.edu.builder;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.PlatformShelfCacheFilterFetcher.SHELF_TAGS;

/**
 * Created by 李维隆 on 2021/01/31
 */
@ExtPointInstance(name = "教培-POI页-早教团购货架主标题模块构造扩展点")
public class EduEarlyEducationDealShelfMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.edu.earlyeducation.deal.constant.config", defaultValue = "{}")
    public MainTitleConstant mainTitleConstant;

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.edu.builder.EduEarlyEducationDealShelfMainTitleBuilderExt.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return mainTitleConstant.getTitle();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.edu.builder.EduEarlyEducationDealShelfMainTitleBuilderExt.icon(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) return mainTitleConstant.getMtTitleIcon();
        return mainTitleConstant.getDpTitleIcon();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.edu.builder.EduEarlyEducationDealShelfMainTitleBuilderExt.tags(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<ShelfGroupM> shelfGroupCompletableFuture = activityContext.getMainData();
        ShelfGroupM shelfGroupM = shelfGroupCompletableFuture.join();
        if (shelfGroupM.getFilterMs() == null || CollectionUtils.isEmpty(shelfGroupM.getFilterMs().values())) {
            return null;
        }
        Map<String, Object> extra = Lists.newArrayList(shelfGroupM.getFilterMs().values()).get(0).getExtra();
        if (MapUtils.isEmpty(extra) || extra.get(SHELF_TAGS) == null) {
            return null;
        }
        return buildTags(extra);
    }

    private List<IconRichLabelVO> buildTags(Map<String, Object> extra) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.edu.builder.EduEarlyEducationDealShelfMainTitleBuilderExt.buildTags(java.util.Map)");
        List<String> shelfTags = (List<String>) extra.get(SHELF_TAGS);
        if (CollectionUtils.isEmpty(shelfTags)) {
            return null;
        }
        String icon = mainTitleConstant.getTitleTagIcon();
        List<IconRichLabelVO> returnValue = new ArrayList<>();
        for (String shelfTag : shelfTags) {
            IconRichLabelVO iconRichLabelVO = new IconRichLabelVO();
            iconRichLabelVO.setIcon(icon);
            iconRichLabelVO.setText(new RichLabelVO(shelfTag));
            returnValue.add(iconRichLabelVO);
        }
        return returnValue;
    }

    @Data
    private static class MainTitleConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
    }
}
