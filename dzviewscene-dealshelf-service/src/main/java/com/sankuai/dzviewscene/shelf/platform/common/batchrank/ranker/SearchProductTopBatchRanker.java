package com.sankuai.dzviewscene.shelf.platform.common.batchrank.ranker;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRanker;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankerEnum;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankingCtx;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankingRequestParamEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 可解释置顶排序
 * <AUTHOR>
 * @date 2022/8/1
 */
@Component
public class SearchProductTopBatchRanker implements BatchRanker {
    @Override
    public Map<String, List<RankingItem>> ranking(BatchRankingCtx batchRankingCtx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.batchrank.ranker.SearchProductTopBatchRanker.ranking(com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankingCtx)");
        List<Integer> topDealIds = batchRankingCtx.getTargetParam(BatchRankingRequestParamEnum.ShopRecommendProductIds.getKey());
        if (CollectionUtils.isEmpty(topDealIds)) {
            return batchRankingCtx.getSource();
        }
        Map<String, List<RankingItem>> resultList = new HashMap<>();
        for (Map.Entry<String, List<RankingItem>> entry : batchRankingCtx.getSource().entrySet()) {
            resultList.put(entry.getKey(), doSingleSort(entry.getValue(), topDealIds));
        }
        return resultList;
    }

    private List<RankingItem> doSingleSort(List<RankingItem> rankingItems, List<Integer> topDealIds) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.batchrank.ranker.SearchProductTopBatchRanker.doSingleSort(java.util.List,java.util.List)");
        if (CollectionUtils.isEmpty(rankingItems)) {
            return new ArrayList<>();
        }
        //置顶优先，其他按照原序
        return rankingItems.stream().map(o->(ProductM)o).sorted((o1, o2) -> {
            if (topDealIds.contains(o1.getProductId()) ^ topDealIds.contains(o2.getProductId())) {
                return topDealIds.contains(o1.getProductId()) ? -1 : 1;
            }
            if (topDealIds.contains(o1.getProductId()) && topDealIds.contains(o2.getProductId())) {
                return topDealIds.indexOf(o1.getProductId()) - topDealIds.indexOf(o2.getProductId());
            }
            return 0;
        }).collect(Collectors.toList());
    }

    @Override
    public String getName() {
        return BatchRankerEnum.SearchProductTop.toString();
    }
}
