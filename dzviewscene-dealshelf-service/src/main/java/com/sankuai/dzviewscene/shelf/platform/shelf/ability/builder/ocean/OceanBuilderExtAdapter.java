package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.productshelf.vu.vo.ShelfOceanVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by float.lu on 2020/9/15.
 */
public abstract class OceanBuilderExtAdapter implements OceanBuilderExt {

    @Override
    public ShelfOceanVO shelfOceanVO(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String childrenFilterBarLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String filterBarLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String moreLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String productItemLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String searchIconLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String wholeShelfLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String bookingBtnLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String secondBookingBtnLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String joyCardLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String secondProductItemLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String moreFilterLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String secondMoreLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String productAreaLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String secondProductAreaLabs(ActivityContext activityContext) {
        return null;
    }

    @Override
    public String promoPopViewLabs(ActivityContext activityContext) {
        return getDefaultPopViewLabs(activityContext);
    }

    @Override
    public String promoPopViewButtonLabs(ActivityContext activityContext) {
        return getDefaultPopViewLabs(activityContext);
    }

    @Override
    public String promoPopViewCloseLabs(ActivityContext activityContext) {
        return getDefaultPopViewLabs(activityContext);
    }

    @Override
    public String productAreaTipLabs(ActivityContext activityContext) {
        return null;
    }


    private String getDefaultPopViewLabs(ActivityContext activityContext) {
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("cat_id", getCategoryId(activityContext));
        return JsonCodec.encode(oceanMap);
    }

    private int getCategoryId(ActivityContext ctx) {
        ShopM shopM = ctx.getParam(ProductDetailActivityConstants.Ctx.ctxShop);
        if (shopM != null) {
            return shopM.getCategory();
        }
        return 0;
    }

    @Override
    public String pinSessionLabs(ActivityContext activityContext) {
        return null;
    }
}
