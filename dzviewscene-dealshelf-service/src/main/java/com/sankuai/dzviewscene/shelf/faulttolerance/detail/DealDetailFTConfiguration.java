package com.sankuai.dzviewscene.shelf.faulttolerance.detail;

import com.dianping.cat.Cat;
import com.sankuai.athena.stability.faulttolerance.FaultToleranceConfiguration;
import com.sankuai.athena.stability.faulttolerance.core.Executor;
import com.sankuai.athena.stability.faulttolerance.mirror.MirrorConfiguration;
import com.sankuai.dzviewscene.shelf.faulttolerance.detail.req.DealDetailModelContextRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 默认团详降级策略
 * 目前只支持统一的数据清洗方式，后续有需要按不同类目做不同的清洗逻辑可扩展
 */
@Service
public class DealDetailFTConfiguration extends FaultToleranceConfiguration<DealDetailModelContextRequest, Object> {

    @Resource
    private DealDetailExecutor dealDetailExecutor;

    @Resource
    private DealDetailDefaultMirrorReportCleaner dealDetailDefaultMirrorReportCleaner;

    @Override
    public String getFtName() {
        return "dealGroupDetail";
    }

    @Override
    public Executor<DealDetailModelContextRequest, Object> getMainExecutor() {
        return dealDetailExecutor;
    }

    @Override
    public Executor<DealDetailModelContextRequest, Object> getFallback(String fallback) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.faulttolerance.detail.DealDetailFTConfiguration.getFallback(java.lang.String)");
        return null;
    }

    @Override
    public MirrorConfiguration getMirrorConfiguration() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.faulttolerance.detail.DealDetailFTConfiguration.getMirrorConfiguration()");
        return dealDetailDefaultMirrorReportCleaner;
    }
}
