package com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.List;

/**
 * 根据excludeProductIds参数过滤部分结果
 * Created by liweilong06 on 2020/11/04.
 */
public class ProductIdFilterQuery implements SimplexQuery {

    @Override
    public boolean match(QueryContext context, ProductM product) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.ProductIdFilterQuery.match(QueryContext,ProductM)");
        if (context.getActivityContext().getParameters().get(QueryFetcher.Params.excludeProductIds) == null) {
            return true;
        }
        List<Integer> excludeProductIds = (List<Integer>) context.getActivityContext().getParameters().get(QueryFetcher.Params.excludeProductIds);
        if (CollectionUtils.isEmpty(excludeProductIds)) {
            return true;
        }
        return !excludeProductIds.contains(product.getProductId());
    }
}
