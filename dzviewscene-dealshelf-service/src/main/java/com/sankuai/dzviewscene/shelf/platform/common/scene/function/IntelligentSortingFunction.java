package com.sankuai.dzviewscene.shelf.platform.common.scene.function;

import com.dianping.cat.Cat;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.ShelfAttrItem;
import com.dianping.product.shelf.common.request.ShelfShopAttrQueryRequest;
import com.google.common.collect.Lists;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.sankuai.athena.client.executor.Futures;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * 商户是否开启算法召回排序查询函数
 * intelligent_sorting() 返回boolean值
 * true代表商户开启了算法排序，false代表未开启算法排序
 */
@Component
public class IntelligentSortingFunction extends AbstractFunction {

    private static final String ATTR_NAME = "intelligent_sorting_switch";

    private static final int timeout = 200;

    private static AtomFacadeService atomFacadeService;

    @Override
    public String getName() {
        return "intelligent_sorting";
    }

    @Override
    public AviatorObject call(Map<String, Object> env) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.scene.function.IntelligentSortingFunction.call(java.util.Map)");
        try {
            Long dpShopId = PoiIdUtil.getDpPoiIdL(env, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
            if (dpShopId <= 0) {
                return AviatorRuntimeJavaType.valueOf(false);
            }
            CompletableFuture<Boolean> useIntelligentSortingFuture = atomFacadeService.getShopShelfAttrs(buildShelfShopAttrQueryRequest(dpShopId))
                    .thenApply(response -> getIntelligentSorting(response,dpShopId));
            boolean useIntelligentSorting = Optional.ofNullable(Futures.get(useIntelligentSortingFuture,timeout)).orElse(false);
            return AviatorRuntimeJavaType.valueOf(useIntelligentSorting);
        } catch (Exception e) {
            return AviatorRuntimeJavaType.valueOf(false);
        }

    }

    private boolean getIntelligentSorting (Response<Map<Long, List<ShelfAttrItem>>> response, long dpShopId) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.scene.function.IntelligentSortingFunction.getIntelligentSorting(com.dianping.product.shelf.common.dto.Response,long)");
        if(response == null || !response.isSuccess()) {
            return false;
        }
        Map<Long, List<ShelfAttrItem>> shopAttrsMap = response.getContent();
        if (shopAttrsMap == null || !shopAttrsMap.containsKey(dpShopId)) {
            return false;
        }
        List<ShelfAttrItem> shelfAttrItems = shopAttrsMap.get(dpShopId);
        if (CollectionUtils.isEmpty(shelfAttrItems)) {
            return false;
        }
        return shelfAttrItems.stream()
                .filter(attr -> ATTR_NAME.equals(attr.getName()))
                .findFirst().map(ShelfAttrItem::getValue)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .map(Boolean::parseBoolean)
                .orElse(false);
    }

    private ShelfShopAttrQueryRequest buildShelfShopAttrQueryRequest(long dpShopId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.scene.function.IntelligentSortingFunction.buildShelfShopAttrQueryRequest(long)");
        ShelfShopAttrQueryRequest shelfShopAttrQueryRequest = new ShelfShopAttrQueryRequest();
        shelfShopAttrQueryRequest.setShopIds(Lists.newArrayList(dpShopId));
        shelfShopAttrQueryRequest.setAttrNames(Lists.newArrayList(ATTR_NAME));
        shelfShopAttrQueryRequest.setStatus(1);
        return shelfShopAttrQueryRequest;
    }

    @Resource
    public void setAtomFacadeService(AtomFacadeService atomFacadeService) {
        IntelligentSortingFunction.atomFacadeService = atomFacadeService;
    }
}
