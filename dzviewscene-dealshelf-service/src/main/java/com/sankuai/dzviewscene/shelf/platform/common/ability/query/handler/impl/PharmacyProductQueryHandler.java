package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.common.Convert;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.health.sc.api.thrift.*;
import jodd.util.StringUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 药品召回
 * Created by wangxinyuan on 2021/09/02.
 */
@Component
public class PharmacyProductQueryHandler implements GroupQueryHandler {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityContext ctx, String groupName, Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.query(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String,java.util.Map)");
        //1、获取筛选ID、筛选类型、外卖商户ID、平台、用户ID
        CompletableFuture<Map<String, FilterM>> filterMsCompletableFuture = ctx.getAttachment(Convert.MEDICAL_GROUP_FILTER);
        if(filterMsCompletableFuture == null) {
            return batchGetMedicineShelfProducts(ctx);
        }
        //2、根据中间数据或者入参获取药品信息
        return filterMsCompletableFuture.thenCompose(filterMs -> batchGetMedicineShelfProducts(filterMs, ctx));
    }

    private CompletableFuture<ProductGroupM> batchGetMedicineShelfProducts(Map<String, FilterM> filterMs, ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.batchGetMedicineShelfProducts(Map,ActivityContext)");
        if(MapUtils.isEmpty(filterMs)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        FilterM filterM = getFirstFilterM(filterMs);
        if(hasProducts(filterM)) {
            //构建默认的商品列表
            return buildProductGroupM(filterM);
        }
        return batchGetMedicineShelfProducts(ctx);
    }

    private CompletableFuture<ProductGroupM> batchGetMedicineShelfProducts(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.batchGetMedicineShelfProducts(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        Map<String, Object> extraMap = getExtraMap(ctx);
        if(MapUtils.isEmpty(extraMap)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        CompletableFuture<SpuTagResponse> spuTagResponseFuture = compositeAtomService.batchGetMedicineShelfProducts(Convert.buildSpuTagProductParam(ctx, extraMap));
        if(spuTagResponseFuture == null) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        return spuTagResponseFuture.thenApply(spuTagResponse -> convert(spuTagResponse, extraMap));
    }

    private Map<String, Object> getExtraMap(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.getExtraMap(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        String extra = ctx.getParam(ShelfActivityConstants.Params.extra);
        if(StringUtil.isEmpty(extra)) {
            return Maps.newHashMap();
        }
        return JsonCodec.decode(extra, new TypeReference<Map<String, Object>>() {});
    }

    private ProductGroupM convert(SpuTagResponse spuTagResponse, Map<String, Object> filterBtnMExtraM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.convert(com.sankuai.health.sc.api.thrift.SpuTagResponse,java.util.Map)");
        ProductGroupM productGroupM = new ProductGroupM();
        if(spuTagResponse == null || spuTagResponse.getData() == null || CollectionUtils.isEmpty(spuTagResponse.getData().getProduct_spu_list())) {
            return new ProductGroupM();
        }
        productGroupM.setProducts(Convert.buildProduct(spuTagResponse.getData().getProduct_spu_list(), filterBtnMExtraM));
        productGroupM.setTotal(CollectionUtils.size(spuTagResponse.getData().getProduct_spu_list()));
        return productGroupM;
    }

    private FilterM getFirstFilterM(Map<String, FilterM> filterMs) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.getFirstFilterM(java.util.Map)");
        return filterMs.entrySet().stream()
                .map(Map.Entry::getValue)
                .findFirst().orElse(null);
    }

    private CompletableFuture<ProductGroupM> buildProductGroupM(FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.buildProductGroupM(com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM)");
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(findFirstHasProduct(filterM));
        return CompletableFuture.completedFuture(productGroupM);
    }

    private List<ProductM> findFirstHasProduct(FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.findFirstHasProduct(com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM)");
        if(filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return Lists.newArrayList();
        }
        return filterM.getFilters().stream()
                .filter(filterBtnM -> CollectionUtils.isNotEmpty(filterBtnM.getProducts()) && filterBtnM.isSelected())
                .map(FilterBtnM::getProducts)
                .findFirst().orElse(Lists.newArrayList());
    }

    private boolean hasProducts(FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.PharmacyProductQueryHandler.hasProducts(com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM)");
        if(filterM == null || CollectionUtils.isEmpty(filterM.getFilters())) {
            return false;
        }
        for(FilterBtnM filterBtnM : filterM.getFilters()) {
            if(filterBtnM != null && CollectionUtils.isNotEmpty(filterBtnM.getProducts())) {
                return true;
            }
        }
        return false;
    }
}
