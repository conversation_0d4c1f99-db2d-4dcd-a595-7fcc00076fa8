/*
 * Create Author  : liyanmin
 * Create Date    : 2023-08-03
 * Project        :
 * File Name      : BPShelfRequestBuilder.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.shelf.platform.shelf.utils;

import com.alibaba.fastjson.TypeReference;
import com.dianping.appkit.utils.VersionUtil;
import com.dianping.cat.Cat;
import com.dianping.product.shelf.common.request.ShelfRequest;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mtrace.Tracer;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.shelf.utils.LogUtils;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.gateways.utils.MagicMemberUtil;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.MagicMemberFilterGrayConfig;
import com.sankuai.dzviewscene.shelf.platform.common.model.MatrixExperimentM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2023-08-03
 * @since dzviewscene-dealshelf-home 1.0
 */
@Component
public class BPShelfRequestBuilder {

    private static final String ONLY_SHOW_GAME_COIN_KEY = "onlyShowGameCoin";

    private static final String HIDE_MAGIC_FILTER_FLAG = "magicalMemberTabHide";

    public static final String DP_LIMIT_VERSION = "11.21.11";

    public static final String MT_WX_XCX_LIMIT_VERSION = "8.47.100";

    public static final String HARMONY = "harmony";
    public static final int MT_WX_XCX_SHELF_VERSION = 112;

    @ConfigValue(key = "com.sankuai.dzviewscene.dealshelf.scenetype.black.shop", defaultValue = "{}")
    public static Map<String, List<Long>> SCENE_TYPE_BLACK_LIST_SHOP;

    // 向bp和选单透传定位城市，下游根据platform区分点评/美团city
    public static void addLocationCityId(ActivityContext ctx, Map<String, String> attrMap) {
        int locationCityId = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.locationCityId);
        if (locationCityId > 0) {
            attrMap.put("userLocateCityId", String.valueOf(locationCityId));
        }
    }

    private static int getPlatform(ActivityContext ctx) {
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        if (platform < 10) {
            // platform = 1 或者 2，使用userAgent
            int userAgent = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.userAgent);
            if (userAgent > 0) {
                return userAgent;
            }
        }
        return platform;
    }

    public static void addSceneTypeParam(ActivityContext ctx, ShelfRequest shelfRequest) {
        try {
            Map<String, Object> recallConfig = ctx.getParam(QueryFetcher.Params.recallConfig);
            if (MapUtils.isEmpty(recallConfig)) {
                return;
            }
            List<Integer> sceneTypeLimitClient = (List<Integer>) recallConfig.get(QueryFetcher.Params.sceneTypeLimitClient);
            int userAgent = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.userAgent);
            if (CollectionUtils.isNotEmpty(sceneTypeLimitClient) && !sceneTypeLimitClient.contains(userAgent)) {
                return;
            }
            int sceneType = getSceneType(ctx, recallConfig);
            if (sceneType > 0) {
                shelfRequest.setSceneType(sceneType);
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    public static int getSceneType(ActivityContext ctx, Map<String, Object> recallConfig) {
        // 先走实验获取一下
        int sceneType = getSceneTypeByDouHu(ctx, recallConfig);
        if (sceneType <= 0) {
            // 取固定配置
            sceneType = ParamsUtil.getIntSafely(recallConfig, QueryFetcher.Params.sceneType);
        }
        //黑名单判断下
        if(MapUtils.isEmpty(SCENE_TYPE_BLACK_LIST_SHOP)){
            return sceneType;
        }
        List<Long> blackShopList = SCENE_TYPE_BLACK_LIST_SHOP.get(String.valueOf(sceneType));
        if(CollectionUtils.isEmpty(blackShopList)){
            return sceneType;
        }
        long dpShopId = ParamsUtil.getLongSafely(ctx, ShelfActivityConstants.Params.dpPoiIdL);
        if(blackShopList.contains(dpShopId)){
            return 0;
        }
        return sceneType;
    }

    public static Integer getSceneTypeByDouHu(ActivityContext ctx, Map<String, Object> recallConfig) {
        int sceneTypeABShelfVersion = ParamsUtil.getIntSafely(recallConfig, QueryFetcher.Params.sceneTypeABShelfVersion);
        int paramShelfVersion = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.shelfVersion);
        if (sceneTypeABShelfVersion > 0 && paramShelfVersion < sceneTypeABShelfVersion) {
            // 如果配置了版本控制，但是参数版本低于配置版本时，则不参与实验
            return 0;
        }
        // 如果没有配置版本，或者参数版本大于等于配置版本时则参与实验
        Map<String, Integer> sceneTypeABCfg = (Map<String, Integer>) recallConfig.get(QueryFetcher.Params.sceneTypeABCfg);
        List<DouHuM> douHuMList = ctx.getParam(ShelfActivityConstants.Params.douHus);
        Optional<Integer> configByDouHu = (Optional<Integer>) DouHuUtils.getConfigByDouHu(douHuMList, sceneTypeABCfg);
        if (configByDouHu.isPresent() && configByDouHu.get() > 0) {
            return configByDouHu.get();
        }
        return 0;
    }

    public static void addMagicalMemberParam(ActivityContext ctx, Map<String, String> attrMap) {
        //压测流量不隐藏
        if (isNibPtest()) {
            Cat.logEvent("magicalMemberPtest", "hit");
            return;
        }
        int platform = getPlatform(ctx);
        // 点评app
        if (platform == VCClientTypeEnum.DP_APP.getCode()) {
            if (validateVersionForDpMagicMem(ctx) && MagicMemberUtil.passGrayCheck(ctx.getParameters())) {
                return;
            }
            // 屏蔽
            hideMagicTab(attrMap, ctx);
            return;
        }
        // 鸿蒙系统（app侧）
        if (HARMONY.equals(ParamsUtil.getStringSafely(ctx, ShelfActivityConstants.Params.clientType))) {
            // 屏蔽
            hideMagicTab(attrMap, ctx);
            return;
        }
        // 美团微信小程序
        if (platform == VCClientTypeEnum.MT_XCX.getCode()) {
            if (validateVersionForMTWXCXCMagicMem(ctx) && MagicMemberUtil.passGrayCheck(ctx.getParameters())) {
                return;
            }
            // 屏蔽
            hideMagicTab(attrMap, ctx);
            return;
        }
        Map<String, Object> recallConfig = ctx.getParam(QueryFetcher.Params.recallConfig);
        MagicMemberFilterGrayConfig grayConfig = ParamsUtil.getObjectSafely(recallConfig, QueryFetcher.Params.magicMemberFilterGrayConfig, new TypeReference<MagicMemberFilterGrayConfig>() {
        });
        //只要是神会员，无论1.5期还是1期，PMF上magicMemberFilterGrayConfig不为空；非神会员场景这里直接命中grayConfig == null
        if (grayConfig == null || !grayConfig.isValidCondition(ctx)) {
            hideMagicTab(attrMap, ctx);
            return;
        }

        //通过统一灰度开关则不隐藏
        if (MagicMemberUtil.passGrayCheck(ctx.getParameters())) {
            LogUtils.recordKeyMsg(ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtUserId), "graySwitchPass", "true");
            return;
        }
        LogUtils.recordKeyMsg(ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtUserId), "graySwitchPass", "false");

        //未启用实验，则隐藏（1.5期此时enableMatrix=false）
        if (!grayConfig.isEnableMatrix()) {
            hideMagicTab(attrMap, ctx);
            return;
        }
        // 启用实验，策略为空，则不隐藏
        if (CollectionUtils.isEmpty(grayConfig.getHitMatrixExps())) {
            return;
        }
        //matrix实验
        List<MatrixExperimentM> matrixExpLists = ctx.getParam(ShelfActivityConstants.Params.matrixExps);
        //没有matrix实验结果, 说明不准入
        if (CollectionUtils.isEmpty(matrixExpLists)) {
            hideMagicTab(attrMap, ctx);
            return;
        }
        boolean hitMatrixAb = matrixExpLists.stream().filter(v -> v.getGroupKey().equals(grayConfig.getMatrixGroupKey()))
                .anyMatch(v -> grayConfig.getHitMatrixExps().contains(v.getExpKey()));
        //未命中，隐藏
        if (!hitMatrixAb) {
            hideMagicTab(attrMap, ctx);
        }
    }

    /**
     * 验证点评侧的神会员
     * @param ctx
     * @return
     */
    public static boolean validateVersionForDpMagicMem(ActivityContext ctx) {
        if (ctx == null) {
            return false;
        }
        String appVersion = ctx.getParam(ShelfActivityConstants.Params.appVersion);
        if (StringUtils.isBlank(appVersion)) {
            return true;
        }
        if (VersionUtil.compare(appVersion, DP_LIMIT_VERSION) < 0) {
            return false;
        }
        Integer shelfVersion = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.shelfVersion);
        return DzPromoUtils.validateCouponShelfVersionForDp(VCPlatformEnum.DP.getType(), shelfVersion);
    }

    /**
     * 验证微信美团小程序侧的神会员
     * @param ctx
     * @return
     */
    public static boolean validateVersionForMTWXCXCMagicMem(ActivityContext ctx) {
        if (ctx == null) {
            return false;
        }
        String appVersion = ctx.getParam(ShelfActivityConstants.Params.appVersion);
        if (StringUtils.isBlank(appVersion) || VersionUtil.compare(appVersion, MT_WX_XCX_LIMIT_VERSION) < 0) {
            return false;
        }
        Integer shelfVersion = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.shelfVersion);
        return shelfVersion != null && shelfVersion >= MT_WX_XCX_SHELF_VERSION;
    }

    public static void addGameCoinParam(ActivityContext ctx, Map<String, String> attrMap) {
        //只有游戏厅游戏币货架需要
        if (!ctx.getSceneCode().equals("game_room_coin_deal_shelf")) {
            return;
        }
        attrMap.put(ONLY_SHOW_GAME_COIN_KEY, "true");
    }

    private static boolean isNibPtest() {
        return Boolean.parseBoolean(Tracer.getContext("nib-ptest"));
    }

    private static void hideMagicTab(Map<String, String> attrMap, ActivityContext ctx) {
        attrMap.put(HIDE_MAGIC_FILTER_FLAG, "true");
        LogUtils.recordKeyMsg(ParamsUtil.getLongSafely(ctx, PmfConstants.Params.mtUserId), "tabHide", "true");
    }

}
