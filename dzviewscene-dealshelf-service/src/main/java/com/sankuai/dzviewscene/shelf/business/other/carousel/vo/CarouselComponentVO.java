package com.sankuai.dzviewscene.shelf.business.other.carousel.vo;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@MobileDo(id = 0x48dd)
public class CarouselComponentVO implements Serializable {
    /**
     * 轮播区 item
     */
    @MobileDo.MobileField(key = 0xa63a)
    private List<CarouselItemVO> carouselItemList;

    /**
     * 标题对象
     */
    @MobileDo.MobileField(key = 0x2876)
    private MainTitleComponentVO mainTitle;

    /**
     * 按钮对象
     */
    @MobileDo.MobileField(key = 0x7e5d)
    private DzSimpleButtonVO btn;

    /**
     * 拼场引导信息
     */
    @MobileDo.MobileField(key = 0xe0d3)
    private DzPoolGuideVO dzPoolGuide;

    /**
     * 样式，1为新
     */
    @MobileDo.MobileField(key = 0xfc19)
    private int showType;

}