package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.core.IExtPoint;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.*;

import java.util.List;
import java.util.Map;

/**
 * item区定制扩展点
 * <p>
 * Created by float on 2020/9/19.
 */
public interface ProductBuilderVoExt extends IExtPoint {

    String EXT_POINT_PRODUCT_LIST_BUILDER_CODE = "ProductListFloorBuilderExt";


    /**
     * 商品Id
     *
     * @param activityContext
     * @param productM
     * @return
     */
    int productId(ActivityContext activityContext, ProductM productM);

    /**
     * 跳转链接链接
     *
     * @param activityContext
     * @param productM
     * @return
     */
    String detailJumpUrl(ActivityContext activityContext, ProductM productM);

    /**
     * 图片链接
     *
     * @param activityContext
     * @param productM
     * @return
     */
    String picUrl(ActivityContext activityContext, ProductM productM);

    /**
     * 拼团价格拼接
     *
     * @param activityContext
     * @param productM
     * @return
     */
    DzFixPriceVO pinPrice(ActivityContext activityContext, ProductM productM);


    /**
     * 次卡价格拼接
     * @param activityContext
     * @param productM
     * @return
     */
    DzFixPriceVO cardPrice(ActivityContext activityContext, ProductM productM);

    /**
     * 活动标签
     * @param activityContext
     * @param productM
     * @return
     */
    List<DzActivityTagVO> activityTags(ActivityContext activityContext, ProductM productM);

    /**
     * 活动标签
     * @param activityContext
     * @param productM
     * @param index
     * @return
     */
    List<DzActivityTagVO> activityTags(ActivityContext activityContext, ProductM productM, int index);

    /**
     * 售价定制
     * @param activityContext
     * @param productM
     * @return
     */
    String salePrice(ActivityContext activityContext, ProductM productM);

    /**
     * 售价描述
     * @param activityContext
     * @param productM
     * @return
     */
    String salePriceDesc(ActivityContext activityContext, ProductM productM);

    /**
     * 原价描述
     * @param activityContext
     * @param productM
     * @return
     */
    String marketPriceDesc(ActivityContext activityContext, ProductM productM);

    /**
     * 是否是商户推荐
     * @param activityContext
     * @param productM
     * @return
     */
    boolean isShopRecommend(ActivityContext activityContext, ProductM productM);

    /**
     * 会员价
     * @param activityContext
     * @param productM
     * @return
     */
    DzVipPriceVO vipPrice(ActivityContext activityContext, ProductM productM);

    /**
     * 返券
     * @param activityContext
     * @param productM
     * @return
     */
    String coupons(ActivityContext activityContext, ProductM productM);

    /**
     * 商品决策标签
     * @param activityContext
     * @param productM
     * @return
     */
    List<DzTagVO> aidDecisionTags(ActivityContext activityContext, ProductM productM);

    /**
     * 优惠标签
     * @param activityContext
     * @param productM
     * @return
     */
    List<String> promo(ActivityContext activityContext, ProductM productM);

    /**
     * 商户名称
     * @param activityContext
     * @param productM
     * @return
     */
    String shopName(ActivityContext activityContext, ProductM productM);

    /**
     * 商户距离
     * @param activityContext
     * @param productM
     * @return
     */
    String distance(ActivityContext activityContext, ProductM productM);


    /**
     * 商户评分
     *
     * @param activityContext
     * @param productM
     * @return
     */
    String shopScoreStr(ActivityContext activityContext, ProductM productM);

    /**
     * 按钮名称
     * @param activityContext
     * @param productM
     * @return
     */
    String buttonName(ActivityContext activityContext, ProductM productM);

    /**
     * 按钮跳转链接
     * @param activityContext
     * @param productM
     * @return
     */
    String buttonJumpUrl(ActivityContext activityContext, ProductM productM);

    /**
     * 是否可用
     * @param activityContext
     * @param productM
     * @return
     */
    boolean available(ActivityContext activityContext, ProductM productM);

    /**
     * 活动剩余时间（用于倒计时）
     * @param activityContext
     * @param productM
     * @return
     */
    long activityRemainSeconds(ActivityContext activityContext, ProductM productM);

    /**
     * 商品配送方式
     * @param activityContext
     * @param productM
     * @return
     */
    String deliveryType(ActivityContext activityContext, ProductM productM);

    /**
     * 门店信息
     * @param activityContext
     * @param productM
     * @return
     */
    ShopVO shop(ActivityContext activityContext, ProductM productM);

    /**
     * 按钮状态
     * @param activityContext
     * @param productM
     * @return
     */
    int buttonStatus(ActivityContext activityContext, ProductM productM);

    /**
     * 库存
     * @param activityContext
     * @param productM
     * @return
     */
    StockVO stock(ActivityContext activityContext, ProductM productM);

    /**
     * 销量，数值类型
     * @param activityContext
     * @param productM
     * @return
     */
    long saleNum(ActivityContext activityContext, ProductM productM);

    ShopInfoVO shopInfo(ActivityContext ctx, ProductM productM);

    /**
     * 商品下单链接
     * @param activityContext
     * @param productM
     * @return
     */
    String createOrderUrl(ActivityContext activityContext, ProductM productM);

    /**
     * 市场价
     * @param activityContext
     * @param productM
     * @return
     */
    String marketPrice(ActivityContext activityContext, ProductM productM);

    /**
     * 商户地址
     * @param activityContext
     * @param productM
     * @return
     */
    String address(ActivityContext activityContext, ProductM productM);

    /**
     * 商品销量
     * @param ctx
     * @param productM
     * @return
     */
    String sale(ActivityContext ctx, ProductM productM);

    /**
     * 图片宽高比
     * @param ctx
     * @param productM
     * @return
     */
    double picScale(ActivityContext ctx, ProductM productM);

    /**
     * 商品适用门店信息
     * @param activityContext
     * @param productM
     * @return
     */
    String applyShopDesc(ActivityContext activityContext, ProductM productM);


    /**
     * 拼场信息
     * @param ctx
     * @param productM
     * @return
     */
    List<DzPoolInfoVO> poolInfoList(ActivityContext ctx, ProductM productM);

    /**
     * 商品标签
     * @param activityContext
     * @param productM
     * @return
     */
    List<String> productTags(ActivityContext activityContext, ProductM productM);

    /**
     * 扩展属性
     * @param activityContext
     * @param productM
     * @return
     */
    Map<String, Object> extAttrs(ActivityContext activityContext, ProductM productM);

    /**
     * 适用时间属性
     * @param ctx
     * @param productM
     * @return
     */
    String timeAttr(ActivityContext ctx, ProductM productM);

    /**
     * 优惠信息
     * @param ctx
     * @param productM
     * @return
     */
    List<DzPromoVO> promoVOList(ActivityContext ctx, ProductM productM);

    /**
     * 购买信息
     * @param ctx
     * @param productM
     * @return
     */
    String purchase(ActivityContext ctx, ProductM productM);

    /**
     * 评分
     * @param ctx
     * @param productM
     * @return
     */
    String score(ActivityContext ctx, ProductM productM);

    /**
     * 商品星级
     * @param ctx
     * @param productM
     * @return
     */
    int star(ActivityContext ctx, ProductM productM);

    /**
     * 排行榜
     * @param ctx
     * @param productM
     * @return
     */
    String ranking(ActivityContext ctx, ProductM productM);

    /**
     * 商品类型
     * @param ctx
     * @param productM
     * @return
     */
    int productType(ActivityContext ctx, ProductM productM);

}
