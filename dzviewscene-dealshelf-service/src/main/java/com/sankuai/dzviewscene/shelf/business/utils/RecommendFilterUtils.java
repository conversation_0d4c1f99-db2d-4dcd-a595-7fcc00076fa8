package com.sankuai.dzviewscene.shelf.business.utils;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 推荐筛选项通用逻辑 取其他每个tab下的销量最高的一个团单+商家推荐执行，如果不够5个则从所有团单种补充，不能重复
 * @auther: wangxinyuan02
 * @date: 2022/03/03 2:28
 */
public class RecommendFilterUtils {

    private static final String RECOMMEND_ATTR_NAME = "shopRecommendAttr";
    private static final int RECOMMEND_LIMIT = 5;
    private static final String TRUE = "true";

    public static void recommendFilterM(Map<Long, List<ProductM>> filterMs, long filterId) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.recommendFilterM(java.util.Map,long)");
        if(MapUtils.isEmpty(filterMs)) {
            return;
        }
        //1、获取"全部"filterBtnM 已排好序
        List<ProductM> allFilterProducts = filterMs.get(filterId);
        //2、遍历所有其他tab，按销量排序，取第一个，存到列表2中
        List<ProductM> othersFilterTopOneProducts = filterOtherFilterTabTopOneProducts(filterMs, filterId);
        //3、如果有商家推荐， 加入到otherFilterProducts中 去重
        List<ProductM> otherAndRecommendProducts = addRecommendProductIfNotExist(allFilterProducts, othersFilterTopOneProducts);
        //4、如果列表2中少于5个，在列表1中过滤列表2的团单，在列表1中取top(5-列表2个数)个商品填充到列表3中 去重
        List<ProductM> resultProducts = filterResultProducts(allFilterProducts, otherAndRecommendProducts);
        //5、最后排序 按商家推荐和销量
        List<ProductM> resultProductsBySorted = sortByRecommendAndSale(resultProducts);
        //6、将AllTab下的商品列表替换掉
        setAllFilterTab(resultProductsBySorted, filterMs, filterId);
    }

    public static List<ProductM> sortByRecommendAndSale(List<ProductM> resultProducts) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.sortByRecommendAndSale(java.util.List)");
        if(CollectionUtils.isEmpty(resultProducts)) {
            return Lists.newArrayList();
        }
        return resultProducts.stream().sorted(comparatorBySale()).collect(Collectors.toList());
    }

    private static List<ProductM> addRecommendProductIfNotExist(List<ProductM> allFilterProducts, List<ProductM> othersFilterTopOneProducts) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.addRecommendProductIfNotExist(java.util.List,java.util.List)");
        ProductM productM = getRecommendProduct(allFilterProducts);
        if(productM == null) {
            return othersFilterTopOneProducts;
        }
        if(CollectionUtils.isEmpty(othersFilterTopOneProducts)) {
            return Lists.newArrayList(productM);
        }
        othersFilterTopOneProducts.add(productM);
        return othersFilterTopOneProducts.stream().distinct().collect(Collectors.toList());
    }

    private static ProductM getRecommendProduct(List<ProductM> allFilterProducts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.getRecommendProduct(java.util.List)");
        if(CollectionUtils.isEmpty(allFilterProducts)) {
            return null;
        }
        // 只需取第一个是商家推荐的即可
        return allFilterProducts.stream()
                .filter(productM -> productM != null && TRUE.equals(productM.getAttr(RECOMMEND_ATTR_NAME)))
                .findFirst().orElse(null);
    }

    private static List<ProductM> filterOtherFilterTabTopOneProducts(Map<Long, List<ProductM>> filterMs, long filterId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.filterOtherFilterTabTopOneProducts(java.util.Map,long)");
        return filterMs.entrySet().stream()
                .filter(filterM -> filterM.getKey() != null && filterM.getKey() != filterId && CollectionUtils.isNotEmpty(filterM.getValue()))
                .map(filterM -> getTopOneProduct(filterM.getValue())).filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
    }

    private static ProductM getTopOneProduct(List<ProductM> productMList) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.getTopOneProduct(java.util.List)");
        if(CollectionUtils.isEmpty(productMList)) {
            return null;
        }
        return sortBySale(productMList).stream().findFirst().orElse(null);
    }

    // 重建列表排序， 不影响原有筛选项商品顺序
    public static List<ProductM> sortBySale(List<ProductM> products) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.sortBySale(java.util.List)");
        List<ProductM> copyProducts = Lists.newArrayList(products);
        return copyProducts.stream().sorted(
                Comparator.comparingInt(productM -> getSaleSafely((ProductM) productM)).reversed()
        ).collect(Collectors.toList());
    }

    private static List<ProductM> filterResultProducts(List<ProductM> sortedAllProducts, List<ProductM> othersFilterTopOneProducts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.filterResultProducts(java.util.List,java.util.List)");
        if(CollectionUtils.isNotEmpty(othersFilterTopOneProducts) && CollectionUtils.size(othersFilterTopOneProducts) >= RECOMMEND_LIMIT) {
            return othersFilterTopOneProducts;
        }
        if(CollectionUtils.isEmpty(othersFilterTopOneProducts)) {
            return sortedAllProducts;
        }
        if(CollectionUtils.isEmpty(sortedAllProducts)) {
            return othersFilterTopOneProducts;
        }
        return addAndLimitProduct(sortedAllProducts, othersFilterTopOneProducts);
    }

    private static List<ProductM> addAndLimitProduct(List<ProductM> sortedAllProducts, List<ProductM> othersFilterTopOneProducts) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.addAndLimitProduct(java.util.List,java.util.List)");
        sortedAllProducts.forEach(productM -> {
            if(CollectionUtils.size(othersFilterTopOneProducts) >= RECOMMEND_LIMIT) {
                return;
            }
            if(!containsProduct(othersFilterTopOneProducts, productM.getProductId())) {
                othersFilterTopOneProducts.add(productM);
            }
        });
        return othersFilterTopOneProducts;
    }

    private static void setAllFilterTab(List<ProductM> resultProducts, Map<Long, List<ProductM>> filterMs, long filterId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.setAllFilterTab(java.util.List,java.util.Map,long)");
        if(CollectionUtils.isEmpty(resultProducts)) {
            return;
        }
        filterMs.put(filterId, resultProducts);
    }

    private static boolean containsProduct(List<ProductM> productMList, int productId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.containsProduct(java.util.List,int)");
        return productMList.stream().anyMatch(productM -> productM.getProductId() == productId);
    }

    public static int getSaleSafely(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.getSaleSafely(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(productM == null || productM.getSale() == null) {
            return 0;
        }
        return productM.getSale().getSale();
    }

    public static Comparator<ProductM> comparatorBySale() {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.comparatorBySale()");
        List<Comparator> comparators = new LinkedList<>();
        comparators.add(recommendComparator());
        comparators.add(saleDescComparator());
        return ((o1, o2) -> {
            for (Comparator comparator : comparators) {
                if (comparator.compare(o1, o2) != 0) {
                    return comparator.compare(o1, o2);
                }
            }
            return 0;
        });
    }

    //商家推荐
    private static Comparator<ProductM> recommendComparator() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.recommendComparator()");
        return ((o1, o2) -> {
            boolean attr1 = trueTag(o1.getAttr(RECOMMEND_ATTR_NAME));
            boolean attr2 = trueTag(o2.getAttr(RECOMMEND_ATTR_NAME));
            if(attr1 ^ attr2) {
                return attr1 ? -1 : 1;
            }
            return 0;
        });
    }

    //销量
    private static Comparator<ProductM> saleDescComparator() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.saleDescComparator()");
        return ((o1, o2) -> {
            if(o1.getSale() == null && o2.getSale() == null) {
                return 0;
            }
            if (o1.getSale() == null) {
                return 1;
            }
            if (o2.getSale() == null) {
                return -1;
            }
            return o2.getSale().getSale() - o1.getSale().getSale();
        });
    }

    private static boolean trueTag(String attr) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.utils.RecommendFilterUtils.trueTag(java.lang.String)");
        return StringUtils.isNotEmpty(attr) && "true".equals(attr);
    }
}
