package com.sankuai.dzviewscene.shelf.business.shelf.baby.builder;

import cn.hutool.core.map.MapUtil;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Created by 李维隆 on 2020/12/16
 */
@ExtPointInstance(name = "亲子-摄影团购货架主标题模块构造扩展点")
public class BabyPhotographyMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.baby.photo.constant.config", defaultValue = "{}")
    public MainTitleConstant mainTitleConstant;

    @Override
    public String title(ActivityContext activityContext) {
        if("baby_poi_pregnant_photography_new_deal_shelf".equals(activityContext.getSceneCode())) {
            return buildMainTitle(activityContext);
        }
        return mainTitleConstant.getTitle();
    }

    private String buildMainTitle(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyMainTitleBuilderExt.buildMainTitle(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        if(StringUtils.isEmpty(mainTitleConstant.getMainTitleFormat())) {
            return mainTitleConstant.getTitle();
        }
        int totalNum = getProductCount(activityContext.getMainData());
        if(totalNum <= 0) {
            return mainTitleConstant.getTitle();
        }
        return String.format(mainTitleConstant.getMainTitleFormat(), totalNum);
    }

    private int getProductCount(CompletableFuture<ShelfGroupM> shelfMCompletableFuture) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyMainTitleBuilderExt.getProductCount(java.util.concurrent.CompletableFuture)");
        if (shelfMCompletableFuture == null) {
            return 0;
        }
        ShelfGroupM shelfGroupM = shelfMCompletableFuture.join();
        if(shelfGroupM == null || MapUtil.isEmpty(shelfGroupM.getProductGroupMs())) {
            return 0;
        }
        return getProductCount(shelfGroupM.getProductGroupMs());
    }

    private int getProductCount(Map<String, ProductGroupM> groupName2ProductGroupM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyMainTitleBuilderExt.getProductCount(java.util.Map)");
        if (MapUtil.isEmpty(groupName2ProductGroupM)) {
            return 0;
        }
        return groupName2ProductGroupM.values().stream()
                .filter(productGroupM -> productGroupM != null && CollectionUtils.isNotEmpty(productGroupM.getProducts()))
                .mapToInt(productGroupM -> CollectionUtils.size(productGroupM.getProducts())).sum();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (PlatformUtil.isMT(platform)) return mainTitleConstant.getMtTitleIcon();
        return mainTitleConstant.getDpTitleIcon();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        IconRichLabelVO returnAnyTimeIcon = new IconRichLabelVO();
        String icon = mainTitleConstant.getTitleTagIcon();
        returnAnyTimeIcon.setIcon(icon);
        returnAnyTimeIcon.setText(new RichLabelVO("随时退"));
        IconRichLabelVO returnExpiredIcon = new IconRichLabelVO();
        returnExpiredIcon.setIcon(icon);
        returnExpiredIcon.setText(new RichLabelVO("过期退"));
        return Lists.newArrayList(returnAnyTimeIcon, returnExpiredIcon);
    }

    @Data
    private static class MainTitleConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
        private String mainTitleFormat;
    }
}
