package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.CarAndPetUnCoopCommonInfoOpt;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.common.DealRecordQueryService;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.common.DealRecordUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 汽车和宠物非合作商户推荐查询处理器
 */
@Slf4j
@Component
public class CarAndPetUnCoopShopRecommendQueryHandler extends NearestShopRecommendAbstractHandler
        implements GroupQueryHandler {

    @Resource
    private DealRecordQueryService dealRecordQueryService;

    @Resource
    private DealRecordUtils dealRecordUtils;

    private static final int CORE_POOL_SIZE = 8;
    private static final int MAX_POOL_SIZE = 20;
    public static final ThreadPool carAndPetUnCoopShopRecommendQueryPool = Rhino.newThreadPool(
            "carAndPetUnCoopShopRecommendQueryPool",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(CORE_POOL_SIZE)
                    .withMaxSize(MAX_POOL_SIZE)
                    .withRejectHandler(new ThreadPoolExecutor.CallerRunsPolicy())
    );

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityContext activityContext, String groupName,
            Map<String, Object> params) {
        CarAndPetUncoopShopShelfAttrM carAndPetUncoopShopShelfAttrM = activityContext
                .getParam(CarAndPetUnCoopCommonInfoOpt.CODE);
        if (carAndPetUncoopShopShelfAttrM == null
                || CollectionUtils.isEmpty(carAndPetUncoopShopShelfAttrM.getDataList())) {
            return CompletableFuture.completedFuture(null);
        }

        List<CompletableFuture<List<ProductM>>> futures = new ArrayList<>();

        for (Map<String, Object> item : carAndPetUncoopShopShelfAttrM.getDataList()) {
            if (MapUtils.isEmpty(item)) {
                continue;
            }
            Object value = item.get("attrValue");
            if (value instanceof String) {
                if (StringUtils.isEmpty((String)value)) {
                    continue;
                }
                String strValue = (String)value;

                // 根据配置值选择不同的排序策略
                if ("$.bean.commonDealOrderInfoHandler".equals(strValue)) {
                    // 通用排序策略
                    futures.add(dealRecordQueryService
                            .queryDealRecords(carAndPetUncoopShopShelfAttrM, activityContext,
                                    dealRecordUtils::commonMtSort, dealRecordUtils::commonDpSort, true)
                            .exceptionally(ex -> {
                                log.error("dealRecordQueryService.queryDealRecords error", ex);
                                return Collections.emptyList();
                            }));
                } else if ("$.bean.petAndCarDealOrderInfoHandler".equals(strValue)) {
                    // 宠物和汽车排序策略
                    futures.add(dealRecordQueryService
                            .queryDealRecords(carAndPetUncoopShopShelfAttrM, activityContext,
                                    dealRecordUtils::petAndCarMtSort, dealRecordUtils::petAndCarDpSort, false)
                            .exceptionally(ex -> {
                                log.error("dealRecordQueryService.queryDealRecords error", ex);
                                return Collections.emptyList();
                            }));
                }
            }
        }
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenCompose(v -> CompletableFuture.supplyAsync(() -> futures.stream().map(CompletableFuture::join)
                        .filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList()), 
                        carAndPetUnCoopShopRecommendQueryPool.getExecutor()))
                .thenApply(productMList -> {
                    if (CollectionUtils.isEmpty(productMList)) {
                        productMList = Lists.newArrayList();
                    }
                    ProductGroupM productGroupM = new ProductGroupM();
                    productGroupM.setTotal(productMList.size());
                    productGroupM.setHasNext(false);
                    productGroupM.setProducts(productMList);
                    return productGroupM;
                });
    }
}
