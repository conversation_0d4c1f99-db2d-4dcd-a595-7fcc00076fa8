package com.sankuai.dzviewscene.shelf.framework.monitor;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.framework.core.IAbility;
import com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutFuture;
import com.sankuai.dzviewscene.shelf.framework.timeout.TimeoutUtils;
import jodd.util.ObjectUtil;
import org.springframework.core.annotation.AnnotationUtils;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 能力执行监控器, 完成能力实例执行的日志和异常打点
 * <p>
 * Created by float.lu on 2020/8/24.
 */
public class AbilityProxy implements IAbility {

    private static String NAME = "能力点";

    // 被代理的原始能力实例
    private IAbility ability;

    // 开始时间
    private long abilityStart = System.currentTimeMillis();


    public AbilityProxy(IAbility ability) {
        this.ability = ability;
    }

    @Override
    public CompletableFuture build(ActivityContext ctx) {

        CompletableFuture completableFuture =  CompletableFuture.completedFuture(null);
        // 针对读场景, 统一捕获异常, 个别能力异常不影响主流程
        try {
            if (TimeoutUtils.openTimeout()) {
                completableFuture = TimeoutFuture.of(ability.build(ctx), TimeoutUtils.getRestTimeout(ctx), TimeUnit.MILLISECONDS, null);
            }else {
                completableFuture = ability.build(ctx);
            }
        } catch (Throwable e) {
            addException(e);
            doTrace(ctx, null, e);
        }

        if (ctx.isTrace()) {
            traceActivity(ctx, completableFuture);
        }

        // 容错逻辑, 如果能力内部错误, 那么做静默降级处理
        if (completableFuture == null || completableFuture.isCompletedExceptionally()) {
            completableFuture = CompletableFuture.completedFuture(null);
        }
        return completableFuture;
    }

    private void addException(Throwable throwable) {
        try{
            ExceptionTracer.addException(String.format("%s_%s", "AbilityProxy", ability.getClass().getName()), throwable);
        } catch (Exception e) {
            Cat.logError(e);
        }
    }

    private void traceActivity(ActivityContext ctx, CompletableFuture completableFuture) {
        CompletableFuture tempCompletableFuture = completableFuture;
        if (tempCompletableFuture == null) {
            tempCompletableFuture = CompletableFuture.completedFuture(null);
        }

        tempCompletableFuture.whenComplete((result, e) -> {
            doTrace(ctx, result, (Throwable) e);
        });
    }

    private void doTrace(ActivityContext ctx, Object result, Throwable throwable) {
        AbilityInstance abilityInstance = AnnotationUtils.getAnnotation(ability.getClass(), AbilityInstance.class);
        if (abilityInstance == null) {
            return; // never here...
        }

        long activityLatency =  System.currentTimeMillis() - abilityStart;
        // 1. 把trace添加到上下文, 实时定位, 需要信息量不多
        ctx.addTrace(TraceElement.build(
                NAME,
                abilityInstance.name(),
                JsonCodec.encode(ctx.getParameters()),
                cloneResult(result),
                throwable,
                activityLatency
        ));

        if (throwable != null) {
            ctx.setSuccess(false);
            Cat.logErrorWithCategory(String.format("AbilityError.%s", ctx.getSceneCode()), JsonCodec.encode(ctx), throwable);
        }
    }

    private Object cloneResult(Object result) {
        try {
            return ObjectUtil.clone(result);
        } catch (Exception e) {}
        return result;
    }

}
