package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product;

import com.sankuai.dzviewscene.shelf.framework.AbstractAbility;
import com.sankuai.dzviewscene.shelf.framework.annotation.Ability;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzProductVO;

import java.util.List;

/**
 * Created by liweilong06 on 2020/9/19.
 */
@Ability(code = ProductVoBuilder.ABILITY_PRODUCTS_CODE, name = "item区构造能力")
public abstract class ProductVoBuilder extends AbstractAbility<List<DzProductVO>, ProductBuilderVoExt> {

    // item区构造能力码
    public static final String ABILITY_PRODUCTS_CODE = "FloorBuilder";

}
