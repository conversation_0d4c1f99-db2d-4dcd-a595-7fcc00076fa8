package com.sankuai.dzviewscene.shelf.framework.monitor;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/11/3.
 */
public class DefaultActivityMonitor implements ActivityMonitor {
    /**
     * 活动Transaction打点类型
     */
    private static String TRANSACTION_TYPE = "Activity";

    /*活动请求业务指标KEY*/
    private static String ACTIVITY_REQUEST_BUSINESS_KEY_TEMPLATE = "activity_request";

    /*活动失败业务指标KEY*/
    private static String ACTIVITY_FAILED_BUSINESS_KEY_TEMPLATE = "activity_failed";


    @Override
    public CompletableFuture monitor(ActivityContext activityContext, CompletableFuture resultCompletableFuture) {
        // 1. 活动请求业务指标
        monitorRequest(activityContext);
        // 2. 活动耗时监控指标
        monitorLatency(activityContext, resultCompletableFuture);
        // 3. 活动失败监控指标
        monitorFailed(activityContext, resultCompletableFuture);
        return resultCompletableFuture;
    }

    private void monitorRequest(ActivityContext activityContext) {
        try {
            Cat.logMetricForCount(ACTIVITY_REQUEST_BUSINESS_KEY_TEMPLATE, buildMetricTags(activityContext));
        } catch (Exception e) {/*静默*/}
    }

    private Map<String, String> buildMetricTags(ActivityContext activityContext) {
        return new HashMap<String, String>() {{
            put("activity", activityContext.getActivityCode());
        }};
    }

    private void monitorLatency(ActivityContext activityContext, CompletableFuture<?> responseCompletableFuture) {
        responseCompletableFuture.thenAccept(dzShelfResponseVO -> {
            try {
                Cat.newCompletedTransactionWithDuration(TRANSACTION_TYPE, activityContext.getSceneCode(), System.currentTimeMillis() - activityContext.getStartTime());
            } catch (Exception e) {/*静默*/}
        });
    }

    private void monitorFailed(ActivityContext activityContext, CompletableFuture<?> responseCompletableFuture) {
        responseCompletableFuture.whenComplete((result, throwable) -> {
            try {
                if (!activityContext.isSuccess() || throwable != null) {
                    Cat.logMetricForCount(ACTIVITY_FAILED_BUSINESS_KEY_TEMPLATE, buildMetricTags(activityContext));
                }
                if (throwable != null) {
                    // 1. 出错打精确Error点, 输出当前上下文和异常
                    Cat.logError(JsonCodec.encode(activityContext), throwable);
                }
            } catch (Exception e) {/*静默*/}
        });
    }

}
