package com.sankuai.dzviewscene.shelf.business.other.carousel.validator;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.core.IActivityValidator;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Component
public class RolePlayPinCarouselEntranceValidator implements IActivityValidator {


    @Resource
    private CompositeAtomService compositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.roleplay.grey.config", defaultValue = "{}")
    private Map<String, String> greyConfig;

    @Override
    public boolean isValid(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.other.carousel.validator.RolePlayPinCarouselEntranceValidator.isValid(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        long dpShopId = PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);

        //如果门店在新在拼场次黑名单里则不展示
        if(checkInNewBlackList(greyConfig, dpShopId)){
            return  false;
        }

        //新拼场入口全量展示
        if(BooleanUtils.toBoolean(greyConfig.getOrDefault("openAllNewCarouselSwitch", "false"))){
            return true;
        }

        //新拼场入口全量隐藏
        if(BooleanUtils.toBoolean(greyConfig.getOrDefault("closeAllNewCarouselSwitch", "false"))){
            return false;
        }

        return compositeAtomService.getIsGreyWhiteShop(dpShopId, "jubensha").join();
    }

    private boolean checkInNewBlackList(Map<String, String> greyConfig, long dpShopId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.other.carousel.validator.RolePlayPinCarouselEntranceValidator.checkInNewBlackList(java.util.Map,long)");
        List<Long> hideNewShelfList = JsonCodec.converseList(greyConfig.get("hideNewCarouselList"), Long.class);
        return CollectionUtils.isNotEmpty(hideNewShelfList) && hideNewShelfList.contains(dpShopId);
    }

}
