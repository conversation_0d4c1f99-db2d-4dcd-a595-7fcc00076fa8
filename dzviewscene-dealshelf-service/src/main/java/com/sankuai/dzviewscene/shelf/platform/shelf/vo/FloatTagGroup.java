/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2023-11-07
 * Project        :
 * File Name      : FloatTagGroupVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.shelf.platform.shelf.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import lombok.Data;

import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2023-11-07
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FloatTagGroup {

    /**
     * 头图标签列表
     */
    private List<FloatTagVO> floatTagVOList;


    /**
     * 头图多标签排列方式
     */
    private Integer floatTagType;

    
}
