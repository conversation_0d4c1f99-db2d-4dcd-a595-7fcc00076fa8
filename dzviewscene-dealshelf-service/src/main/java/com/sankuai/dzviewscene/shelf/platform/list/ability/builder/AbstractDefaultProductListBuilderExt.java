package com.sankuai.dzviewscene.shelf.platform.list.ability.builder;

import com.dianping.cat.Cat;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzPromoDetailVO;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzPromoItemVO;
import com.sankuai.dzviewscene.shelf.platform.list.vo.PromoDetailVO;
import com.sankuai.dzviewscene.shelf.platform.list.vo.PromoItemVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @auther: liweilong06
 * @date: 2021/5/5 下午5:31
 */
public abstract class AbstractDefaultProductListBuilderExt extends ProductListBuilderExtAdapter {

    /**
     * 优惠弹窗的标题
     */
    private static final String PROMO_POP_TITLE = "优惠明细";

    /**
     * 优惠弹窗的总优惠标签
     */
    private static final String PROMO_POP_TOTAL_LAB = "共优惠：";

    @Override
    public PromoDetailVO promoDetail(ActivityContext activityContext, ProductM productM)  {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.AbstractDefaultProductListBuilderExt.promoDetail(ActivityContext,ProductM)");
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null || CollectionUtils.isEmpty(productPromoPriceM.getPromoItemList())) {
            return null;
        }
        PromoDetailVO promoDetailVO = new PromoDetailVO();
        promoDetailVO.setTitle(PROMO_POP_TITLE);
        promoDetailVO.setTotalPromoLab(PROMO_POP_TOTAL_LAB);
        promoDetailVO.setTotalPromoPrice(productPromoPriceM.getTotalPromoPriceTag());
        promoDetailVO.setPromoItems(buildPromoItems(productPromoPriceM.getPromoItemList()));
        return promoDetailVO;
    }

    private List<PromoItemVO> buildPromoItems(List<PromoItemM> promoItemList) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.list.ability.builder.AbstractDefaultProductListBuilderExt.buildPromoItems(java.util.List)");
        return promoItemList.stream().map(promoItemM -> {
            PromoItemVO promoItemVO = new PromoItemVO();
            promoItemVO.setPromoId(promoItemM.getPromoId());
            promoItemVO.setPromoType(promoItemM.getPromoTypeCode());
            promoItemVO.setTitle(promoItemM.getPromoType());
            promoItemVO.setDesc(promoItemM.getDesc());
            promoItemVO.setPromoPrice(promoItemM.getPromoTag());
            return promoItemVO;
        }).collect(Collectors.toList());
    }

}
