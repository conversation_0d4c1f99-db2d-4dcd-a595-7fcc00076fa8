package com.sankuai.dzviewscene.shelf.business.detail.edu.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 课程详情页
 * @auther: liweilong06
 * @date: 2020/9/28 1:41 上午
 */
@Data
@Builder
public class DzEduCourseVO implements Serializable {

    /**
     * 当前商户信息
     */
    private DzShopVO shopInfo;

    /**
     * 课程信息
     */
    private DzEduCourseDetailVO courseInfo;

    /**
     * 在线试听课
     */
    private DzOnlineCourseVO onlineCourse;

    /**
     * 班级信息
     */
    private List<DzEduClassVO> classInfos;

    /**
     * 分享信息
     */
    private DzShareVO shareInfo;

    /**
     * 收藏信息
     */
    private DzCollectVO collectInfo;

    /**
     * 师资信息
     */
    private DzTeacherListVO teacherList;

    /**
     * 精选点评
     */
    private DzUgcVO ugcInfo;

    /**
     * 适用门店
     */
    private DzAvailableShopVO availableShop;

    /**
     * 更多相关课程
     */
    private DzEduMoreCourseVO moreCourse;
}
