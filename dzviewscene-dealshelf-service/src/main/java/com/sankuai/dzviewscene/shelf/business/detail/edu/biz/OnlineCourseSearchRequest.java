package com.sankuai.dzviewscene.shelf.business.detail.edu.biz;

import com.dianping.beauty.zone.annotation.ItemMark;
import com.dianping.beauty.zone.annotation.ItemReferenceMark;
import com.dianping.beauty.zone.biz.dto.ItemBaseLocalRequest;
import com.dianping.beauty.zone.biz.enums.ItemType;
import com.dianping.beauty.zone.biz.enums.ReferenceEntityType;
import com.dianping.beauty.zone.biz.enums.ReferenceRelationType;
import lombok.Data;

import java.util.List;

/**
 * 新版体验课搜索请求对象
 *
 * <AUTHOR>
 * @date 2020-10-16 10:54
 */
@Data
@ItemMark(itemType= ItemType.EDU_COURSE_CLASS)
public class OnlineCourseSearchRequest extends ItemBaseLocalRequest {

    @ItemReferenceMark(entityType = ReferenceEntityType.COURSE_ID, relationType = ReferenceRelationType.OTHER)
    private List<Integer> courseIds;
}
