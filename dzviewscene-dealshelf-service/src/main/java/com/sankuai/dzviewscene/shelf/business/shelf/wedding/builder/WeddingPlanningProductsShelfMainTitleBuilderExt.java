package com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder;

import com.dianping.cat.Cat;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by 李维隆 on 2020/03/30
 */
@ExtPointInstance(name = "结婚-商详-结婚策划预付货架主标题模块构造扩展点")
public class WeddingPlanningProductsShelfMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.wedding.planning.product.constant.config", defaultValue = "{}")
    public Constant constant;

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingPlanningProductsShelfMainTitleBuilderExt.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return constant.getTitle();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingPlanningProductsShelfMainTitleBuilderExt.tags(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<IconRichLabelVO> returnValue = new ArrayList<>();
        for (String shelfTag : constant.tagNames) {
            IconRichLabelVO iconRichLabelVO = new IconRichLabelVO();
            iconRichLabelVO.setIcon(constant.getTitleTagIcon());
            iconRichLabelVO.setText(new RichLabelVO(shelfTag));
            returnValue.add(iconRichLabelVO);
        }
        return returnValue;
    }

    @Data
    private static class Constant {
        private String title;
        private String titleTagIcon;
        private List<String> tagNames;
    }
}
