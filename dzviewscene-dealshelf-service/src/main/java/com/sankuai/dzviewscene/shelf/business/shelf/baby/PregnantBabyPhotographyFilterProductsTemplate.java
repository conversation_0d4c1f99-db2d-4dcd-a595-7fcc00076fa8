package com.sankuai.dzviewscene.shelf.business.shelf.baby;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyFilterBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyMainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPhotographyOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.config.SceneTemplateConfig;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.MultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformMultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.FilterFirstShelfFlow;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 孕婴童摄影团购模板
 * Created by leimengdan on 2020/01.15
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "baby_poi_pregnant_photography_deal_shelf", name = "孕婴童摄影团购模板")
public class PregnantBabyPhotographyFilterProductsTemplate implements IActivityTemplate {

    @Resource
    private SceneTemplateConfig sceneTemplateConfig;

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.baby.PregnantBabyPhotographyFilterProductsTemplate.flow()");
        return FilterFirstShelfFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.baby.PregnantBabyPhotographyFilterProductsTemplate.extParams(java.util.Map)");
        // 1. 设置召回参数, 代表只召回一组商品, 商品是化妆品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList("团购"));

        // 2. 设置第一组商品参数
        List<String> attrKeys = buildAttrKeys();
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>(){{
            put("团购", new HashMap<String, Object>(){{
                // 2.1 设置第一组商品填充参数
                put(PaddingFetcher.Params.planId, sceneTemplateConfig.getPlanId(exParams, "团购", "10002126"));
                put("attributeKeys", attrKeys);
                put("promoTemplateId", 145);
                put("tcMergePromoTemplateId", 226);
                put("enablePreSalePromoTag", true);
                put("directPromoScene", 400005);
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
            }});
        }});

        // 3. 设置商品平台组件和商品组映射关系
        exParams.put(FilterFetcher.Params.filterComponent2Group, new HashMap<String, String>(){{
            put("团购"/*filter组件ID*/, "团购");
        }});
        exParams.put(QueryFetcher.Params.productComponent2Group, new HashMap<String, String>(){{
            put("团购"/*商品组件ID*/,  "团购");
        }});
        //4. 设置showType
        exParams.put(ShelfActivityConstants.Style.showType, 19);
        exParams.put(ShelfActivityConstants.Params.pageSize, 50);
        // 设置填充后的排序规则
        exParams.put(PaddingFetcher.QueryParam.sortId, PaddingFetcher.QueryValue.pregnantBabyPhotographySort);
        exParams.put(DouHuFetcher.Params.DP_EXP_ID, DouHuUtils.getDPDouHuExpId("baby_poi_pregnant_photography_new_deal_shelf", "searchProductTop"));
        exParams.put(DouHuFetcher.Params.MT_EXP_ID, DouHuUtils.getMTDouHuExpId("baby_poi_pregnant_photography_new_deal_shelf", "searchProductTop"));
    }

    private List<String> buildAttrKeys() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.baby.PregnantBabyPhotographyFilterProductsTemplate.buildAttrKeys()");
        return Lists.newArrayList("service_type", "ticket_usernumbers", "ticke_usertime", "preSaleTag");
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.baby.PregnantBabyPhotographyFilterProductsTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.baby.PregnantBabyPhotographyFilterProductsTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.baby.PregnantBabyPhotographyFilterProductsTemplate.extAbilities(java.util.Map)");
        // 1、筛选使用默认能力
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, ShelfFilterFetcher.class);
        // 2、查询使用默认能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, ShelfQueryFetcher.class);
        // 3. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 注册批量斗斛查询能力
        abilities.put(MultiDouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformDouHuFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.baby.PregnantBabyPhotographyFilterProductsTemplate.extPoints(java.util.Map)");
        // 1. 打点数据能力扩展点
        extPoints.put(OceanBuilderExt.SHELF_OCEAN_EXT_CODE, BabyPhotographyOceanBuilderExt.class);
        // 2. 过滤器构造器扩展点
        extPoints.put(FilterBuilderExt.EXT_POINT_FILTER_COMPONENT_CODE, BabyPhotographyFilterBuilderExt.class);
        // 3. 商品楼层构造器扩展点
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, BabyPhotographyFloorsBuilderExt.class);
        // 4. 标题扩展点
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, BabyPhotographyMainTitleBuilderExt.class);
        // 5. 筛选栏关键词扩展点
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, ConfigFilterFetcherExt.class);
    }

}
