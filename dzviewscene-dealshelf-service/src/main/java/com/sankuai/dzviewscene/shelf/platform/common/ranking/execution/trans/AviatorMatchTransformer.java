package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.function.product.*;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.MatchTransformer;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 基于Aviator规则的匹配算子实现
 *
 * Created by float.lu on 2020/11/6.
 */
public class AviatorMatchTransformer implements MatchTransformer {

    private String expression;


    static {
        AviatorEvaluator.addFunction(new AttrFunction());
        AviatorEvaluator.addFunction(new AttrLongFunction());
        AviatorEvaluator.addFunction(new PromoTypeDiscountFunction());
        AviatorEvaluator.addFunction(new ShopBooleanFunction());
    }

    public AviatorMatchTransformer(String expression) {
        this.expression = expression;
    }

    @Override
    public boolean match(RankingContext context, RankingItem item) {

        Expression expression = AviatorEvaluator.getInstance().compile(this.expression, true);

        Object result = expression.execute(buildMatchEnv(context, item));

        return convertToBooleanSafely(result);
    }

    private boolean convertToBooleanSafely(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return true;
    }

    private Map<String, Object> buildMatchEnv(RankingContext rankingContext, RankingItem item) {
        Map<String, Object> env = new HashMap<>();
        env.put("pool", rankingContext.getPool());
        env.put("matched", rankingContext.getMatched());
        env.put("params", rankingContext.getParams());
        env.put("item", item);
        return env;
    }

    @Override
    public List<RankingItem> execute(RankingContext context, List<RankingItem> items) {
        return items.stream().filter(new Predicate<RankingItem>() {
            @Override
            public boolean test(RankingItem item) {
                return match(context, item);
            }
        }).collect(Collectors.toList());
    }
}
