package com.sankuai.dzviewscene.shelf.platform.common.model;

import lombok.Data;

import java.util.List;

/**
 * 缓存的门店信息，点评侧
 */
@Data
public class ShopCacheM {

    private long shopId;

    private String shopUuid;

    /**
     * 门店类目
     */
    private int shopType;

    /**
     * 商户分类
     */
    private int category;

    /**
     * 后台类目ID集合
     */
    private List<Integer> backCategory;

    /**
     * 门店名
     */
    private String shopName;

    /**
     * 门店类型
     */
    private int useType;

    /**
     * 城市id
     */
    private int cityId;

    /**
     * 纬度
     */
    private double lat;

    /**
     * 经度
     */
    private double lng;
}
