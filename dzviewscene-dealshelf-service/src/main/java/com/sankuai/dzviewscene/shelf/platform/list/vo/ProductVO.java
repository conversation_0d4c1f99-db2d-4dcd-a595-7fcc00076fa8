package com.sankuai.dzviewscene.shelf.platform.list.vo;


import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: wangxinyuan02
 * @create: 2020-09-04 11:01
 **/
@Data
public class ProductVO {

    ///////////////////////////////基础信息///////////////////////////////

    /**
     * 商品ID
     */
    private int productId;

    /**
     * 标题
     */
    private String title;

    /**
     * 商品图片
     */
    private String picUrl;

    /**
     * 跳转链接
     */
    private String detailUrl;

    ///////////////////////////////价格///////////////////////////////

    /**
     * 售卖价格, 需定制
     */
    private String price;

    /**
     * 市场价格
     */
    private String marketPrice;

    /**
     * 会员价格
     */
    private String vipPrice;

    ///////////////////////////////库存///////////////////////////////

    /**
     * 销量
     */
    private ProductSaleVO sale;


    ///////////////////////////////营销///////////////////////////////

    /**
     * 优惠信息 如"已减20"，"领券再减20"
     */
    private String promoTag;

    /**
     * 优惠细节
     */
    private PromoDetailVO promoDetail;

    /**
     * 活动标签 如"618活动"
     */
    private String activityTag;

    /**
     * 折扣信息 如"2.3折"
     */
    private String discountTag;

    /**
     * 购买返券标签
     */
    private String couponTag;

    ///////////////////////////////交易///////////////////////////////

    /**
     * 购买信息 如"1小时内购买过"，"最早今日可订"
     */
    private String purchase;

    ///////////////////////////////扩展描述///////////////////////////////

    /**
     * 商品分类信息 如"【足疗/按摩】"，"【烫发】"
     */
    private String categoryTag;

    /**
     * 商品标签 如"80分钟"。"手法"
     */
    private List<String> productTags;

    /**
     * 商品关联商户信息
     */
    private ShopVO shopVO;

    /**
     * 扩展字段
     */
    private List<AttrVO> attrs;

}
