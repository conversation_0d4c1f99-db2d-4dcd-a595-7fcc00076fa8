package com.sankuai.dzviewscene.shelf.business.shelf.medical.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * Created by 李维隆 on 2020/11/10
 */
@ExtPointInstance(name = "丽人-瑜伽舞蹈体验课货架楼层模块构造扩展点")
public class YogaDanceTrialCourseFloorsBuilderExt extends AbstractFloorsBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.yoga.constant.config", defaultValue = "{}")
    private FloorConstant floorConstant;

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.floorDefaultShowNum(ActivityContext,String,List)");
        return floorConstant.defaultShowProductCount;
    }

    @Override
    public List<DzTagVO> itemComponentPriceBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.itemComponentPriceBottomTags(ActivityContext,String,ProductM)");
        if(productM == null) {
            return null;
        }
        //玩美季弹窗底部标签
        DzTagVO perfectDzTagVO = PerfectActivityBuildUtils.buildPerfectDzTagVOs(productM);
        if(perfectDzTagVO != null) {
            return Lists.newArrayList(perfectDzTagVO);
        };
        return super.itemComponentPriceBottomTags(activityContext, groupName, productM);
    }

    @Override
    public RichLabelVO activityRemainSecondsLabel(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.activityRemainSecondsLabel(ActivityContext,String,ProductM)");
        return PerfectActivityBuildUtils.buildActivityRemainSecondsLabel(productM);
    }

    @Override
    public FloatTagVO itemComponentPreTitleTag(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.itemComponentPreTitleTag(ActivityContext,String,ProductM)");
        if(StringUtils.isNotEmpty(productM.getPicUrl())) {
            return null;
        }
        return CollectUtils.firstValue(PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM));
    }

    @Override
    public List<RichLabelVO> itemComponentBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.itemComponentBottomTags(ActivityContext,String,ProductM)");
        RichLabelVO card = buildRichLabel(productM.getCardPrice());
        RichLabelVO pin = buildRichLabel(productM.getPinPrice());
        List<RichLabelVO> richLabelVOS = new ArrayList<>();
        if (card != null) richLabelVOS.add(card);
        if (pin != null) richLabelVOS.add(pin);
        return richLabelVOS;
    }

    private RichLabelVO buildRichLabel(ProductPriceM productPriceM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.buildRichLabel(com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM)");
        if (productPriceM == null) return null;
        RichLabelVO richLabelVO = new RichLabelVO();
        String[] priceDesc = productPriceM.getPriceTag().split("/");
        if (priceDesc.length < 2) {
            List<RichLabel> richLabelList = Lists.newArrayList(
                    new RichLabel(productPriceM.getPriceDesc(), floorConstant.color777777, floorConstant.front11),
                    new RichLabel(productPriceM.getPriceTag(), floorConstant.colorFF6633, floorConstant.front11, floorConstant.boldFontWeight));
            richLabelVO.setText(JsonCodec.encode(richLabelList));
            return richLabelVO;
        }
        List<RichLabel> richLabelList = Lists.newArrayList(
                new RichLabel(productPriceM.getPriceDesc(), floorConstant.color777777, floorConstant.front11),
                new RichLabel(priceDesc[0], floorConstant.colorFF6633, floorConstant.front11, floorConstant.boldFontWeight),
                new RichLabel("/" + priceDesc[1], floorConstant.colorFF6633, floorConstant.front11));
        richLabelVO.setText(JsonCodec.encode(richLabelList));
        return richLabelVO;
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.itemComponentPic(ActivityContext,String,ProductM)");
        if (StringUtils.isEmpty(productM.getPicUrl())) {
            return null;
        }
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setAspectRadio(floorConstant.getHeaderPicAspectRadio());
        pic.setPicUrl(PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), floorConstant.getPicWidth(), floorConstant.getPicHeight(), PictureURLBuilders.ScaleType.Cut));
        PicAreaVO picAreaVO = new PicAreaVO();
        picAreaVO.setPic(pic);
        if(PerfectActivityBuildUtils.isShowPerfectActivity(productM)) {
            picAreaVO.setFloatTags(PerfectActivityBuildUtils.buildHeadPicFloatTagVO(productM));
        }
        return picAreaVO;
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, DzItemAreaComponentVO itemAreaComponentVO, long filterId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.moreComponentText(ActivityContext,String,DzItemAreaComponentVO,long)");
        if (itemAreaComponentVO.getProductItems().size() <= floorConstant.getDefaultShowProductCount()) return null;
        return floorConstant.getMorePrefix() + (itemAreaComponentVO.getProductItems().size() - itemAreaComponentVO.getDefaultShowNum()) + floorConstant.getMoreSuffix();
    }

    @Override
    public List<String> itemComponentProductTags(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.itemComponentProductTags(ActivityContext,String,ProductM,long)");
        return productM.getProductTags();
    }

    @Override
    public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM, long filterId) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.YogaDanceTrialCourseFloorsBuilderExt.itemComponentSalePrice(ActivityContext,String,ProductM,long)");
        String perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPrice(productM);
        if (PerfectActivityBuildUtils.isDuringPerfectActivity(productM) && StringUtils.isNotEmpty(perfectActivityPrice)) {
            return perfectActivityPrice;
        }
        return super.itemComponentSalePrice(activityContext, groupName, productM);
    }

}
