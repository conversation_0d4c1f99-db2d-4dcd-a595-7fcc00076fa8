package com.sankuai.dzviewscene.shelf.platform.detail.vo.roleplay;

import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.detail.vo.backroom.BackroomDetailThemeBaseVO;
import com.sankuai.dzviewscene.shelf.platform.list.vo.AttrVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @title 剧本杀标品详情页
 * @package com.sankuai.dzviewscene.shelf.platform.detail.vo.roleplay
 * @date 2021/6/7
 */
@Data
public class RolePlayDetailVO implements Serializable{

    /**
     * 基础信息
     */
    private BackroomDetailThemeBaseVO rolePlayBaseVo;

    /**
     * 图片列表
     */
    private List<String> picList;

    /**
     * 相关分类。name=显示分类值，jumpUrl=跳转到筛选页的url，带上选中的筛选值。
     */
    private List<DzSimpleButtonVO> tagList;

    /**
     * 编辑推荐
     */
    private List<CommentVO> commentList;

    /**
     * 收藏状态，1为已收藏，0为未收藏
     */
    private int collectStatus;
}
