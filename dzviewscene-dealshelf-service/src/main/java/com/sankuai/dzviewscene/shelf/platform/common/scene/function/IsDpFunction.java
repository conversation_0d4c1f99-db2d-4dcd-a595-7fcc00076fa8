package com.sankuai.dzviewscene.shelf.platform.common.scene.function;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;
/**
 * <AUTHOR>
 * is_dp() true 代表点评侧
 */

@Component
public class IsDpFunction extends AbstractFunction {
    @Override
    public String getName() {
        return "is_dp";
    }

    @Override
    public AviatorObject call(Map<String, Object> env) {

        int platform = Optional.ofNullable((Integer) env.get(ShelfActivityConstants.Params.platform)).orElse(1);
        if (!PlatformUtil.isMT(platform)) {
            return AviatorRuntimeJavaType.valueOf(true);
        }
        return AviatorRuntimeJavaType.valueOf(false);
    }
}
