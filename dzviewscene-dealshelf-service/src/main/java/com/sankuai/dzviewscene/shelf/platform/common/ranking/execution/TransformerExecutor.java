package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.GroupByTransformer;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.Transformer;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by float.lu on 2020/11/30.
 */
public class TransformerExecutor {

    public static List<RankingItem> executeSafely(Transformer transformer, RankingContext rankingContext, List<RankingItem> rankingItems) {
        try {
            return transformer.execute(rankingContext, rankingItems);
        } catch (Throwable e) {
            Cat.logErrorWithCategory(String.format("TransformerExecutor#executeSafely.%s", rankingContext.getRankId()), e);
        }
        return rankingItems;
    }

    public static Map<String, List<RankingItem>> executeGroupBy(GroupByTransformer transformer, RankingContext rankingContext, List<RankingItem> rankingItems) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.TransformerExecutor.executeGroupBy(GroupByTransformer,RankingContext,List)");
        try {
            return transformer.groupBy(rankingContext, rankingItems);
        } catch (Throwable e) {
            Cat.logErrorWithCategory(String.format("TransformerExecutor#executeGroupBy%s", rankingContext.getRankId()), e);
        }
        return new HashMap<String, List<RankingItem>>() {{
            put("-", rankingItems);
        }};
    }

}
