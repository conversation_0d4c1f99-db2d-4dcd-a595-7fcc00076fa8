package com.sankuai.dzviewscene.shelf.platform.common.ranking.function;

import com.dianping.cat.Cat;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorNumber;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: wangxinyuan02
 * @create: 2020-12-07 18:35
 **/
public class ExpStringAscFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ranking.function.ExpStringAscFunction.call(java.util.Map,com.googlecode.aviator.runtime.type.AviatorObject)");
        String functionExpression = FunctionUtils.getStringValue(arg1, env);
        Expression expression = AviatorEvaluator.getInstance().compile(functionExpression, true);
        Number item1Number = getNumberFromString(env, expression, "item1");
        Number item2Number = getNumberFromString(env, expression, "item2");
        return AviatorNumber.valueOf(item1Number.doubleValue() - item2Number.doubleValue());
    }


    private Map<String, Object> buildItemEnv(Object item) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ranking.function.ExpStringAscFunction.buildItemEnv(java.lang.Object)");
        Map<String, Object> env = new HashMap<>();
        env.put("item", item);
        return env;
    }

    private Number getNumberFromString(Map<String, Object> env, Expression expression, String item) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ranking.function.ExpStringAscFunction.getNumberFromString(java.util.Map,com.googlecode.aviator.Expression,java.lang.String)");
        Object itemStr = expression.execute(buildItemEnv(env.get(item)));
        return NumberUtils.objToInt(itemStr);
    }

    @Override
    public String getName() {
        return "exp_str_asc";
    }
}
