package com.sankuai.dzviewscene.shelf.business.list.life;

import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.list.life.ability.LifeReviewQueryProductListQueryFetcher;
import com.sankuai.dzviewscene.shelf.business.list.life.builder.LifeReviewProductListBuilderVoExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivity;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.flow.FilterListOnlyProductsFlow;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/23 8:06 上午
 */
@ActivityTemplate(activityCode = FilterListActivity.ACTIVITY_FILTER_LIST_CODE, sceneCode = "life_review_query_product_list", name = "生活服务_评价列表/详情页_商品列表")
public class LifeReviewQueryProductListTemplate implements IActivityTemplate  {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.list.life.LifeReviewQueryProductListTemplate.flow()");
        return FilterListOnlyProductsFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.list.life.LifeReviewQueryProductListTemplate.extParams(java.util.Map)");
        // 1. 设置召回参数, 代表只召回一组商品, 商品是化妆品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.deal.name()));

        // 2. 设置第一组商品参数,
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.deal.name(), new HashMap<String, Object>() {{
                // 2.1 配置召回策略
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.shopOnLineDealsQuery.name());
                // 2.2 配置填充策略
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                // 2.3 设置第一组商品填充参数
                put(PaddingFetcher.Params.planId, "10002197");
            }});
        }});

    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.list.life.LifeReviewQueryProductListTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.list.life.LifeReviewQueryProductListTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.list.life.LifeReviewQueryProductListTemplate.extAbilities(java.util.Map)");
        // 1. 配置召回能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, LifeReviewQueryProductListQueryFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.list.life.LifeReviewQueryProductListTemplate.extPoints(java.util.Map)");
        extPoints.put(ProductBuilderVoExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, LifeReviewProductListBuilderVoExt.class);
    }
}
