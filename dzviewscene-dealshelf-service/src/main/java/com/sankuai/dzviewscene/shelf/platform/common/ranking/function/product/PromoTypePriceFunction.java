package com.sankuai.dzviewscene.shelf.platform.common.ranking.function.product;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;

import java.util.Map;

/**
 * @description:
 * @author: zhaoyu58
 * @create: 2020-11-16 14:34
 **/
public class PromoTypePriceFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Number promoType = FunctionUtils.getNumberValue(arg1, env);
        ProductM productM = (ProductM) env.get("item");
        ProductPromoPriceM promoPriceM = productM.getPromo(promoType.intValue());
        if (promoPriceM == null || promoPriceM.getPromoPrice() == null) {
            return AviatorRuntimeJavaType.valueOf(Integer.MAX_VALUE);
        }
        return AviatorRuntimeJavaType.valueOf(promoPriceM.getPromoPrice());
    }

    @Override
    public String getName() {
        return "promoTypePrice";
    }
}
