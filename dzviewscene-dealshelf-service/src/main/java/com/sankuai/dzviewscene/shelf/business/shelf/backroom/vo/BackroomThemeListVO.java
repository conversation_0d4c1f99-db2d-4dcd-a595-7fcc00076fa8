package com.sankuai.dzviewscene.shelf.business.shelf.backroom.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfCardBarVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.cardbar.CardBarBuilder;
import lombok.Data;

import java.util.List;

/**
 * Title: BackroomThemeListVO
 * Description: 密室主题列表接口返回模型
 *
 * <AUTHOR>
 * @date 2020-09-16
 */
@Data
@MobileDo(id = 0x934d)
public class BackroomThemeListVO {

    /**
     * 图片ICON
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * card bar
     */
    @MobileDo.MobileField(key = 0xcfe0)
    private DzShelfCardBarVO cardBar;

    /**
     * 密室主题列表
     */
    @MobileDo.MobileField(key = 0x8f02)
    private List<BackroomThemeVO> themeList;

    /**
     * 商家持卡类型，1:持有折扣卡，2:持有玩乐卡，3:都持有,  4:都不持有
     */
    @MobileDo.MobileField(key = 0xb3b4)
    private int cardTypeOcean;

    /**
     * 用户持卡类型，1:持有折扣卡，2:持有玩乐卡，3:都持有,  4:都不持有
     */
    @MobileDo.MobileField(key = 0x3265)
    private int memberProfileOcean;

    /**
     * 前台类目
     */
    @MobileDo.MobileField(key = 0x33fe)
    private int categoryId;

    /**
     * 展示样式，默认为1
     */
    @MobileDo.MobileField(key = 0xfc19)
    private int showType = 1;

    /**
     * 斗斛分流信息
     */
    @MobileDo.MobileField(key = 0x6741)
    private String abTest;
}
