package com.sankuai.dzviewscene.shelf.business.detail.baby.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.shelf.platform.detail.vo.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ability.builder.ProductDetailBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

/**
 * Created by zhaizhui on 2020/12/8
 */
@ExtPointInstance(name = "亲子爆款图案详情页扩展实现")
public class BabyHotDealDetailBuilderExt implements ProductDetailBuilderExt {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.baby.url.config", defaultValue = "")
    private UrlConfig urlConfig;

    @Override
    public Object buildDetail(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDetail(ActivityContext,ProductM)");
        if (productM == null) {
            return null;
        }
        DzDetailVO.DzDetailVOBuilder builder = DzDetailVO.builder();
        DzDetailVO dealDetailVO = builder
                .base(buildDealBaseVO(productM))
                .detailInfo(buildDealDetailInfoVO(productM))
                .activity(buildDealActivityVO(productM))
                .buyRule(buildDealBuyRuleVO(productM))
                .price(buildDzPriceVO(productM, activityContext))
                .availableShop(buildDealAvailableShopVO(productM, activityContext))
                .build();
        return dealDetailVO;
    }

    private DzPriceVO buildDzPriceVO(ProductM productM, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDzPriceVO(ProductM,ActivityContext)");
        DzPriceVO dzPriceVO = new DzPriceVO();
        dzPriceVO.setDealGroupPrice(productM.getMarketPrice());
        if (CollectionUtils.isNotEmpty(productM.getPromoPrices()) && productM.getPromoPrice(0) != null && productM.getPromo(0).getPromoPrice() != null) {
            dzPriceVO.setSalePrice(productM.getPromo(0).getPromoPrice().stripTrailingZeros().toPlainString());
            dzPriceVO.setPromoTag(productM.getPromo(0).getDiscount().stripTrailingZeros().toPlainString() + "折");
        } else {
            dzPriceVO.setSalePrice(productM.getBasePriceTag());
            if (CollectionUtils.isNotEmpty(productM.getPromoPrices()) && productM.getPromoPrice(7) != null && productM.getPromoPrice(7).getDiscount() != null) {
                dzPriceVO.setPromoTag(productM.getPromoPrice(7).getDiscount().stripTrailingZeros().toPlainString() + "折");
            }
        }
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        if (CollectionUtils.isNotEmpty(productM.getShopMs())) {
            if (PlatformUtil.isMT(platform)) {
                dzPriceVO.setPurchaseUrl(String.format(urlConfig.getMtCreateOrderUrl(), productM.getProductId(), productM.getShopMs().get(0).getLongShopId()));
            } else {
                if (CollectionUtils.isNotEmpty(productM.getShopMs())) {
                    dzPriceVO.setPurchaseUrl(String.format(urlConfig.getDpCreateOrderUrl(), productM.getProductId(), productM.getShopMs().get(0).getLongShopId(), productM.getShopMs().get(0).getShopUuid()));
                }
            }
        }
        return dzPriceVO;
    }

    private DzAvailableShopVO buildDealAvailableShopVO(ProductM productM, ActivityContext context) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDealAvailableShopVO(ProductM,ActivityContext)");
        if (CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return null;
        }
        ShopM shopM = productM.getShopMs().get(0);
        DzAvailableShopVO dealAvailableShopVO = new DzAvailableShopVO();
        dealAvailableShopVO.setTotalNum(NumberUtils.toInt(productM.getAttr("nearestShopNum")) + NumberUtils.toInt(productM.getAttr("babyHotDealRelatedDealNum")));
        int platform = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        if (PlatformUtil.isMT(platform)) {
            dealAvailableShopVO.setMoreJumpUrl(String.format(urlConfig.getMtAppMoreShopUrl(), productM.getProductId()));
        } else {
            dealAvailableShopVO.setMoreJumpUrl(String.format(urlConfig.getDpAppMoreShopUrl(), productM.getProductId(), shopM.getLongShopId(), shopM.getShopUuid()));
        }
        List<DzShopVO> shops = Lists.newArrayList();
        DzShopVO.DzShopVOBuilder builder = DzShopVO.builder();
        DzShopVO dzShopVO = builder.address(shopM.getAddress())
                .shopId(PoiIdUtil.getShopIdL(shopM))
                .shopName(shopM.getShopName())
                .distance(shopM.getDistance())
                .mapJumpUrl(buildShopMapUrl(productM, context))
                .detailUrl(buildShopUrl(productM, context))
                .build();
        shops.add(dzShopVO);
        dealAvailableShopVO.setShops(shops);
        return dealAvailableShopVO;
    }

    private String buildShopUrl(ProductM productM, ActivityContext context) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildShopUrl(ProductM,ActivityContext)");
        if (CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return null;
        }
        int platform = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        if (PlatformUtil.isMT(platform)) {
            return String.format(urlConfig.mtAppShopUrl, productM.getShopMs().get(0).getLongShopId());
        }
        return String.format(urlConfig.dpAppShopUrl, productM.getShopMs().get(0).getLongShopId(), productM.getShopMs().get(0).getShopUuid());
    }

    private String buildShopMapUrl(ProductM productM, ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildShopMapUrl(ProductM,ActivityContext)");
        if (CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return null;
        }
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        if (PlatformUtil.isMT(platform)) {
            double lat = activityContext.getParam(ShelfActivityConstants.Params.lat);
            double lng = activityContext.getParam(ShelfActivityConstants.Params.lng);
            long shopId = PoiIdUtil.getShopIdL(productM.getShopMs().get(0));
            return String.format(urlConfig.getMtShopMapUrl(), shopId, lat, lng);
        }
        return buildMapJumpUrlForDp(activityContext, productM);
    }

    private String buildMapJumpUrlForDp(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildMapJumpUrlForDp(ActivityContext,ProductM)");
        if (CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null) {
            return null;
        }
        int dpCityId = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        ShopM shopM = productM.getShopMs().get(0);
        double lat = activityContext.getParam(ShelfActivityConstants.Params.lat);
        double lng = activityContext.getParam(ShelfActivityConstants.Params.lng);
        StringBuilder addressBuilder = new StringBuilder("dianping://picassobox?");
        addressBuilder
                .append("picassoid=").append("picasso_shopmap/RouteVC-bundle.js")
                .append("&notitlebar=").append(true)
                .append("&disableslideback=").append(true)
                .append("&destlat=").append(shopM.getLat())
                .append("&destlng=").append(shopM.getLng())
                .append("&sourcelat=").append(lat)
                .append("&sourcelng=").append(lng)
                .append("&cityid=").append(dpCityId)
                .append("&source=").append("com.sankuai.dzviewscene.productshelf")
                .append("&shopaddress=").append(encode(shopM.getAddress()))
                .append("&shopuuid=").append(shopM.getShopUuid())
                .append("&shopid=").append(shopM.getLongShopId());
        return addressBuilder.toString();
    }

    private String encode(String urlParam) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.encode(java.lang.String)");
        if (StringUtils.isBlank(urlParam)) {
            return null;
        }
        try {
            return URLEncoder.encode(urlParam, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            return "";
        }
    }

    private DzBaseVO buildDealBaseVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDealBaseVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        DzBaseVO dealBaseVO = new DzBaseVO();
        dealBaseVO.setDealId(productM.getProductId());
        dealBaseVO.setTitle(productM.getTitle());
        dealBaseVO.setAutoRefund("随时退".equals(productM.getAttr("autoRefundTag")));
        dealBaseVO.setOverdueAutoRefund("过期退".equals(productM.getAttr("overdueAutoRefundTag")));
        dealBaseVO.setPics(Lists.newArrayList(buildDzImgVO(productM.getPicUrl())));
        dealBaseVO.setDesc(productM.getAttr("babyHotSaleDealDesc"));
        dealBaseVO.setSaleStr(getSale(productM));
        return dealBaseVO;
    }

    private String getSale(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.getSale(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if (CollectionUtils.isEmpty(productM.getShopMs()) || productM.getShopMs().get(0) == null
                || productM.getShopMs().get(0).getSale() == null || productM.getShopMs().get(0).getSale().getSale() < 30) {
            return null;
        }
        return productM.getShopMs().get(0).getSale().getSaleTag();
    }

    private DzBuyRuleVO buildDealBuyRuleVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDealBuyRuleVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if (StringUtils.isBlank(productM.getAttr("dealBuyRule"))) {
            return null;
        }
        DzBuyRuleVO dealBuyRuleVO = new DzBuyRuleVO();
        dealBuyRuleVO.setContent(productM.getAttr("dealBuyRule"));
        return dealBuyRuleVO;
    }


    private DzDetailInfoVO buildDealDetailInfoVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDealDetailInfoVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        String babyDetailDescPicStr = productM.getAttr("babyDetailDescPic");
        if (StringUtils.isBlank(babyDetailDescPicStr)) {
            return null;
        }
        String[] picUrls = babyDetailDescPicStr.split(",");
        if (picUrls == null || picUrls.length <= 0) {
            return null;
        }
        DzDetailInfoVO dealDetailInfoVO = new DzDetailInfoVO();
        List<DzImgVO> picList = Lists.newArrayList();
        for (String picUrl : picUrls) {
            picList.add(buildDzImgVO(picUrl));
        }
        dealDetailInfoVO.setDealDesc(picList);
        return dealDetailInfoVO;
    }

    private DzActivityVO buildDealActivityVO(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDealActivityVO(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if (CollectionUtils.isEmpty(productM.getActivities())) {
            return null;
        }
        ProductActivityM productActivityM = productM.getActivities().get(0);
        if (productActivityM == null || productActivityM.getRemainingTime() <= 0) {
            return null;
        }
        DzActivityVO dzActivityVO = new DzActivityVO();
        dzActivityVO.setRemainingTime(productActivityM.getRemainingTime());
        return dzActivityVO;
    }

    private DzImgVO buildDzImgVO(String picUrl) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.baby.builder.BabyHotDealDetailBuilderExt.buildDzImgVO(java.lang.String)");
        DzImgVO dzImgVO = new DzImgVO();
        dzImgVO.setUrl(picUrl);
        return dzImgVO;
    }

    @Data
    static class UrlConfig {

        /**
         * 点评下单页url native
         */
        private String dpCreateOrderUrl;

        /**
         * 美团下单页url native
         */
        private String mtCreateOrderUrl;

        /**
         * 点评app 更多商户url
         */
        private String dpAppMoreShopUrl;

        /**
         * 美团app 更多商户url
         */
        private String mtAppMoreShopUrl;

        /**
         * 点评m站 更多商户url http://m.dianping.com/tuan/shoplist/621632010
         */
        private String dpMMoreShopUrl;

        /**
         * 美团i站 更多商户url https://i.meituan.com/deal/branch/621632010
         */
        private String mtIMoreShopUrl;

        /**
         * 美团app 商户map url imeituan://www.meituan.com/mapchannel/poi/detail?mapsource=gc&overseas=0&coordtype=0&poi_id=**********&latitude=39.995834861883644&longitude=116.48383182897408
         */
        private String mtShopMapUrl;

        /**
         * 美团app 商详页url
         */
        private String mtAppShopUrl;

        /**
         * 点评url 商详页url
         */
        private String dpAppShopUrl;
    }
}
