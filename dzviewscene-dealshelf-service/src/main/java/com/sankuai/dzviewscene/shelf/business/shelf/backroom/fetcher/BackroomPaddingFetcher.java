package com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher;

import com.dianping.cat.Cat;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.DateUtils;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dztheme.generalproduct.GeneralProductItemDTO;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.*;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.InactiveRolePlayProductBusinessTimeContext;
import com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayVirtualDPUserIdContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 这里的Product存的其实是SKU的数据
 * Created by float.lu on 2020/9/16.
 */
@AbilityInstance(name = "密室预订货架填充能力")
public class BackroomPaddingFetcher extends PaddingFetcher {

    private static final String planId = "10100013";

    /**
     * 新版价格服务调用货架场景枚举
     */
    private static final int SHELF_LIST = 200000;

    /**
     * 泛拼场来源标识
     */
    private static final String GENERAL_PIN_SOURCE = "generalpin";

    /**
     * 门店的营业结束时间和开始时间
     */
    private static final String PRODUCT_BUSINESS_BEGIN_TIME_ATTR = "saleBeginTime";

    private static final String PRODUCT_BUSINESS_END_TIME_ATTR = "saleEndTime";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.build(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<String> groupNames = ctx.getParam(QueryFetcher.Params.groupNames);
        Date selectDate = Optional.ofNullable((Date) ctx.getParam(ShelfActivityConstants.Params.selectDate)).orElse(getToday());
        // 调用商品查询能力前，已经调用其他能力构造了召回商品id数据的future（这个future中的ProductGroupM只有商品id）
        CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture = ctx.getAttachment(PaddingFetcher.Attachments.productGroups);

        // 使用商品id或城市、日期等一系列条件去查询商品信息
        GeneralProductRequest request = buildGeneralProductRequest(ctx);
        CompletableFuture<GeneralProductResult> generalProductResultFuture = compositeAtomService.queryGeneralProductTheme(request);
        // 查看请求来源，如果是泛拼场请求需要过滤拼场商品
        Map<String, Object> extraMap = JsonCodec.decode((String) ctx.getParam(ShelfActivityConstants.Params.extra), new TypeReference<Map<String, Object>>() {
        });
        String extraSource = Optional.ofNullable(extraMap).map(map -> (String) map.get("source")).orElse(StringUtils.EMPTY);
        // 构建中间对象ProductGroupM，用于后续统一的拼装
        return CompletableFuture.allOf(generalProductResultFuture, productGroupsCompletableFuture)
                .thenApply(aVoid -> buildResultMap(generalProductResultFuture, productGroupsCompletableFuture, groupNames, selectDate, extraSource));
    }



    private Map<String, ProductGroupM> buildResultMap(CompletableFuture<GeneralProductResult> generalProductResultFuture,
                                                      CompletableFuture<Map<String, ProductGroupM>> productGroupsCompletableFuture,
                                                      List<String> groupNames, Date selectDate, String extraSource) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildResultMap(CompletableFuture,CompletableFuture,List,Date,String)");
        Map<String, ProductGroupM> resultMap = Maps.newHashMap();
        GeneralProductResult result = generalProductResultFuture.join();
        groupNames.forEach(groupName -> resultMap.put(groupName, buildProductGroupM(result, selectDate, extraSource)));
        productGroupsCompletableFuture.thenAccept(a -> a.putAll(resultMap));
        return resultMap;
    }

    private ProductGroupM buildProductGroupM(GeneralProductResult result, Date selectDate, String extraSource) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildProductGroupM(com.sankuai.dztheme.generalproduct.res.GeneralProductResult,java.util.Date,java.lang.String)");
        if (result == null || CollectionUtils.isEmpty(result.getProducts())) {
            return null;
        }
        LocalDateTime reservationBeginTime = getReservationBeginTime(result);
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setTotal(result.getProducts().size());
        productGroupM.setProducts(buildProducts(result.getProducts(), reservationBeginTime, selectDate, extraSource));
        return productGroupM;
    }

    private List<ProductM> buildProducts(List<GeneralProductDTO> productDTOS, LocalDateTime reservationBeginTime, Date selectDate, String extraSource) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildProducts(java.util.List,java.time.LocalDateTime,java.util.Date,java.lang.String)");
        if (CollectionUtils.isEmpty(productDTOS)) {
            return null;
        }
        GeneralProductDTO productDTO = productDTOS.get(0);
        //密室用SKU模型来进行填充ProductM对象
        List<GeneralProductItemDTO> productItemDTOs = productDTO.getProductItems();
        if (CollectionUtils.isEmpty(productItemDTOs)) {
            return null;
        }
        return productItemDTOs.stream().map(item -> convert2ProductM(item, productDTO)).filter(item -> filterPinItem(item, extraSource)).filter(product -> filter(product, selectDate, reservationBeginTime)).sorted(periodComparator).sorted(canBookComparator).collect(Collectors.toList());
    }

    /**
     * 如果是泛拼场请求，则过滤掉不可拼场的商品
     *
     * @param productM
     * @return
     */
    private boolean filterPinItem(ProductM productM, String extraSource) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.filterPinItem(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM,java.lang.String)");
        //不是泛拼场请求则不过滤
        if (!GENERAL_PIN_SOURCE.equals(extraSource)) {
            return true;
        }
        // 获取
        String backroomPin = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_PIN.getKey());
        String rolePlayPin = productM.getAttr("skuCanPinPool");
        return StringUtils.isNotEmpty(backroomPin) || StringUtils.isNotEmpty(rolePlayPin);
    }

    /**
     * 过滤掉当前时间已经不可订的商品
     *
     * @param productM
     * @return
     */
    private boolean filter(ProductM productM, Date selectDate, LocalDateTime reservationBeginTime) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.filter(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM,java.util.Date,java.time.LocalDateTime)");
        if (productM == null) {
            return false;
        }
        // 获取时间段起始时间对应的时间LocalDateTime，用于后续比较
        String period = productM.getTitle();
        LocalDateTime localDateTime = getTimeByPeriodAndDate(period, selectDate);
        if (reservationBeginTime.isAfter(localDateTime)) {
            return false;
        }
        return true;
    }

    private LocalDateTime getTimeByPeriodAndDate(String period, Date selectDate) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.getTimeByPeriodAndDate(java.lang.String,java.util.Date)");
        LocalDate localDate = getLocalDate(selectDate);
        LocalTime localTime = LocalTime.parse(getTimeString(period));
        if (period.startsWith("次日")) {
            localDate = localDate.plusDays(1);
        }
        LocalDateTime result = LocalDateTime.of(localDate, localTime);
        return result;
    }

    private String getTimeString(String period) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.getTimeString(java.lang.String)");
        if (period.startsWith("次日")) {
            return period.substring(2, 7);
        }
        return period.substring(0, 5);
    }

    private ProductM convert2ProductM(GeneralProductItemDTO itemDTO, GeneralProductDTO generalProductDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.convert2ProductM(GeneralProductItemDTO,GeneralProductDTO)");
        ProductM productM = new ProductM();
        productM.setProductId(itemDTO.getProductItemId());
        productM.setTitle(itemDTO.getPeriod());
        productM.setBasePriceTag(itemDTO.getPrice());
        productM.setJumpUrl(itemDTO.getActionUrl());
        productM.setAvailable(itemDTO.getCanBook());
        productM.setExtAttrs(itemDTO.getAttrs().stream().map(this::convert2AttrM).collect(Collectors.toList()));
        productM.getExtAttrs().addAll(
                Lists.newArrayList(new AttrM("cardIcon", itemDTO.getCardIcon()),
                        new AttrM("promoTag", itemDTO.getPromoTag()),
                        new AttrM("actionText", itemDTO.getActionText()),
                        new AttrM("productItemId", transferAttrValue(itemDTO.getProductItemId())),
                        new AttrM("attr_backroom_max_num", generalProductDTO.getAttr("attr_backroom_max_num", "0"))
                )
        );
        if (CollectionUtils.isNotEmpty(generalProductDTO.getPinPools())) {
            productM.setPinPools(generalProductDTO.getPinPools().stream().filter(pinPoolDTO -> itemDTO.getProductItemId() == pinPoolDTO.getProductItemId()).map(this::buildPinPool).collect(Collectors.toList()));
        }
        return productM;
    }

    private ProductPinPoolM buildPinPool(GeneralProductPinPoolDTO pinPoolDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildPinPool(com.sankuai.dztheme.generalproduct.res.GeneralProductPinPoolDTO)");
        ProductPinPoolM productPinPoolM = new ProductPinPoolM();
        productPinPoolM.setMaxNum(pinPoolDTO.getMaxNum());
        productPinPoolM.setMinNum(pinPoolDTO.getMinNum());
        productPinPoolM.setCurrentNum(pinPoolDTO.getCurrentNum());
        productPinPoolM.setDescription(pinPoolDTO.getDescription());
        productPinPoolM.setInPool(pinPoolDTO.isInPool());
        productPinPoolM.setPrice(pinPoolDTO.getPrice());
        productPinPoolM.setJumpUrl(pinPoolDTO.getJumpUrl());
        productPinPoolM.setPeriod(pinPoolDTO.getPeriod());
        productPinPoolM.setPoolStatus(pinPoolDTO.getPoolStatus());
        productPinPoolM.setPoolType(pinPoolDTO.getPoolType());
        productPinPoolM.setCanJoin(pinPoolDTO.isCanJoin());
        productPinPoolM.setProductItemId(pinPoolDTO.getProductItemId());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(pinPoolDTO.getPoolGroups())) {
            productPinPoolM.setPoolGroups(pinPoolDTO.getPoolGroups().stream().map(this::buildPinPoolGroup).collect(Collectors.toList()));
        }
        return productPinPoolM;
    }

    private ProductPinPoolGroupM buildPinPoolGroup(GeneralProductPinPoolGroupDTO pinPoolGroupDTO) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildPinPoolGroup(com.sankuai.dztheme.generalproduct.res.GeneralProductPinPoolGroupDTO)");
        ProductPinPoolGroupM productPinPoolGroup = new ProductPinPoolGroupM();
        productPinPoolGroup.setDescription(pinPoolGroupDTO.getDescription());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(pinPoolGroupDTO.getUsers())) {
            productPinPoolGroup.setUsers(pinPoolGroupDTO.getUsers().stream().map(this::buildProductUser).collect(Collectors.toList()));
        }
        return productPinPoolGroup;
    }

    private ProductUserM buildProductUser(GeneralProductUserDTO userDTO) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildProductUser(com.sankuai.dztheme.generalproduct.res.GeneralProductUserDTO)");
        ProductUserM productUser = new ProductUserM();
        productUser.setAvatar(userDTO.getAvatar());
        productUser.setName(userDTO.getName());
        productUser.setSex(userDTO.getSex());
        return productUser;
    }

    private AttrM convert2AttrM(GeneralProductItemAttrDTO attrDTO) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.convert2AttrM(com.sankuai.dztheme.generalproduct.res.GeneralProductItemAttrDTO)");
        AttrM attrM = new AttrM();
        attrM.setName(attrDTO.getName());
        attrM.setValue(attrDTO.getValue());
        return attrM;
    }

    private GeneralProductRequest buildGeneralProductRequest(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildGeneralProductRequest(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        GeneralProductRequest request = new GeneralProductRequest();
        request.setPlanId(StringUtils.isEmpty(ctx.getParam(PaddingFetcher.Params.planId)) ? planId : ctx.getParam(PaddingFetcher.Params.planId));
        request.setProductIds(Lists.newArrayList(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.productId)));
        request.setExtParams(buildExtParams(ctx));
        return request;
    }

    private static String transferAttrValue(Object value) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.transferAttrValue(java.lang.Object)");
        if (value == null) {
            return null;
        }
        if (value instanceof String) {
            return (String) value;
        }
        if (isPrimitiveType(value)) {
            return String.valueOf(value);
        }
        return JsonCodec.encode(value);
    }

    private static boolean isPrimitiveType(Object value) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.isPrimitiveType(java.lang.Object)");
        return value instanceof Number || value instanceof Character || value instanceof Boolean;
    }

    private Map<String, Object> buildExtParams(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildExtParams(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        Map<String, Object> extParams = Maps.newHashMap();
        int uaCode = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.userAgent)).orElse(0);
        int platform = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        long dpUserId = Optional.ofNullable((Long) ctx.getParam(ShelfActivityConstants.Params.dpUserId)).orElse(0L);
        long mtUserId = Optional.ofNullable((Long) ctx.getParam(ShelfActivityConstants.Params.mtUserId)).orElse(0L);
        long dpPoiId = PoiIdUtil.getDpPoiIdL(ctx, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        long mtPoiId = PoiIdUtil.getMtPoiIdL(ctx, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        int dpCityId = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        int mtCityId = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.mtCityId)).orElse(0);
        Date selectDate = Optional.ofNullable((Date) ctx.getParam(ShelfActivityConstants.Params.selectDate)).orElse(getToday());

        String dpId = ctx.getParam(ShelfActivityConstants.Params.deviceId);
        String shopUuid = ctx.getParam(ShelfActivityConstants.Params.shopUuid);
        extParams.put("platform", platform);
        extParams.put("userId", platform == VCPlatformEnum.MT.getType() ? mtUserId : dpUserId);
        extParams.put("shopId", platform == VCPlatformEnum.MT.getType() ? mtPoiId : dpPoiId);
        extParams.put("cityId", platform == VCPlatformEnum.MT.getType() ? mtCityId : dpCityId);
        extParams.put("dpid", dpId);
        extParams.put("uaCode", uaCode);
        extParams.put("shopUuid", shopUuid);
        extParams.put("selectDate", selectDate);
        extParams.put("dpShopId", dpPoiId);
        extParams.put("scene", SHELF_LIST);

        //计算需要查出来的拼场日期区间
        setQueryPinPoolDateInfo(ctx, extParams, selectDate);

        // 设置美团实用户id对应的点评虚用户id
        setVirtualDPUserId(ctx, extParams);
        return extParams;
    }

    private void setQueryPinPoolDateInfo(ActivityContext ctx, Map<String, Object> extParams, Date selectDate) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.setQueryPinPoolDateInfo(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.Map,java.util.Date)");
        extParams.put(QueryFetcher.Params.QUERY_PIN_POOL_START_DATE, calPinPoolStartDate(selectDate));
        extParams.put(QueryFetcher.Params.QUERY_PIN_POOL_END_DATE, calPinPoolEndDate(ctx, selectDate));
    }

    private void setVirtualDPUserId(ActivityContext ctx, Map<String, Object> extParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.setVirtualDPUserId(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.Map)");
        CompletableFuture<Long> virtualDPUserIdFuture = ctx.getExtContext(RolePlayVirtualDPUserIdContext.CONTEXT_KEY);
        if (virtualDPUserIdFuture == null) {
            return;
        }

        long virtualDPUserId = virtualDPUserIdFuture.join();
        if (virtualDPUserId != 0) {
            extParams.put("virtualDPUserId", virtualDPUserId);
        }
    }

    //选中日期的date是某一天的0点时刻，所以如果当前时间大于选中日期的时间，开始时间以当前时间为准
    private Date calPinPoolStartDate(Date selectDate) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.calPinPoolStartDate(java.util.Date)");
        if (selectDate == null) {
            return null;
        }
        Date now = new Date();
        return now.after(selectDate) ? now : selectDate;
    }

    //结束日期以选中日期加2天，可以保证当天和次日的场次都查出来，同时避免查到下周的场次
    private Date calPinPoolEndDate(ActivityContext ctx, Date selectDate) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.calPinPoolEndDate(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.Date)");
        if (selectDate == null) {
            return null;
        }

        CompletableFuture<Map<String, String>> ProductBusinessTimeAttrKey2ValueMapFuture = ctx.getExtContext(InactiveRolePlayProductBusinessTimeContext.CONTEXT_KEY);
        if (ProductBusinessTimeAttrKey2ValueMapFuture == null) {
            return new DateTime(selectDate).plusDays(2).toDate();
        }

        Map<String, String> productBusinessTimeAttrKey2ValueMap = ProductBusinessTimeAttrKey2ValueMapFuture.join();
        if (MapUtils.isEmpty(productBusinessTimeAttrKey2ValueMap)) {
            return new DateTime(selectDate).plusDays(2).toDate();
        }

        Date businessEndDate = buildFullProductBusinessEndTime(selectDate, productBusinessTimeAttrKey2ValueMap);
        if (businessEndDate == null) {
            return new DateTime(selectDate).plusDays(2).toDate();
        }
        return businessEndDate;
    }

    private Date buildFullProductBusinessEndTime(Date selectDate, Map<String, String> productBusinessTimeAttrKey2ValueMap) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.buildFullProductBusinessEndTime(java.util.Date,java.util.Map)");
        String businessBeginTime = productBusinessTimeAttrKey2ValueMap.get(PRODUCT_BUSINESS_BEGIN_TIME_ATTR);
        String businessEndTime = productBusinessTimeAttrKey2ValueMap.get(PRODUCT_BUSINESS_END_TIME_ATTR);
        if (StringUtils.isEmpty(businessBeginTime) || StringUtils.isEmpty(businessEndTime)) {
            return null;
        }
        DateTime noDateBusinessEndTime = new DateTime(DateUtils.HOUR_MINUTE_SECOND_TIME_PARSER.parse(businessEndTime));
        DateTime selectDateFormat = new DateTime(selectDate);
        DateTime fullProductBusinessEndTime = selectDateFormat.plusSeconds(noDateBusinessEndTime.getSecondOfDay());
        if (businessEndTime.compareTo(businessBeginTime) < 0) {
            return fullProductBusinessEndTime.plusDays(1).toDate();
        }
        return fullProductBusinessEndTime.toDate();
    }

    private Date getToday() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.getToday()");
        LocalDate localDate = LocalDate.now();
        long epochMilli = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        return new Date(epochMilli);
    }

    private Comparator<ProductM> periodComparator = (product1, product2) -> {
        if (Objects.nonNull(product1.getTitle()) && Objects.nonNull(product2.getTitle())) {
            return product1.getTitle().compareTo(product2.getTitle());
        }
        return 0;
    };

    private Comparator<ProductM> canBookComparator = (product1, product2) -> {
        if (Objects.isNull(product1.getAvailable()) || Objects.isNull(product2.getAvailable())) {
            return 0;
        }
        if (product1.getAvailable() && !product2.getAvailable()) {
            return -1;
        } else if (!product1.getAvailable() && product2.getAvailable()) {
            return 1;
        }
        return 0;
    };

    /**
     * 获取最早可订的起始时间
     *
     * @param result
     * @return
     */
    private LocalDateTime getReservationBeginTime(GeneralProductResult result) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.getReservationBeginTime(com.sankuai.dztheme.generalproduct.res.GeneralProductResult)");
        LocalDateTime localDateTime = LocalDateTime.now();
        int aheadTime = getAheadTime(result);
        LocalDateTime reserveBeginTime = localDateTime.plusMinutes(aheadTime);
        return reserveBeginTime;
    }

    /**
     * 获取提前可订时间，单位分钟
     *
     * @return
     */
    private int getAheadTime(GeneralProductResult result) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.getAheadTime(com.sankuai.dztheme.generalproduct.res.GeneralProductResult)");
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getProducts())) {
            return 0;
        }
        List<GeneralProductAttrDTO> attrs = result.getProducts().get(0).getAttrs();
        String aheadTimeStr = attrs.stream().filter(attr -> attr.getName().equals(GeneralProductAttrEnum.ATTR_BOOKING_AHEAD_TIME.getKey())).findFirst().map(GeneralProductAttrDTO::getValue).orElse("0");
        return NumberUtils.toInt(aheadTimeStr);
    }

    private LocalDate getLocalDate(Date selectDate) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.fetcher.BackroomPaddingFetcher.getLocalDate(java.util.Date)");
        Instant instant = selectDate.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
        return localDateTime.toLocalDate();
    }


}
