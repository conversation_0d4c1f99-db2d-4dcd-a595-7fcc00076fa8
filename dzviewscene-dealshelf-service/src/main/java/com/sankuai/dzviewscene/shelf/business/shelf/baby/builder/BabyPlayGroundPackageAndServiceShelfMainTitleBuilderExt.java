package com.sankuai.dzviewscene.shelf.business.shelf.baby.builder;

import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/1/12 8:19 下午
 */
@ExtPointInstance(name = "亲子-儿童乐园团购套餐与服务货架主标题模块构造扩展点")
public class BabyPlayGroundPackageAndServiceShelfMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.baby.play.ground.package.and.service.shelf.constant.config", defaultValue = "{}")
    public BabyPlayGroundPackageAndServicetShelfConstant babyPlayGroundPackageAndServicetShelfConstant;

    @Override
    public String title(ActivityContext activityContext) {
        return babyPlayGroundPackageAndServicetShelfConstant.getTitle();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (PlatformUtil.isMT(platform)) return babyPlayGroundPackageAndServicetShelfConstant.getMtTitleIcon();
        return babyPlayGroundPackageAndServicetShelfConstant.getDpTitleIcon();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        IconRichLabelVO returnAnyTimeIcon = new IconRichLabelVO();
        String icon = babyPlayGroundPackageAndServicetShelfConstant.getTitleTagIcon();
        returnAnyTimeIcon.setIcon(icon);
        returnAnyTimeIcon.setText(new RichLabelVO(babyPlayGroundPackageAndServicetShelfConstant.getAnyTimeRefund()));
        IconRichLabelVO returnExpiredIcon = new IconRichLabelVO();
        returnExpiredIcon.setIcon(icon);
        returnExpiredIcon.setText(new RichLabelVO(babyPlayGroundPackageAndServicetShelfConstant.getOverTimeRefund()));
        return Lists.newArrayList(returnAnyTimeIcon, returnExpiredIcon);
    }

    @Data
    private static class BabyPlayGroundPackageAndServicetShelfConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
        private String anyTimeRefund;
        private String overTimeRefund;
    }
}

