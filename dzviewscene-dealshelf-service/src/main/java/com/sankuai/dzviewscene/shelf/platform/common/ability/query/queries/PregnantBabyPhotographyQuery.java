package com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.DouHuExtContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/5
 */
public class PregnantBabyPhotographyQuery implements SimplexQuery {

    /**
     * {@link PregnantBabyPhotographyQueryConfig}
     */
    private static final String PREGNANT_BABY_PHOTOGRAPHY_QUERY = "com.sankuai.dzviewscene.productshelf.deal.baby.photography.query.config";


    private static final List<String> douHuStrategy = Lists.newArrayList("exp000700_b", "exp000702_b");


    /**
     * 按布尔匹配策略、排序策略和阶段策略召回一组商品
     *
     * @param context
     * @return
     */
    @Override
    public List<ProductM> query(QueryContext context) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.PregnantBabyPhotographyQuery.query(com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext)");
        if (context == null || CollectionUtils.isEmpty(context.getPool())) {
            return Lists.newArrayList();
        }
        if (!isNeedQueryByAB(context.getActivityContext())) {
            //AB结果告知不需要走排序，则直接 return
            return context.getPool();
        }
        try {
            return context.getPool().stream()
                    .filter(productM -> match(context, productM)).sorted(comparator(context)).limit(nonNegative(topN(context))).collect(Collectors.toList());
        } catch (Exception e) {
            throw new BusinessException("执行单路召回, 发生异常", e);
        }
    }

    /**
     * 根据 AB 结果判断是否需要额外的匹配排序
     *
     * @param activityContext
     * @return false：不需要； True：需要
     */
    private boolean isNeedQueryByAB(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.PregnantBabyPhotographyQuery.isNeedQueryByAB(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<DouHuM> douHuMFuture = activityContext.getExtContext(DouHuExtContext.CONTEXT_KEY);
        if (douHuMFuture == null || douHuMFuture.join() == null) {
            return false;
        }
        return douHuStrategy.contains(douHuMFuture.join().getSk());
    }

    /**
     * 排序器
     *
     * @param context
     * @return
     */
    @Override
    public Comparator<ProductM> comparator(QueryContext context) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.PregnantBabyPhotographyQuery.comparator(com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext)");
        return ((o1, o2) -> {
            if (o1 == null || o2 == null) {
                return 0;
            }
            if (CollectionUtils.isEmpty(o1.getActivities()) && CollectionUtils.isEmpty(o2.getActivities())) {
                return 0;
            }
            List<Integer> dealActivityIds = getDealActivityIdsWithPlatform(context);
            boolean isTeHuiPaiO1 = isContainsTeHuiPai(o1, dealActivityIds);
            boolean isTeHuiPaiO2 = isContainsTeHuiPai(o2, dealActivityIds);
            if (isTeHuiPaiO1 ^ isTeHuiPaiO2) {
                //异或情况下，特惠拍沉底
                return isTeHuiPaiO1 ? 1 : -1;
            }
            if (isTeHuiPaiO1 && isTeHuiPaiO2) {
                //都是特惠拍,按照销量
                return saleDescSort(o1, o2);
            } else {
                //都不是，按原来顺序
                return 0;
            }
        });
    }

    /**
     * @param productM
     * @param dealActivityIds
     * @return true ：包含特惠拍活动
     */
    private boolean isContainsTeHuiPai(ProductM productM, List<Integer> dealActivityIds) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.PregnantBabyPhotographyQuery.isContainsTeHuiPai(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM,java.util.List)");
        if (productM == null || CollectionUtils.isEmpty(dealActivityIds) || CollectionUtils.isEmpty(productM.getActivities())) {
            return false;
        }
        return productM.getActivities().stream().anyMatch(o -> dealActivityIds.contains(o.getPageId()));
    }

    /**
     * 获取特惠拍的彩虹活动Id
     *
     * @param ctx
     * @return
     */
    private List<Integer> getDealActivityIdsWithPlatform(QueryContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.PregnantBabyPhotographyQuery.getDealActivityIdsWithPlatform(QueryContext)");
        if (ctx == null || ctx.getActivityContext() == null) {
            return null;
        }
        PregnantBabyPhotographyQueryConfig config = Lion.getBean(PREGNANT_BABY_PHOTOGRAPHY_QUERY, PregnantBabyPhotographyQueryConfig.class);
        if (config == null) {
            return null;
        }
        int platform = NumberUtils.toInt(ctx.getActivityContext().getParam(ShelfActivityConstants.Params.platform) + "", 1);
        if (PlatformUtil.isMT(platform)) {
            return config.getMtIds();
        } else {
            return config.getDpIds();
        }
    }


    private int saleDescSort(ProductM product1, ProductM product2) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.PregnantBabyPhotographyQuery.saleDescSort(ProductM,ProductM)");
        if (product1.getSale() == null || product2.getSale() == null) {
            return 0;
        }
        return product2.getSale().getSale() - product1.getSale().getSale();
    }

    @Data
    static class PregnantBabyPhotographyQueryConfig {
        /**
         * 美团活动Id
         */
        private List<Integer> mtIds;

        /**
         * 点评活动Id
         */
        private List<Integer> dpIds;
    }
}
