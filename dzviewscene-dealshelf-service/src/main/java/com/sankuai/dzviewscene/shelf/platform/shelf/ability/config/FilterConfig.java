package com.sankuai.dzviewscene.shelf.platform.shelf.ability.config;

/**
 * Created by float.lu on 2020/9/14.
 */

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.lang.StringUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import lombok.Data;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 场景筛选数据配置
 *
 * Created by float on 2020/9/13.
 */
@Component
public class FilterConfig {

    /**
     * 配置项目
     */
    private static final String FILTER_CONFIG_APP_KEY = "com.sankuai.dzviewscene.productshelf.filter";

    /**
     * 场景维度筛选数据配置KEY拼接模板
     */
    private static final String FILTER_CONFIG_KEY_TEMPLATE = "com.sankuai.dzviewscene.productshelf.filter.%s";

    /**
     * 查询场景下指定分组的筛选数据
     *
     * @param sceneCode
     * @param groupName
     * @return
     */
    public FilterM getFilter(String sceneCode, String groupName) {
        String configStr = Lion.getConfigRepository(FILTER_CONFIG_APP_KEY).get(String.format(FILTER_CONFIG_KEY_TEMPLATE, sceneCode), StringUtils.EMPTY);
        Config config = JsonCodec.decode(configStr, Config.class);
        if (config == null) return null;
        return config.getFilter(groupName);
    }

    /**
     * 查询场景下关键
     * @param sceneCode
     * @param groupName
     * @return
     */
    public Map<String, List<String>> getKeywords(String sceneCode, String groupName) {
        String configStr =  Lion.getConfigRepository(FILTER_CONFIG_APP_KEY).get(String.format(FILTER_CONFIG_KEY_TEMPLATE, sceneCode), StringUtils.EMPTY);
        Config config = JsonCodec.decode(configStr, Config.class);
        if (config == null) return null;
        return config.getKeywords(groupName);
    }

    /**
     * 查询展示筛选数据配置
     * @param sceneCode
     * @return
     */
    public List<FilterM> getShowFilters(String sceneCode) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig.getShowFilters(java.lang.String)");
        String configStr =  Lion.getConfigRepository(FILTER_CONFIG_APP_KEY).get(String.format(FILTER_CONFIG_KEY_TEMPLATE, sceneCode), StringUtils.EMPTY);
        Config config = JsonCodec.decode(configStr, Config.class);
        if (config == null) return null;
        return config.getShowFilters();
    }

    /**
     * 查询场景下双层货架筛选栏
     * @param sceneCode
     * @param groupName
     * @return key - 第一层标题，value - 配置及子配置
     */
    public Map<String,MultipleFilterConfig> getMultipleFilterKeywords(String sceneCode, String groupName) {
        String configStr =  Lion.getConfigRepository(FILTER_CONFIG_APP_KEY).get(String.format(FILTER_CONFIG_KEY_TEMPLATE, sceneCode), StringUtils.EMPTY);
        Config config = JsonCodec.decode(configStr, Config.class);
        if (config == null) {
            return null;
        }
        return config.getMultipleFilterKeyWordsByGroupName(groupName);
    }

    public Boolean isConfigAutoUpgrade(String sceneCode) {
        String configStr =  Lion.getConfigRepository(FILTER_CONFIG_APP_KEY).get(String.format(FILTER_CONFIG_KEY_TEMPLATE, sceneCode), StringUtils.EMPTY);
        Config config = JsonCodec.decode(configStr, Config.class);
        if (config == null) {
            return false;
        }
        return config.isSearchKeywordConfigAutoUpgrade();
    }


    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Config {

        /**
         * 展示筛选列表配置
         */
        private List<FilterM> showFilters;

        /*筛选数据配置项*/
        private Map<String, FilterM> filters;

        /*筛选标签名和关键词列表的映射, 用于实现按关键词匹配选中标签功能*/
        private Map<String, Map<String, List<String>>> keywords;

        /**
         * 多层货架筛选关键词映射
         * Key1 - GroupName，Value1 - 多层筛选栏[Key2-一层筛选栏Title，Value2 - Config]
         * Ex: {"团购":{"家电维修":{"child":{"冰箱":{"word":"冰箱维修"}}}}}
         */
        private Map<String,Map<String,MultipleFilterConfig>> multipleFilterKeywords;

        /**
         * multipleFilterKeywords扩展
         * tab锚定关键字支持二级筛选配置自动升级为一级筛选配置
         * 优先按照配置的结构查找，找不到后将二级筛选配置作为一级筛选配置使用，尝试匹配
         */
        private boolean searchKeywordConfigAutoUpgrade;

        public FilterM getFilter(String groupName) {
            return filters == null ? null : filters.get(groupName);
        }

        public Map<String, List<String>> getKeywords(String groupName) {
            return keywords == null ? null : keywords.get(groupName);
        }

        public Map<String,MultipleFilterConfig> getMultipleFilterKeyWordsByGroupName(String groupName){
            return multipleFilterKeywords == null ? null : multipleFilterKeywords.get(groupName);
        }
    }

    /**
     * 多层货架（注，字段命名从简，减少字节数）
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class MultipleFilterConfig{

        /**
         * 匹配策略：精确匹配
         */
        public static final String EXACT_MATCH_METHOD = "EXACT";

        /**
         * 匹配策略：包含关键词
         */
        public static final String CONTAIN_MATCH_METHOD = "CONTAIN";

        /**
         * 子节点
         * key - 分类，value - config
         */
        private Map<String, MultipleFilterConfig> child;
        /**
         * 关键词
         */
        private List<String> key;
        /**
         * 策略，缺省，默认为全匹配
         */
        private String method;
    }
}
