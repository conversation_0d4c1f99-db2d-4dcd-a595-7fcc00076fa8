package com.sankuai.dzviewscene.shelf.business.filterlist.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;

/**
 * <AUTHOR>
 * @title 跳转链接工具类
 * @package com.sankuai.dzviewscene.shelf.business.filterlist.utils
 * @date 2021/7/31
 */
public class UrlConstantsUtils {


    public static String getGDomain(boolean isMT) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.utils.UrlConstantsUtils.getGDomain(boolean)");
        if (isMT) {
            return Lion.get("joy-booking-web.g.mt.domain");
        } else {
            return Lion.get("joy-booking-web.g.dp.domain");
        }
    }

    public static String getWebHeader(Boolean isMT) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.utils.UrlConstantsUtils.getWebHeader(java.lang.Boolean)");
        if (isMT) {
            return "imeituan://www.meituan.com/web?url=";
        }
        return "dianping://web?url=";
    }


}
