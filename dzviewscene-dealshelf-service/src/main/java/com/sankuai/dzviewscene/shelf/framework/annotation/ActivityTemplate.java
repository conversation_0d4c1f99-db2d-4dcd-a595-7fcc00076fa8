package com.sankuai.dzviewscene.shelf.framework.annotation;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.lang.annotation.*;

/**
 * 模板注解, 场景通过模板定制逻辑
 * <p>
 * Created by float.lu on 2020/8/21.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Indexed
@Component
public @interface ActivityTemplate {
    /**
     * 活动ID
     *
     * @return
     */
    String activityCode();

    /**
     * 场景ID
     *
     * @return
     */
    String sceneCode() default "";

    /**
     * 配置描述
     *
     * @return
     */
    String name();
}
