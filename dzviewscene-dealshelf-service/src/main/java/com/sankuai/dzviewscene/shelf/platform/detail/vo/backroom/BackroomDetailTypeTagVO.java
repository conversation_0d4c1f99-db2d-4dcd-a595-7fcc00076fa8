package com.sankuai.dzviewscene.shelf.platform.detail.vo.backroom;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/21 7:30 下午
 */
@NoArgsConstructor
@Data
public class BackroomDetailTypeTagVO {

    String typeName;

    String icon;

    List<BackroomDetailThemeFeatureVO> themeFeatures;

    public BackroomDetailTypeTagVO(String typeName, String icon) {
        this.typeName = typeName;
        this.icon = icon;
        this.themeFeatures = Lists.newArrayList();
    }

    public void add2TypeList(BackroomDetailThemeFeatureVO backroomDetailThemeFeatureVO) {
        this.themeFeatures.add(backroomDetailThemeFeatureVO);
    }
}
