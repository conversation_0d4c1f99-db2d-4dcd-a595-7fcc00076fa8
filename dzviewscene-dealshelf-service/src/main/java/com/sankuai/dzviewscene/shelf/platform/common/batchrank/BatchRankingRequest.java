package com.sankuai.dzviewscene.shelf.platform.common.batchrank;

import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import org.apache.commons.collections.MapUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
public class BatchRankingRequest {

    private Map<String, List<RankingItem>> source;

    /**
     * {@link com.sankuai.dzviewscene.shelf.platform.common.batchrank.BatchRankerEnum#toString}
     */
    private List<String> batchRankers;

    /**
     * key - tabId，value -rankId
     */
    private Map<String, String> singleRankIdMap;

    /**
     * key - rankId，value - 单个rank对应的扩展参数
     */
    private Map<String, Map<String, Object>> singleRankParams;

    /**
     * key : {@link BatchRankingRequestParamEnum#getKey()}
     */
    private Map<String, Object> batchRankParams = new ConcurrentHashMap<>();

    private String sceneCode;

    public Map<String, Object> getBatchRankParams() {
        return batchRankParams;
    }

    public Map<String, List<RankingItem>> getSource() {
        return source;
    }

    public void setSource(Map<String, List<RankingItem>> source) {
        this.source = source;
    }

    public Map<String, String> getSingleRankIdMap() {
        return singleRankIdMap;
    }

    public void setSingleRankIdMap(Map<String, String> singleRankIdMap) {
        this.singleRankIdMap = singleRankIdMap;
    }

    public List<String> getBatchRankers() {
        return batchRankers;
    }

    public void setBatchRankers(List<String> batchRankers) {
        this.batchRankers = batchRankers;
    }

    public Map<String, Object> getSingleRankParams(String rankId) {
        if (MapUtils.isEmpty(singleRankParams)) {
            return new HashMap<>();
        }
        return singleRankParams.get(rankId);
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }
}
