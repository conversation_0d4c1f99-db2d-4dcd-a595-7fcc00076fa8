package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context;

import com.dianping.cat.Cat;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheClientConfig;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.utils.CompletableFutureUtil;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextExt;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.MapUtils;
import org.joda.time.DateTime;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2021/10/14 20:05 下午
 */
@ContextExt(name = "剧本杀筛选列表页-找拼场模块在拼场次数量")
public class RolePlayFilterListFindPinNumsContext implements IContextExt<Integer> {

    public static final String CONTEXT_KEY = "rolePlayChannelFindPinNumsContext";

    private static final int PAGE_SIZE = 5;

    private static final int QUERY_DAYS = 7;

    private static final int INACTIVE_BIZID = 143;

    private static final int ACTIVE_BIZID = 144;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.roleplay.pinnums.refreshAfterSeconds", defaultValue = "600")
    private int REFRESH_AFTER_SECONDS;

    private static final String PIN_NUMS_CATEGORY = "dzp_roleplay_pinpool_daycount";

    private static final int PIN_NUMS_EXPIRE_TIME = 172800;//2天

    private static Random random = new Random();

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    public String contextKey() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.contextKey()");
        return CONTEXT_KEY;
    }

    @Override
    public CompletableFuture<Integer> contextExt(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.contextExt(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        try {
            return buildTotalPinNums(activityContext);
        } catch (Exception e) {
            Cat.logError("剧本杀频道页找拼场模块获取7天在拼场次数量错误", e);
            return CompletableFuture.completedFuture(0);
        }
    }

    private CompletableFuture<Integer> buildTotalPinNums(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.buildTotalPinNums(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CacheClientConfig cacheClientConfig = new CacheClientConfig("redis-vc", "master-slave");
        CacheClient cacheClient = AthenaInf.getCacheClient(cacheClientConfig);
        List<CacheKey> cacheKeys = buildCacheKeys(ctx);
        return cacheClient.asyncBatchGetRefreshBehind(cacheKeys, new TypeReference<Integer>() {
        }, this::dayPinNumBatchFetcher, PIN_NUMS_EXPIRE_TIME, REFRESH_AFTER_SECONDS + random.nextInt(60))
                .thenApply(this::sumDayCount);
    }

    private Integer sumDayCount(Map<CacheKey, Integer> cacheKey2ResultMap) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.sumDayCount(java.util.Map)");
        if (MapUtils.isEmpty(cacheKey2ResultMap)) {
            return 0;
        }
        return cacheKey2ResultMap.values().stream()
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
    }

    private CompletableFuture<Map<CacheKey, Integer>> dayPinNumBatchFetcher(List<CacheKey> cacheKeys) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.dayPinNumBatchFetcher(java.util.List)");
        Map<CacheKey, CompletableFuture<Integer>> cache2PinNumFuture = Maps.newHashMap();
        for (CacheKey cacheKey : cacheKeys) {
            CompletableFuture<Integer> perDayPinNums = compositeAtomService.getRecommendResult(getRecommendRequest(cacheKey.get("ctx"), cacheKey.get("bizId"), cacheKey.get("poolBeginDate")))
                    .thenApply(RecommendResult::getTotalSize);
            cache2PinNumFuture.put(cacheKey, perDayPinNums);
        }
        return CompletableFutureUtil.each(cache2PinNumFuture);
    }

    private List<CacheKey> buildCacheKeys(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.buildCacheKeys(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<CacheKey> cacheKeys = Lists.newArrayList();
        DateTime queryPinPoolStartDate = new DateTime().withTimeAtStartOfDay();

        for (int plusDay = 0; plusDay < QUERY_DAYS; plusDay++) {
            cacheKeys.add(buildCacheKey(ctx, INACTIVE_BIZID, queryPinPoolStartDate));
            cacheKeys.add(buildCacheKey(ctx, ACTIVE_BIZID, queryPinPoolStartDate));
            queryPinPoolStartDate = queryPinPoolStartDate.plusDays(1);
        }
        return cacheKeys;
    }

    private CacheKey buildCacheKey(ActivityContext ctx, int bizId, DateTime queryPinPoolStartDate) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.buildCacheKey(ActivityContext,int,DateTime)");
        int platform = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.platform);
        int cityId = PlatformUtil.isMT(platform) ? ctx.getParam(FilterListActivityConstants.Params.mtCityId) : ctx.getParam(FilterListActivityConstants.Params.dpCityId);
        CacheKey cacheKey = new CacheKey(PIN_NUMS_CATEGORY, cityId, platform, INACTIVE_BIZID, queryPinPoolStartDate.getMillis());
        cacheKey.put("ctx", ctx);
        cacheKey.put("bizId", bizId);
        cacheKey.put("poolBeginDate", queryPinPoolStartDate.toString("MMdd"));
        return cacheKey;
    }

    private RecommendParameters getRecommendRequest(ActivityContext ctx, int recommendBizId, String poolBeginDate) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.getRecommendRequest(ActivityContext,int,String)");
        int platform = ParamsUtil.getIntSafely(ctx, FilterListActivityConstants.Params.platform);
        double lat = Optional.ofNullable((Double) ctx.getParam(FilterListActivityConstants.Params.lat)).orElse(0.0);
        double lng = Optional.ofNullable((Double) ctx.getParam(FilterListActivityConstants.Params.lng)).orElse(0.0);
        String deviceId = ctx.getParam(FilterListActivityConstants.Params.deviceId);
        int cityId = PlatformUtil.isMT(platform) ? ctx.getParam(FilterListActivityConstants.Params.mtCityId) : ctx.getParam(FilterListActivityConstants.Params.dpCityId);
        long userId = PlatformUtil.isMT(platform) ? ctx.getParam(FilterListActivityConstants.Params.mtUserId) : ctx.getParam(FilterListActivityConstants.Params.dpUserId);
        int pageNum = Optional.ofNullable((Integer) ctx.getParam(FilterListActivityConstants.Params.pageNo)).orElse(1);
        return buildRecommendRequest(platform, lat, lng, deviceId, cityId, userId, pageNum, recommendBizId, poolBeginDate);
    }

    private RecommendParameters buildRecommendRequest(int platform, double lat, double lng,
                                                      String deviceId, int cityId, long userId,
                                                      int pageNum, int recommendBizId, String poolBeginDate) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.buildRecommendRequest(int,double,double,java.lang.String,int,long,int,int,java.lang.String)");
        RecommendParameters parameters = new RecommendParameters();
        parameters.setBizId(recommendBizId);
        parameters.setCityId(cityId);
        parameters.setPlatformEnum(PlatformUtil.isMT(platform) ? PlatformEnum.MT : PlatformEnum.DP);
        parameters.setLat(lat);
        parameters.setLng(lng);
        parameters.setPageSize(PAGE_SIZE);
        parameters.setPageNumber(pageNum);
        parameters.setBizParams(buildBizParams(poolBeginDate));
        parameters.setOriginUserId(userId > 0 ? String.valueOf(userId) : null);
        if (PlatformUtil.isMT(platform)) {
            parameters.setUuid(deviceId);
        } else {
            parameters.setDpid(deviceId);
        }
        return parameters;
    }

    private Map<String, Object> buildBizParams(String poolBeginDate) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.RolePlayFilterListFindPinNumsContext.buildBizParams(java.lang.String)");
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("sortType", "AI");
        bizParams.put("poolBeginDate", poolBeginDate);
        return bizParams;
    }
}
