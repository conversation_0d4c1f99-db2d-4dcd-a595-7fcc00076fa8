package com.sankuai.dzviewscene.shelf.business.shelf.medical.builder.maintitle;

import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.builder.BeautyMainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import lombok.Data;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/10/21
 */
@ExtPointInstance(name = "医疗-妇科团购货架主标题模块构造扩展点")
public class FukeMainTitleBuilderAdapterExt extends MainTitleBuilderExtAdapter {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.fuke.constant.config", defaultValue = "{}")
    public MainTitleConstant mainTitleConstant;

    @Override
    public String title(ActivityContext activityContext) {
        return mainTitleConstant.getTitle();
    }

    @Override
    public String icon(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (VCPlatformEnum.MT.getType() == platform) return mainTitleConstant.getMtTitleIcon();
        return mainTitleConstant.getDpTitleIcon();
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        IconRichLabelVO returnAnyTimeIcon = new IconRichLabelVO();
        String icon = mainTitleConstant.getTitleTagIcon();
        returnAnyTimeIcon.setIcon(icon);
        returnAnyTimeIcon.setText(new RichLabelVO("随时退"));
        IconRichLabelVO returnExpiredIcon = new IconRichLabelVO();
        returnExpiredIcon.setIcon(icon);
        returnExpiredIcon.setText(new RichLabelVO("过期退"));
        return Lists.newArrayList(returnAnyTimeIcon, returnExpiredIcon);
    }

    @Data
    private static class MainTitleConstant {
        private String title;
        private String dpTitleIcon;
        private String mtTitleIcon;
        private String titleTagIcon;
    }
}

