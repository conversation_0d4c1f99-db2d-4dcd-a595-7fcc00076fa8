package com.sankuai.dzviewscene.shelf.business.shelf.backroom.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.NumberUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzTagVO;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;


@ExtPointInstance(name = "组队编辑页密室主题列表扩展实现")
public class JoyPinBackroomThemeBuilder extends ProductBuilderVoExtAdapter {

    private static final int NORMAL = 0;

    private static final String DIFFICULTY = "difficulty";

    private static final String PIN_PICTURE = "pinPicture";


    @Override
    public List<String> productTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.builder.JoyPinBackroomThemeBuilder.productTags(ActivityContext,ProductM)");
        String startNum = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_STARTNUM.getKey());
        String adviceNum = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_ADVICENUM.getKey());
        String duration = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_DURATION.getKey());
        return Lists.newArrayList(startNum, adviceNum, duration).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<DzTagVO> aidDecisionTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.builder.JoyPinBackroomThemeBuilder.aidDecisionTags(ActivityContext,ProductM)");
        List<DzTagVO> result = Lists.newArrayList();
        //排行榜需要高亮
        String rank = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_RANK.getKey());
        String style = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_STYLE.getKey());
        List<String> backroomTags = JsonCodec.decode(productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_TAGS.getKey()), new TypeReference<List<String>>(){});
        result.addAll(buildNormalTag(rank, style, backroomTags));
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> extAttrs(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.builder.JoyPinBackroomThemeBuilder.extAttrs(ActivityContext,ProductM)");
        String difficulty = productM.getAttr(GeneralProductAttrEnum.ATTR_BACKROOM_DIFFICULTLEVEL.getKey());
        String pinPicture = productM.getAttr("attr_backroom_pinIconUrl_optimized");
        Map<String, Object> attrs = Maps.newHashMap();
        attrs.put(DIFFICULTY, NumberUtils.objToInt(difficulty));
        attrs.put(PIN_PICTURE, pinPicture);
        return attrs;
    }

    private List<DzTagVO> buildNormalTag(String rank, String style, List<String> backroomTags) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.builder.JoyPinBackroomThemeBuilder.buildNormalTag(java.lang.String,java.lang.String,java.util.List)");
        List<String> tags = Lists.newArrayList();
        tags.add(style);
        tags.add(rank);
        tags.addAll(Optional.ofNullable(backroomTags).orElse(Collections.emptyList()));
        return tags.stream().filter(StringUtils::isNotEmpty).map(tag -> new DzTagVO(tag, NORMAL)).collect(Collectors.toList());
    }

    @Override
    public String sale(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.backroom.builder.JoyPinBackroomThemeBuilder.sale(ActivityContext,ProductM)");
        return productM.getSaleTag();
    }
}
