package com.sankuai.dzviewscene.shelf.business.filterlist.wedding;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.filterlist.wedding.builder.WeddingCaseDetailProductBuilderVoExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.filterlist.flow.FilterListOnlyProductsFlow;
import com.sankuai.dzviewscene.shelf.platform.list.ProductListActivity;
import com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilderExt;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangsuping on 2021/6/18.
 */
@ActivityTemplate(activityCode = ProductListActivity.ACTIVITY_PRODUCT_LIST_CODE, sceneCode = "wedding_case_detail_prepaylist", name = "结婚-案例详情-预付商品列表")
public class WeddingCaseDetailProductsTemplate implements IActivityTemplate {
    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.WeddingCaseDetailProductsTemplate.flow()");
        return FilterListOnlyProductsFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.WeddingCaseDetailProductsTemplate.extParams(java.util.Map)");
        // 1. 设置召回商品组
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList("product"));

        // 2. 设置商品组召回和填充参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put("product", new HashMap<String, Object>(1) {{
                // 设置召回能力
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.paramGeneralProductsQuery.name());
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.generalProductPadding.name());
                put(PaddingFetcher.Params.QUERY_PRICE_SCENE, 400000);
                put(PaddingFetcher.Params.planId, "10100172");
                put(PaddingFetcher.Params.statisticSaleBizType, 10);
            }});
        }});
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.WeddingCaseDetailProductsTemplate.extContexts(java.util.List)");
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.WeddingCaseDetailProductsTemplate.extValidators(java.util.List)");
    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.WeddingCaseDetailProductsTemplate.extAbilities(java.util.Map)");
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.wedding.WeddingCaseDetailProductsTemplate.extPoints(java.util.Map)");
        // 1. 推荐商品VO构造扩展点
        extPoints.put(ProductListBuilderExt.EXT_POINT_PRODUCT_LIST_BUILDER_CODE, WeddingCaseDetailProductBuilderVoExt.class);
    }

}
