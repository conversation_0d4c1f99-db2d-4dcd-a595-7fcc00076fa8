package com.sankuai.dzviewscene.shelf.platform.utils;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.mapstruct.ap.internal.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * Created by float.lu on 2020/9/22.
 */
public class CollectUtils {

    /**
     * 获取一个map集合重的第一个值
     * @param map
     * @param <T>
     * @return
     */
    public static <T> T firstValue(Map map) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return (T) Collections.first(map.values());
    }

    /**
     * 获取一个List集合中的第一个值
     * @param list
     * @param <T>
     * @return
     */
    public static <T> T firstValue(List list) {
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }
        return (T) Collections.first(list);
    }
}
