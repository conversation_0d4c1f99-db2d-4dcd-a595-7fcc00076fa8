package com.sankuai.dzviewscene.shelf.framework.annotation;

import org.springframework.stereotype.Component;
import org.springframework.stereotype.Indexed;

import java.lang.annotation.*;

/**
 * 能力实例注解
 * <p>
 * Created by float on 2020/8/22.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Indexed
@Component
public @interface AbilityInstance {
    /**
     * 能力实例名
     *
     * @return
     */
    String name();
}
