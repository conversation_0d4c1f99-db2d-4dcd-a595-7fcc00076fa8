package com.sankuai.dzviewscene.shelf.business.detail.medical.model;

import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/13 12:03 下午
 */
@Data
public class DealDetailModel {

    //团单Id
    private int dealGroupId;

    //团详模块名
    private String title;

    //泛商品团详结构化数据
    private DealDetailSkuUniStructuredDto skuUniStructuredDto;

    //结构化类型
    private String structType;

}
