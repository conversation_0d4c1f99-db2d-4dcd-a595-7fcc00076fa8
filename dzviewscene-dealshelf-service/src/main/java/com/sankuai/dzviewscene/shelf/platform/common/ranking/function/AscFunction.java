package com.sankuai.dzviewscene.shelf.platform.common.ranking.function;

import cn.hutool.core.bean.BeanUtil;
import com.dianping.cat.Cat;
import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorJavaType;
import com.googlecode.aviator.runtime.type.AviatorNumber;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.util.NumberUtil;

import java.util.Map;

/**
 * Created by float on 2020/11/7.
 */
public class AscFunction extends AbstractFunction {

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.common.ranking.function.AscFunction.call(java.util.Map,com.googlecode.aviator.runtime.type.AviatorObject)");
        Number value1 = BeanUtil.getProperty(env.get("item1"), ((AviatorJavaType) arg1).getName());
        Number value2 = BeanUtil.getProperty(env.get("item2"), ((AviatorJavaType) arg1).getName());
        return AviatorNumber.valueOf(NumberUtil.notNullNumber(value1).longValue()
                - NumberUtil.notNullNumber(value2).longValue());
    }

    @Override
    public String getName() {
        return "asc";
    }

}
