package com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @title 密室热门主题列表页商品扩展点
 * @package com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder
 * @date 2021/7/29
 */
@ExtPointInstance(name = "密室热门主题列表页商品扩展点")
public class BackroomPopularProductsBuilderExt extends ProductBuilderVoExtAdapter {

    private static final int TEN_THOUSAND = 10000;

    private static final int MILLION = 1000000;

    @Override
    public int productId(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.productId(ActivityContext,ProductM)");
        return productM.getProductId();
    }

    @Override
    public String picUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.picUrl(ActivityContext,ProductM)");
        return productM.getPicUrl();
    }

    @Override
    public String sale(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.sale(ActivityContext,ProductM)");
        if (Objects.isNull(productM.getSale())) {
            return null;
        }
        return getSaleTagThroughSale(productM.getSale().getSale(), productM.getSale().getSaleTag());
    }

    private String getSaleTagThroughSale(int sale, String saleTag) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.getSaleTagThroughSale(int,java.lang.String)");
        if (sale <= TEN_THOUSAND) {
            return saleTag;
        } else if (sale <= MILLION) {
            BigDecimal saleDecimal = new BigDecimal(String.valueOf(sale));
            return "已订"+saleDecimal.divide(new BigDecimal(TEN_THOUSAND)).setScale(1, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString()+"万";
        }
        return "已订100万+";
    }

    @Override
    public String salePriceDesc(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.salePriceDesc(ActivityContext,ProductM)");
        String desc = productM.getAttr(GeneralProductAttrEnum.ATTR_DIFFERENT_PRICE_DESC.getKey());
        return StringUtils.isEmpty(desc) ? "/人" : String.format("%s/人", desc);
    }

    @Override
    public String salePrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.salePrice(ActivityContext,ProductM)");
        return "¥"+productM.getBasePriceTag();
    }

    @Override
    public List<String> productTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.productTags(ActivityContext,ProductM)");
        if (Objects.isNull(productM)) {
            return null;
        }
        return Lists.newArrayList(productM.getAttr("backroomTerrorLevel"));
    }

    @Override
    public String shopName(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPopularProductsBuilderExt.shopName(ActivityContext,ProductM)");
        if (CollectionUtils.isEmpty(productM.getShopMs())) {
            return null;
        }
        return productM.getShopMs().get(0).getShopName();
    }
}
