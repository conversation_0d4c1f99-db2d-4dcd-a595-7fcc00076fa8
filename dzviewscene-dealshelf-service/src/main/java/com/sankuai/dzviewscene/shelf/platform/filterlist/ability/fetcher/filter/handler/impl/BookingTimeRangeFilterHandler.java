package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;

import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.enums.WeekEnum;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.ShowFilterHandler;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Component
public class BookingTimeRangeFilterHandler implements ShowFilterHandler {
    @Override
    public CompletableFuture<FilterM> handle(ActivityContext activityContext, FilterM filterM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl.BookingTimeRangeFilterHandler.handle(ActivityContext,FilterM)");
        filterM.setFilters(buildFilters(activityContext));
        filterM.setName("日期");
        return CompletableFuture.completedFuture(filterM);
    }

    private List<FilterBtnM> buildFilters(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl.BookingTimeRangeFilterHandler.buildFilters(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<FilterBtnM> filterBtnMList = Lists.newArrayList();
        DateTime date = new DateTime().withTimeAtStartOfDay();
        for (int i = 1; i <= 15; i++) {
            filterBtnMList.add(buildDayBtn(date));
            date = date.plusDays(1);
        }
        return filterBtnMList;
    }

    private FilterBtnM buildDayBtn(DateTime date) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl.BookingTimeRangeFilterHandler.buildDayBtn(org.joda.time.DateTime)");
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(date.getMillis());
        filterBtnM.setType(110);
        filterBtnM.setTitle(buildTitle(date));
        filterBtnM.setSubTitle(date.toString("MM-dd"));
        filterBtnM.setExtra(buildExtra(date));
        return filterBtnM;
    }

    private String buildExtra(DateTime date) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl.BookingTimeRangeFilterHandler.buildExtra(org.joda.time.DateTime)");
        Map<String, String> extra = Maps.newHashMap();
        extra.put("poolBeginDate", date.toString("MMdd"));
        extra.put("timestamp", String.valueOf(date.withTimeAtStartOfDay().getMillis()));
        return JsonCodec.encode(extra);
    }

    private String buildTitle(DateTime date) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl.BookingTimeRangeFilterHandler.buildTitle(org.joda.time.DateTime)");
        DateTime startOfToday = new DateTime().withTimeAtStartOfDay();
        if (startOfToday.equals(date)) {
            return "今天";
        }
        if (startOfToday.plusDays(1).equals(date)) {
            return "明天";
        }
        return WeekEnum.getDesc(date.getDayOfWeek());
    }

}
