package com.sankuai.dzviewscene.shelf.business.shelf.laundry.book.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.enums.FilterShowTypeEnums;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExtAdapter;

@ExtPointInstance(name = "洗衣预定货架筛选构造扩展点")
public class LaundryBookingShelfFilterBuilder extends FilterBuilderExtAdapter {

    @Override
    public int showType(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.laundry.book.builder.LaundryBookingShelfFilterBuilder.showType(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return FilterShowTypeEnums.FIRST_UNDERLINE_SECONDE_CIRCULAR.getType();
    }

    @Override
    public int minShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.laundry.book.builder.LaundryBookingShelfFilterBuilder.minShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 1;
    }

}
