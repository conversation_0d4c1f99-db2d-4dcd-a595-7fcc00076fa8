package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mpproduct.general.trade.api.dto.SpuProductDTO;
import com.meituan.mpproduct.general.trade.api.request.QuerySpuProductRequest;
import com.sankuai.athena.client.executor.Futures;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.GroupQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.mpproduct.trade.api.enums.Owner;
import com.sankuai.mpproduct.trade.api.enums.ProductQueryStrategy;
import com.sankuai.mpproduct.trade.api.model.ProductSkuDTO;
import com.sankuai.mpproduct.trade.api.request.ProductSkuBatchQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Component
@Slf4j
public class LeCrossProductQueryHandler extends NearestShopRecommendAbstractHandler implements GroupQueryHandler {

    @Resource
    private CompositeAtomService compositeAtomService;
    // 参数
    private static final int PLATFORM_PRODUCT = 0;
    private static final String MERCHANT_CUSTOM_INFORMATION = "merchantCustomInformation";
    private static final String SUBJECTCLASSIFICATION = "subjectClassification";
    private static final String MULTISUBJECTCLASSIFICATION = "multiSubjectClassification";
    private static final int EDU_SPU_PRODUCT_ID_TYPE = 16;
    public static final String EXPLAIN_DOC = "explainDoc";
    private static final String BACK_CATE_ID_HAI_MA = "backCategoryId";
    private static final String PRODUCT_ID = "productId";
    // 推荐标品海马配置
    private static final String STANDARD_PRODUCT_SCENE = "le_cross_standard_product";

    @Override
    public CompletableFuture<ProductGroupM> query(ActivityContext activityContext, String groupName, Map<String, Object> params) {

        List<ProductM> spuProductMs = getSpuProductMs(activityContext);
        if (CollectionUtils.isEmpty(spuProductMs)) {
            return CompletableFuture.completedFuture(null);
        }

        List<Long> spuIds = spuProductMs.stream().map(ProductM::getProductId).map(Long::valueOf).collect(Collectors.toList());
        CompletableFuture<List<SpuProductDTO>> spuProductDTOsCf = compositeAtomService.batchQuerySpuRelatedProductIds(buildQuerySpuProductIdsReq(activityContext, spuIds));
        return spuProductDTOsCf.thenCompose(spuProductDTO -> fetchSubjectsInfo(spuIds, spuProductDTO))
                .thenApply(spuId2SubjectsMap -> convert2ProductGroupM(spuIds, spuId2SubjectsMap, groupName, activityContext));
    }

    private List<ProductM> getSpuProductMs(ActivityContext activityContext) {
        CompletableFuture<HaimaResponse> standardProductFuture = getStandardProductCF(activityContext);
        HaimaResponse haimaResponse = getHaimaResponse(standardProductFuture);
        if (haimaResponse == null) {
            return new ArrayList<>();
        }
        List<ProductM> productList = loadProducts(haimaResponse);
        return productList;
    }

    private List<ProductM> loadProducts(HaimaResponse response) {
        List<HaimaConfig> configs = response.getData();
        if (configs.get(0) == null || CollectionUtils.isEmpty(configs.get(0).getContents())) {
            return Lists.newArrayList();
        }
        List<HaimaContent> contents = configs.get(0).getContents();
        List<String> spuProductIds = Lists.newArrayList();
        contents.forEach(content -> addSpuProductIds(spuProductIds, content));
        return spuProductIds.stream().filter(Objects::nonNull)
                .map(productId -> buildProduct(productId, ProductTypeEnum.GENERAL_SPU.getType()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    private ProductM buildProduct(String productId, int productType) {
        try {
            ProductM productM = new ProductM();
            productM.setProductId(Integer.parseInt(productId));
            productM.setProductType(productType);
            return productM;
        } catch (Exception e) {
            log.error("LeCrossProductQueryHandler build Product error", e);
            Cat.logEvent("LeCrossProductQueryHandler", "buildProductError");
        }
        return null;
    }

    private void addSpuProductIds(List<String> spuProductIds, HaimaContent content) {
        if (content == null) {
            return;
        }
        List<String> configProductIds = convertStr2List(content.getContentString(PRODUCT_ID));
        spuProductIds.addAll(configProductIds);
    }

    private List<String> convertStr2List(String str) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }
        return Arrays.stream(str.split(","))
                .map(String::trim).collect(Collectors.toList());
    }

    private HaimaResponse getHaimaResponse(CompletableFuture<HaimaResponse> response) {
        try {
            HaimaResponse haimaResponse = Futures.get(response, 1000);
            if(haimaResponse == null || !haimaResponse.isSuccess() || CollectionUtils.isEmpty(haimaResponse.getData())) {
                return null;
            }
            return haimaResponse;
        } catch (Exception e) {
            Cat.logEvent("LeCrossProductQueryHandler","getHaimaResponse");
            log.error("LeCrossProductQueryHandler getHaimaResponse error", e);
            return null;
        }
    }

    private CompletableFuture<HaimaResponse> getStandardProductCF(ActivityContext context) {
        // 获取线上可用标品ID
        Integer dpCityId = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        List<Integer> backCatIds = context.getParam(PmfConstants.Params.shopBackCatIds);
        HaimaRequest standardProductRequest = buildHaimaRequestWithCityCate(STANDARD_PRODUCT_SCENE, dpCityId, backCatIds);
        return compositeAtomService.getHaiMaResponse(standardProductRequest);
    }

    private CompletableFuture<Map<Long, Set<String>>> fetchSubjectsInfo(List<Long> spuIds, List<SpuProductDTO> spuProductDTOs) {
        if (CollectionUtils.isEmpty(spuIds) || CollectionUtils.isEmpty(spuProductDTOs)) {
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
        Map<Long, List<Long>> spuId2ProductIds = buildSpuId2ProductIds(spuIds, spuProductDTOs);
        List<Long> productIds = spuProductDTOs.stream().map(SpuProductDTO::getProductId).collect(Collectors.toList());
        return compositeAtomService.batchQueryPlatformProducts(buildQueryPlatformProductsReq(productIds))
                .thenApply(productList -> assembleSubjectsInfo(spuId2ProductIds, productList));
    }

    private ProductGroupM convert2ProductGroupM(List<Long> spuIds, Map<Long, Set<String>> spuId2SubjectsMap, String groupName, ActivityContext ctx) {
        List<ProductM> products = buildProductsFromSpuIds(spuIds, spuId2SubjectsMap);
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        products.forEach(product -> product.setGroupName(groupName));
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setTotal(products.size());
        productGroupM.setProducts(products);
        productGroupM.setHasNext(false);
        return productGroupM;
    }

    private List<ProductM> buildProductsFromSpuIds(List<Long> spuIds, Map<Long, Set<String>> spuId2SubjectsMap) {
        if (CollectionUtils.isEmpty(spuIds) || MapUtils.isEmpty(spuId2SubjectsMap)) {
            return null;
        }
        List<ProductM> productMS = Lists.newArrayList();
        for (Long spuId : spuIds) {
            if (!spuId2SubjectsMap.containsKey(spuId)) {
                continue;
            }
            ProductM product = buildProductM(spuId, ProductTypeEnum.GENERAL_SPU.getType(), EDU_SPU_PRODUCT_ID_TYPE, StringUtils.EMPTY);
            productMS.add(product);
        }
        return productMS;
    }

    private ProductM buildProductM(Long productId, int productType, int productIdType, String explainDoc) {
        ProductM productM = new ProductM();
        productM.setProductId(productId.intValue());
        productM.setProductType(productType);
        productM.setId(new ProductIdM(productId, productIdType));
        AttrM attrM = new AttrM();
        attrM.setName(EXPLAIN_DOC);
        attrM.setValue(explainDoc);
        List<AttrM> extAttrs = Lists.newArrayList(attrM);
        productM.setExtAttrs(extAttrs);
        return productM;
    }

    private Map<Long, List<Long>> buildSpuId2ProductIds(List<Long> spuIds, List<SpuProductDTO> spuProductDTOs) {
        return spuIds.stream().collect(Collectors.toMap(spuId -> spuId, spuId -> buildSpuProductIds(spuId, spuProductDTOs), (a,b) -> a));
    }

    private List<Long> buildSpuProductIds(Long spuId, List<SpuProductDTO> spuProductDTOs) {
        return spuProductDTOs.stream()
                .filter(dto -> dto != null && dto.getSpuId().equals(spuId))
                .map(SpuProductDTO::getProductId)
                .collect(Collectors.toList());
    }

    private Map<Long, Set<String>> assembleSubjectsInfo(Map<Long, List<Long>> spuId2ProductIds, List<ProductSkuDTO> products) {
        if (MapUtils.isEmpty(spuId2ProductIds) || CollectionUtils.isEmpty(products)) {
            return Maps.newHashMap();
        }
        return spuId2ProductIds.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> buildSubjects(entry.getValue(), products), (a,b) -> a));
    }

    private Set<String> buildSubjects(List<Long> productIds, List<ProductSkuDTO> productSkuDTOS) {
        if (CollectionUtils.isEmpty(productIds) || CollectionUtils.isEmpty(productSkuDTOS)) {
            return Sets.newHashSet();
        }
        return productSkuDTOS.stream()
                .filter(productSkuDTO -> productIds.contains(productSkuDTO.getProductId()))
                .map(this::parseSubjectsFromProduct)
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
    }

    private Set<String> parseSubjectsFromProduct(ProductSkuDTO productSkuDTO) {
        if (MapUtils.isEmpty(productSkuDTO.getProductInfo())
                || !productSkuDTO.getProductInfo().containsKey(MERCHANT_CUSTOM_INFORMATION)) {
            return Sets.newHashSet();
        }
        String customInfo = productSkuDTO.getProductInfo().get(MERCHANT_CUSTOM_INFORMATION);
        return parseSubjectName(customInfo);
    }

    private Set<String> parseSubjectName(String customInfo) {
        if (StringUtils.isEmpty(customInfo)) {
            return Sets.newHashSet();
        }
        Set<String> res = Sets.newHashSet();
        customInfo = customInfo.trim();
        JsonElement jsonElement = JsonParser.parseString(customInfo);
        // 数组类型（老数据结构）
        if (jsonElement.isJsonArray()) {
            JsonArray jsonArray = jsonElement.getAsJsonArray();
            if (jsonArray == null) {
                return res;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonObject jsonObject = jsonArray.get(i).getAsJsonObject();
                parseSubjectNameToSet(res, jsonObject);
            }
        }
        // 对象类型（新数据结构）
        else if (jsonElement.isJsonObject()) {
            JsonObject jsonObject = jsonElement.getAsJsonObject();
            parseSubjectNameToSet(res, jsonObject);
        }
        return res;
    }

    private void parseSubjectNameToSet(Set<String> res, JsonObject subjectObject) {
        if (subjectObject == null) {
            return;
        }
        // 检查是否存在subjectClassification字段
        if (subjectObject.get(SUBJECTCLASSIFICATION) != null && StringUtils.isNotEmpty(subjectObject.get(SUBJECTCLASSIFICATION).getAsString())) {
            res.add(subjectObject.get(SUBJECTCLASSIFICATION).getAsString());
        }
        // 检查是否存在multiSubjectClassification字段
        if (subjectObject.get(MULTISUBJECTCLASSIFICATION) != null) {
            JsonElement jsonElement = JsonParser.parseString(subjectObject.get(MULTISUBJECTCLASSIFICATION).getAsString());
            if (!jsonElement.isJsonArray()) {
                return;
            }
            JsonArray jsonArray = jsonElement.getAsJsonArray();
            if (jsonArray == null) {
                return;
            }
            for (int i = 0; i < jsonArray.size(); i++) {
                JsonArray innerArray = jsonArray.get(i).getAsJsonArray();
                parseInnerArraySubjectNameToSet(res, innerArray);
            }
        }
    }

    private void parseInnerArraySubjectNameToSet(Set<String> res, JsonArray innerArray) {
        if (innerArray == null) {
            return;
        }
        // 如果数组内只有1个元素，则认为该元素为科目
        if (innerArray.size() == 1 && StringUtils.isNotEmpty(innerArray.get(0).getAsString())) {
            res.add(innerArray.get(0).getAsString());
        }
        // 如果数组内有2个元素，则认为第2个元素为科目
        else if (innerArray.size() == 2 && StringUtils.isNotEmpty(innerArray.get(1).getAsString())) {
            res.add(innerArray.get(1).getAsString());
        }
    }

    private QuerySpuProductRequest buildQuerySpuProductIdsReq(ActivityContext context, List<Long> spuIds) {
        int mtCityId = Optional.ofNullable((Integer) context.getParam(ShelfActivityConstants.Params.mtCityId)).orElse(0);
        QuerySpuProductRequest request = new QuerySpuProductRequest();
        request.setCityId(mtCityId);
        request.setSpuIds(spuIds);
        request.setIsPlatformProduct(PLATFORM_PRODUCT);
        return request;
    }

    private ProductSkuBatchQueryRequest buildQueryPlatformProductsReq(List<Long> productIds) {
        Set<ProductQueryStrategy> queryStrategies = new HashSet<>();
        queryStrategies.add(ProductQueryStrategy.BASIC);
        queryStrategies.add(ProductQueryStrategy.ATTRIBUTE);
        queryStrategies.add(ProductQueryStrategy.SKU);

        ProductSkuBatchQueryRequest request = new ProductSkuBatchQueryRequest();
        request.setProductIds(productIds);
        request.setQueryStrategies(queryStrategies);
        request.setOwnerId(Owner.NIB_GENERAL.getValue());
        return request;
    }

    private HaimaRequest buildHaimaRequestWithCityCate(String sceneKey, Integer dpCityId, List<Integer> backCateList) {
        HaimaRequest request = buildHaimaRequest(sceneKey);
        // 后台类目需要子类目在前，父类目在后
        request.addField(BACK_CATE_ID_HAI_MA, convertList2Str(reverseList(backCateList)));
        request.setCityId(dpCityId);
        return request;
    }

    private HaimaRequest buildHaimaRequest(String sceneKey) {
        HaimaRequest request = new HaimaRequest();
        request.setSceneKey(sceneKey);
        return request;
    }

    private List<Integer> reverseList(List<Integer> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Lists.newArrayList();
        }
        return IntStream.range(0, source.size())
                .mapToObj(i -> source.get(source.size() - i - 1))
                .collect(Collectors.toList());
    }

    private String convertList2Str(List<Integer> list) {
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }
        return StringUtils.join(list, ",");
    }
}
