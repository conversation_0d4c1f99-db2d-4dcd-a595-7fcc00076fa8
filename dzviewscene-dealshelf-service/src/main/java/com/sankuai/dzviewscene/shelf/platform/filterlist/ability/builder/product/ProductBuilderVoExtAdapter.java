package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 抽象扩展点实现, 适配业务不需要实现所有扩展点接口情况
 * <p>
 * Created by liweilong06 on 2020/9/10.
 */
public abstract class ProductBuilderVoExtAdapter implements ProductBuilderVoExt {

    @Override
    public int productId(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.productId(ActivityContext,ProductM)");
        return productM.getProductId();
    }

    @Override
    public String detailJumpUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.detailJumpUrl(ActivityContext,ProductM)");
        return productM.getJumpUrl();
    }

    @Override
    public String picUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.picUrl(ActivityContext,ProductM)");
        return productM.getPicUrl();
    }

    @Override
    public DzFixPriceVO pinPrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.pinPrice(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public DzFixPriceVO cardPrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.cardPrice(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 活动标签
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public List<DzActivityTagVO> activityTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.activityTags(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 活动标签
     *
     * @param activityContext
     * @param productM
     * @param index
     * @return
     */
    @Override
    public List<DzActivityTagVO> activityTags(ActivityContext activityContext, ProductM productM, int index) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.activityTags(ActivityContext,ProductM,int)");
        return null;
    }

    /**
     * 售价定制
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String salePrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.salePrice(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 售价描述
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String salePriceDesc(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.salePriceDesc(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 原价描述
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String marketPriceDesc(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.marketPriceDesc(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 是否是商户推荐
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public boolean isShopRecommend(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.isShopRecommend(ActivityContext,ProductM)");
        return false;
    }

    /**
     * 会员价
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public DzVipPriceVO vipPrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.vipPrice(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 返券
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String coupons(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.coupons(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 商品决策标签
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public List<DzTagVO> aidDecisionTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.aidDecisionTags(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 优惠标签
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public List<String> promo(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.promo(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 商户名称
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String shopName(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.shopName(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 商户距离
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String distance(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.distance(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 商户评分
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String shopScoreStr(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.shopScoreStr(ActivityContext,ProductM)");
        return null;
    }



    /**
     * 按钮名称
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String buttonName(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.buttonName(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 按钮跳转链接
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String buttonJumpUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.buttonJumpUrl(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 是否可用
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public boolean available(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.available(ActivityContext,ProductM)");
        return false;
    }

    /**
     * 活动剩余时间（用于倒计时）
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public long activityRemainSeconds(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.activityRemainSeconds(ActivityContext,ProductM)");
        return 0;
    }

    @Override
    public String deliveryType(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.deliveryType(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 商户信息
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public ShopVO shop(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.shop(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 按钮状态
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public int buttonStatus(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.buttonStatus(ActivityContext,ProductM)");
        return 0;
    }

    /**
     * 库存
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public StockVO stock(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.stock(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 销量，数值类型
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public long saleNum(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.saleNum(ActivityContext,ProductM)");
        return 0;
    }

    /**
     * 市场价
     *
     * @param activityContext
     * @param productM
     * @return
     */
    @Override
    public String marketPrice(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.marketPrice(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public ShopInfoVO shopInfo(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.shopInfo(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String sale(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.sale(ActivityContext,ProductM)");
        return null;
    }

    public String address(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.address(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String createOrderUrl(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.createOrderUrl(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public double picScale(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.picScale(ActivityContext,ProductM)");
        return 0;
    }

    public String applyShopDesc(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.applyShopDesc(ActivityContext,ProductM)");
        return null;
    }

    public List<DzPoolInfoVO> poolInfoList(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.poolInfoList(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public List<String> productTags(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.productTags(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public Map<String, Object> extAttrs(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.extAttrs(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 适用时间属性
     * @param ctx
     * @param productM
     * @return
     */
    @Override
    public String timeAttr(ActivityContext ctx, ProductM productM){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.timeAttr(ActivityContext,ProductM)");
        return null;
    }

    /**
     * 优惠信息
     * @param ctx
     * @param productM
     * @return
     */
    @Override
    public List<DzPromoVO> promoVOList(ActivityContext ctx, ProductM productM){
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.promoVOList(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String purchase(ActivityContext ctx, ProductM productM){
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.purchase(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public String score(ActivityContext ctx, ProductM productM){
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.score(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public int star(ActivityContext ctx, ProductM productM){
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.star(ActivityContext,ProductM)");
        return 0;
    }

    @Override
    public String ranking(ActivityContext ctx, ProductM productM){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.ranking(ActivityContext,ProductM)");
        return null;
    }

    @Override
    public int productType(ActivityContext ctx, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product.ProductBuilderVoExtAdapter.productType(ActivityContext,ProductM)");
        return 0;
    }
}
