package com.sankuai.dzviewscene.shelf.framework.monitor;

import com.dianping.cat.Cat;
import com.sankuai.athena.viewscene.framework.ActivityCxt;

import java.util.concurrent.CompletableFuture;

public class AbilityExecuteMonitor {

    public static String ABILITY_PREFIX = "Ability_";

    public static <T> CompletableFuture<T> executeMonitor(CompletableFuture<T> abilityResult, ActivityCxt ctx, String abilityCode, long startTime){
        if(abilityResult == null) {
            return null;
        }
        abilityResult.thenAccept(result -> {
            Cat.newCompletedTransactionWithDuration(ABILITY_PREFIX + abilityCode,  ctx.getSceneCode(), System.currentTimeMillis() - startTime);
        });
        return abilityResult;
    }
}
