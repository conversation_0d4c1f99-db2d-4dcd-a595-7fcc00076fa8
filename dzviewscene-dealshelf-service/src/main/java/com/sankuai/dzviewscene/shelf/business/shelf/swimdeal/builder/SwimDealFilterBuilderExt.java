package com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;

import java.util.List;

/**
 * @auther: liweilong06
 * @date: 2020/9/2 12:22 下午
 */
@ExtPointInstance(name = "游泳货架货架筛选栏构造扩展点")
public class SwimDealFilterBuilderExt extends FilterBuilderExtAdapter {

    /**
     * 筛选组件样式
     *
     * @param activityContext
     * @param productType
     * @return
     */
    @Override
    public int showType(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.showType(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 2;
    }

    /**
     * 筛选标签最小展示数
     *
     * @param activityContext
     * @param productType
     * @return
     */
    @Override
    public int minShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.minShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 3;
    }

    /**
     * 子标签最小展示数
     *
     * @param activityContext
     * @param productType
     * @return
     */
    @Override
    public int childrenMinShowNum(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.childrenMinShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return 3;
    }

    @Override
    public String labs(ActivityContext activityContext, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.labs(ActivityContext,FilterBtnM)");
        return null;
    }

    /**
     * 标题标签拼接
     *
     * @param activityContext
     * @param productType
     * @param filterBtnM
     * @return
     */
    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.titleButton(ActivityContext,String,FilterBtnM)");
        return new RichLabelVO(13, "#111111", filterBtnM.getTitle());
    }

    /**
     * 子标题样式
     *
     * @param activityContext
     * @param productType
     * @param filterBtnM
     * @return
     */
    @Override
    public RichLabelVO subTitle(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.subTitle(ActivityContext,String,FilterBtnM)");
        return null;
    }

    /**
     * 子标签扩展字段
     *
     * @param activityContext
     * @param productType
     * @param filterBtnM
     * @return
     */
    @Override
    public String extra(ActivityContext activityContext, String productType, FilterBtnM filterBtnM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.extra(ActivityContext,String,FilterBtnM)");
        return null;
    }

    /**
     * 筛选组件扩展按钮
     *
     * @param activityContext
     * @param productType
     * @return
     */
    @Override
    public List<IconRichLabelVO> preFixedBtns(ActivityContext activityContext, String productType) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.preFixedBtns(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String)");
        return null;
    }

    /**
     * 没有商品是否还展示
     * @param activityContext
     * @return
     */
    @Override
    public boolean showWithNoProducts(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.builder.SwimDealFilterBuilderExt.showWithNoProducts(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return false;
    }
}
