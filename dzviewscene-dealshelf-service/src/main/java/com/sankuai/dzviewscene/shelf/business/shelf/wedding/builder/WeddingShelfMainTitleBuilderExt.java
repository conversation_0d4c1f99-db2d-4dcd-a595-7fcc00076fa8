package com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.ConstantUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;

import java.util.List;
import java.util.Map;


/**
 * 结婚货架主标题扩展点实现
 *
 * <AUTHOR>
 */
@ExtPointInstance(name = "结婚货架主标题扩展点实现")
public class WeddingShelfMainTitleBuilderExt extends MainTitleBuilderExtAdapter {

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfMainTitleBuilderExt.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return "精选商品";
    }

    @Override
    public String icon(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfMainTitleBuilderExt.icon(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return getIcon(activityContext);
    }

    private String getIcon(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfMainTitleBuilderExt.getIcon(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = getPlatform(activityContext.getParameters());
        if (PlatformUtil.isMT(platform)) {
            return ConstantUtils.SELECTED_PRODUCT_ICON_MT;
        }
        return ConstantUtils.SELECTED_PRODUCT_ICON_DP;
    }

    private int getPlatform(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfMainTitleBuilderExt.getPlatform(java.util.Map)");
        return ParamsUtil.getIntSafely(params, ShelfActivityConstants.Params.platform);
    }

    @Override
    public List<IconRichLabelVO> tags(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfMainTitleBuilderExt.tags(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return Lists.newArrayList(buildIconRichLabelVO("过期退"), buildIconRichLabelVO("随时退"));
    }

    private IconRichLabelVO buildIconRichLabelVO(String text) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingShelfMainTitleBuilderExt.buildIconRichLabelVO(java.lang.String)");
        IconRichLabelVO returnExpiredIcon = new IconRichLabelVO();
        returnExpiredIcon.setText(new RichLabelVO(text));
        returnExpiredIcon.setIcon(ConstantUtils.CHECK_MARK_ICON_GRAY);
        return returnExpiredIcon;
    }
}
