package com.sankuai.dzviewscene.shelf.business.shelf.baby.builder;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @auther: liweilong06
 * @date: 2020/12/17 12:23 下午
 */
@ExtPointInstance(name = "亲子-摄影团购货架打点信息构造扩展点")
public class BabyPhotographyOceanBuilderExt extends OceanBuilderExtAdapter {

    @Override
    public String filterBarLabs(ActivityContext activityContext) {
        return buildLabs(activityContext);
    }

    @Override
    public String moreLabs(ActivityContext activityContext) {
        return buildLabs(activityContext);
    }

    private String buildLabs(ActivityContext activityContext) {
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("poi_id", getPoiId(activityContext));
        return JsonCodec.encode(oceanMap);
    }

    @Override
    public String bookingBtnLabs(ActivityContext activityContext) {
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("poi_id", getPoiId(activityContext));
        return JsonCodec.encode(oceanMap);
    }

    public long getPoiId(ActivityContext activityContext) {
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

}
