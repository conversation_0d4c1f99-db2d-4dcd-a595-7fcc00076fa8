package com.sankuai.dzviewscene.shelf.platform.other;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.Activity;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityAbstractBuilder;

import java.util.concurrent.CompletableFuture;

/**
 * 非标准场景活动编排
 */
@SuppressWarnings("unchecked")
@Activity(code = OtherActivity.ACTIVITY_CODE, name = "非标准场景活动编排")
public class OtherActivity extends AbstractActivity<Object> {

    public static final String ACTIVITY_CODE = "OtherActivity";

    @Override
    public CompletableFuture<Object> execute(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.other.OtherActivity.execute(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");

        // 1. 流程路由, 构造数据对象, 当前以代码编排流程
        activityContext.setMainData(findActivityFlow(activityContext).execute(this, activityContext));

        // 2. 构造活动结果对象
        return buildOtherResponse(activityContext);
    }

    private CompletableFuture<Object> buildOtherResponse(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.other.OtherActivity.buildOtherResponse(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return findAbility(activityContext, OtherActivityAbstractBuilder.ABILITY_CODE).build(activityContext);
    }

}