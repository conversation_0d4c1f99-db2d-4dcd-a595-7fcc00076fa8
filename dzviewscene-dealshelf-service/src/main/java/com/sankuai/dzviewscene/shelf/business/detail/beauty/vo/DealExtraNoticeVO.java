package com.sankuai.dzviewscene.shelf.business.detail.beauty.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import java.io.Serializable;
import java.util.List;

/**模块说明
 * <AUTHOR>
 * @date 2020/11/13 4:30 下午
 */
@MobileDo(id = 0x37be)
public class DealExtraNoticeVO implements Serializable {
    /**
     * 提示内容
     */
    @MobileDo.MobileField(key = 0x8535)
    private List<String> contents;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public List<String> getContents() {
        return contents;
    }

    public void setContents(List<String> contents) {
        this.contents = contents;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
