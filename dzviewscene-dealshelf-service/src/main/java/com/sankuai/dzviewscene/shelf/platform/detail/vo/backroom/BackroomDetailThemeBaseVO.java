package com.sankuai.dzviewscene.shelf.platform.detail.vo.backroom;

import com.sankuai.dzviewscene.shelf.platform.detail.vo.roleplay.*;
import lombok.Data;

import java.util.List;

/**
 * Title: BackroomDetailThemeVO
 * Description: 密室预订详情页主题信息部分
 *
 * <AUTHOR>
 * @date 2021-01-04
 */
@Data
public class BackroomDetailThemeBaseVO {

    /**
     * 商品id
     */
    private int productId;

    /**
     * 标题
     */
    private String title;

    /**
     * 头图
     */
    private String headPic;

    /**
     * 排行榜信息
     */
    private String rank;

    /**
     * 排行榜个数信息
     */
    private String rankNum;

    /**
     * 榜单页跳转链接
     */
    private String rankPageUrl;

    /**
     * 价格
     */
    private String price;

    /**
     * 起字
     */
    private String priceDesc;

    /**
     * 竖版视频链接
     */
    private String videoUrl;

    /**
     * 横版视频
     */
    private String horizontalVideoUrl;

    /**
     * 密室难度，数值为10，20，30，40，50
     */
    private int difficultLevel;

    /**
     * 起订数目+建议人数+时长
     */
    private List<String> themeInfo;

    /**
     * 主题风格
     */
    private String style;

    /**
     * 主题标签集
     */
    private List<String> tags;

    /**
     * XX人订过
     */
    private String bookingNum;

    /**
     * 剧情简介
     */
    private String introduction;

    /**
     * 剧本杀角色介绍
     */
    private List<RoleplayDetailCharacterIntroVO> roleplayDetailCharacterIntroVO;

    /**
     * 分享功能模块
     */
    private ThemeShareVO themeShareVO;

    /**
     * 剧本标签
     * 热门、独家、城限或上新
     */
    private String roleplayLabel;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 可玩门店
     */
    private String availableShop;

    /**
     * 出版信息
     */
    private String publisher;

    /**
     * 剧本评分
     */
    private RolePlayScoreVO rolePlayScoreVO;

    /**
     * 对应标品ID
     */
    private String relatedResourceId;

    /**
     * 剧本标品榜单信息
     */
    private ResourceRankVO resourceRankVO;

    /**
     * 剧本用户维度评价信息
     */
    private UserReviewVO userReviewVO;

}
