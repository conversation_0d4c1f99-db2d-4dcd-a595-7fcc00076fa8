package com.sankuai.dzviewscene.shelf.business.other.carousel.builder;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.shelf.business.other.carousel.vo.DzCarouselOceanVO;
import com.sankuai.dzviewscene.shelf.business.other.carousel.vo.OceanEntryVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.shopcarousel.CarouselOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.other.model.CarouselM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.WeakHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@ExtPointInstance(name = "休娱酒吧拼场轮播区打点 builder")
public class JoyBarPinCarouselOceanBuilderExt implements CarouselOceanBuilderExt {

    private static final String OCEAN_CONFIG = "com.sankuai.dzviewscene.productshelf.ocean.joy_poi_pincarousel";

    @Override
    public DzCarouselOceanVO oceanVO(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.other.carousel.builder.JoyBarPinCarouselOceanBuilderExt.oceanVO(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<ProductM> productMList = loadShowProductList(ctx);
        OceanEntryVO wholeModule = new OceanEntryVO();
        wholeModule.setBidClick("b_gc_s96fmnap_mc");
        wholeModule.setBidView("b_gc_s96fmnap_mv");
        wholeModule.setCategory("gc");
        DzCarouselOceanVO dzCarouselOceanVO = new DzCarouselOceanVO();
        dzCarouselOceanVO.setWholeModule(wholeModule);
        return dzCarouselOceanVO;
    }

    private List<ProductM> loadShowProductList(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.other.carousel.builder.JoyBarPinCarouselOceanBuilderExt.loadShowProductList(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        CompletableFuture<CarouselM> mainDataCf = ctx.getMainData();
        ProductGroupM productGroupM = Optional.ofNullable(mainDataCf.join()).map(CarouselM::getProductGroup).orElse(null);
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return null;
        }
        return productGroupM.getProducts().stream().filter(product -> {
            String poolInfoStr = product.getAttr("poolInfo");
            return StringUtils.isNotEmpty(poolInfoStr);
        }).collect(Collectors.toList());
    }

}
