package com.sankuai.dzviewscene.shelf.business.filterlist.utils;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dztheme.generalproduct.enums.ProductTagTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzActivityTagVO;
import joptsimple.internal.Strings;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 医美商品活动信息填充
 * Created by zhangsuping on 2021/6/2.
 */
public class MedicalProductActivityPaddingUtils {

    private static final String PLATFORM_ACTIVITY_KEY = "platformPromoTag";
    private static final String PRODUCT_TAG_KEY = "productTagsAttr";

    public static List<DzActivityTagVO> activityTags(ProductM productM, String optimizationIcon, String directSupplyIcon, int activityIconPosition, int tagIconPosition) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.utils.MedicalProductActivityPaddingUtils.activityTags(ProductM,String,String,int,int)");
        DzActivityTagVO activityTag = buildDzActivityTag(productM.getAttr(PLATFORM_ACTIVITY_KEY), activityIconPosition);
        DzActivityTagVO productTag = buildProductTag(productM.getAttr(PRODUCT_TAG_KEY), optimizationIcon, directSupplyIcon, tagIconPosition);
        List<DzActivityTagVO> activityTags = new ArrayList<>();
        if (activityTag != null) {
            activityTags.add(activityTag);
        }
        if (productTag != null) {
            activityTags.add(productTag);
        }
        return activityTags;
    }

    private static DzActivityTagVO buildProductTag(String productTagStr, String optimizationIcon, String directSupplyIcon, int tagIconPosition) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.utils.MedicalProductActivityPaddingUtils.buildProductTag(java.lang.String,java.lang.String,java.lang.String,int)");
        List<Integer> tags = JsonCodec.decode(productTagStr, new TypeReference<List<Integer>>() {
        });
        if (CollectionUtils.isEmpty(tags)) {
            return null;
        }
        String imgUrl = getTagIcon(tags, optimizationIcon, directSupplyIcon);
        return buildDzActivityTag(imgUrl, tagIconPosition);
    }

    private static String getTagIcon(List<Integer> tags, String optimizationIcon, String directSupplyIcon) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.utils.MedicalProductActivityPaddingUtils.getTagIcon(java.util.List,java.lang.String,java.lang.String)");
        if (tags.contains(ProductTagTypeEnum.AUTHORIZE_PRODUCT.getType())) {
            return optimizationIcon;
        }
        if (tags.contains(ProductTagTypeEnum.CERTIFIED_PRODUCT.getType())) {
            return directSupplyIcon;
        }
        return Strings.EMPTY;
    }

    private static DzActivityTagVO buildDzActivityTag(String imgUrl, int activityIconPosition) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.utils.MedicalProductActivityPaddingUtils.buildDzActivityTag(java.lang.String,int)");
        if (StringUtils.isBlank(imgUrl)) {
            return null;
        }
        DzActivityTagVO dzActivityTagVO = new DzActivityTagVO();
        dzActivityTagVO.setImgUrl(imgUrl);
        dzActivityTagVO.setPosition(activityIconPosition);
        return dzActivityTagVO;
    }
}
