package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle;

import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPoint;
import com.sankuai.dzviewscene.shelf.framework.core.IExtPoint;

import java.util.List;

/**
 * Created by float on 2020/8/22.
 */
@ExtPoint(code = MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, name = "货架主标题模块")
public interface MainTitleBuilderExt extends IExtPoint {

    String EXT_POINT_MAIN_TITLE_CODE = "MainTitleBuilderExt";

    /**
     * 拼接大标题组件跳转链接
     *
     * @param activityContext
     * @return
     */
    String jumpUrl(ActivityContext activityContext);

    /**
     * 拼接大标题组件标题
     *
     * @param activityContext
     * @return
     */
    String title(ActivityContext activityContext);

    /**
     * 拼接副标题组件标题
     *
     * @param activityContext
     * @return
     */
    RichLabelVO subTitle(ActivityContext activityContext);

    /**
     * 图标
     * @param activityContext
     * @return
     */
    String icon(ActivityContext activityContext);

    /**
     * 添加标签
     * @param activityContext
     * @return
     */
    List<IconRichLabelVO> tags(ActivityContext activityContext);

}
