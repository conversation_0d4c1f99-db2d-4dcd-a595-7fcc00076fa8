package com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.ListUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 多路召回融合排序实现
 *
 * @see <a href="https://zhuanlan.zhihu.com/p/90796257"></a>
 *
 * Created by float on 2020/9/12.
 */
public interface MultiplexQuery extends Query {

    /**
     * 按指定的多组单路召回器实现多路召回, 每路单独召回、排序和截断, 最后按顺序融合
     *
     * @param context
     * @return
     */
    default List<ProductM> query(QueryContext context) {
        if (context == null || CollectionUtils.isEmpty(context.getPool())) {
            return Lists.newArrayList();
        }

        // 防止修改原有列表
        context.setPool(copyProductPool(context.getPool()));

        List<SimplexQuery> queryList = loadQuery();

        for (Query query : queryList) {

            List<ProductM> queryResult = doQuery(context, query);

            context.getMatched().addAll(queryResult);

            context.setPool(ListUtils.subtract(context.getPool(), queryResult));
        }

        return context.getMatched().stream().sorted(comparator(context)).limit(nonNegative(topN(context))).collect(Collectors.toList());
    }

    default int nonNegative(int num) {
        return num < 0 ? 0 : num;
    }

    default List<ProductM> doQuery(QueryContext context, Query query) {
        try {
            return query.query(context);
        } catch (Exception e) {
            throw new BusinessException(String.format("执行多路召回=%s, 发生异常", query.getClass().getName()), e);
        }
    }

    default List<ProductM> copyProductPool(List<ProductM> products) {
        List<ProductM> candidate = new ArrayList<>();
        products.forEach(productM -> candidate.add(productM));
        return candidate;
    }

    /**
     * 召回策略列表
     */
    List<SimplexQuery> loadQuery();

    /**
     * 整体排序规则
     *
     * @return
     */
    default Comparator<ProductM> comparator(QueryContext context) {
        return ((o1, o2) -> 0);
    }

    /**
     * 融合后截取数
     *
     * @return
     */
    default int topN(QueryContext context) {
        return Integer.MAX_VALUE;
    }

}
