package com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist.ProductListBuilderExtAdapter;


@ExtPointInstance(name = "密室在拼场次列表构造扩展实现")
public class BackroomPoolScenesListProductListBuilder extends ProductListBuilderExtAdapter {

    @Override
    public String title(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.filterlist.backroom.themezone.builder.BackroomPoolScenesListProductListBuilder.title(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return "在拼场次";
    }
}
