package com.sankuai.dzviewscene.shelf.business.detail.roleplay.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPinPoolM;
import com.sankuai.dzviewscene.shelf.platform.detail.ability.builder.ProductDetailBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.detail.vo.DzPinPoolBarVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

@ExtPointInstance(name = "剧本杀拼场小黄条扩展实现")
public class JoyPinPoolBarBuilderExt implements ProductDetailBuilderExt {

    @Override
    public Object buildDetail(ActivityContext activityContext, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.roleplay.builder.JoyPinPoolBarBuilderExt.buildDetail(ActivityContext,ProductM)");
        if (CollectionUtils.isEmpty(productM.getPinPools())) {
            return null;
        }

        ProductPinPoolM poolM = productM.getPinPools().get(0);
        String toAdd = String.valueOf(poolM.getMaxNum() - poolM.getCurrentNum());
        String diff = String.valueOf(poolM.getMinNum() - poolM.getCurrentNum());
        List<RichLabelVO> progressDesc;
        if (poolM.getCurrentNum() >= poolM.getMinNum()) {
            progressDesc = Lists.newArrayList(
                    new RichLabelVO(12, "#777777",String.format("当前有%s场在拼，最近一场还可加入", productM.getPinPools().size())),
                    new RichLabelVO(12,"#FF6200",toAdd),
                    new RichLabelVO(12, "#777777","人"));
        } else {
            progressDesc = Lists.newArrayList(
                    new RichLabelVO(12, "#777777",String.format("当前有%s场在拼，最近一场差", productM.getPinPools().size())),
                    new RichLabelVO(12,"#FF6200",diff),
                    new RichLabelVO(12, "#777777","人即可开场"));
        }

        Map<String,Object> popParams = Maps.newHashMap();
        popParams.put("scenecode", "joy_role_play_pin_pop");
        popParams.put("productidlist", JsonCodec.encode(Lists.newArrayList(productM.getProductId())));
        DzPinPoolBarVO dzPinPoolBarVO = new DzPinPoolBarVO();
        dzPinPoolBarVO.setCurrentProgressDesc(progressDesc);
        dzPinPoolBarVO.setPopParams(popParams);
        dzPinPoolBarVO.setJumpText("立即加入");
        return dzPinPoolBarVO;
    }
}
