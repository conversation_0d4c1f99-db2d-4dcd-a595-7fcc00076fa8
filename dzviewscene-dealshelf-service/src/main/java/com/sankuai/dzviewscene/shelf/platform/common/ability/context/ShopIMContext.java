package com.sankuai.dzviewscene.shelf.platform.common.ability.context;

import com.dianping.cat.Cat;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.business.detail.edu.EduCourseDetailContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextExt;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.MapUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 商户的IM信息获取Context
 * @auther: liweilong06
 * @date: 2020/10/23 5:26 下午
 */
@ContextExt(name = "shopIMContext")
public class ShopIMContext implements IContextExt<Map<String, String>> {

    public static final String CONTEXT_KEY = "shopIMContext";

    /**
     * Im链接
     */
    public static final String KEY_IM_URL = "shopImUrl";

    @Resource
    private CompositeAtomService compositeAtomService;

    /**
     * 填充到ActivityContext#extContext中的key
     *
     * @return
     */
    @Override
    public String contextKey() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext.contextKey()");
        return CONTEXT_KEY;
    }

    /**
     * 填充到ActivityContext#extContext中的值
     *
     * @param activityContext
     * @return
     */
    @Override
    public CompletableFuture<Map<String, String>> contextExt(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext.contextExt(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        ClientEntryReqDTO reqDTO = buildClientEntryReqDTO(activityContext);
        try {
            CompletableFuture<ClientEntryDTO> future = compositeAtomService.getImInfo(reqDTO);
            return future.thenApply(clientEntryDTO -> {
                Map<String, String> returnValue = new HashMap<>();
                if (clientEntryDTO == null) {
                    returnValue.put(KEY_IM_URL, "");
                    return returnValue;
                }
                returnValue.put(KEY_IM_URL, clientEntryDTO.getEntryUrl());
                return returnValue;
            });
        } catch (Exception e) {
            Cat.logEvent("shopIMContext", e.getMessage());
            return CompletableFuture.completedFuture(MapUtils.EMPTY_MAP);
        }
    }

    private ClientEntryReqDTO buildClientEntryReqDTO(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext.buildClientEntryReqDTO(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");

        Long dpShopId = PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
        ClientEntryReqDTO reqDTO = new ClientEntryReqDTO();
        reqDTO.setDpPoiId(dpShopId.intValue());
        reqDTO.setDpShopId(dpShopId);
        reqDTO.setClientType(getClientType(activityContext));
        reqDTO.setShopType(EduCourseDetailContext.EDU_SHOP_TYPE);
        return reqDTO;
    }

    private int getClientType(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext.getClientType(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        String clientType = activityContext.getParam(ShelfActivityConstants.Params.clientType);
        if (PlatformUtil.isMT(platform)) {
            return getClientTypeForMt(platform, clientType);

        }
        return getClientTypeForDp(platform, clientType);
    }

    private int getClientTypeForDp(int platform, String clientType) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext.getClientTypeForDp(int,java.lang.String)");
        if (fromApp(platform)) {
            if ("iso".equals(clientType)) {
                return ClientTypeEnum.dp_mainApp_ios.getType();
            }
            return ClientTypeEnum.dp_mainApp_android.getType();
        }
        return ClientTypeEnum.dp_wap.getType();
    }

    private int getClientTypeForMt(int platform, String clientType) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext.getClientTypeForMt(int,java.lang.String)");
        if (fromApp(platform)) {
            if ("iso".equals(clientType)) {
                return ClientTypeEnum.mt_mainApp_ios.getType();
            }
            return ClientTypeEnum.mt_mainApp_android.getType();
        }
        return ClientTypeEnum.mt_wap.getType();
    }

    private boolean fromApp(int platform) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.common.ability.context.ShopIMContext.fromApp(int)");
        if (platform == 100 || platform == 200) {
            return true;
        }
        return false;
    }

}