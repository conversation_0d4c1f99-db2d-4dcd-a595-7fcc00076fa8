package com.sankuai.dzviewscene.shelf.business.shelf.car;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.validator.ShopStatusValidator;
import com.sankuai.dzviewscene.shelf.business.shelf.car.builder.*;
import com.sankuai.dzviewscene.shelf.business.shelf.car.fetcher.CarDealShelfFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.MultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformMultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultiplexQueryFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.MergeMultiABQueryFilterLastShelfFlow;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/15 9:12 下午
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "car_poi_deal_shelf_with_wash_beauty_car_filter", name = "洗车美容含有二级筛选的爱车团单货架模板")
public class CarDealShelfWithWashBeautyCarFilterTemplate implements IActivityTemplate {

    private static final String SCENECODE = "car_poi_deal_shelf_with_wash_beauty_car_filter";

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.car.deal.shelf.with.wash.beauty.car.filter.constant.config", defaultValue = "")
    public ConstantConfig constantConfig;

    /**
     * 洗车团单的属性-适用车型 [新老上单均有]
     */
    public static final String ATTR_WASH_CAR_SUIT_TYPE = "suitable_car_type_2";

    /**
     * 洗车团单的属性-适用车型 [老版上单有值]
     */
    public static final String ATTR_CAR_TYPE_LIMIT = "car_type_limit";

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.flow()");
        return MergeMultiABQueryFilterLastShelfFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.extParams(java.util.Map)");
        // 1. 设置召回分组参数, 代表只召回一组商品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.deal.name()));

        // 2. 设置商品召回填充参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.deal.name(), new HashMap<String, Object>() {{
                // 2.1 召回渠道为门店在线团单
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.shopOnLineDealsQuery.name());
                // 2.2 填充渠道为团单主题填充
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                put(PaddingFetcher.Params.planId, "10002144");
                put(MergeQueryFetcher.Params.rankId, "10100012");
                put("enablePreSalePromoTag", true);
                put("directPromoScene", 400005);
                put("attributeKeys", new ArrayList<String>(){{
                    //私人住宅属性名
                    add("engine_oil_type");
                    //公共住宅属性名
                    add("engine_oil_capacity");
                    add("service_type");
                    //精洗副标题
                    add("delicacy_subtitle");
                    //洗车团单·适用车型
                    add(ATTR_WASH_CAR_SUIT_TYPE);
                    add(ATTR_CAR_TYPE_LIMIT);
                    //玻璃贴膜前挡隔热
                    add("front_bumper_heat_insulation_rate");
                    //玻璃贴膜后挡隔热
                    add("side_rear_block_heat_insulation_rate");
                    //超值预售标签
                    add("preSaleTag");
                }});
                put("scene", 200000);
            }});
        }});
        //3. 设置showType
        exParams.put(ShelfActivityConstants.Style.showType, 20);
        //4. 设置首屏访问
        exParams.put(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfFirstLoad);
        //5. 设置斗斛参数
        exParams.put(MultiDouHuFetcher.Params.MT_EXP_IDS, DouHuUtils.getMTDouHuExpIds(SCENECODE, getDouhuExpName()));
        exParams.put(MultiDouHuFetcher.Params.DP_EXP_IDS, DouHuUtils.getDPDouHuExpIds(SCENECODE, getDouhuExpName()));
        exParams.put(DouHuFetcher.Params.MT_HIT_EXP_ID, getMtDouhuHitStr());
        exParams.put(DouHuFetcher.Params.DP_HIT_EXP_ID, getDpDouhuHitStr());
    }

    private String getDpDouhuHitStr() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.getDpDouhuHitStr()");
        if(constantConfig == null) {
            return null;
        }
        return constantConfig.getDpDouhuHitStr();
    }

    private String getMtDouhuHitStr() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.getMtDouhuHitStr()");
        if(constantConfig == null) {
            return null;
        }
        return constantConfig.getMtDouhuHitStr();
    }

    private List<String> getDouhuExpName() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.getDouhuExpName()");
        if(constantConfig == null) {
            return null;
        }
        if (CollectionUtils.isNotEmpty(constantConfig.getExpNames())) {
            return constantConfig.getExpNames();
        }
        if (StringUtils.isNotBlank(constantConfig.getExpName())) {
            return Lists.newArrayList(constantConfig.getExpName());
        }
        return null;
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.extContexts(java.util.List)");
        extContexts.add(ActivityContextExt.class);
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.extValidators(java.util.List)");
        validators.add(ShopStatusValidator.class);
    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.extAbilities(java.util.Map)");
        // 默认筛选取数器
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, MultiplexQueryFilterFetcher.class);
        // 默认召回能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        // 注册融合查询能力
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
        // 注册批量斗斛查询能力
        abilities.put(MultiDouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformMultiDouHuFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.car.CarDealShelfWithWashBeautyCarFilterTemplate.extPoints(java.util.Map)");
        // 筛选构造扩展点
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, CarDealShelfFilterFetcherExt.class);
        // 筛选VO构造扩展点
        extPoints.put(FilterBuilderExt.EXT_POINT_FILTER_COMPONENT_CODE, CarDealShelfFilterBuilderExt.class);
        // 货架楼层VO构造扩展点
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, CarDealWithWashBeautyCarFilterFloorsBuilderExt.class);
        // 注册货架主标题模块构造扩展点实现
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, CarDealShelfMainTitleBuilderExt.class);
        // 商品填充后处理扩展点
        extPoints.put(PaddingFetcherExt.EXT_POINT_PADDING_FETCHER_CODE, CarDealShelfPostPaddingExt.class);
    }

    @Data
    private static class ConstantConfig {
        @Deprecated
        private String expName;
        private List<String> expNames;
        private String dpDouhuHitStr;
        private String mtDouhuHitStr;
    }
}
