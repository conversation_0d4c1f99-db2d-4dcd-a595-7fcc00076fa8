package com.sankuai.dzviewscene.shelf.business.detail.edu;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduNavAndProductShelfH5ResponseBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivity;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityAbstractBuilder;
import com.sankuai.dzviewscene.shelf.platform.other.ability.flow.NavAndProductShelfOtherActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 教培查看更多落地页-首次加载-H5模板-V2
 * 区别：去掉了课程模块，导航换成新版，副标题文案换了
 * <AUTHOR>
 */
@ActivityTemplate(activityCode = OtherActivity.ACTIVITY_CODE, sceneCode = "edu_poi_deal_course_shelf_landing_filter_v2", name = "教培查看更多落地页-首次加载-H5模板")
public class EduNavAndProductsShelfH5V2Template implements IActivityTemplate {

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.edu.deal.productattrtagwithcategory",
            defaultValue = "[\"learning_target_select_v2\",\"eduFitAgeToC\", \"eduFitGradeToC\", \"study_target_education_2\", \"application_stage_admission_counseling_select\", \"application_stage_select\", \"class_type_with_two_select\", \"class_type_with_three_select\", \"class_type_with_five_select\", \"physical_inspection_item\", \"service_efficacy\", \"ticke_usertime\", \"ticket_usernumbers\", \"baby_early_edu_fit_age\", \"baby_early_edu_lesson\", \"baby_early_edu_class_size\", \"number_of_people\", \"deal_scene\", \"private_movie_servicetime\", \"recommend_person_count\", \"self_limit_time\", \"newHouse_envir_level\", \"newHouse_style\", \"physical_examination_cash_type\", \"recommend_person_count_num\", \"recommend_person_count_scope\", \"body_type_pet_dog\", \"wegith_pet_cat\", \"hair_length_pet_dog\", \"hair_length_pet_cat\", \"available_count_pet\", \"available_time_pet\"]")
    private List<String> attributes;

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.flow()");
        return NavAndProductShelfOtherActivityFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.extParams(java.util.Map)");
        // 1. 设置召回参数，召回两组商品：团购和课程
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList("团购"));

        // 2. 设置商品平台组件和商品组映射关系
        exParams.put(QueryFetcher.Params.productComponent2Group, new HashMap<String, String>() {{
            put("团购", "团购");
        }});

        exParams.put(FilterFetcher.Params.filterComponent2Group, new HashMap<String, String>() {{
            put("团购", "团购");
            //兼容逻辑，因为商品平台也有灰度开关
            put("选课中心", "团购");
        }});
        // 4. 设置商品组召回和填充参数
        exParams.put(QueryFetcher.Params.groupParams, getComponentGroupParamsMap(exParams));
    }

    /**
     * 召回商品组的参数
     */
    private Map<String, Object> getComponentGroupParamsMap(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.getComponentGroupParamsMap(java.util.Map)");
        return new HashMap<String, Object>(1) {{
            put("团购", new HashMap<String, Object>(8) {{
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                put(PaddingFetcher.Params.planId, "10002180");
                put("tcMergePromoTemplateId", 226);
                put("promoTemplateId", 145);
                put("attributeKeys", attributes);
                put(PaddingFetcher.Params.shopCategory, getShopCategory(exParams));
            }});
        }};
    }


    private Integer getShopCategory(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.getShopCategory(java.util.Map)");
        Object o = exParams.get(OtherActivityConstants.Ctx.ctxShop);
        if (o == null || !(o instanceof ShopM)) {
            return 0;
        }

        ShopM shopM = (ShopM) exParams.get(OtherActivityConstants.Ctx.ctxShop);
        return shopM.getCategory();
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.extContexts(java.util.List)");
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.extAbilities(java.util.Map)");
        // 0. 注册融合查询能力
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
        // 1. 商品列表召回能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, ShelfQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 3. 教培查看更多落地页-Response构造能力
        abilities.put(OtherActivityAbstractBuilder.ABILITY_CODE, EduNavAndProductShelfH5ResponseBuilderExt.class);
        // 4. filter查询能力
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, ShelfFilterFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5V2Template.extPoints(java.util.Map)");
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, ShelfFilterFetcherExt.class);
    }

}