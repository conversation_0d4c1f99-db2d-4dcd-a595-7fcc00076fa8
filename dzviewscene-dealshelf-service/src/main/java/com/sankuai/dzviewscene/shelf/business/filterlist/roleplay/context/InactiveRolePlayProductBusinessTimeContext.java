package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context;

import com.dianping.cat.Cat;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.google.common.collect.Maps;
import com.sankuai.athena.nr.atom.ProductAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextExt;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@ContextExt(name = "桌面剧本杀门店营业结束时间")
public class InactiveRolePlayProductBusinessTimeContext implements IContextExt<Map<String, String>> {

    public static final String CONTEXT_KEY = "inactiveRolePlayProductBusinessEndTimeContext";

    // 门店的营业结束时间和开始时间
    private static final String PRODUCT_BUSINESS_BEGIN_TIME_ATTR = "saleBeginTime";

    private static final String PRODUCT_BUSINESS_END_TIME_ATTR = "saleEndTime";

    @Resource
    private ProductAtomService productAtomService;

    @Override
    public String contextKey() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.InactiveRolePlayProductBusinessTimeContext.contextKey()");
        return CONTEXT_KEY;
    }

    @Override
    public CompletableFuture<Map<String, String>> contextExt(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.InactiveRolePlayProductBusinessTimeContext.contextExt(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<Integer> productIds = (List<Integer>) Optional.ofNullable(activityContext.getParam(FilterListActivityConstants.Params.productIdList)).orElse(null);
        if (CollectionUtils.isEmpty(productIds)) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Product> productFuture = (CompletableFuture<Product>) productAtomService.getBaseProductById(productIds.get(0));
        return productFuture.thenApply(product -> {
            if (product == null) {
                return null;
            }
            return buildProductBusinessTimeAttrKey2ValueMap(product);
        });
    }

    private Map<String, String> buildProductBusinessTimeAttrKey2ValueMap(Product product) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.context.InactiveRolePlayProductBusinessTimeContext.buildProductBusinessTimeAttrKey2ValueMap(Product)");
        Map<String, String> businessTimeAttrKey2ValueMap = Maps.newHashMap();
        businessTimeAttrKey2ValueMap.put(PRODUCT_BUSINESS_BEGIN_TIME_ATTR, product.getFirstAttrValue(PRODUCT_BUSINESS_BEGIN_TIME_ATTR));
        businessTimeAttrKey2ValueMap.put(PRODUCT_BUSINESS_END_TIME_ATTR, product.getFirstAttrValue(PRODUCT_BUSINESS_END_TIME_ATTR));
        return businessTimeAttrKey2ValueMap;
    }
}
