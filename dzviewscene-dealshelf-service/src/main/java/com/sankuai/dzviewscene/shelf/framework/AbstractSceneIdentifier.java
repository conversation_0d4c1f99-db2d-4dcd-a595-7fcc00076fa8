package com.sankuai.dzviewscene.shelf.framework;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.stability.faulttolerance.tracer.ExceptionTracer;
import com.sankuai.dzviewscene.shelf.framework.core.ISceneIdentifier;
import com.sankuai.dzviewscene.shelf.framework.core.IScenePredicate;
import com.sankuai.dzviewscene.shelf.framework.exception.SceneIdentifyException;
import org.apache.commons.lang.StringUtils;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 抽象场景识别器实现
 * <p>
 * Created by float on 2020/8/22.
 */
public abstract class AbstractSceneIdentifier implements ISceneIdentifier {

    private String SCENE_CODE_NAME = "sceneCode";

    @Override
    public String identify(ActivityContext activityContext) {
        String sceneCode = identifySceneCode(activityContext);
        if (StringUtils.isEmpty(sceneCode)) {
            throw new SceneIdentifyException(String.format("识别上下文场景=%s, 识别失败", JsonCodec.encode(activityContext)));
        }
        return sceneCode;
    }

    private String identifySceneCode(ActivityContext activityContext) {
        try {
            /**
             * 场景识断言
             */
            Map<String, IScenePredicate> scenePredicates = new LinkedHashMap<>();
            fillPredicates(scenePredicates);
            // 1. 如果参数指定场景, 则优先使用传入场景ID
            if (activityContext.getParam(SCENE_CODE_NAME) != null
                    && scenePredicates.containsKey(activityContext.getParam(SCENE_CODE_NAME))) {
                return activityContext.getParam(SCENE_CODE_NAME);
            }
            // 2. 否则根据注册逻辑识别场景
            return scenePredicates.entrySet()
                    .stream().filter(scenePredicateEntry -> scenePredicateEntry.getValue() != null)
                    .filter(scenePredicateEntry -> scenePredicateEntry.getValue().test(activityContext))
                    .findFirst()
                    .map(scenePredicateEntry -> scenePredicateEntry.getKey())
                    .orElse(StringUtils.EMPTY);
        } catch (Exception e) {
            ExceptionTracer.addException(String.format("%s_%s", "AbstractSceneIdentifier","identifySceneCode"), e);
            throw e;
        }
    }

    /**
     * 实现该方法提供场景
     *
     * @return
     */
    protected abstract void fillPredicates(Map<String, IScenePredicate> scenePredicates);
}
