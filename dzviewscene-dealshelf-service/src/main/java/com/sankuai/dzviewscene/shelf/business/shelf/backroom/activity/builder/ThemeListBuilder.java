package com.sankuai.dzviewscene.shelf.business.shelf.backroom.activity.builder;

import com.sankuai.dzviewscene.shelf.business.shelf.backroom.vo.BackroomThemeListVO;
import com.sankuai.dzviewscene.shelf.framework.AbstractAbility;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.Ability;
import com.sankuai.dzviewscene.shelf.framework.core.IExtPoint;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/9/18.
 */
@Ability(code = ThemeListBuilder.ABILITY_THEME_LIST_CODE, name = "密室预订商品列表VO构造能力")
public abstract class ThemeListBuilder extends AbstractAbility<BackroomThemeListVO, IExtPoint> {

    public static final String ABILITY_THEME_LIST_CODE = "ThemeListBuilder";

}
