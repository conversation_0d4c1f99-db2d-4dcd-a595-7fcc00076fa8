package com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.AbstractConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/9/10
 */
@ExtPointInstance(name = "小区货架筛选组件扩展点")
public class CommunityFilterFetcherExt extends AbstractConfigFilterFetcherExt {

    /*召回的时候填充的标签, 用于分组*/
    private static final String ATTR_GROUP_TAG = "tabId";

    @Override
    public Map<Long, List<ProductM>> groupByFilter(ActivityContext activityContext, String groupName, FilterM filterM, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityFilterFetcherExt.groupByFilter(ActivityContext,String,FilterM,ProductGroupM)");
        if (filterM == null || productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return new HashMap<>();
        }
        return multiGroupProducts(filterM, productGroupM);
    }

    private Map<Long, List<ProductM>> multiGroupProducts(FilterM filterM, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityFilterFetcherExt.multiGroupProducts(FilterM,ProductGroupM)");
        Map<Long, List<ProductM>> returnValue = new HashMap<>();
        for (FilterBtnM filterBtnM : filterM.getFilters()) {
            returnValue.put(filterBtnM.getFilterId(), getSingleProducts(filterBtnM.getFilterId(), productGroupM.getProducts()));
        }
        return returnValue;
    }

    private List<ProductM> getSingleProducts(long filterId, List<ProductM> products) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityFilterFetcherExt.getSingleProducts(long,java.util.List)");
        if (CollectionUtils.isEmpty(products)) {
            return Lists.newArrayList();
        }
        return products.stream().filter(productM -> matchByFilterId(filterId, productM)).collect(Collectors.toList());
    }

    private boolean matchByFilterId(long filterId, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.communitydeal.builder.CommunityFilterFetcherExt.matchByFilterId(long,com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        List<AttrM> attrMS = productM.getExtAttrs();
        if (CollectionUtils.isEmpty(attrMS)) {
            return false;
        }
        // 根据tabId进行分组
        List<AttrM> tabIdAttrMS = attrMS.stream().filter(attrM -> ATTR_GROUP_TAG.equals(attrM.getName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(tabIdAttrMS)) {
            return false;
        }
        return NumberUtils.toLong(tabIdAttrMS.get(0).getValue()) == filterId;
    }

}
