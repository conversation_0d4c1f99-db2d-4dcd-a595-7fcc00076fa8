package com.sankuai.dzviewscene.shelf.business.detail.edu.context;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.productshelf.vu.utils.SchemaUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ContextExt;
import com.sankuai.dzviewscene.shelf.framework.core.IContextExt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 适用商户的Context
 * @auther: liweilong06
 * @date: 2020/10/23 5:26 下午
 */
@ContextExt(name = "availableShopContext")
public class AvailableShopContext implements IContextExt<Map<String, String>> {

    public static final String CONTEXT_KEY = "availableShopContext";

    @Resource
    private CompositeAtomService compositeAtomService;

    /**
     * 填充到ActivityContext#extContext中的key
     *
     * @return
     */
    @Override
    public String contextKey() {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.edu.context.AvailableShopContext.contextKey()");
        return CONTEXT_KEY;
    }

    /**
     * 填充到ActivityContext#extContext中的值
     *
     * @param activityContext
     * @return
     */
    @Override
    public CompletableFuture<Map<String, String>> contextExt(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.context.AvailableShopContext.contextExt(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int productId = NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.productId) + "", 0);
        try {
            return compositeAtomService.batchGetAvailableShopIds(Lists.newArrayList(productId)).thenApply(productId2ShopIds -> {
                Map<String, String> returnValue = new HashMap<>();
                // 全部适用商户链接
                returnValue.put("totalShopListUrl", getTotalShopListUrl(activityContext));
                if (MapUtils.isEmpty(productId2ShopIds)) {
                    // 总共多少适用商户
                    returnValue.put("totalShopNum", "0");
                    return returnValue;
                }
                // 总共多少适用商户
                returnValue.put("totalShopNum", getTotalShopNumStr(productId2ShopIds, productId));
                return returnValue;
            });
        } catch (Exception e) {
            Cat.logEvent("avalibaleShopContext", e.getMessage());
            return CompletableFuture.completedFuture(MapUtils.EMPTY_MAP);
        }
    }

    private String getTotalShopListUrl(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.context.AvailableShopContext.getTotalShopListUrl(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int productId = NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.productId) + "", 0);
        int platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        if (PlatformUtil.isMT(platform)) {
            return SchemaUtils.getMTMDomain() + "/jiaoyupeixun/fit" + productId + "/";
        }
        return SchemaUtils.getDPMDomain() + "/edu/" + productId + "/shops";
    }

    private String getTotalShopNumStr(Map<Integer, List<Integer>> productId2ShopIds, Integer productId) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.context.AvailableShopContext.getTotalShopNumStr(java.util.Map,java.lang.Integer)");
        if (MapUtils.isEmpty(productId2ShopIds) || productId2ShopIds.get(productId) == null) {
            return "0";
        }
        return productId2ShopIds.get(productId).size() + "";
    }
}
