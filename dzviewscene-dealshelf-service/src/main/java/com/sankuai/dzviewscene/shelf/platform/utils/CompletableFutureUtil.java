package com.sankuai.dzviewscene.shelf.platform.utils;

import com.google.common.collect.Maps;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class CompletableFutureUtil {
    public static <U, V> CompletableFuture<Map<U, V>> each(Map<U, CompletableFuture<V>> productGroupCompletableMap) {
        CompletableFuture<Map<U, V>> overallResult = new CompletableFuture();
        if(productGroupCompletableMap == null) {
            return overallResult;
        }
        CompletableFuture.allOf(productGroupCompletableMap.values().toArray(new CompletableFuture[0])).whenComplete((noUsed, exception) -> {
            if (exception != null) {
                overallResult.completeExceptionally(exception);
            } else {
                Map<U, V> results = Maps.newHashMap();
                productGroupCompletableMap.forEach((key, value) -> {
                    results.put(key, value.join());
                });
                overallResult.complete(results);
            }
        });
        return overallResult;
    }
    public static CompletableFuture<Object> covert2ObjCf(CompletableFuture<?> completableFuture) {
        if (completableFuture == null) {
            return CompletableFuture.completedFuture(null);
        }
        return completableFuture.thenApply(t -> t);
    }
}
