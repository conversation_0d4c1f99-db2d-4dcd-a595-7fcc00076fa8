package com.sankuai.dzviewscene.shelf.business.detail.beauty.builder;

import com.sankuai.dzviewscene.shelf.business.detail.beauty.vo.DealDetailStructModuleVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

/**
 * <AUTHOR>
 * @description 丽人美发美甲团详页VO构造
 * @date 2020/11/17 8:53 下午
 */
public interface StructModelBuilder {

    /**
     * 判断属于那种类型的团详页：美发/美甲、特殊结构化/新结构化
     *
     * @param productM 团详页内容
     * @return 是否属于this类型的团详页内容
     */
    boolean test(ProductM productM);

    /**
     * 构造团详页VO
     *
     * @param productM        团详页内容
     * @param activityContext
     * @return 团详页VO
     */
    DealDetailStructModuleVO build(ProductM productM, ActivityContext activityContext);

}
