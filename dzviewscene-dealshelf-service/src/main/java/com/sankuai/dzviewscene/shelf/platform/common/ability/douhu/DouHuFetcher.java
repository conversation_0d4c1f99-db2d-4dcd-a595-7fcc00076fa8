package com.sankuai.dzviewscene.shelf.platform.common.ability.douhu;

import com.sankuai.dzviewscene.shelf.framework.AbstractAbility;
import com.sankuai.dzviewscene.shelf.framework.annotation.Ability;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;

/**
 * 只负责构造查询斗斛数据
 * <p>
 * Created by float on 2020/8/22.
 */
@Ability(code = DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, name = "斗斛数据查询能力")
public abstract class DouHuFetcher extends AbstractAbility<DouHuM, DouHuFetcherExt> {

    public static final String ABILITY_FETCHER_DOU_HU_CODE = "DouHuFetcher";

    /**
     * 能力接收的参数名
     */
    public interface Params {

        /**
         * 1. 点评指定策略ID名
         */
        String DP_EXP_ID = "DP_EXP_ID";

        /**
         * 2. 美团指定策略ID名
         */
        String MT_EXP_ID = "MT_EXP_ID";

        /**
         * 3. 点评命中策略ID名
         */
        String DP_HIT_EXP_ID = "DP_HIT_EXP_ID";

        /**
         * 4. 美团命中策略ID名
         */
        String MT_HIT_EXP_ID = "MT_HIT_EXP_ID";

        /**
         * 5. 分流模式
         */
        String SHUNT_MODE = "SHUNT_MODE";
    }

    /**
     * 斗斛的分流模式，枚举参考创建实验的选项。
     * http://douhu.sankuai.com/config/abtest/create/0.html
     * 应用 {@link DifferShuntModeDouHuFetcher}
     */
    public interface ShuntMode {
        /**
         * 统一设备ID
         */
        int UNION_ID = 0;

        /**
         * 用户ID
         */
        int USER_ID = 1;
    }
}
