package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;

import java.util.List;
import java.util.Map;

/**
 * Created by float.lu on 2020/8/26.
 */
@ExtPointInstance(name = "默认货架楼层区构造扩展点")
public class ShelfFloorsBuilderExt extends FloorsBuilderExtAdapter {

    @Override
    public Map<Long, List<ProductM>> groupByFilter(ActivityContext activityContext, String groupName, FilterM filterM, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.groupByFilter(ActivityContext,String,FilterM,ProductGroupM)");
        return null;
    }

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.floorDefaultShowNum(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String,java.util.List)");
        return 0;
    }

    @Override
    public int floorShowType(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.floorShowType(ActivityContext,String,ProductGroupM)");
        return 0;
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, DzItemAreaComponentVO ItemAreaComponentVO) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.moreComponentText(ActivityContext,String,ProductGroupM,DzItemAreaComponentVO)");
        return null;
    }

    @Override
    public String moreComponentJumpUrl(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.moreComponentJumpUrl(ActivityContext,String,ProductGroupM)");
        return null;
    }

    @Override
    public String titleComponentTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.titleComponentTitle(ActivityContext,String,ProductGroupM)");
        return null;
    }

    @Override
    public String titleComponentSubTitle(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.titleComponentSubTitle(ActivityContext,String,ProductGroupM)");
        return null;
    }

    @Override
    public String titleComponentIcon(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.titleComponentIcon(ActivityContext,String,ProductGroupM)");
        return null;
    }

    @Override
    public List<IconRichLabelVO> titleComponentTags(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.titleComponentTags(ActivityContext,String,ProductGroupM)");
        return null;
    }

    @Override
    public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentPurchase(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public List<RichLabelVO> itemComponentBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentBottomTags(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public DzSimpleButtonVO itemComponentButton(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentButton(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentPic(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public FloatTagVO itemComponentPreTitleTag(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentPreTitleTag(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public List<DzTagVO> itemComponentPriceBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentPriceBottomTags(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public List<DzPromoVO> itemComponentPromoTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentPromoTags(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public VipPriceVO itemComponentVipPrice(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentVipPrice(ActivityContext,String,ProductM)");
        return null;
    }

    /**
     * 打点的labs
     *
     * @param activityContext
     * @param groupName
     * @param productM
     * @param index
     * @return
     */
    @Override
    public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentLabs(ActivityContext,String,ProductM,int)");
        return null;
    }

    @Override
    public String itemComponentSalePriceDesc(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentSalePriceDesc(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public String itemComponentMarketPriceDesc(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.itemComponentMarketPriceDesc(ActivityContext,String,ProductM)");
        return null;
    }

    @Override
    public String jumpUrl(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilderExt.jumpUrl(ActivityContext,String,ProductM)");
        return null;
    }
}
