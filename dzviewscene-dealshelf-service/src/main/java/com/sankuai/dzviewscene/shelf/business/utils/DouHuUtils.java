package com.sankuai.dzviewscene.shelf.business.utils;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.config.LionObject;
import com.google.common.collect.Lists;
import com.sankuai.athena.client.executor.Futures;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.douhu.absdk.bean.DouHuResponse;
import com.sankuai.douhu.absdk.util.DouHuUtil;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class DouHuUtils {

    private static final String DOUHU_HIT_EXP_FORMAT = "%s%s";

    private static final int timeout = 100;

    private static final String SEPERATOR = ",";

    @Resource
    private AtomFacadeService atomFacadeService;

    private static LionObject<DouHuExpConfigList> douHuExpConfigLionObject = LionObject.create("com.sankuai.dzviewscene.productshelf.common.douhu.exp.config", DouHuExpConfigList.class);

    /**
     * 判断是否命中斗斛
     *@param activityContext 上下文，用于获取unionid 和 platform
     *@param scenecode 场景码，用于根据场景码获取斗斛实验号
     *@param sk 需要命中的逻辑，参照DouHuUtils.DouhuSkEnum
     *@return
     */
    public boolean isHitExpSk(ActivityContext activityContext, String scenecode, String sk, String expName) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils.isHitExpSk(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String,java.lang.String,java.lang.String)");
        try {
            String expSk = getHitExpSkFromParam(activityContext, scenecode, sk, expName);
            if(StringUtils.isEmpty(expSk)) {
                return false;
            }
            String skResult = getSkResult(activityContext, scenecode, expName);
            return expSk.equals(skResult);
        } catch (Exception e) {
            Cat.logError(String.format("斗斛命中过程失败, scenecode: %s, sk: %s, expName: %s", scenecode, sk, expName), e);
            return false;
        }
    }

    /**
     * 获取单个ABTest数据
     * @param douHuMFuture
     * @return
     */
    public static String getSingleDouHuAbTest(CompletableFuture<DouHuM> douHuMFuture) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils.getSingleDouHuAbTest(java.util.concurrent.CompletableFuture)");
        if (douHuMFuture == null) {
            return JsonCodec.encode(Lists.newArrayList());
        }
        DouHuM douHuM = douHuMFuture.join();
        if (douHuM == null) {
            return JsonCodec.encode(Lists.newArrayList());
        }
        return douHuM.getAbtest();
    }


    public boolean isHitExpSk(Map<String, Object> exParams, String scenecode, String sk, String expName) {
        try {
            String expSk = getHitExpSkFromParam(exParams, scenecode, sk, expName);
            if(StringUtils.isEmpty(expSk)) {
                return false;
            }
            String skResult = getSkResultNotInFlow(exParams, scenecode, expName);
            return expSk.equals(skResult);
        } catch (Exception e) {
            Cat.logError(String.format("斗斛命中过程失败, scenecode: %s, sk: %s, expName: %s", scenecode, sk, expName), e);
            return false;
        }
    }

    /**
     * 判断是否命中某一平台斗斛
     *@return
     */
    public static boolean isHitExpSk(ActivityContext ctx, String expMtSk, String expDpSk) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils.isHitExpSk(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String,java.lang.String)");
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return hitExpectSk(ctx, expMtSk);
        }
        return hitExpectSk(ctx, expDpSk);
    }

    /**
     * 获取斗斛结果
     * @param ctx
     * @param sceneCode 场景码，用于根据场景码获取斗斛实验号
     * @param expName 需要实验的实验名称
     * @return
     */
    public CompletableFuture<DouHuResponse> getDouHuResult(ActivityContext ctx, String sceneCode, String expName) {
        try {
            String expId = getExpIdFromParam(ctx, sceneCode, expName);
            String unionId = ctx.getParam(ShelfActivityConstants.Params.unionId);
            int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
            String deviceId = ctx.getParam(ShelfActivityConstants.Params.deviceId);
            String userId = getUserId(ctx, platform);
            if (StringUtils.isEmpty(expId)) {
                return CompletableFuture.completedFuture(null);
            }
            DouHuRequest douHuRequest = buildDouHuRequest(platform, unionId, deviceId, userId, expId);
            return atomFacadeService.getPoiABTest(douHuRequest);
        } catch (Exception e) {
            Cat.logError(String.format("斗斛命中过程失败, scenecode: %s, expName: %s", sceneCode, expName), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    private String getSkResult(ActivityContext ctx, String scenecode, String expName) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils.getSkResult(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String,java.lang.String)");
        String expId = getExpIdFromParam(ctx, scenecode, expName);
        String unionId = ctx.getParam(ShelfActivityConstants.Params.unionId);
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        String deviceId = ctx.getParam(ShelfActivityConstants.Params.deviceId);
        String userId = getUserId(ctx, platform);
        if (StringUtils.isEmpty(expId)) {
            return null;
        }
        DouHuRequest douHuRequest = buildDouHuRequest(platform, unionId, deviceId, userId, expId);
        CompletableFuture<DouHuResponse> douHuResponseCompletableFuture = atomFacadeService.getPoiABTest(douHuRequest);
        DouHuResponse douHuResponse = Futures.get(douHuResponseCompletableFuture, timeout);
        if(douHuResponse == null) {
            return null;
        }
        return douHuResponse.getSk();
    }

    private String getSkResultNotInFlow(Map<String, Object> exParams, String scenecode, String expName) {
        int platform = ParamsUtil.getIntSafely(exParams, ShelfActivityConstants.Params.platform);
        String expId = getExpIdFromPlatform(platform, scenecode, expName);
        String unionId = ParamsUtil.getStringSafely(exParams, ShelfActivityConstants.Params.unionId);
        String deviceId = ParamsUtil.getStringSafely(exParams, ShelfActivityConstants.Params.deviceId);
        String userId = getUserIdByMap(exParams, platform);
        if (StringUtils.isEmpty(expId)) {
            return null;
        }
        DouHuRequest douHuRequest = buildDouHuRequest(platform, unionId, deviceId, userId, expId);
        CompletableFuture<DouHuResponse> douHuResponseCompletableFuture = atomFacadeService.getPoiABTest(douHuRequest);
        DouHuResponse douHuResponse = Futures.get(douHuResponseCompletableFuture, timeout);
        if(douHuResponse == null) {
            return null;
        }
        return douHuResponse.getSk();
    }

    private String getUserId(ActivityContext ctx, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return ParamsUtil.getStringSafely(ctx,ShelfActivityConstants.Params.mtUserId);
        }
        return ParamsUtil.getStringSafely(ctx,ShelfActivityConstants.Params.dpUserId);
    }

    private String getUserIdByMap(Map<String, Object> exParams, int platform) {
        if (PlatformUtil.isMT(platform)) {
            return ParamsUtil.getStringSafely(exParams,ShelfActivityConstants.Params.mtUserId);
        }
        return ParamsUtil.getStringSafely(exParams,ShelfActivityConstants.Params.dpUserId);
    }

    private DouHuRequest buildDouHuRequest(int platform, String unionId, String deviceId, String userId, String expId) {
        DouHuRequest douHuRequest = new DouHuRequest();
        douHuRequest.setExpId(expId);
        douHuRequest.setUnionId(unionId);
        douHuRequest.setUserId(userId);
        if (PlatformUtil.isMT(platform)) {
            douHuRequest.setUuid(deviceId);
        } else {
            douHuRequest.setDpid(deviceId);
        }
        return douHuRequest;
    }

    /**
     * 获取斗斛命中逻辑
     *@param
     *@return 斗斛命中逻辑字段
     */
    public static String getHitExpSkFromParam(ActivityContext ctx, String scenecode, String hitSk, String expName) {
        int platform = ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform);
        String expId = PlatformUtil.isMT(platform) ? getMTDouHuExpId(scenecode, expName) : DouHuUtils.getDPDouHuExpId(scenecode, expName);
        return String.format(DOUHU_HIT_EXP_FORMAT, expId, hitSk);
    }

    public static String getHitExpSkFromParam(Map<String, Object> exParams, String scenecode, String hitSk, String expName) {
        int platform = ParamsUtil.getIntSafely(exParams, ShelfActivityConstants.Params.platform);
        String expId = PlatformUtil.isMT(platform) ? getMTDouHuExpId(scenecode, expName) : DouHuUtils.getDPDouHuExpId(scenecode, expName);
        return String.format(DOUHU_HIT_EXP_FORMAT, expId, hitSk);
    }

    /**
     * 获取斗斛实验id
     *@param
     *@return 斗斛实验id
     */
    public static String getExpIdFromParam(ActivityContext ctx, String scenecode, String expName) {
        if (PlatformUtil.isMT(ParamsUtil.getIntSafely(ctx, ShelfActivityConstants.Params.platform))) {
            return getMTDouHuExpId(scenecode, expName);
        }
        return getDPDouHuExpId(scenecode, expName);
    }

    public static String getExpIdFromPlatform(int platform, String scenecode, String expName) {
        if (PlatformUtil.isMT(platform)) {
            return getMTDouHuExpId(scenecode, expName);
        }
        return getDPDouHuExpId(scenecode, expName);
    }

    public static String getDPDouHuExpIds(String scenecode, List<String> expNames) {
        if(CollectionUtils.isEmpty(expNames)) {
            return null;
        }
        List<String> expNameList = expNames.stream().map(exp -> DouHuUtils.getDPDouHuExpId(scenecode, exp)).filter(expName -> StringUtils.isNotEmpty(expName)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(expNameList)) {
            return null;
        }
        return StringUtils.join(expNameList, SEPERATOR);
    }

    public static String getDPDouHuExpId(String scenecode, String expName) {
        DouHuExpConfig douHuExpIDConfig = getDouHuExpConfigWithSpecificScenecode(scenecode);
        if(douHuExpIDConfig == null) {
            return null;
        }
        return getDouHuExpIdWithSpecificExpName(douHuExpIDConfig.getDpDouHuExpIdConfigs(), expName);
    }

    public static String getMTDouHuExpIds(String scenecode, List<String> expNames) {
        if(CollectionUtils.isEmpty(expNames)) {
            return null;
        }
        List<String> expNameList = expNames.stream().map(exp -> DouHuUtils.getMTDouHuExpId(scenecode, exp)).filter(expname -> StringUtils.isNotEmpty(expname)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(expNameList)) {
            return null;
        }
        return StringUtils.join(expNameList, SEPERATOR);
    }

    public static String getMTDouHuExpId(String scenecode, String expName) {
        DouHuExpConfig douHuExpIDConfig = getDouHuExpConfigWithSpecificScenecode(scenecode);
        if(douHuExpIDConfig == null) {
            return null;
        }
        return getDouHuExpIdWithSpecificExpName(douHuExpIDConfig.getMtDouHuExpIdConfigs(), expName);
    }

    private static String getDouHuExpIdWithSpecificExpName(List<DouHuExpIdConfig> douHuExpIdConfigList, String expName) {
        if(CollectionUtils.isEmpty(douHuExpIdConfigList) || StringUtils.isEmpty(expName)) {
            return null;
        }
        DouHuExpIdConfig douHuExpIdConfig = douHuExpIdConfigList.stream().filter(expIdConfig -> expIdConfig.getExpName().equals(expName)).findFirst().orElse(null);
        if(douHuExpIdConfig == null) {
            return null;
        }
        return douHuExpIdConfig.getExpId();
    }

    private static DouHuExpConfig getDouHuExpConfigWithSpecificScenecode(String scenecode) {
        if(douHuExpConfigLionObject == null || douHuExpConfigLionObject.getObject() == null) {
            return null;
        }
        List<DouHuExpConfig> douHuExpIDConfigList = douHuExpConfigLionObject.getObject().getDouHuExpConfigList();
        if(CollectionUtils.isEmpty(douHuExpIDConfigList) || StringUtils.isEmpty(scenecode)) {
            return null;
        }
        return douHuExpIDConfigList.stream().filter(douHuExp -> douHuExp.getScenecode().equals(scenecode)).findFirst().orElse(null);
    }

    public static String convertDouHuResponse (DouHuResponse douHuResponse) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils.convertDouHuResponse(com.sankuai.douhu.absdk.bean.DouHuResponse)");
        String douHuInfo = DouHuUtil.extractExpABInfo4Front(douHuResponse);
        if (StringUtils.isEmpty(douHuInfo)) {
            return "[]";
        }
        if ("{}".equals(douHuInfo) || "[]".equals(douHuInfo) ) {
            return "[]";
        }
        douHuInfo = "[" + douHuInfo + "]";
        return douHuInfo;
    }

    /**
     * 是否命中期望的分流结果策略名，命中期望的分流结果策略名返回true, 否则返回false
     *
     * @param ctx   活动上下文
     * @param sk 分流结果策略名
     * @return
     */
    public static boolean hitExpectSk(ActivityContext ctx, String sk) {
        if (StringUtils.isEmpty(sk) || ctx.getMainData() == null) {
            return false;
        }
        //1. 从活动上下文获取货架数据模型
        ShelfGroupM shelfGroupM = (ShelfGroupM) ctx.getMainData().join();
        if (shelfGroupM == null) {
            return false;
        }
        //2. 批量斗斛数据列表为空，默认判断单个斗斛数据是否命中期望的分流结果策略名
        if (CollectionUtils.isEmpty(shelfGroupM.getDouHus())) {
            return hitSk(shelfGroupM.getDouHu(), sk);
        }
        //3. 根据分流结果策略名筛选出匹配的斗斛数据
        DouHuM douHu = shelfGroupM.getDouHus().stream().filter(douHuM -> douHuM != null && sk.equals(douHuM.getSk())).findFirst().orElse(null);
        return hitSk(douHu, sk);
    }

    /**
     * 根据上下文判断是否命中期望的分流结果策略名，命中期望的分流结果策略名返回true, 否则返回false
     *
     * @param ctx   活动上下文
     * @param sk 分流结果策略名
     * @return
     */
    public static boolean hitExpectSkByContextKey(ActivityContext ctx, String sk, String contextKey) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils.hitExpectSkByContextKey(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.lang.String,java.lang.String)");
        try {
            if (StringUtils.isEmpty(sk) || ctx.getExtContext(contextKey) == null) {
                return false;
            }
            //1. 从上下文获取斗斛
            DouHuM douHuM = (DouHuM) ctx.getExtContext(contextKey).join();
            //2. 判断是否命中期望的分流结果策略名
            return hitSk(douHuM, sk);
        } catch (Exception e) {
            //静默
        }
        return false;
    }

    private static boolean hitSk(DouHuM douHu, String sk) {
        return douHu != null && StringUtils.isNotEmpty(douHu.getSk()) && douHu.getSk().equals(sk);
    }

    /**
     * @param douHuMList
     * @param sks
     * @return  命中任意策略都会返回 true
     */
    public static boolean hitAnySk(List<DouHuM> douHuMList, String ... sks) {
        if (CollectionUtils.isEmpty(douHuMList)) {
            return false;
        }
        for (DouHuM douHuM : douHuMList) {
            for (String sk : sks) {
                if (hitSk(douHuM, sk)) {
                    return true;
                }
            }
        }
        return false;
    }



    /**
     * @param douHuMList
     * @param sks
     * @return  命中任意策略都会返回 true
     */
    public static boolean hitAnySk(List<DouHuM> douHuMList, List<String> sks) {
        if (CollectionUtils.isEmpty(douHuMList) || CollectionUtils.isEmpty(sks)) {
            return false;
        }
        for (DouHuM douHuM : douHuMList) {
            for (String sk : sks) {
                if (hitSk(douHuM, sk)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isHitDouHu(List<DouhuResultModel> douHuResultModels, List<String> douHuSkList){
        if(CollectionUtils.isEmpty(douHuResultModels) || CollectionUtils.isEmpty(douHuSkList)){
            return false;
        }
        return douHuResultModels
                .stream()
                .filter(Objects::nonNull)
                .anyMatch(douhuResultModel -> douHuSkList.contains(douhuResultModel.getSk()));
    }

    /**
     * @param douHuMList
     * @return  返回分流结果策略列表
     */
    public static List<String> getDouHuMSkList(List<DouHuM> douHuMList){
        if (CollectionUtils.isEmpty(douHuMList)){
            return new ArrayList<>();
        }
        return douHuMList.stream()
                .map(DouHuM::getSk)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public enum DouhuSkEnum {
        A_SK("_a"),
        B_SK("_b"),
        C_SK("_c");

        public String sk;

        DouhuSkEnum(String sk)
        {
            this.sk = sk;
        }

        public String getSk() {
            return sk;
        }
    }

    /**
     * 具体业务策略的斗斛封装，届时斗斛结束可删除相应方法
     */
    public static class CurrentStrategyUtil {
        /**
         * 是否命中足疗斗斛B-副标题变黑
         * @param douHu
         * @return
         */
        public static boolean hitMassageB(DouHuResponse douHu) {
            return Objects.equals(douHu.getSk(), "exp001532_b") || Objects.equals(douHu.getSk(), "exp001533_b");
        }

        /**
         * 是否命中足疗斗斛C-标题副标题互换
         * @param douHu
         * @return
         */
        public static boolean hitMassageC(DouHuResponse douHu) {
            return Objects.equals(douHu.getSk(), "exp001532_c") || Objects.equals(douHu.getSk(), "exp001533_c");
        }

        public static boolean hitMedicalHotDeal(List<DouHuM> douHuMList) {
            return DouHuUtils.hitAnySk(douHuMList, "exp001626_a", "exp001627_a");
        }
        
        /**
         * 是否命中足疗无头图
         * @param douHu
         * @return
         */
        public static boolean hitMassageNoPic(DouHuResponse douHu) {
            return Objects.equals(douHu.getSk(), "exp001742_b") || Objects.equals(douHu.getSk(), "exp001742_d") || Objects.equals(douHu.getSk(), "exp001743_b") || Objects.equals(douHu.getSk(), "exp001743_d");
        }
    }

    @Data
    private static class DouHuExpConfigList {
        private List<DouHuExpConfig> douHuExpConfigList;
    }

    @Data
    private static class DouHuExpIdConfig {
        private String expName;
        private String expId;
    }

    @Data
    private static class DouHuExpConfig {
        private String scenecode;
        private List<DouHuExpIdConfig> dpDouHuExpIdConfigs;
        private List<DouHuExpIdConfig> mtDouHuExpIdConfigs;
    }

    public static Optional< ? extends Object> getConfigByDouHu(List<DouHuM> douHuList, Map<String,? extends Object> douHuConfig){
        if(CollectionUtils.isEmpty(douHuList) || MapUtils.isEmpty(douHuConfig)){
            return Optional.empty();
        }
        List<String> douHuMSkList = DouHuUtils.getDouHuMSkList(douHuList);
        return Optional.ofNullable(douHuMSkList.stream()
                .filter(sk -> douHuConfig.containsKey(sk))
                .findFirst()
                .map(sk -> douHuConfig.get(sk))
                .orElse(null));
    }
}
