package com.sankuai.dzviewscene.shelf.business.detail.edu;

import com.dianping.cat.Cat;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dzviewscene.shelf.business.detail.edu.builder.EduNavAndProductShelfH5ResponseBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivity;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityAbstractBuilder;
import com.sankuai.dzviewscene.shelf.platform.other.ability.flow.NavAndProductShelfOtherActivityFlow;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ShelfFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 教培查看更多落地页-首次加载-H5模板
 *
 * <AUTHOR>
 */
@ActivityTemplate(activityCode = OtherActivity.ACTIVITY_CODE, sceneCode = "edu_poi_deal_course_shelf_landing_filter", name = "教培查看更多落地页-首次加载-H5模板")
public class EduNavAndProductsShelfH5Template implements IActivityTemplate {

    private static final int DEAL_TYPE = 2;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.edu.deal.productattrtag",
            defaultValue = "[\"eduFitAgeToC\", \"eduFitGradeToC\", \"study_target_education_2\", \"application_stage_admission_counseling_select\", \"application_stage_select\", \"class_type_with_two_select\", \"class_type_with_three_select\", \"class_type_with_five_select\", \"physical_inspection_item\", \"service_efficacy\", \"ticke_usertime\", \"ticket_usernumbers\", \"baby_early_edu_fit_age\", \"baby_early_edu_lesson\", \"baby_early_edu_class_size\", \"number_of_people\", \"deal_scene\", \"private_movie_servicetime\", \"recommend_person_count\", \"self_limit_time\", \"newHouse_envir_level\", \"newHouse_style\", \"physical_examination_cash_type\", \"recommend_person_count_num\", \"recommend_person_count_scope\", \"body_type_pet_dog\", \"wegith_pet_cat\", \"hair_length_pet_dog\", \"hair_length_pet_cat\", \"available_count_pet\", \"available_time_pet\"]")
    private List<String> attributes;

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.flow()");
        return NavAndProductShelfOtherActivityFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.extParams(java.util.Map)");
        // 1. 设置召回参数，召回两组商品：团购和课程
        exParams.put(QueryFetcher.Params.groupNames, getComponentGroupList(exParams));

        // 2. 设置商品平台组件和商品组映射关系
        exParams.put(QueryFetcher.Params.productComponent2Group, getProductComponentGroupMap(exParams));
        // 3. filter组件ID和商品组名映射关系
        exParams.put(FilterFetcher.Params.filterComponent2Group, getFilterComponentGroupMap(exParams));

        // 4. 设置商品组召回和填充参数
        exParams.put(QueryFetcher.Params.groupParams, getComponentGroupParamsMap(exParams));
    }

    private boolean isDealRequest(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.isDealRequest(java.util.Map)");
        return DEAL_TYPE == Integer.parseInt(exParams.get(ShelfActivityConstants.Params.shelfType).toString());
    }


    /**
     * 召回商品组
     */
    private List<String> getComponentGroupList(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.getComponentGroupList(java.util.Map)");
        if (isDealRequest(exParams)) {
            return new ArrayList<String>() {{
                add("团购");
            }};
        }
        return new ArrayList<String>() {{
            add("课程");
        }};
    }

    /**
     * 召回商品组的参数
     */
    private Map<String, Object> getComponentGroupParamsMap(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.getComponentGroupParamsMap(java.util.Map)");
        if (isDealRequest(exParams)) {
            return new HashMap<String, Object>(1) {{
                put("团购", new HashMap<String, Object>(8) {{
                    put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                    put(PaddingFetcher.Params.planId, "10002141");
                    put("tcMergePromoTemplateId", 226);
                    put("promoTemplateId", 145);
                    put("attributeKeys", attributes);
                    put(PaddingFetcher.Params.shopCategory, getShopCategory(exParams));
                }});
            }};
        }
        return new HashMap<String, Object>() {{
            put("课程", new HashMap<String, Object>(4) {{
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.generalProductPadding.name());
                put(PaddingFetcher.Params.planId, "10100057");
            }});
        }};
    }


    private Integer getShopCategory(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.getShopCategory(java.util.Map)");
        Object o = exParams.get(OtherActivityConstants.Ctx.ctxShop);
        if (o == null || !(o instanceof ShopM)) {
            return 0;
        }

        ShopM shopM = (ShopM) exParams.get(OtherActivityConstants.Ctx.ctxShop);
        return shopM.getCategory();
    }

    /**
     * filter组件ID和商品组名映射关系
     */
    private Map<String, String> getFilterComponentGroupMap(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.getFilterComponentGroupMap(java.util.Map)");
        if (isDealRequest(exParams)) {
            return new HashMap<String, String>(1) {{
                put("选课中心", "团购");
            }};
        }
        return new HashMap<String, String>(1) {{
            put("选课中心", "课程");
        }};
    }

    /**
     * 商品平台商品组件ID和商品组的映射关系
     */
    private Map<String, String> getProductComponentGroupMap(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.getProductComponentGroupMap(java.util.Map)");
        Map<String, String> result = new HashMap<>();
        result.put("团购", "团购");
        result.put("课程", "课程");
        return result;
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.extContexts(java.util.List)");
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.extAbilities(java.util.Map)");
        // 0. 注册融合查询能力
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
        // 1. 商品列表召回能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, ShelfQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 3. 教培查看更多落地页-Response构造能力
        abilities.put(OtherActivityAbstractBuilder.ABILITY_CODE, EduNavAndProductShelfH5ResponseBuilderExt.class);
        // 4. filter查询能力
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, ShelfFilterFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.detail.edu.EduNavAndProductsShelfH5Template.extPoints(java.util.Map)");
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, ShelfFilterFetcherExt.class);
    }

}