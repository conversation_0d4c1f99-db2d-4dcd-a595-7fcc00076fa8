package com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder;

import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dztheme.generalproduct.enums.GeneralProductAttrEnum;
import com.sankuai.dzviewscene.productshelf.vu.utils.SchemaUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import com.sankuai.dzviewscene.shelf.business.shelf.wedding.config.WeddingConfig;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.AbstractDefaultFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by 李维隆 on 2021/07/06
 */
@ExtPointInstance(name = "婚纱摄影-婚纱礼服预付货架楼层模块构造扩展点")
public class WeddingDressProductsShelfFloorsBuilderExt extends AbstractDefaultFloorsBuilderExt implements InitializingBean {

    private static final String ATTR_KEY_SALE_TYPE = "attr_weddingSaleType";

    private static final String PRODUCT_TAG_COLOR = "#FF6633";

    private static final String DEFAULT_TAG_COLOR = "#777777";

    private Map<String, FloorsBuilderExtAdapter> spuTypeAreaBuilderMap = null;

    // 旧版落地页跳转链接
    private static final String ORIG_MORE_JUMP_URL = "%s/migrate-product-universal-web/product-shelf/index.html?poiid=%d&cityid=%d&shopid=%d&shopuuid=%s&shelfcomponentid=%s&shelfnavtagid=%d";

    @Resource
    private WeddingTravelPhotoShelfItemAreaBuilderExt weddingTravelPhotoShelfItemAreaBuilderExt;
    @Resource
    private WeddingIdPhotoShelfItemAreaBuilderExt weddingIdPhotoShelfItemAreaBuilderExt;
    @Resource
    private WeddingShelfSimpleItemAreaBuilderExt weddingShelfSimpleItemAreaBuilderExt;
    @Resource
    private WeddingTrialShelfItemAreaBuilderExt weddingTrialShelfItemAreaBuilderExt;

    @Override
    public void afterPropertiesSet() {
        spuTypeAreaBuilderMap = new HashMap<String, FloorsBuilderExtAdapter>(4) {{
            // 旅拍婚纱照
            put("1345", weddingTravelPhotoShelfItemAreaBuilderExt);
            // 形象证件照
            put("1367", weddingIdPhotoShelfItemAreaBuilderExt);
            // 预约试纱
            put("2000117", weddingTrialShelfItemAreaBuilderExt);
        }};
    }

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.wedding.dress.product.constant.config", defaultValue = "{}")
    private FloorConstant floorConstant;
    @ConfigValue(key = WeddingConfig.WEDDING_LION_KEY)
    private WeddingConfig weddingConfig;

    @Override
    public List<RichLabelVO> productRichLabelTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.productRichLabelTags(ActivityContext,String,ProductM)");
        List<RichLabelVO> result = new ArrayList<>();
        addSaleType(result, productM);
        addProductInfo(activityContext, groupName, result, productM);
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result;
    }

    private void addProductInfo(ActivityContext activityContext, String groupName, List<RichLabelVO> result, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.addProductInfo(ActivityContext,String,List,ProductM)");
        List<String> productTags = buildProductTags(activityContext, groupName, productM);
        if (CollectionUtils.isEmpty(productTags)) {
            return;
        }
        productTags.stream()
                .filter(tag -> StringUtils.isNotBlank(tag))
                .forEach(tag -> result.add(new RichLabelVO(11, DEFAULT_TAG_COLOR, tag)));
    }

    private List<String> buildProductTags(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.buildProductTags(ActivityContext,String,ProductM)");
        String spuType = productM.getAttr(QueryFetcher.Params.spuType);
        // 婚纱礼服由主题提供
        List<String> productTagsByPadding = productM.getProductTags();
        List<String> productTagsBySpuType = Optional.ofNullable(spuTypeAreaBuilderMap.get(spuType)).orElse(weddingShelfSimpleItemAreaBuilderExt).itemComponentProductTags(activityContext, groupName, productM, 0);
        List<String> currentProductTags = new ArrayList<>();
        addProductTags(productTagsByPadding, currentProductTags);
        addProductTags(productTagsBySpuType, currentProductTags);
        return currentProductTags;
    }

    private void addProductTags(List<String> productTags, List<String> currentProductTags) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.addProductTags(java.util.List,java.util.List)");
        if (CollectionUtils.isNotEmpty(productTags)) {
            currentProductTags.addAll(productTags);
        }
    }

    private void addSaleType(List<RichLabelVO> result, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.addSaleType(java.util.List,com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        String saleType = productM.getAttr(ATTR_KEY_SALE_TYPE);
        if (StringUtils.isEmpty(saleType)) {
            return;
        }
        result.add(new RichLabelVO(11, PRODUCT_TAG_COLOR, saleType));
    }

    @Override
    public int floorDefaultShowNum(ActivityContext activityContext, String groupName, List<ProductM> productMs) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.floorDefaultShowNum(ActivityContext,String,List)");
        return floorConstant.getDefaultShowProductCount();
    }

    @Override
    public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.itemComponentPurchase(ActivityContext,String,ProductM)");
        //玩美季和购买信息不能同时展示
        if (PerfectActivityBuildUtils.isShowPerfectActivity(productM)) {
            return null;
        }
        if (StringUtils.isBlank(productM.getPurchase())) {
            return null;
        }
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText(productM.getPurchase());
        return richLabelVO;
    }

    @Override
    public PicAreaVO itemComponentPic(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.itemComponentPic(ActivityContext,String,ProductM)");
        if (StringUtils.isEmpty(productM.getPicUrl())) {
            return null;
        }
        PicAreaVO picAreaVO = new PicAreaVO();
        // 图片信息
        addPicUrl(productM, picAreaVO);
        // 图片标签
        addPicLab(productM, picAreaVO);
        return picAreaVO;
    }

    @Override
    public RichLabelVO activityRemainSecondsLabel(ActivityContext activityContext, String groupName, ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.activityRemainSecondsLabel(ActivityContext,String,ProductM)");
        RichLabelVO richLabelVO = PerfectActivityBuildUtils.buildPerfectActivityRemainSecondsLabel(productM);
        return richLabelVO;
    }

    private void addPicLab(ProductM productM, PicAreaVO picAreaVO) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.addPicLab(ProductM,PicAreaVO)");
        String activityIcon = productM.getAttr(GeneralProductAttrEnum.ATTR_SINGLE_ROW_SHELF_ICON.key);

        if (!StringUtils.isEmpty(activityIcon)) {
            picAreaVO.setFloatTags(buildFloatTag(activityIcon));
        }
    }

    private List<FloatTagVO> buildFloatTag(String activityIcon) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.buildFloatTag(java.lang.String)");
        FloatTagVO floatTagVO = new FloatTagVO();
        floatTagVO.setPosition(floorConstant.floatTagPosition);
        floatTagVO.setIcon(new DzPictureComponentVO(activityIcon, floorConstant.floatPicAspectRadio));
        return Lists.newArrayList(floatTagVO);
    }

    private void addPicUrl(ProductM productM, PicAreaVO picAreaVO) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.addPicUrl(ProductM,PicAreaVO)");
        DzPictureComponentVO pic = new DzPictureComponentVO();
        pic.setAspectRadio(floorConstant.getHeaderPicAspectRadio());
        pic.setPicUrl(PictureURLBuilders.toHttpsUrl(productM.getPicUrl(), floorConstant.getPicWidth(), floorConstant.getPicHeight(), PictureURLBuilders.ScaleType.Cut));
        if (floorConstant.getPicAreaHeight() > 0) {
            pic.setPicHeight(floorConstant.getPicAreaHeight());
        } else {
            pic.setPicHeight(floorConstant.getPicHeight());
        }
        picAreaVO.setPic(pic);
    }

    @Override
    public String moreComponentText(ActivityContext activityContext, String groupName, ProductGroupM productGroupM, DzItemAreaComponentVO itemAreaComponentVO) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.moreComponentText(ActivityContext,String,ProductGroupM,DzItemAreaComponentVO)");
        if (itemAreaComponentVO.getProductItems().size() <= floorConstant.getDefaultShowProductCount()) return null;
        return floorConstant.getMorePrefix() + (productGroupM.getTotal() - itemAreaComponentVO.getDefaultShowNum()) + floorConstant.getMoreSuffix();
    }

    @Override
    public String moreComponentJumpUrl(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.moreComponentJumpUrl(ActivityContext,String,ProductGroupM)");
        if (floorConstant.oldMoreJumpUrl) {
            return oldMoreComponentJumpUrl(activityContext, groupName, productGroupM);
        }
        int clientType = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.userAgent);
        String url = String.format(floorConstant.dpMoreUrl,
                ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpCityId),
                ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.dpPoiId),
                ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.selectedFilterId));
        if (PlatformUtil.isMT(clientType)) {
            url = String.format(floorConstant.mtMoreUrl,
                    ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtCityId),
                    ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.mtPoiId),
                    ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.selectedFilterId));
        }
        return SchemaUtils.getJumpUrlByClientType(clientType, url);
    }

    private String oldMoreComponentJumpUrl(ActivityContext activityContext, String groupName, ProductGroupM productGroupM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.oldMoreComponentJumpUrl(ActivityContext,String,ProductGroupM)");
        Map<String, Object> params = activityContext.getParameters();
        return String.format(ORIG_MORE_JUMP_URL, getDomain(params), getShopId(activityContext), getCityId(params),
                getShopId(activityContext), getShopUuId(params), getComponentId(activityContext), getSelectId(activityContext));
    }

    private String getDomain(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getDomain(java.util.Map)");
        int platform = getPlatform(params);
        if (PlatformUtil.isMT(platform)) {
            return SchemaUtils.getMTIDomain();
        }
        return SchemaUtils.getDPMDomain();
    }

    private int getSelectId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getSelectId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        return ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.selectedFilterId);
    }

    private int getPlatform(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getPlatform(java.util.Map)");
        return ParamsUtil.getIntSafely(params, ShelfActivityConstants.Params.platform);
    }

    private String getShopCategory(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getShopCategory(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        ShopM shopM = ParamsUtil.getValue(activityContext, ShelfActivityConstants.Ctx.ctxShop, null);
        if (shopM == null) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        return String.valueOf(shopM.getCategory());
    }

    private String getShopUuId(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getShopUuId(java.util.Map)");
        Object shopUuId = ParamsUtil.getValue(params, ShelfActivityConstants.Params.shopUuid, null);
        if (shopUuId == null) {
            return "";
        }
        return shopUuId.toString();
    }

    private String getComponentId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getComponentId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        if (MapUtils.isEmpty(weddingConfig.getCategoryFilterComponentMap())) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        return weddingConfig.getCategoryFilterComponentMap().get(Integer.parseInt(getShopCategory(activityContext)));
    }

    private int getCityId(Map<String, Object> params) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getCityId(java.util.Map)");
        int platform = getPlatform(params);
        if (PlatformUtil.isMT(platform)) {
            return ParamsUtil.getIntSafely(params, ShelfActivityConstants.Params.mtCityId);
        }
        return ParamsUtil.getIntSafely(params, ShelfActivityConstants.Params.dpCityId);
    }

    public long getShopId(ActivityContext activityContext) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getShopId(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        int platform = ParamsUtil.getIntSafely(activityContext, ShelfActivityConstants.Params.platform);
        if (PlatformUtil.isMT(platform)) {
            return PoiIdUtil.getMtPoiIdL(activityContext, ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId);
        }
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }

    @Override
    public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.itemComponentLabs(ActivityContext,String,ProductM,int)");
        Map<String, Object> oceanMap = new HashMap<>();
        oceanMap.put("product_id", productM.getProductId());
        oceanMap.put("index", index);
        addPromoInfo(oceanMap, itemComponentPromoTags(activityContext, groupName, productM));
        return JsonCodec.encode(oceanMap);
    }

    protected void addPromoInfo(Map<String, Object> oceanMap, List<DzPromoVO> promoList) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.addPromoInfo(java.util.Map,java.util.List)");
        if (CollectionUtils.isEmpty(promoList)) {
            return;
        }
        oceanMap.put("promotion_title", promoList.get(0).getName());
        if (promoList.get(0).getDetail() == null || CollectionUtils.isEmpty(promoList.get(0).getDetail().getPromoItems())) {
            return;
        }
        oceanMap.put("promotion_array", getPromoInfoList(promoList.get(0).getDetail().getPromoItems()));
    }

    protected List<Map<String, String>> getPromoInfoList(List<DzPromoPerItemVO> promoItems) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.wedding.builder.WeddingDressProductsShelfFloorsBuilderExt.getPromoInfoList(java.util.List)");
        return promoItems.stream()
                .filter(promoItem -> StringUtils.isNotEmpty(promoItem.getPromoId()))
                .map(promoItem -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("promotion_type", promoItem.getPromoType() + "");
                    map.put("promotion_id", promoItem.getPromoId() + "");
                    return map;
                }).collect(Collectors.toList());
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RichLabel {
        private String text;
        private String textcolor;
        private int textsize;
        private String textstyle;

        public RichLabel() {
        }

        public RichLabel(String text, String textcolor, int textsize) {
            this.text = text;
            this.textcolor = textcolor;
            this.textsize = textsize;
        }

        public RichLabel(String text, String textcolor, int textsize, String textstyle) {
            this.text = text;
            this.textcolor = textcolor;
            this.textsize = textsize;
            this.textstyle = textstyle;
        }

    }

    @Data
    public static class FloorConstant {
        private int front11;
        private String color777777;
        private String colorFF6633;
        private String boldFontWeight;
        private double activityPicAspectRadio;
        private String activityFontColor;
        private int activityFontSize;
        private String activityFontWeight;
        private String activityBgPic;
        private double headerPicAspectRadio;
        private int defaultShowProductCount;
        private List<Long> titleFormatFilterIds;
        private String morePrefix;
        private String moreSuffix;
        private int picWidth;
        private int picHeight;
        private int picAreaHeight;
        private String shopRecommendPic;
        private double shopRecommendAspectRadio;
        private String dpMoreUrl;
        private String mtMoreUrl;
        // 旧版落地页跳转链接
        private boolean oldMoreJumpUrl;
        private int floatTagPosition;
        private double floatPicAspectRadio;
    }

}
