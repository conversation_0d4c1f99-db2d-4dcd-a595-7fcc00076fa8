package com.sankuai.dzviewscene.shelf.business.shelf.baby;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPlayGroundPackageAndServiceShelfFloorBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPlayGroundPackageAndServiceShelfMainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPlayGroundShelfPostPaddingExt;
import com.sankuai.dzviewscene.shelf.business.shelf.baby.builder.BabyPlaygroundOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.config.SceneTemplateConfig;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.MultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformMultiDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultiplexQueryFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.MergeMultiABQueryFilterLastShelfFlow;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.MergeQueryFilterLastShelfFlow;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/12 8:16 下午
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "baby_joy_package_dealgroup_shelf", name = "亲子-儿童乐园团购套餐与服务货架模板")
public class BabyPlaygroundPackageAndServiceShelfTemplate implements IActivityTemplate {

    @Resource
    private SceneTemplateConfig sceneTemplateConfig;

    @Override
    public Class<? extends IActivityFlow> flow() {
        return MergeMultiABQueryFilterLastShelfFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {

        // 1. 设置召回参数, 代表只召回一组商品, 商品组名使用dealGroup
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.deal.name()));

        // 2. 设置商品组召回和填充参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.deal.name(), new HashMap<String, Object>() {{
                // 2.1 设置商品组召回渠道为门店在线团单
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.shopOnLineDealsQuery.name());
                // 2.2 设置第一组商品填充参数
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                put("attributeKeys", buildAttrKeys());
                put("enablePreSalePromoTag", true);
                put("directPromoScene", 400005);
                put(PaddingFetcher.Params.planId, sceneTemplateConfig.getPlanId(exParams, ProductEnums.deal.name(), "10002125"));
                put(MergeQueryFetcher.Params.rankId, "10100010");
            }});
        }});
        //3. 设置showType
        exParams.put(ShelfActivityConstants.Style.showType, 13);
        exParams.put(MultiDouHuFetcher.Params.DP_EXP_IDS, "exp000471,exp000862,exp001594");
        exParams.put(MultiDouHuFetcher.Params.MT_EXP_IDS, "exp000470,exp000861,exp001595");
    }

    private List<String> buildAttrKeys() {
        return Lists.newArrayList("service_type", "ticket_usernumbers", "ticke_usertime", "preSaleTag");
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        // 1. 召回能力实现
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 3. 注册融合查询能力
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
        // 4. 筛选构造能力
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, MultiplexQueryFilterFetcher.class);
        // 注册批量斗斛查询能力
        abilities.put(MultiDouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformMultiDouHuFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        // 1. 商品填充后处理扩展点
        extPoints.put(PaddingFetcherExt.EXT_POINT_PADDING_FETCHER_CODE, BabyPlayGroundShelfPostPaddingExt.class);
        // 2. 商品楼层构造器扩展点
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, BabyPlayGroundPackageAndServiceShelfFloorBuilderExt.class);
        // 3. 主标题扩展点
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, BabyPlayGroundPackageAndServiceShelfMainTitleBuilderExt.class);
        // 4. 注册打点模块构造扩展点实现
        extPoints.put(OceanBuilderExt.SHELF_OCEAN_EXT_CODE, BabyPlaygroundOceanBuilderExt.class);
    }
}
