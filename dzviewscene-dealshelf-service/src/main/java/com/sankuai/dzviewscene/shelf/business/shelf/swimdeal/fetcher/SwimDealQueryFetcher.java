package com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.fetcher;

import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.deal.shop.dto.Attribute;
import com.dianping.deal.shop.dto.DealGroupDTO;
import com.dianping.deal.shop.dto.ShopOnlineDealGroup;
import com.dianping.deal.shop.dto.ShopOnlineDealGroupRequest;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.ShelfShopRecProduct;
import com.dianping.product.shelf.common.enums.ProductType;
import com.dianping.product.shelf.common.request.ShelfShopRecProductRequest;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.SwimContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 游泳团单货架的召回能力
 * @auther: liweilong06
 * @date: 2020/10/10 12:21 下午
 */@AbilityInstance(name = "游泳团单货架的召回能力")
public class SwimDealQueryFetcher extends QueryFetcher {

    private static final String TRAIN_COURSE = "train_course";
    private static final String NO_SERVICE_TYPE = "no_servcie_type";

    /**
     * 游泳卡的ServiceType
     */
    private static final List<String> SWIM_TYPE = Lists.newArrayList("单次票", "多次卡", "月卡", "年卡");

    /**
     * 整个货架加载多少个团单
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.ablity.query.onlineDeals.maxQueryNum", defaultValue = "50")
    private int maxQueryNum;

    /**
     * 商家推荐的TabId，固定
     */
    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.ablity.query.onlineDeals.swimShopRecommendTabId", defaultValue = "200120596")
    private long shopRecommendTabId;

    @Resource
    private CompositeAtomService compositeAtomService;

    /**
     * 构造对象
     *
     * @param ctx
     * @return
     */
    @Override
    public CompletableFuture<Map<String, ProductGroupM>> build(ActivityContext ctx) {
        // 1、查询商户推荐团单
        CompletableFuture<Response<List<ShelfShopRecProduct>>> recommendDealFuture = getRecommendFuture(ctx);
        // 2、查询查询在线团单
        CompletableFuture<Map<Long, ShopOnlineDealGroup>> onlineDealFuture = getOnlineDealFuture(ctx);
        // 3、分类组装
        return CompletableFuture.allOf(recommendDealFuture, onlineDealFuture).thenApply(v -> {
            Response<List<ShelfShopRecProduct>> recommendDealResponse = recommendDealFuture.join();
            Map<Long, ShopOnlineDealGroup> onlineDealResponse = onlineDealFuture.join();
            Long dpShopId = getDpShopId(ctx);
            if (isNoOnlineDeals(onlineDealResponse, dpShopId)) {
                // 在线团单会包含商户推荐团，所以只判断在线团单即可
                return null;
            }
            return getFloors(ctx, recommendDealResponse, onlineDealResponse.get(dpShopId));
        });
    }

    /**
     * 填充货架召回
     *
     * @param ctx
     * @param recommendDealResponse
     * @param onlineDealResponse
     * @return
     */
    private Map<String, ProductGroupM> getFloors(ActivityContext ctx, Response<List<ShelfShopRecProduct>> recommendDealResponse,
                                                 ShopOnlineDealGroup onlineDealResponse) {
        int platform = Optional.ofNullable((Integer) ctx.getParam(ShelfActivityConstants.Params.platform)).orElse(1);
        // 1、读取商品
        List<ProductM> totalDeals = getOnlineDeals(onlineDealResponse);
        List<ProductM> shopRecommendDeals = getShopRecommendDeals(recommendDealResponse, totalDeals);
        if (VCPlatformEnum.MT.getType() == platform) {
            // 1.1 如果是美团侧，需要转换为美团的商品ID
            convertToMtDealId(shopRecommendDeals, totalDeals);
        }
        // 2、装载每个floor
        Map<String, ProductGroupM> floors = fillFloors(shopRecommendDeals, totalDeals);
        // 3、根据货架的显示规则过滤掉不需要显示的货架
        Map<String, ProductGroupM> returnValue = filterFloorsByRule(totalDeals, floors);
        // 4、根据入参的分组信息过滤掉不需要的货架
        returnValue = filterFloorsByRequestGroupName(returnValue, ctx);
        return returnValue;
    }

    private Map<String, ProductGroupM> filterFloorsByRequestGroupName(Map<String, ProductGroupM> returnValue, ActivityContext ctx) {
        List<String> groupNames = ctx.getParam(Params.groupNames);
        return returnValue.entrySet().stream()
                .filter(map -> groupNames.contains(map.getKey()))
                .collect(Collectors.toMap(p -> p.getKey(), p -> p.getValue()));
    }

    private void convertToMtDealId(List<ProductM> shopRecommendDeals, List<ProductM> totalDeals) {
        // 1、提取所有团单Id
        Set<Integer> dealIds = new HashSet<>();
        dealIds.addAll(getDealIds(shopRecommendDeals));
        dealIds.addAll(getDealIds(totalDeals));
        // 2、查询美团团单Id
        Map<Integer, Integer> dp2MtIdMap = batchGetDpMtDealIdMapByDpId(new ArrayList<>(dealIds));
        // 3、设置美团团单Id
        updateDealIdAsMt(dp2MtIdMap, shopRecommendDeals);
        updateDealIdAsMt(dp2MtIdMap, totalDeals);
    }

    /**
     * 将productId更换为美团侧Id
     * @param dp2MtIdMap
     * @param dealList
     */
    private void updateDealIdAsMt(Map<Integer, Integer> dp2MtIdMap, List<ProductM> dealList) {
        if (MapUtils.isEmpty(dp2MtIdMap) || CollectionUtils.isEmpty(dealList)) {
            return;
        }
        for (ProductM productM : dealList) {
            if (dp2MtIdMap.get(productM.getProductId()) != null) {
                productM.setProductId(dp2MtIdMap.get(productM.getProductId()));
            }
        }
    }

    /**
     * 根据点评团单Id获取美团团单Id
     * kye=点评，value=mt
     * @param dpDealIds
     * @return 不为空，kye=点评，value=mt
     */
    private Map<Integer, Integer> batchGetDpMtDealIdMapByDpId(List<Integer> dpDealIds) {
        if (CollectionUtils.isEmpty(dpDealIds)) {
            return new HashMap<>();
        }
        List<CompletableFuture<Map<Integer, Integer>>> futures = getDpMtDealFuturesByDpIds(dpDealIds);
        return collect(futures).join();
    }

    private CompletableFuture<Map<Integer, Integer>> collect(List<CompletableFuture<Map<Integer, Integer>>> futures) {
        if (CollectionUtils.isEmpty(futures)) {
            return CompletableFuture.completedFuture(new HashMap<>());
        }
        CompletableFuture<List<Map<Integer, Integer>>> assembleFuture = assemble(futures);
        return assembleFuture.thenCompose(aVoid -> {
                    List<Map<Integer, Integer>> maps = assembleFuture.join();
                    if (CollectionUtils.isEmpty(maps)) {
                        return CompletableFuture.completedFuture(null);
                    }
                    return CompletableFuture.completedFuture(convert(maps));
                }
        );
    }

    private Map<Integer, Integer> convert(List<Map<Integer, Integer>> maps) {
        Map<Integer, Integer> returnValue = new HashMap<>();
        maps.forEach(idMap -> {
            if (MapUtils.isNotEmpty(idMap)) {
                returnValue.putAll(idMap);
            }
        });
        return returnValue;
    }

    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }

    private List<CompletableFuture<Map<Integer, Integer>>> getDpMtDealFuturesByDpIds(List<Integer> dpDealIds) {
        List<CompletableFuture<Map<Integer, Integer>>> returnValue = new ArrayList<>();
        List<List<Integer>> splitDpDealIdsList = Lists.partition(dpDealIds, 40);
        for (List<Integer> perDealIds : splitDpDealIdsList) {
            returnValue.add(buildResultFutureByDp(new ArrayList<>(perDealIds)));
        }
        return returnValue;
    }

    private CompletableFuture<Map<Integer, Integer>> buildResultFutureByDp(List<Integer> requestIds) {
        CompletableFuture<List<IdMapper>> idMapperFuture = compositeAtomService.batchGetDealIdByDpId(requestIds);
        return idMapperFuture.thenCompose(aVoid -> {
            List<IdMapper> response = idMapperFuture.join();
            if (CollectionUtils.isEmpty(response)) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.completedFuture(convertToMap(response));
        });
    }

    /**
     * 读取ID查询结果
     * @param response
     * @return 返回Map，key=dpId，value=mtId
     */
    private Map<Integer, Integer> convertToMap(List<IdMapper> response) {
        Map<Integer, Integer> returnValue = new HashMap<>(response.size());
        response.forEach(idMapper -> {
            returnValue.put(idMapper.getDpDealGroupID(), idMapper.getMtDealGroupID());
        });
        return returnValue;
    }

    private Set<Integer> getDealIds(List<ProductM> productMS) {
        Set<Integer> dealIds = new HashSet<>();
        if (CollectionUtils.isEmpty(productMS)) {
            return dealIds;
        }
        productMS.forEach(productM -> {
            dealIds.add(productM.getProductId());
        });
        return dealIds;
    }

    /**
     * 1、商家推荐   本身不为空则显示
     * 2、游泳卡     游泳卡数据不为空，并且不存在NO_SERVICE_TYPE，则显示
     * 2、团购       存在NO_SERVICE_TYPE 或者 游泳卡floor为空，则显示
     * 4、培训课     不存在团购，并且培训课数据不为空，则显示
     * @param totalDeals
     * @param floors
     * @return
     */
    private Map<String, ProductGroupM> filterFloorsByRule(List<ProductM> totalDeals, Map<String, ProductGroupM> floors) {
        Map<String, ProductGroupM> returnValue = new HashMap<>();
        addIfNotNull(returnValue, SwimContext.SHOP_RECOMMEND_FLOOR, filterRecommendFloor(floors.get(SwimContext.SHOP_RECOMMEND_FLOOR)));
        addIfNotNull(returnValue, SwimContext.SWIM_CARD_FLOOR, filterSwimCard(totalDeals, floors.get(SwimContext.SWIM_CARD_FLOOR)));
        addIfNotNull(returnValue, SwimContext.SWIM_DEAL_FLOOR, filterNormalDealFloor(returnValue, totalDeals, floors.get(SwimContext.SWIM_DEAL_FLOOR)));
        addIfNotNull(returnValue, SwimContext.SWIM_COURSE_FLOOR, filterSwimCourse(returnValue, floors.get(SwimContext.SWIM_COURSE_FLOOR)));
        return returnValue;
    }

    /**
     * 团购，存在NO_SERVICE_TYPE 或者 游泳卡floor为空，则显示
     * @param returnValue
     * @param totalDeals
     * @param productGroupM
     * @return
     */
    private ProductGroupM filterNormalDealFloor(Map<String, ProductGroupM> returnValue, List<ProductM> totalDeals,
                                                ProductGroupM productGroupM) {
        if (returnValue.get(SwimContext.SWIM_CARD_FLOOR) == null || hasNoServiceTypeDeal(totalDeals)) {
            return productGroupM;
        }
        return null;
    }

    /**
     * 不存在团购，并且培训课数据不为空，则显示
     * @param returnValue
     * @param productGroupM
     * @return
     */
    private ProductGroupM filterSwimCourse(Map<String, ProductGroupM> returnValue, ProductGroupM productGroupM) {
        if (returnValue.get(SwimContext.SWIM_DEAL_FLOOR) == null && productGroupM != null && CollectionUtils.isNotEmpty(productGroupM.getProducts())) {
            return productGroupM;
        }
        return null;
    }

    /**
     * 游泳卡数据不为空，并且不存在NO_SERVICE_TYPE，则显示
     * @param totalDeals
     * @param productGroupM
     * @return
     */
    private ProductGroupM filterSwimCard(List<ProductM> totalDeals, ProductGroupM productGroupM) {
        if (productGroupM != null && CollectionUtils.isNotEmpty(productGroupM.getProducts()) && !hasNoServiceTypeDeal(totalDeals)) {
            return productGroupM;
        }
        return null;
    }

    private boolean hasNoServiceTypeDeal(List<ProductM> totalDeals) {
        return CollectionUtils.isNotEmpty(totalDeals.stream().filter(
                product -> NO_SERVICE_TYPE.equals(getFormatServiceType(product)))
                .collect(Collectors.toList()));
    }

    /**
     * 商家推荐
     * @param productGroupM
     * @return
     */
    private ProductGroupM filterRecommendFloor(ProductGroupM productGroupM) {
        return productGroupM;
    }

    private void addIfNotNull(Map<String, ProductGroupM> result, String title, ProductGroupM productGroupM) {
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return;
        }
        result.put(title, productGroupM);
    }

    /**
     * 1、商家推荐   填充商家推荐的团单
     * 2、团购      全部
     * 3、游泳卡    服务类型（"单次票", "多次卡", "月卡", "年卡"）
     * 4、培训课    服务类型文本中包含培训
     * @param shopRecommendDeals
     * @param totalDeals
     * @return
     */
    private Map<String, ProductGroupM> fillFloors(List<ProductM> shopRecommendDeals, List<ProductM> totalDeals) {
        Map<String, ProductGroupM> returnValue = new HashMap<>();
        returnValue.put(SwimContext.SHOP_RECOMMEND_FLOOR, getRecommendDeals(shopRecommendDeals));
        returnValue.put(SwimContext.SWIM_DEAL_FLOOR, getNormalDeals(totalDeals));
        returnValue.put(SwimContext.SWIM_CARD_FLOOR, getSwimCard(totalDeals));
        returnValue.put(SwimContext.SWIM_COURSE_FLOOR, getSwimCourse(totalDeals));
        return returnValue;
    }

    /**
     * 服务类型train_course
     * @param notShopRecommendDeals
     * @return
     */
    private ProductGroupM getSwimCourse(List<ProductM> notShopRecommendDeals) {
        List<ProductM> selectedDeals = notShopRecommendDeals.stream().filter(
                product -> TRAIN_COURSE.equals(getFormatServiceType(product)))
                .collect(Collectors.toList());
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(selectedDeals);
        return productGroupM;
    }

    /**
     * serviceType（"单次票", "多次卡", "月卡", "年卡"）的任一种
     * @param notShopRecommendDeals
     * @return
     */
    private ProductGroupM getSwimCard(List<ProductM> notShopRecommendDeals) {
        List<ProductM> selectedDeals = notShopRecommendDeals.stream().filter(
                product -> SWIM_TYPE.contains(getFormatServiceType(product)))
                .collect(Collectors.toList());
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(selectedDeals);
        return productGroupM;
    }

    /**
     * 全部
     * @param totalDeals
     * @return
     */
    private ProductGroupM getNormalDeals(List<ProductM> totalDeals) {
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(totalDeals);
        return productGroupM;
    }

    /**
     * （"单次票", "多次卡", "月卡", "年卡"） -> 原样返回
     * 包含培训 -> train_course
     * 其他 -> no_servcie_type
     * @param productM
     * @return
     */
    private String getFormatServiceType(ProductM productM) {
        String origServiceType = getServiceType(productM);
        if (StringUtils.isEmpty(origServiceType)) {
            return NO_SERVICE_TYPE;
        }
        if (SWIM_TYPE.contains(origServiceType)) {
            return origServiceType;
        }
        if (origServiceType.contains("培训")) {
            return TRAIN_COURSE;
        }
        return NO_SERVICE_TYPE;
    }

    private String getServiceType(ProductM productM) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            return null;
        }
        List<AttrM> filterList = productM.getExtAttrs().stream().filter(attrM -> SwimContext.ONLINE_DEAL_ATTR_SERVICE_TYPE.equals(attrM.getName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterList)) {
            return null;
        }
        return filterList.get(0).getValue();
    }

    private List<ProductM> getOnlineDeals(ShopOnlineDealGroup onlineDealResponse) {
        List<ProductM> returnValue = new ArrayList<>();
        if (CollectionUtils.isEmpty(onlineDealResponse.getDealGroups())) {
            return returnValue;
        }
        for (DealGroupDTO dealGroup : onlineDealResponse.getDealGroups()) {
            returnValue.add(buildProductM(dealGroup));
        }
        return returnValue;
    }

    /**
     * 只填充Id、serviceType、suitablePeople
     * @param dealGroup
     * @return
     */
    private ProductM buildProductM(DealGroupDTO dealGroup) {
        ProductM productM = new ProductM();
        productM.setProductId(dealGroup.getDealGroupId());
        List<AttrM> attrMS = new ArrayList<>();
        // serviceType
        String serviceType = getSingleAttrValue("service_type", dealGroup.getAttributes());
        if (StringUtils.isNotEmpty(serviceType)) {
            attrMS.add(new AttrM(SwimContext.ONLINE_DEAL_ATTR_SERVICE_TYPE, serviceType));
        }
        // suitablePeople 适用人群
        String suitablePeople = getSingleAttrValue("swimming_suitable_person", dealGroup.getAttributes());
        if (StringUtils.isNotEmpty(suitablePeople)) {
            attrMS.add(new AttrM(SwimContext.ONLINE_DEAL_ATTR_SUITE_PEOPLE, suitablePeople));
        }
        productM.setExtAttrs(attrMS);
        return productM;
    }


    private String getSingleAttrValue(String attKey, List<Attribute> attributeList) {
        List<String> temp = getAttrValue(attKey,attributeList);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(temp)) {
            return StringUtils.EMPTY;
        }
        return temp.get(0);
    }

    private List<String> getAttrValue(String attKey,List<Attribute> attributeList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(attributeList) || StringUtils.isEmpty(attKey)) {
            return null;
        }
        for (Attribute attribute : attributeList) {
            if (attribute != null && attKey.equals(attribute.getName())
                    && org.apache.commons.collections.CollectionUtils.isNotEmpty(attribute.getValue())) {
                return attribute.getValue();
            }
        }
        return null;
    }

    /**
     * 获取商家推荐商品
     * 需要移除缓存的失效的团单
     * @param recommendDealResponse
     * @param totalOnlineDeals
     * @return
     */
    private List<ProductM> getShopRecommendDeals(Response<List<ShelfShopRecProduct>> recommendDealResponse, List<ProductM> totalOnlineDeals) {
        if (!hasRecommendDeals(recommendDealResponse)) {
            return new ArrayList<>();
        }
        List<Integer> totalOnlineDealIds = totalOnlineDeals.stream().map(ProductM::getProductId).collect(Collectors.toList());
        return recommendDealResponse.getContent().stream()
                .filter(shelfShopRecProduct -> hasShopRecommend(totalOnlineDealIds, shelfShopRecProduct))
                .map(ShelfShopRecProduct::getProductID)
                .map(productId -> buildProductM(productId.intValue()))
                .collect(Collectors.toList());
    }

    private boolean hasShopRecommend(List<Integer> totalOnlineDealIds, ShelfShopRecProduct shelfShopRecProduct) {
        return shelfShopRecProduct != null && shelfShopRecProduct.getProductID() != null
                && totalOnlineDealIds.contains(shelfShopRecProduct.getProductID().intValue());
    }

    /**
     * 填充商家推荐的团单
     * @param recommendDealResponse
     * @return
     */
    private ProductGroupM getRecommendDeals(List<ProductM> recommendDealResponse) {
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(recommendDealResponse);
        return productGroupM;
    }

    private boolean hasRecommendDeals(Response<List<ShelfShopRecProduct>> recommendDealResponse) {
        return recommendDealResponse != null && recommendDealResponse.isSuccess() && CollectionUtils.isNotEmpty(recommendDealResponse.getContent());
    }

    private boolean isNoOnlineDeals(Map<Long, ShopOnlineDealGroup> onlineDealResponse, Long dpShopId) {
        return MapUtils.isEmpty(onlineDealResponse) || CollectionUtils.isEmpty(onlineDealResponse.get(dpShopId).getDealGroups());
    }

    private CompletableFuture<Response<List<ShelfShopRecProduct>>> getRecommendFuture(ActivityContext ctx) {
        ShelfShopRecProductRequest request = buildQueryRecommendRequest(ctx);
        return compositeAtomService.multiGetShopRecommendProducts(request);
    }

    private ShelfShopRecProductRequest buildQueryRecommendRequest(ActivityContext ctx) {
        ShelfShopRecProductRequest request = new ShelfShopRecProductRequest();
        request.setShopID(getDpShopId(ctx));
        request.setProductType(Lists.newArrayList(ProductType.DEAL.getType()));
        request.setNavTagID(shopRecommendTabId);
        return request;
    }

    /**
     * 获取商户在线团单
     * @param ctx
     * @return
     */
    private CompletableFuture<Map<Long, ShopOnlineDealGroup>> getOnlineDealFuture(ActivityContext ctx) {
        ShopOnlineDealGroupRequest queryRequest = buildQueryOnlineRequest(ctx);
        return compositeAtomService.batchGetLongShopOnlineDealGroups(queryRequest);
    }

    private ProductM buildProductM(int productId) {
        ProductM productM = new ProductM();
        productM.setProductId(productId);
        return productM;
    }

    private ShopOnlineDealGroupRequest buildQueryOnlineRequest(ActivityContext activityContext) {
        ShopOnlineDealGroupRequest request = new ShopOnlineDealGroupRequest();
        int vcPlatform = getPlatform(activityContext);
        request.setShopIds(Lists.newArrayList(getDpShopId(activityContext).intValue()));
        request.setLongShopIds(Lists.newArrayList(getDpShopId(activityContext)));
        // 1是团单基础信息,2是团单属性,3是团单销量
        request.setTypes(Lists.newArrayList(1, 2, 3));
        // 默认1按照销量排序
        request.setSortType(1);
        request.setStart(0);
        // 只能传点评侧CityId
        request.setUserCityId(getDpCityId(activityContext));
        request.setLimit(maxQueryNum);
        // "service_type"：服务类型，"swimming_suitable_person"：适用人群
        request.setRequiredAttributes(Lists.newArrayList("service_type", "swimming_suitable_person"));
        request.setPlatform(vcPlatform);
        return request;
    }

    private int getDpCityId(ActivityContext activityContext) {
        return NumberUtils.toInt(activityContext.getParam(ShelfActivityConstants.Params.dpCityId) + "", 0);
    }

    private int getPlatform(ActivityContext activityContext) {
        int platform = activityContext.getParam(ShelfActivityConstants.Params.platform);
        return PlatformUtil.getPlatform(platform);
    }

    private Long getDpShopId(ActivityContext activityContext) {
        return PoiIdUtil.getDpPoiIdL(activityContext, ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId);
    }
}