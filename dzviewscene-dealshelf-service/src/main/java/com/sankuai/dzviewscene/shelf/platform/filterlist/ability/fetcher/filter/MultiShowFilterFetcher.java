package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.AbilityInstance;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.ShowFilterHandler;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.impl.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import graphql.execution.Async;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 基础展示筛选配置 + 删选配置后处理通用能力
 *
 * Created by float.lu on 2020/9/22.
 */
@AbilityInstance(name = "批量扩展筛选生成能力实例")
public class MultiShowFilterFetcher extends ShowFilterFetcher implements BeanFactoryAware {

    /**
     * 筛选条件ID和配置处理器的映射关系
     */
    private static Map<Integer, Class<?>> handlers = new HashMap<>();

    static {
        handlers.put(FilterTypeEnum.Filter.id, FilterShowFilterHandler.class);
        handlers.put(FilterTypeEnum.Location.id, LocationShowFilterHandler.class);
        handlers.put(FilterTypeEnum.BackroomThemeZone.id, BackroomThemeZoneShowFilterHandler.class);
        handlers.put(FilterTypeEnum.BookingTimeRange.id, BookingTimeRangeFilterHandler.class);
        handlers.put(FilterTypeEnum.RecommendNearby.id, RecommendNearbyFilterFetcher.class);
    }

    @Resource
    private FilterConfig filterConfig;

    @Resource
    private BeanFactory beanFactory;

    @Override
    public CompletableFuture<List<FilterM>> build(ActivityContext ctx) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.MultiShowFilterFetcher.build(com.sankuai.dzviewscene.shelf.framework.ActivityContext)");
        List<FilterM> filters = filterConfig.getShowFilters(ctx.getSceneCode());
        if (CollectionUtils.isEmpty(filters)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }

        return handleFilters(ctx, filters).thenApply(filterMs -> {
            if (CollectionUtils.isEmpty(filterMs)) {
                return Lists.newArrayList();
            }
            List<FilterM> validFilterMs = filterMs.stream().filter(filterM -> filterM != null).collect(Collectors.toList());
            setSelected(ctx, validFilterMs);
            return validFilterMs;
        });
    }

    private void setSelected(ActivityContext activityContext, List<FilterM> filterMs) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.MultiShowFilterFetcher.setSelected(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.List)");
        if (CollectionUtils.isEmpty(filterMs)) {
            return;
        }
        filterMs.forEach(filterM -> {
            if (CollectionUtils.isEmpty(filterM.getFilters())) {
                return;
            }
            int selectedId = ParamsUtil.getIntSafely(activityContext, FilterTypeEnum.getParamName(filterM.getType()));
            if (selectedId <= 0) {
                return;
            }
            filterM.getFilters().forEach(filterBtnM -> ModelUtils.setSelectStatusRecursively(selectedId, filterBtnM, filterBtnM.getChildren()));
        });
    }

    private CompletableFuture<List<FilterM>> handleFilters(ActivityContext activityContext, List<FilterM> filterMs ) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.MultiShowFilterFetcher.handleFilters(com.sankuai.dzviewscene.shelf.framework.ActivityContext,java.util.List)");
        List<CompletableFuture<FilterM>> filterCompletableFutureList = filterMs.stream().map(filterM -> {
            ShowFilterHandler showFilterHandler = getHandlerQuietly(handlers.get(filterM.getType()));
            if (showFilterHandler == null) {
                return CompletableFuture.completedFuture(filterM);
            }
            return showFilterHandler.handle(activityContext, filterM);
        }).collect(Collectors.toList());

        return Async.each(filterCompletableFutureList);
    }

    private <T> T getHandlerQuietly(Class<?> beanClazz) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.MultiShowFilterFetcher.getHandlerQuietly(java.lang.Class)");
        try {
            return (T) beanFactory.getBean(beanClazz);
        } catch (Exception e) {
            // NO OP
        }
        return null;
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}
