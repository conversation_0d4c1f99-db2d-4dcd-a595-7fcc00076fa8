package com.sankuai.dzviewscene.shelf.business.shelf.home;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.home.builder.*;
import com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher.MethanalFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.business.shelf.home.validator.HomeValidator;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mock.MockPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultiplexQueryFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.MergeQueryFilterLastShelfFlow;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**甲醛团购货架模版类
 * <AUTHOR>
 * @date 2020/12/1 8:01 下午
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "home_poi_methanal_deal_shelf", name = "甲醛团购货架模版")
public class MethanalDealShelfTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        return MergeQueryFilterLastShelfFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {

        // 1. 设置只展示一组商品, 商品组名使用dealGroup
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.deal.name()));

        // 2. 设置商品组召回和填充参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.deal.name(), new HashMap<String, Object>() {{
                // 2.1 设置商品组召回渠道为门店在线团单
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.shopOnLineDealsQuery.name());
                // 2.2 设置第一组商品填充参数
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                put("attributeKeys", new ArrayList<String>(){{
                    //私人住宅属性名
                    add("scene_decorate_house");
                    //公共住宅属性名
                    add("decorate_public_place");
                    //汽车属性名
                    add("decorate_car");
                    //代金券属性名
                    add("sys_deal_universal_type");
                    //场景属性名
                    add("scene_decorate");
                    add("sence_formaldehyde_governance");
                    add("sence_formaldehyde_detection");
                    add("formaldehyde_content");
                }});
                put(MergeQueryFetcher.Params.rankId, "10100006");
                put(PaddingFetcher.Params.planId, "10000050");
                put("directPromoScene", 400200); // 优惠感知400200
                put("priceDescType", 2); // 双列货架传2
            }});
        }});
        //3. 设置showType
        exParams.put(ShelfActivityConstants.Style.showType, 9);

        exParams.put(DouHuFetcher.Params.DP_EXP_ID, DouHuUtils.getDPDouHuExpId("home_poi_methanal_deal_shelf", "searchProductTop"));
        exParams.put(DouHuFetcher.Params.MT_EXP_ID, DouHuUtils.getMTDouHuExpId("home_poi_methanal_deal_shelf", "searchProductTop"));
    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        // 1. 召回能力实现
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);// MockPaddingFetcher  MultiGroupPaddingFetcher
        // 3. 多路分组筛选查询能力
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, MultiplexQueryFilterFetcher.class);
        // 4. 注册融合查询能力
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
        // 5. 斗斛实验能力
        abilities.put(DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformDouHuFetcher.class);

    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        // 1. 注册筛选分组模块构造扩展点实现
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, MethanalFilterFetcherExt.class);
        // 2. 注册货架楼层模块构造扩展点实现
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, MethanalFloorsBuilderExt.class);
        // 3. 注册货架主标题模块构造扩展点实现
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, MethanalMainTitleBuilderExt.class);
        // 4. 注册货架筛选项模块构造扩展点实现，区别于"FilterFetcherExt"，此处是构造筛选栏每个tab的样式
        extPoints.put(FilterBuilderExt.EXT_POINT_FILTER_COMPONENT_CODE, MethanalFilterBuilderExt.class);
        // 5. 注册打点模块构造扩展点实现
        extPoints.put(OceanBuilderExt.SHELF_OCEAN_EXT_CODE, HomeOceanBuilderExt.class);
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        //validators.add(HomeValidator.class);
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        extContexts.add(ActivityContextExt.class);
    }
}

