package com.sankuai.dzviewscene.shelf.business.shelf.home;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.common.builder.CommonFilterBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.home.builder.FurnitureFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.home.builder.FurnitureMainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.home.builder.HomeOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.home.fetcher.HomeFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.business.shelf.home.validator.HomeValidator;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.EmptyFilterBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.WholePlatformShelfFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.WholePlatformShelfQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.MergeQueryFilterFirstShelfFlow;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by zhangsuping on 2020/9/28.
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "home_poi_furniture_only_voucher_shelf", name = "家居-家具家居-仅返回所有代金券，不含团购")
public class FurnitureOnlyWholeVoucherTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.flow()");
        return MergeQueryFilterFirstShelfFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.extParams(java.util.Map)");
        // 1. 设置只展示一组商品, 商品组名使用dealGroup
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList("代金券"));

        // 2. 设置商品组召回和填充参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put("代金券", new HashMap<String, Object>() {{
                // 2.2 设置第一组商品填充参数
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
                put("attributeKeys", buildAttributeKeys());
                put(PaddingFetcher.Params.planId, "10002117");
                put(MergeQueryFetcher.Params.rankId, "10100005");
                put("directPromoScene", 400200); // 优惠感知400200
                put("priceDescType", 2); // 双列货架传2
            }});
        }});
        //3. 设置showType
        exParams.put(DouHuFetcher.Params.DP_EXP_ID, DouHuUtils.getDPDouHuExpId("home_poi_furniture_only_voucher_shelf", "entryDetail"));
        exParams.put(DouHuFetcher.Params.MT_EXP_ID, DouHuUtils.getMTDouHuExpId("home_poi_furniture_only_voucher_shelf", "entryDetail"));

        exParams.put(QueryFetcher.Params.platformSceneCode, "furniture_home_furnishings_voucher");

        exParams.put(FilterFetcher.Params.filterComponent2Group, new HashMap<String, String>(){{
            put("代金券"/*filter组件ID*/, "代金券");
        }});
        exParams.put(QueryFetcher.Params.productComponent2Group, new HashMap<String, String>(){{
            put("代金券"/*商品组件ID*/,  "团购");
        }});

        exParams.put(ShelfActivityConstants.Style.showType, getShowTypeByVersion(exParams));

    }

    private int getShowTypeByVersion(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.getShowTypeByVersion(java.util.Map)");
        if (!exParams.containsKey(ShelfActivityConstants.Params.shelfVersion)) {
            return 9;
        }
        Integer version = (Integer) exParams.get(ShelfActivityConstants.Params.shelfVersion);
        if (version == null || version < 100) {
            return 9;
        }
        return 60203;
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.extContexts(java.util.List)");
        extContexts.add(ActivityContextExt.class);
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.extValidators(java.util.List)");
        validators.add(HomeValidator.class);
    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.extAbilities(java.util.Map)");
        // 1. 注册筛选能力实现
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, WholePlatformShelfFilterFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 3. 多路分组筛选查询能力
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, WholePlatformShelfQueryFetcher.class);
        // 4. 注册融合查询能力
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
        // 5. 斗斛实验能力
        abilities.put(DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformDouHuFetcher.class);
        //6. 筛选构造能力
        abilities.put(FilterBuilder.ABILITY_FILTER_GEN_CODE, EmptyFilterBuilder.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_5", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.extPoints(java.util.Map)");
        // 1. 注册筛选分组模块构造扩展点实现
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, HomeFilterFetcherExt.class);
        // 2. 注册货架楼层模块构造扩展点实现
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, FurnitureFloorsBuilderExt.class);
        // 3. 注册货架主标题模块构造扩展点实现
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, FurnitureMainTitleBuilderExt.class);
        // 4. 注册货架筛选项模块构造扩展点实现
        extPoints.put(FilterBuilderExt.EXT_POINT_FILTER_COMPONENT_CODE, CommonFilterBuilderExt.class);
        // 5. 注册打点模块构造扩展点实现
        extPoints.put(OceanBuilderExt.SHELF_OCEAN_EXT_CODE, HomeOceanBuilderExt.class);
    }

    private List<String> buildAttributeKeys() {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.shelf.home.FurnitureOnlyWholeVoucherTemplate.buildAttributeKeys()");
        return Lists.newArrayList("sys_deal_universal_type");
    }
}
