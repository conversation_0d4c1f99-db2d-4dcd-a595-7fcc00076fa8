package com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl;

import com.dianping.cat.Cat;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.PaddingParallelUtils;
import com.sankuai.dzviewscene.shelf.business.utils.ParamUtil;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.monitor.TraceElement;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.GroupPaddingHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import com.sankuai.dzviewscene.shelf.platform.utils.ProductMUtils;
import com.sankuai.mppack.api.client.enums.*;
import com.sankuai.mppack.api.client.request.CombineProductInfoRequest;
import com.sankuai.mppack.api.client.request.QueryId;
import com.sankuai.mppack.api.client.request.UserParam;
import com.sankuai.mppack.api.client.response.CombineProductInfo;
import com.sankuai.mppack.api.client.response.CombineProductInfoResponse;
import com.sankuai.mppack.api.client.response.ComponentProductInfo;
import com.sankuai.mppack.api.client.response.JumpUrl;
import com.sankuai.mppack.api.client.response.price.PriceInfo;
import com.sankuai.mppack.api.client.response.price.PromoExplanation;
import com.sankuai.mppack.api.client.response.price.PromotionDetail;
import com.sankuai.mppack.api.client.response.price.PromotionSummary;
import com.sankuai.mppack.api.client.response.vo.DisplayTagVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 组合产品填充处理器
 */
@Slf4j
@Component
public class CombinationProductPaddingHandler implements GroupPaddingHandler {

    private static final int PADDING_PRODUCT_LIMIT = 20;
    public static final BigDecimal B_1_W = new BigDecimal(10000);
    public static final BigDecimal B_100 = new BigDecimal(100);
    public static final String SCENE = "MALL_WALK_CARD";
    public static final String SALE_TEMPLATE = "年售%s";
    public static final String PROMO_TAG_TEMPLATE = "比单买省%s";
    public static final String PROMO_ICON_URL = "https://p1.meituan.net/travelcube/1339ce7ffc6fbf31a8edc399641779bf17929.png";
    public static final String PROMO_ICON_TEXT = "套餐专属";
    public static final int COUPON_TYPE = 0;
    public static final String FILTER_PROMO_TITLE = "套餐专属优惠";

    @Resource
    private CompositeAtomService compositeAtomService;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.deal.theme.rpc.service.new.switch", defaultValue = "false")
    private boolean dealThemeRpcServiceNewSwitch;

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.deal.theme.rpc.service.new.scenecode.list", defaultValue = "[]")
    private List<String> dealThemeRpcServiceNewSceneCodeList;

    @Override
    public CompletableFuture<ProductGroupM> padding(ActivityContext activityContext, ProductGroupM productGroupM, Map<String, Object> params) {
        if (productGroupM == null || CollectionUtils.isEmpty(productGroupM.getProducts()) || MapUtils.isEmpty(params)) {
            return CompletableFuture.completedFuture(new ProductGroupM());
        }
        if (usePrePadding(activityContext, productGroupM)) {
            //走预填充合并
            PaddingParallelUtils.parallelPaddingMonitor(activityContext, productGroupM, ProductTypeEnum.PACK.getType());
            return CompletableFuture.completedFuture(paddingByPrePaddingResult(productGroupM));
        }

        // 调用主题
        CompletableFuture<List<CombineProductInfoResponse>> responseCompletableFuture = batchQueryProductTheme(activityContext, productGroupM, params);
        return responseCompletableFuture.thenApply(productResultList -> {
            ProductMUtils.asyncDiffPaddingPreHandler(activityContext, productGroupM, ProductTypeEnum.PACK.getType());
            // 填充
            productResultList.forEach(productResult -> paddingWithDealProductResult(productGroupM, productResult));
            ProductMUtils.asyncDiffPrePadding(activityContext, productGroupM, ProductTypeEnum.PACK.getType());
            return productGroupM;
        });
    }

    private CompletableFuture<List<CombineProductInfoResponse>> batchQueryProductTheme(ActivityContext activityContext, ProductGroupM productGroupM,
                                                                                       Map<String, Object> params) {
        if (CollectionUtils.isEmpty(productGroupM.getProducts())) {
            return CompletableFuture.completedFuture(null);
        }
        List<CompletableFuture<CombineProductInfoResponse>> paddingResultFutureList = new ArrayList<>();
        int startIndex = 0;
        int fetchCount = (int) Math.ceil(productGroupM.getProducts().size() / (PADDING_PRODUCT_LIMIT * 1.0));
        for (int i = startIndex; i < fetchCount; i++) {
            int endIndex = Math.min((i + 1) * PADDING_PRODUCT_LIMIT, productGroupM.getProducts().size());
            List<ProductM> products = productGroupM.getProducts().subList(i * PADDING_PRODUCT_LIMIT, endIndex);
            CombineProductInfoRequest productRequest = buildCombProductRequest(activityContext, productGroupM, products, params);
            CompletableFuture<CombineProductInfoResponse> productResultCompletableFuture
                    = compositeAtomService.batchQueryCombinationProductInfo(productRequest);

            addInvokeTraceElement(activityContext, System.currentTimeMillis(), productResultCompletableFuture, productRequest);
            paddingResultFutureList.add(productResultCompletableFuture);
        }
        return assemble(paddingResultFutureList);
    }

    private boolean usePrePadding(ActivityContext activityContext, ProductGroupM productGroupM) {
        //diff不走预填充
        return !ProductMUtils.enableProductDiff(activityContext, ProductTypeEnum.PACK.getType()) && MapUtils.isNotEmpty(productGroupM.getPreLoadProducts()) &&
                CollectionUtils.isNotEmpty(productGroupM.getPreLoadProducts().get(ProductTypeEnum.PACK.getType()));
    }

    private ProductGroupM paddingByPrePaddingResult(ProductGroupM productGroupM) {
        List<ProductM> preProductMS = productGroupM.getPreLoadProducts().get(ProductTypeEnum.PACK.getType());
        Map<Integer, ProductM> preProductMap = preProductMS.stream()
                .collect(HashMap::new, (map, productM) -> map.put(productM.getProductId(), productM), HashMap::putAll);
        List<ProductM> productMS = Lists.newArrayList();
        productGroupM.getProducts().forEach(productM -> {
                    paddingByPreProductM(productM, preProductMap.get(productM.getProductId()));
                    if (productM.getProductType() == ProductTypeEnum.PACK.getType()
                            && !productM.isPrePadded()) {
                        Cat.logEvent("CombinationProductPaddingHandler", "padding.miss");
                        return;
                    }
                    productMS.add(productM);
                }
        );
        productGroupM.setProducts(productMS);
        return productGroupM;
    }


    private void paddingByPreProductM(ProductM productM, ProductM preProductM) {
        if (preProductM == null) {
            // miss
            return;
        }
        ProductMUtils.copyProductM(productM, preProductM);
    }

    private void addInvokeTraceElement(ActivityContext activityContext, long startTime,
                                       CompletableFuture<CombineProductInfoResponse> productResultCompletableFuture,
                                       CombineProductInfoRequest productRequest) {
        if (productResultCompletableFuture == null || !activityContext.isTrace()) {
            return;
        }
        productResultCompletableFuture.whenComplete((result, throwable) -> {
            try {
                activityContext.addTrace(TraceElement.build(
                        "业务点",
                        "组合商品主题填充",
                        JacksonUtils.serialize(productRequest),
                        result,
                        throwable,
                        System.currentTimeMillis() - startTime
                ));
            } catch (Exception e) {

            }
        });
    }

    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }

    private void paddingWithDealProductResult(ProductGroupM productGroupM, CombineProductInfoResponse productResult) {
        //log.info("返回结果为：" + JsonCodec.encodeWithUTF8(productResult));
        if (productResult == null || MapUtils.isEmpty(productResult.getData())) {
            return;
        }
        Map<Integer, CombineProductInfo> products = buildProductInfoMap(productResult.getData());
        productGroupM.getProducts().forEach(productM -> paddingProductM(productM, products.get(productM.getProductId())));
    }

    private Map<Integer, CombineProductInfo> buildProductInfoMap(Map<Long, List<CombineProductInfo>> data) {
        Map<Integer, CombineProductInfo> result = new HashMap<>();
        for (Long productId : data.keySet()) {
            List<CombineProductInfo> combineProductInfos = data.get(productId);
            if (CollectionUtils.isEmpty(combineProductInfos)) {
                continue;
            }
            result.put(productId.intValue(), combineProductInfos.get(0));
        }
        return result;
    }


    private void paddingProductM(ProductM productM, CombineProductInfo combineProductInfo) {
        if (combineProductInfo == null) {
            // miss
            return;
        }
        // 1. 团单ID
        productM.setProductId(combineProductInfo.getProductId().intValue());
        // 2. 团单标题
        productM.setTitle(combineProductInfo.getBaseInfo().getTitle());
        // 3. 团单详情跳转URL
        productM.setJumpUrl(getJumpUrl(combineProductInfo.getBaseInfo().getJumpUrl()));
        // 4. 团单销量
        productM.setSale(buildProductSale(combineProductInfo.getSoldCount()));
        // 5. 团单售卖价格标签
        productM.setBasePriceTag(buildBasePriceTag(combineProductInfo.getPriceInfo()));
        // 6. 团单原始售卖价格
        productM.setBasePrice(buildBasePrice(combineProductInfo.getPriceInfo()));
        // 7. 团单市场价格
        productM.setMarketPrice(buildMarketPrice(combineProductInfo.getPriceInfo()));
        // 8. 融合优惠
        productM.setPromoPrices(buildPromoPrice(combineProductInfo.getPriceInfo(), combineProductInfo.getPromotionSummary()));
        // 9. 团单标准商品标签
        productM.setProductTags(buildProductTags(combineProductInfo.getDisplayTagList()));
        // 10.团单商品头图
        productM.setPicUrl(combineProductInfo.getBaseInfo().getHeadImage());
        // 11.商品活动 本期没有
//        productM.setActivities(buildDealActivities(combineProductInfo.getActivities()));
        // 12.下单链接
        productM.setOrderUrl(getJumpUrl(combineProductInfo.getBaseInfo().getBuyUrl()));
        // 13.标记下已填充
        productM.setPrePadded(true);
        // 14.团单是否为太极团单
        productM.setUnifyProduct(false);
        // 15. 添加attr
        paddingProductMAttrs(combineProductInfo, productM);
    }

    private List<ProductPromoPriceM> buildPromoPrice(PriceInfo priceInfo, PromotionSummary promotionSummary) {
        if (promotionSummary == null || CollectionUtils.isEmpty(promotionSummary.getPromoExplanationList())) {
            return Lists.newArrayList();
        }
        List<PromoExplanation> promoExplanationList = promotionSummary.getPromoExplanationList().stream()
                .filter(promoExplanation -> FILTER_PROMO_TITLE.equals(promoExplanation.getTitle()) && promoExplanation.getDiscount() != null && promoExplanation.getDiscount() > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(promoExplanationList)) {
            return Lists.newArrayList();
        }
        int totalPromoInt = getTotalPromoInt(promoExplanationList);

        List<ProductPromoPriceM> promoPrices = new ArrayList<>();
        ProductPromoPriceM dealProductPromoDTO = getProductPromoPriceM(priceInfo, promoExplanationList, totalPromoInt);
        promoPrices.add(dealProductPromoDTO);

        return promoPrices;
    }

    private ProductPromoPriceM getProductPromoPriceM(PriceInfo priceInfo, List<PromoExplanation> promoExplanationList, int totalPromoInt) {
        ProductPromoPriceM dealProductPromoDTO = new ProductPromoPriceM();
        BigDecimal promoPrice = new BigDecimal(priceInfo.getPayPrice()).divide(B_100, 2, BigDecimal.ROUND_HALF_UP)
                .stripTrailingZeros();
        BigDecimal totalPromo = new BigDecimal(totalPromoInt).divide(B_100, 2, BigDecimal.ROUND_HALF_UP)
                .stripTrailingZeros();
        dealProductPromoDTO.setPromoType(PromoTypeEnum.DIRECT_PROMO.getType());
        dealProductPromoDTO.setPromoPrice(promoPrice);
        dealProductPromoDTO.setPromoPriceTag(promoPrice.stripTrailingZeros().toPlainString());
        dealProductPromoDTO.setPromoTag(String.format(PROMO_TAG_TEMPLATE, totalPromo.toPlainString()));
        dealProductPromoDTO.setPromoTagType(PromoTagTypeEnum.Official_Subsidies.getCode());
        dealProductPromoDTO.setIcon(PROMO_ICON_URL);
        dealProductPromoDTO.setIconText(PROMO_ICON_TEXT);
        return dealProductPromoDTO;
    }

    private int getTotalPromoInt(List<PromoExplanation> promoExplanationList) {
        int totalPromoInt = 0;
        for (PromoExplanation promoExplanation : promoExplanationList) {
            totalPromoInt = totalPromoInt + promoExplanation.getDiscount();
        }
        return totalPromoInt;
    }

    private void paddingProductMAttrs(CombineProductInfo combineProductInfo, ProductM productM) {
        List<AttrM> themeAttrs = buildThemeProductAttr(combineProductInfo);
        mergeThemeAttrs2ProductMAttrs(productM, themeAttrs);
    }

    private List<AttrM> buildThemeProductAttr(CombineProductInfo combineProductInfo) {
        List<AttrM> attrMList = new ArrayList<>();
        addAttrIfNotNull(attrMList, buildIssueCouponAttr(combineProductInfo));
        return attrMList;
    }

    private void addAttrIfNotNull(List<AttrM> attrMList, AttrM attrM) {
        if (attrM == null) {
            return;
        }
        attrMList.add(attrM);
    }

    private AttrM buildIssueCouponAttr(CombineProductInfo combineProductInfo) {
        if (CollectionUtils.isEmpty(combineProductInfo.getComponentProductInfoList())) {
            return null;
        }
        List<DealIssueCouponUnit> couponUnits = readIssueCouponUnits(combineProductInfo.getComponentProductInfoList());
        if (CollectionUtils.isEmpty(couponUnits)) {
            return null;
        }
        return new AttrM("newIssueCouponUnitList", JsonCodec.encodeWithUTF8(couponUnits));
    }

    private List<DealIssueCouponUnit> readIssueCouponUnits(List<ComponentProductInfo> componentProductInfoList) {
        List<DealIssueCouponUnit> result = new ArrayList<>();
        for (ComponentProductInfo componentProductInfo : componentProductInfoList) {
            if (noIssueCouponUnits(componentProductInfo)) {
                continue;
            }
            List<DealIssueCouponUnit> issueCouponUnits = componentProductInfo.getPromotionSummary().getPromotionDetailList().stream()
                    .filter(detail -> BooleanUtils.isTrue(detail.getVoucherAssign()))
                    .map(detail -> buildIssueCouponUnit(componentProductInfo, detail))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(issueCouponUnits)) {
                result.addAll(issueCouponUnits);
            }
        }
        return result;
    }

    private boolean noIssueCouponUnits(ComponentProductInfo componentProductInfo) {
        return componentProductInfo.getPromotionSummary() == null
                || CollectionUtils.isEmpty(componentProductInfo.getPromotionSummary().getPromotionDetailList());
    }

    private DealIssueCouponUnit buildIssueCouponUnit(ComponentProductInfo componentProductInfo, PromotionDetail detail) {
        DealIssueCouponUnit dealIssueCouponUnit = new DealIssueCouponUnit();
        dealIssueCouponUnit.setCouponType(COUPON_TYPE);
        dealIssueCouponUnit.setUnitId(UUID.randomUUID().toString());
        dealIssueCouponUnit.setProductId(componentProductInfo.getProductId());
        dealIssueCouponUnit.setUnifiedCouponGroupId(detail.getPromotionGroupId());
        return dealIssueCouponUnit;
    }

    private void mergeThemeAttrs2ProductMAttrs(ProductM productM, List<AttrM> themeAttrs) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            productM.setExtAttrs(Lists.newArrayList());
        }
        productM.getExtAttrs().addAll(themeAttrs);
    }

    private List<String> buildProductTags(List<DisplayTagVO> displayTagList) {
        if (CollectionUtils.isEmpty(displayTagList)) {
            return null;
        }
        return displayTagList.stream()
                .filter(tag -> tag != null && tag.getDisplayTag() != null && StringUtils.isNotBlank(tag.getDisplayTag().getText()))
                .map(tag -> StringUtils.trim(tag.getDisplayTag().getText()))
                .collect(Collectors.toList());
    }

    private String buildMarketPrice(PriceInfo priceInfo) {
        if (priceInfo == null || priceInfo.getMarketPrice() == null || priceInfo.getMarketPrice() < 0) {
            return null;
        }
        return new BigDecimal(priceInfo.getMarketPrice()).divide(B_100, 2, BigDecimal.ROUND_HALF_UP)
                .stripTrailingZeros().toPlainString();
    }

    private BigDecimal buildBasePrice(PriceInfo priceInfo) {
        if (priceInfo == null || priceInfo.getPayPrice() == null || priceInfo.getPayPrice() < 0) {
            return null;
        }
        // 注意：由于打包商品的优惠比较特殊，商品会返回所有优惠，但是我们只能展示套餐优惠，所以可能导致payPrice小于sellPrice，但是没有promoM
        // 此时页面展示basePrice值，就需要直接使用payPrice（真实付款金额）
        // 目前来看，sellPrice已经被废弃，不会透出前端，所以直接用payPrice
        return new BigDecimal(priceInfo.getPayPrice()).divide(B_100, 2, BigDecimal.ROUND_HALF_UP)
                .stripTrailingZeros();
    }

    private String buildBasePriceTag(PriceInfo priceInfo) {
        if (priceInfo == null || priceInfo.getPayPrice() == null || priceInfo.getPayPrice() < 0) {
            return null;
        }
        return new BigDecimal(priceInfo.getPayPrice()).divide(B_100, 2, BigDecimal.ROUND_HALF_UP)
                .stripTrailingZeros().toPlainString();
    }

    private ProductSaleM buildProductSale(Long soldCount) {
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSale(soldCount != null ? soldCount.intValue() : 0);
        productSaleM.setSaleTag(getSaleStr(soldCount));
        return productSaleM;
    }

    private String getSaleStr(long totalSaleNum) {
        String saleStr = getFuzzySaleStr(totalSaleNum);
        if (StringUtils.isBlank(saleStr)) {
            return null;
        }
        return String.format(SALE_TEMPLATE, saleStr);
    }

    private String getFuzzySaleStr(long totalSaleNum) {
        if (totalSaleNum < 1) {
            return null;
        }
        if (totalSaleNum < 50) {
            return totalSaleNum + "";
        }
        if (totalSaleNum < 100) {
            return (totalSaleNum / 10) * 10 + "+";
        }
        if (totalSaleNum < 1000) {
            return (totalSaleNum / 100) * 100 + "+";
        }
        if (totalSaleNum < 10000) {
            return (totalSaleNum / 1000) * 1000 + "+";
        }
        return new BigDecimal(totalSaleNum).divide(B_1_W, BigDecimal.ROUND_DOWN, 1).stripTrailingZeros().toPlainString() + "万+";
    }

    private String getJumpUrl(JumpUrl jumpUrl) {
        if (jumpUrl == null) {
            return null;
        }
        // 仅支持app，小程序要额外打通（双方环境适配）
        return jumpUrl.getNativeUrl();
    }

    private CombineProductInfoRequest buildCombProductRequest(ActivityContext activityContext, ProductGroupM productGroupM,
                                                              List<ProductM> products, Map<String, Object> params) {
        long shopId = ParamUtil.getPoiId(activityContext);

        List<Integer> productIdList = products.stream().map(ProductM::getProductId).collect(Collectors.toList());
        CombineProductInfoRequest productRequest = new CombineProductInfoRequest();
        productRequest.setQueryIdList(productIdList.stream().map(productId -> buildQueryId(productId, shopId)).collect(Collectors.toList()));
        productRequest.setScene(SCENE);
        productRequest.setPageSource(PageSourceEnum.GENERAL_SHELF.getCode());
        productRequest.setUserParam(buildUserParam(activityContext));
        return productRequest;
    }

    private UserParam buildUserParam(ActivityContext activityContext) {
        Integer platform = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.platform)).orElse(0);
        Integer dpCityId = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.dpCityId)).orElse(0);
        Integer mtCityId = Optional.ofNullable((Integer) activityContext.getParam(ShelfActivityConstants.Params.mtCityId)).orElse(0);
        Long dpUserId = Optional.ofNullable((Long) activityContext.getParam(ShelfActivityConstants.Params.dpUserId)).orElse(0L);
        Long mtUserId = Optional.ofNullable((Long) activityContext.getParam(ShelfActivityConstants.Params.mtUserId)).orElse(0L);
        String clientType = Optional.ofNullable((String) activityContext.getParam(ShelfActivityConstants.Params.clientType)).orElse("");
        String deviceId = activityContext.getParam(ShelfActivityConstants.Params.deviceId);

        UserParam userParam = new UserParam();
        if (PlatformUtil.isMT(platform)) {
            userParam.setUserId(mtUserId);
            userParam.setPageCityId(mtCityId.toString());
            userParam.setSellChannel(SellChannelEnum.MT.getCode());
            userParam.setUuid(deviceId);
        } else {
            userParam.setUserId(-1);
            userParam.setPageCityId(dpCityId.toString());
            userParam.setSellChannel(SellChannelEnum.DP.getCode());
            userParam.setDpUserId(dpUserId);
        }
        userParam.setDeviceId(deviceId);
        userParam.setClientType("android".equals(clientType) ? ClientTypeEnum.android.name() : ClientTypeEnum.iphone.name());
        userParam.setClientVersion(activityContext.getParam(ShelfActivityConstants.Params.appVersion));
        return userParam;
    }

    private QueryId buildQueryId(Integer productId, long shopId) {
        QueryId queryId = new QueryId();
        queryId.setIdDimension(QueryIdDimension.PRODUCT.getCode());
        queryId.setBizLine(BizLine.GENERAL.name());

        Map<Integer, Long> ids = new HashMap<>();
        ids.put(IdTypeEnum.PRODUCT.getCode(), productId.longValue());
        queryId.setIds(ids);

        Map<String, String> extra = new HashMap<>();
        extra.put("poiId", Long.valueOf(shopId).toString());
        queryId.setExt(extra);
        return queryId;
    }

    /**
     * 请求发券单元
     */
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DealIssueCouponUnit {

        /**
         * 用于标志发券请求单元的惟一id，返回结果：通过该字段识别发券结果详情
         */
        private String unitId;

        /**
         * 商品id
         */
        private Long productId;

        /**
         * 券类型，0:opt券，目前仅支持opt券
         */
        private Integer couponType;

        /**
         * 券活动id
         */
        private String unifiedCouponGroupId;

    }
}
