package com.sankuai.dzviewscene.shelf.business.shelf.life;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.shelf.beauty.context.ActivityContextExt;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.DouHuExtContext;
import com.sankuai.dzviewscene.shelf.business.shelf.life.builder.LifeDealFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.life.builder.LifeDealMainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.business.shelf.life.builder.LifeDealOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.PlatformDouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean.OceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.ConfigFilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcherExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultiplexQueryFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.flow.MergeQueryOnlyProductsShelfFlow;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生活服务-POI页-养老团购货架
 * Created by liweilong06 on 2020/02/06
 */
@ActivityTemplate(activityCode = ShelfActivity.ACTIVITY_SHELF_CODE, sceneCode = "life_poi_default_deal_shelf", name = "生活服务默认团购货架模板")
public class LifeDefaultDealProductsTemplate implements IActivityTemplate {
    @Override
    public Class<? extends IActivityFlow> flow() {
        return MergeQueryOnlyProductsShelfFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        // 1. 设置召回参数, 代表只召回一组商品, 商品是化妆品
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList("团购"));

        // 2. 设置第一组商品参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>(){{
            put("团购", new HashMap<String, Object>(){{
                // 2.1 设置第一组商品填充参数
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.shopOnLineDealCacheProxyQuery.name());
                put(PaddingFetcher.Params.planId, "10002132");
                put("promoTemplateId", 145);
                put("tcMergePromoTemplateId", 226);
                put("attributeKeys", buildAttrKeys());
                put("enablePreSalePromoTag", true);
                put("directPromoScene", 400200);
                put("priceDescType", 4);
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
            }});
        }});

        // 3. 设置商品平台组件和商品组映射关系
        exParams.put(QueryFetcher.Params.productComponent2Group, new HashMap<String, String>(){{
            put("团购"/*商品组件ID*/,  "团购");
        }});
        //4. 设置showType
        exParams.put(ShelfActivityConstants.Style.showType, 20);
        //5. 设置商品数量
        exParams.put(ShelfActivityConstants.Params.pageSize, 100);

        exParams.put(DouHuFetcher.Params.DP_EXP_ID, DouHuUtils.getDPDouHuExpId("life_poi_default_deal_shelf", "searchProductTop"));
        exParams.put(DouHuFetcher.Params.MT_EXP_ID, DouHuUtils.getMTDouHuExpId("life_poi_default_deal_shelf", "searchProductTop"));
    }

    private List<String> buildAttrKeys() {
        return Lists.newArrayList("service_type", "reservation_is_needed_or_not", "preSaleTag");
    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        extContexts.add(ActivityContextExt.class);
        extContexts.add(DouHuExtContext.class);
    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        // 1. 注册筛选能力实现
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        // 2. 商品填充能力
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        // 3. 多路分组筛选查询能力
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, MultiplexQueryFilterFetcher.class);
        // 4. 斗斛实验能力
        abilities.put(DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE, PlatformDouHuFetcher.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        // 1. 打点数据能力扩展点
        extPoints.put(OceanBuilderExt.SHELF_OCEAN_EXT_CODE, LifeDealOceanBuilderExt.class);
        // 2. 商品楼层构造器扩展点
        extPoints.put(FloorsBuilderExt.EXT_POINT_ITEM_AREA_CODE, LifeDealFloorsBuilderExt.class);
        // 3. 标题扩展点
        extPoints.put(MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE, LifeDealMainTitleBuilderExt.class);
        // 4. 筛选栏关键词扩展点
        extPoints.put(FilterFetcherExt.EXT_POINT_FILTER_CODE, ConfigFilterFetcherExt.class);
    }

}
