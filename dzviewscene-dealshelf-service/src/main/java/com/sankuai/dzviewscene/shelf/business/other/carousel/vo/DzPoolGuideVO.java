package com.sankuai.dzviewscene.shelf.business.other.carousel.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import lombok.Data;

/**
 * Title: DzPoolGuideVO
 * Description: 拼场引导
 *
 * <AUTHOR>
 * @date 2021-02-22
 */
@Data
@MobileDo(id = 0x9e8a)
public class DzPoolGuideVO {

    /**
     * 引导标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 描述
     */
    @MobileDo.MobileField(key = 0xd894)
    private String desc;

    /**
     * 按钮对象
     */
    @MobileDo.MobileField(key = 0x7e5d)
    private DzSimpleButtonVO btn;

}
