package com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin;

import com.dianping.cat.Cat;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.business.other.popup.common.builder.DefaultPopupOceanBuilder;
import com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.builder.JoyBarPinPopupComponentBuilderExt;
import com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.fetcher.JoyBarPinFilterFetcher;
import com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.flow.StandardPopupFlow;
import com.sankuai.dzviewscene.shelf.framework.annotation.ActivityTemplate;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MultiGroupMergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivity;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.OtherActivityAbstractBuilder;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.popup.PopupComponentBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.popup.PopupOceanBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.other.ability.builder.popup.PopupResponseBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import org.joda.time.LocalDate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ActivityTemplate(activityCode = OtherActivity.ACTIVITY_CODE, sceneCode = "bar_poi_joypin_popup", name = "酒吧POI页发起拼场时间弹窗")
public class JoyBarPinPopupTemplate implements IActivityTemplate {

    @Override
    public Class<? extends IActivityFlow> flow() {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.JoyBarPinPopupTemplate.flow()");
        return StandardPopupFlow.class;
    }

    @Override
    public void extParams(Map<String, Object> exParams) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.JoyBarPinPopupTemplate.extParams(java.util.Map)");
        // 1. 设置召回参数, 代表只召回一组商品, 商品是场馆
        exParams.put(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.joy_pin.name()));
        // 2. 设置第一组商品参数
        exParams.put(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.joy_pin.name(), new HashMap<String, Object>() {{
                // 2.1 配置召回
                put(QueryFetcher.Params.queryType, QueryFetcher.QueryType.shopGeneralProductsQuery.name());
                // 2.2 配置填充策略
                put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.generalProductPadding.name());
                // 2.3 设置第一组商品填充参数
                put(PaddingFetcher.Params.planId, "10100074");
                put(PaddingFetcher.Params.beginTime, LocalDate.now().toDate());
                put(PaddingFetcher.Params.endTime, LocalDate.now().plusDays(6).toDate());
                put(PaddingFetcher.Params.planId, "10100074");
                // 2.4 设置productType和spuType
                put(QueryFetcher.Params.productType, 100004);
                put(QueryFetcher.Params.spuType, 550L);
                // 2.5 设置筛选排序ID
                put(MergeQueryFetcher.Params.rankId, "10400002");
            }});
        }});

    }

    @Override
    public void extContexts(List<Class<? extends IContextExt>> extContexts) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.JoyBarPinPopupTemplate.extContexts(java.util.List)");

    }

    @Override
    public void extValidators(List<Class<? extends IActivityValidator>> validators) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.JoyBarPinPopupTemplate.extValidators(java.util.List)");

    }

    @Override
    public void extAbilities(Map<String, Class<? extends IAbility>> abilities) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.JoyBarPinPopupTemplate.extAbilities(java.util.Map)");

        /*召回能力*/
        abilities.put(QueryFetcher.ABILITY_PRODUCT_QUERY_CODE, MultiGroupQueryFetcher.class);
        /*填充能力*/
        abilities.put(PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE, MultiGroupPaddingFetcher.class);
        /*融合召回填充能力*/
        abilities.put(MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE, MultiGroupMergeQueryFetcher.class);
        /*筛选构造能力*/
        abilities.put(FilterFetcher.ABILITY_FILTER_CODE, JoyBarPinFilterFetcher.class);
        /*弹窗对象构造能力*/
        abilities.put(OtherActivityAbstractBuilder.ABILITY_CODE, PopupResponseBuilder.class);
    }

    @Override
    public void extPoints(Map<String, Class<? extends IExtPoint>> extPoints) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.other.popup.joybarpin.JoyBarPinPopupTemplate.extPoints(java.util.Map)");
        // 1. 弹窗构造器扩展点
        extPoints.put(PopupComponentBuilderExt.EXT_CODE, JoyBarPinPopupComponentBuilderExt.class);
        extPoints.put(PopupOceanBuilderExt.EXT_CODE, DefaultPopupOceanBuilder.class);
    }
}
