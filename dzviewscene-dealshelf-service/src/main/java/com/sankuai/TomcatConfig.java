package com.sankuai;

import org.apache.tomcat.util.http.LegacyCookieProcessor;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * tomcat定制类
 * 因为SpringBoot的tomcat引用了更为严格的URL协议，导致部分get请求报400
 * 参见：http://www.codebaoku.com/it-java/it-java-221133.html
 * 为此，使用本配置放开部分字符限制
 * <AUTHOR>
 * @datetime 2022-12-20日 下午14:03
 */
@Configuration
public class TomcatConfig {

    @Bean
    public ServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory fa = new TomcatServletWebServerFactory();
        fa.addConnectorCustomizers((TomcatConnectorCustomizer) connector -> {
            // url允许的特殊字符
            connector.setProperty("relaxedQueryChars", "(),/:;<=>?@[\\]{}");
            // 关闭严格的RFC7230校验
            connector.setProperty("rejectIllegalHeader", "false");
        });
        // 指定 Cookie 的处理器，解决 Tomcat 高版本带来的 Cookie 验证异常
        fa.addContextCustomizers(context -> context.setCookieProcessor(new LegacyCookieProcessor()));
        return fa;
    }

}
