package com.sankuai.debug;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.dzviewscene.product.shelf.activity.deal.DealShelfActivityCtxBuilder;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.Setter;

/**
 * created by zhangzhiyuan04 in 2021/6/19
 */
public class ShelfDealsAPI {
    /**
     * 统一商户ID
     */
    @Setter
    private String shopuuid;

    /**
     * 定位的关键字
     */
    @Setter
    private String searchkeyword;

    /**
     * 扩展信息
     */
    @Setter
    private String extra;

    /**
     * 选择的导航ID
     */
    @Setter
    private long filterbtnid;

    /**
     * 经度
     */
    @Setter
    private Double lng;

    /**
     * 纬度
     */
    @Setter
    private Double lat;

    /**
     * 城市Id
     */
    @Setter
    private Integer cityid;

    /**
     * 商户Id
     */
    @Setter
    private int shopid;

    /**
     * 来自后端分配@float.lu
     */
    @Setter
    private String sceneCode;

    @Setter
    private int platform = VCClientTypeEnum.DP_APP.getCode();

    /**
     * 客户端类型：ios | android | 空字符串
     */
    @Setter
    private String client = "android";

    /**
     * 版本号
     */
    private String version;

    /**
     * 设备ID，dpId or uuid
     */
    private String deviceId;

    /**
     * unionid
     */
    private String unionId;

    /**
     * 点评用户ID
     */
    private long dpUserId;

    /**
     * 美团用户ID
     */
    private long mtUserId;

    public ActivityRequest buildActivityRequest() {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, sceneCode);
        activityRequest.addParam(ShelfActivityConstants.Params.keyword, searchkeyword);
        activityRequest.addParam(ShelfActivityConstants.Params.selectedFilterId, filterbtnid);
        activityRequest.addParam(ShelfActivityConstants.Params.shopUuid, shopuuid);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, unionId);
        activityRequest.addParam(ShelfActivityConstants.Params.deviceId, deviceId);
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, client);
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, version);
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.DEAL_GROUP.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.dealShelfListForTab);
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, cityid);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, shopid);
            activityRequest.addParam(ShelfActivityConstants.Params.mtUserId, mtUserId);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, cityid);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, shopid);
            activityRequest.addParam(ShelfActivityConstants.Params.dpUserId, dpUserId);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        activityRequest.addParam("spaceKey", DealShelfActivityCtxBuilder.DEAL_SHELF_SPACE_KEY);
        return activityRequest;
    }
}
