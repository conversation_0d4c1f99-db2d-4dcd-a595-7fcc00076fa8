package com.sankuai.debug.util;

import com.sankuai.dzviewscene.client.LaunchClient;
import com.sankuai.dzviewscene.client.LaunchRequest;
import com.sankuai.dzviewscene.client.LaunchResult;
import org.junit.Ignore;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * created by zhangzhiyuan04 in 2021/8/23
 */
@Ignore("没有可执行的方法")
public class SDKTest {

    //@Test
    public void test() {
        Map<String, Object> extParams = new HashMap<>();
        //点评休娱类目
        extParams.put("category", 30);
        extParams.put("platform", 1);
        //商详页货架
        extParams.put("resourceSpaceKey", "shop_detail_dealgroup_productshelf");

        LaunchRequest launchRequest = new LaunchRequest();
        launchRequest.setFeatureParams(extParams);

        LaunchResult launchResult = LaunchClient.getLaunch(launchRequest);
        System.out.println(1);
    }
}
