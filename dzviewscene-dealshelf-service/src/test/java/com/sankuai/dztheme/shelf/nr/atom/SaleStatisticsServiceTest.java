package com.sankuai.dztheme.shelf.nr.atom;

import com.dianping.ktv.gather.api.common.IResponse;
import com.dianping.ktv.gather.api.dto.SaleStatisticsDTO;
import com.dianping.ktv.gather.api.dto.SaleStatisticsQueryDTO;
import com.dianping.ktv.gather.api.enums.ClusterTypeEnum;
import com.dianping.ktv.gather.api.service.SaleStatisticsService;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/10/16.
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.xxxxx"})
public class SaleStatisticsServiceTest {

    @RpcClient(url = "http://service.dianping.com/ktvGatherService/saleStatisticsRemoteService_1.0.0")
    private SaleStatisticsService saleStatisticsService;


    //@Test
    @DisplayName("联调线上SaleStatisticsService#queryFunSaleStatistics接口")
    @Environment(env = AthenaEnv.Product, swimlane = "")
    public void test_load_sales() throws Exception {
        CompletableFuture<IResponse<List<SaleStatisticsDTO>>> saleCompletableFuture =
                AthenaInf.getRpcCompletableFuture(saleStatisticsService.queryFunSaleStatistics(buildSaleStatisticsQueryDTO()));
        Assert.assertTrue(saleCompletableFuture.join() != null);
    }

    private SaleStatisticsQueryDTO buildSaleStatisticsQueryDTO() {
        SaleStatisticsQueryDTO saleStatisticsQueryDTO = new SaleStatisticsQueryDTO();
        saleStatisticsQueryDTO.setClusterType(ClusterTypeEnum.SPU.getType());
        saleStatisticsQueryDTO.setBizType(8);
        saleStatisticsQueryDTO.setClusterIds(buildProductIds());
        saleStatisticsQueryDTO.setPlatforms(platforms());
        return saleStatisticsQueryDTO;
    }

    private List<Integer> platforms() {
        List<Integer> platformItems = new ArrayList<>();
        platformItems.add(1);
        platformItems.add(2);
        return platformItems;
    }

    private List<Long> buildProductIds() {
        List<Long> productIds = new ArrayList<>();
        productIds.add(607843909L);
        productIds.add(607859902L);
        productIds.add(607756002L);
        productIds.add(607842623L);
        productIds.add(613419877L);
        return productIds;
    }

}
