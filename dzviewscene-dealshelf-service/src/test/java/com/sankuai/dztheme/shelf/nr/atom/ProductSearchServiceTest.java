package com.sankuai.dztheme.shelf.nr.atom;

import com.dianping.beauty.clove.enums.GoodsSortItem;
import com.dianping.beauty.clove.enums.SkuProductTypeEnum;
import com.dianping.beauty.clove.model.commonGoods.AttrTermQuery;
import com.dianping.beauty.clove.request.GoodsQueryRequest;
import com.dianping.cat.Cat;
import com.dianping.general.unified.search.api.sku.ProductSearchRequest;
import com.dianping.general.unified.search.api.sku.ProductSearchResponse;
import com.dianping.general.unified.search.api.sku.ProductSearchService;
import com.dianping.general.unified.search.api.sku.enums.SortEnum;
import com.dianping.general.unified.search.api.sku.model.Product;
import com.dianping.general.unified.search.api.sku.model.SortItemEntity;
import com.dianping.general.unified.search.api.sku.query.AttrAndQuery;
import com.dianping.general.unified.search.api.sku.query.AttrOrQuery;
import com.dianping.general.unified.search.api.sku.query.AttrQuery;
import com.dianping.general.unified.search.api.sku.query.AttrValueQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/10/16.
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.xxxxx"})
public class ProductSearchServiceTest {

    @RpcClient(url = "http://service.dianping.com/generalSearchService/ProductSearchService_1.0.0")
    private ProductSearchService productSearchService;


    //@Test
    @DisplayName("联调线上ProductSearchService#commonSearchProduct接口")
    @Environment(env = AthenaEnv.Product, swimlane = "")
    public void test_search_products() throws Exception {
        ProductSearchRequest productSearchRequest = getProductSearchRequest(92044157);
        CompletableFuture<ProductSearchResponse<Product>> productSearchCompletableFuture = AthenaInf.getRpcCompletableFuture(productSearchService.commonSearchProduct(productSearchRequest));
        Assert.assertTrue(productSearchCompletableFuture.join() != null);
    }

    /*商品搜索参数构造*/
    private ProductSearchRequest getProductSearchRequest(int shopId) {
        com.dianping.general.unified.search.api.sku.ProductSearchRequest productSearchRequest = new ProductSearchRequest();
        productSearchRequest.setShopIds(buildShopIds(shopId));
        productSearchRequest.setProductStatus(1);
        productSearchRequest.setDateFilterType(1);
        SkuProductTypeEnum skuProductTypeEnum = SkuProductTypeEnum.loadByType(100045);
        productSearchRequest.setProductTypes(Lists.newArrayList(skuProductTypeEnum.type));
        productSearchRequest.setFields(Sets.newHashSet("ItemEntity", "ProductAttrs", "TagEntity"));
        productSearchRequest.setNum(10);
        productSearchRequest.setStart(0);
        productSearchRequest.setSortItems(getSkuSortItem());
        return productSearchRequest;
    }

    private static AttrValueQuery convertTermQuery(com.dianping.beauty.clove.model.commonGoods.AttrQuery goodsAttrQuery){
        AttrTermQuery termQuery = (AttrTermQuery) goodsAttrQuery;
        return new AttrValueQuery(termQuery.getAttrKey(),termQuery.getAttrValue());
    }

    public static AttrQuery convertSkuQuery(com.dianping.beauty.clove.model.commonGoods.AttrQuery query){
        AttrQuery attrQuery = null;
        if(query instanceof com.dianping.beauty.clove.model.commonGoods.AttrTermQuery){
            attrQuery = convertTermQuery(query);
        }
        if(query instanceof com.dianping.beauty.clove.model.commonGoods.AttrOrQuery){
            attrQuery = convertOrQuery(query);
        }
        if(query instanceof com.dianping.beauty.clove.model.commonGoods.AttrAndQuery){
            attrQuery = convertAndQuery(query);
        }
        return attrQuery;
    }

    private static AttrAndQuery convertAndQuery(com.dianping.beauty.clove.model.commonGoods.AttrQuery goodsAttrQuery){
        com.dianping.beauty.clove.model.commonGoods.AttrAndQuery andQuery = (com.dianping.beauty.clove.model.commonGoods.AttrAndQuery) goodsAttrQuery;
        AttrAndQuery result = new AttrAndQuery();
        if(org.apache.commons.collections.CollectionUtils.isEmpty(andQuery.getQueryList()))return result;
        for(com.dianping.beauty.clove.model.commonGoods.AttrQuery and:andQuery.getQueryList()){
            AttrQuery attrQuery = convertSkuQuery(and);
            if(attrQuery!=null)result.addQuery(attrQuery);
        }
        return result;
    }


    private static AttrOrQuery convertOrQuery(com.dianping.beauty.clove.model.commonGoods.AttrQuery goodsAttrQuery){
        com.dianping.beauty.clove.model.commonGoods.AttrOrQuery orQuery = (com.dianping.beauty.clove.model.commonGoods.AttrOrQuery) goodsAttrQuery;
        AttrOrQuery result = new AttrOrQuery();
        if(org.apache.commons.collections.CollectionUtils.isEmpty(orQuery.getQueryList()))return result;
        for(com.dianping.beauty.clove.model.commonGoods.AttrQuery or:orQuery.getQueryList()){
            AttrQuery attrQuery = convertSkuQuery(or);
            if(attrQuery!=null)result.addQuery(attrQuery);
        }
        return result;
    }

    public static List<SortItemEntity> getSkuSortItem() {
        List<SortItemEntity> sortItems = Lists.newArrayList();
        sortItems.add(new SortItemEntity("productdptotalsalescount", SortEnum.DESC.getValue()));
        sortItems.add(new SortItemEntity("minitemprice", SortEnum.ASC.getValue()));
        sortItems.add(new SortItemEntity("_id", SortEnum.DESC.getValue()));
        return sortItems;
    }

    private static List<Integer> buildShopIds(int shopId) {
        List<Integer> shopIds = new ArrayList<>();
        shopIds.add(shopId);
        return shopIds;
    }

}
