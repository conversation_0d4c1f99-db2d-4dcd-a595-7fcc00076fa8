package com.sankuai.dztheme.shelf.nr.atom;

import com.dianping.gis.remote.dto.RegionInfoDTO;
import com.dianping.gis.remote.enums.RegionType;
import com.dianping.gis.remote.service.RegionInfoService;
import com.dianping.shopremote.remote.ShopUuidService;
import com.dianping.shopremote.remote.dto.ShopUuidDTO;
import com.google.gson.Gson;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @auther: liweilong06
 * @date: 2020/7/23 2:23 下午
 */
@Ignore
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.nr.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class RegionInfoAtomServiceIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(RegionInfoAtomServiceIntegTest.class);

    static {
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
    }

    @RpcClient(url = "http://service.dianping.com/gisService/regionInfoService_1.0.0")
    private RegionInfoService regionInfoService;

    private Gson gson = new Gson();

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_01() {
        List<RegionInfoDTO> result = regionInfoService.findChildRegionList(1325, RegionType.MetroStation, true, 0, 1000);
        logger.info("结果为：" + gson.toJson(result));
    }

    //
    @Environment(AthenaEnv.Test)
    //@Test
    public void test_02() {
        List<RegionInfoDTO> result = regionInfoService.findRegionListByCityId(1, RegionType.MetroLine, 0, 1000);
        logger.info("结果为：" + gson.toJson(result));
    }

}
