package com.sankuai.dzviewscene.shelf.platform.common.query.handler.impl;

import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopOnlineDealGroupHandler;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Test;

/**
 * Created by float.lu on 2020/11/6.
 */
public class ShopOnlineDealGroupHandlerUnitTest {

    private ShopOnlineDealGroupHandler shopOnlineDealGroupHandler = new ShopOnlineDealGroupHandler();

    //@Test
    public void test_totalLimit0_currentPage0() throws Exception {
        Assert.assertThat(shopOnlineDealGroupHandler.computeCurrentLimit(0, 0), Matchers.equalTo(0));
    }

    //@Test
    public void test_totalLimit10_currentPage0() throws Exception {
        Assert.assertThat(shopOnlineDealGroupHandler.computeCurrentLimit(10, 0), Matchers.equalTo(10));
    }

    //@Test
    public void test_totalLimit50_currentPage0() throws Exception {
        Assert.assertThat(shopOnlineDealGroupHandler.computeCurrentLimit(50, 0), Matchers.equalTo(50));
    }

    //@Test
    public void test_totalLimit51_currentPage0() throws Exception {
        Assert.assertThat(shopOnlineDealGroupHandler.computeCurrentLimit(51, 0), Matchers.equalTo(50));
    }

    //@Test
    public void test_totalLimit1_currentPage1() throws Exception {
        Assert.assertThat(shopOnlineDealGroupHandler.computeCurrentLimit(1, 1), Matchers.equalTo(0));
    }

    //@Test
    public void test_totalLimit51_currentPage1() throws Exception {
        Assert.assertThat(shopOnlineDealGroupHandler.computeCurrentLimit(51, 1), Matchers.equalTo(1));
    }
}
