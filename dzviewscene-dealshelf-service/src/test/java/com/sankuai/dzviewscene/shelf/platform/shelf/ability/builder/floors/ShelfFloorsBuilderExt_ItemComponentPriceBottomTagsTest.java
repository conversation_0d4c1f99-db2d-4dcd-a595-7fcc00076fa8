package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFloorsBuilderExt_ItemComponentPriceBottomTagsTest {

    /**
     * 测试 itemComponentPriceBottomTags 方法是否返回 null
     */
    @Test
    public void testItemComponentPriceBottomTagsReturnNull() throws Throwable {
        // arrange
        ShelfFloorsBuilderExt shelfFloorsBuilderExt = new ShelfFloorsBuilderExt();
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroupName";
        ProductM productM = new ProductM();
        // act
        List<DzTagVO> result = shelfFloorsBuilderExt.itemComponentPriceBottomTags(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }
}
