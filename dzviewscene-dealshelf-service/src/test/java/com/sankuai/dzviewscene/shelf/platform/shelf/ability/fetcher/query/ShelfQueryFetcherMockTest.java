package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.product.shelf.common.dto.ProductBaseInfo;
import com.dianping.product.shelf.common.dto.ShelfItemDataDTO;
import com.dianping.product.shelf.common.dto.ShelfItemUnitDTO;
import com.dianping.product.shelf.common.request.ShelfRequestAttrMapKeys;
import com.dianping.product.shelf.common.shelf.item.data.dto.SkuDTO;
import com.dianping.product.shelf.common.utils.ShelfItemDataDtoGsonUtil;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mock.MockFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher.Product;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShelfQueryFetcherMockTest {

    @InjectMocks
    private ShelfQueryFetcher shelfQueryFetcher;
    private ProductBaseInfo productBaseInfo;
    private Product product;

    private static MockedStatic<AthenaInf> athenaInfMockedStatic;

    private static MockedStatic<Environment> environmentMockedStatic;

    @Before
    public void setUp() {
        shelfQueryFetcher = new ShelfQueryFetcher();
        productBaseInfo = new ProductBaseInfo();
        product = new Product();
        athenaInfMockedStatic = Mockito.mockStatic(AthenaInf.class);
        athenaInfMockedStatic.when(()->AthenaInf.getCacheClient(any())).thenReturn(null);

        environmentMockedStatic = Mockito.mockStatic(Environment.class);
        environmentMockedStatic.when(()->Environment.isProductEnv()).thenReturn(false);
    }

    @After
    public void tearDown(){
        athenaInfMockedStatic.close();
        environmentMockedStatic.close();
    }

    /**
     * 测试当productBaseInfo的shelfItemDataList为空时
     */
    @Test
    public void testParseShelfItemWithEmptyShelfItemDataList() {
        // arrange
        productBaseInfo.setShelfItemDataList(Collections.emptyList());

        // act
        shelfQueryFetcher.parseShelfItem(productBaseInfo, product);

        // assert
        assertNull(product.getSkuIdList());
    }

    /**
     * 测试当productBaseInfo的shelfItemDataList不为空，但不包含skuInfo时
     */
    @Test
    public void testParseShelfItemWithNonEmptyShelfItemDataListWithoutSkuInfo() {
        // arrange
        ShelfItemDataDTO shelfItemDataDTO = new ShelfItemDataDTO();
        shelfItemDataDTO.setDataName("nonSkuInfo");
        productBaseInfo.setShelfItemDataList(Collections.singletonList(shelfItemDataDTO));

        // act
        shelfQueryFetcher.parseShelfItem(productBaseInfo, product);

        // assert
        assertNull(product.getSkuIdList());
    }

    /**
     * 测试当productBaseInfo的shelfItemDataList包含skuInfo，但skuDTOList为空时
     */
    @Test
    public void testParseShelfItemWithSkuInfoButEmptySkuDTOList() {
        // arrange
        ShelfItemDataDTO shelfItemDataDTO = new ShelfItemDataDTO();
        shelfItemDataDTO.setDataName("skuInfo");
        shelfItemDataDTO.setDataValue("[]");
        productBaseInfo.setShelfItemDataList(Collections.singletonList(shelfItemDataDTO));

        // act
        shelfQueryFetcher.parseShelfItem(productBaseInfo, product);

        // assert
        assertTrue(CollectionUtils.isEmpty(product.getSkuIdList()));
    }

    /**
     * 测试当productBaseInfo的shelfItemDataList包含skuInfo，且skuDTOList不为空时
     */
    @Test
    public void testParseShelfItemWithSkuInfoAndNonEmptySkuDTOList() {
        // arrange
        ShelfItemDataDTO shelfItemDataDTO = new ShelfItemDataDTO();
        shelfItemDataDTO.setDataName("skuInfo");
        SkuDTO skuDTO = new SkuDTO();
        skuDTO.setSkuId("sku123");
        String skuDTOJson = ShelfItemDataDtoGsonUtil.toJsonDataValue(Collections.singletonList(skuDTO));
        shelfItemDataDTO.setDataValue(skuDTOJson);
        productBaseInfo.setShelfItemDataList(Collections.singletonList(shelfItemDataDTO));

        // act
        shelfQueryFetcher.parseShelfItem(productBaseInfo, product);

        // assert
        assertNotEquals(Arrays.asList("sku123"), product.getSkuIdList());
    }

    /**
     * 测试当productBaseInfo的shelfItemUnitList为空时
     */
    @Test
    public void testParseShelfItemWithEmptyShelfItemUnitList() {
        // arrange
        productBaseInfo.setShelfItemUnitList(Collections.emptyList());

        // act
        shelfQueryFetcher.parseShelfItem(productBaseInfo, product);

        // assert
        assertNull(product.getItemUnitList());
    }

    /**
     * 测试当productBaseInfo的shelfItemUnitList不为空时
     */
    @Test
    public void testParseShelfItemWithNonEmptyShelfItemUnitList() {
        // arrange
        ShelfItemUnitDTO shelfItemUnitDTO = new ShelfItemUnitDTO();
        shelfItemUnitDTO.setItemUnitId("unit123");
        shelfItemUnitDTO.setItemUnitType(1);
        productBaseInfo.setShelfItemUnitList(Collections.singletonList(shelfItemUnitDTO));

        // act
        shelfQueryFetcher.parseShelfItem(productBaseInfo, product);

        // assert
        assertEquals(1, product.getItemUnitList().size());
        assertEquals("unit123", product.getItemUnitList().get(0).getItemUnitId());
    }

    /**
     * 测试增加搜索词参数
     */
    @Test
    public void testAddSearchItemParam() {
        // arrange
        ActivityContext ctx = new ActivityContext();
        ctx.addParam(ShelfActivityConstants.Params.searchterm, "测试搜索词");
        Map<String, String> attrMap = new HashMap<>();

        // act
        shelfQueryFetcher.addSearchItemParam(ctx, attrMap);

        // assert
        assertEquals("测试搜索词", attrMap.get("searchItemTitle"));
    }

    @Test
    public void test_buildSearchKeyScene(){
        try (MockedStatic<LionConfigHelper> mockedStatic = mockStatic(LionConfigHelper.class)) {
            ShelfQueryFetcher shelf = new ShelfQueryFetcher();
            Map<String, Map> stringStringMap = new HashMap<>();
            Map<String, List<String>> beautyColProductMap = new HashMap<>();
            beautyColProductMap.put("beautyColProduct", Arrays.asList( "氧气嫩肤", "氧气嫩", "GeneoX","科伊人","GeneoX氧气嫩肤"));
            stringStringMap.put("searchKeyScene",beautyColProductMap);
            mockedStatic.when(() -> LionConfigHelper.getSearchKeyScene()).thenReturn(stringStringMap);
            HashMap<String, String> attrMap = new HashMap<>();
            shelf.buildSearchKeyScene(attrMap, "genEOx氧气嫩肤");
            System.out.println(JSON.toJSONString(attrMap));
            Assert.assertEquals("beautyColProduct", attrMap.get("searchKeyScene"));
        }
    }

    @Test
    public void test_buildSearchKeySceneLionNull(){
        try (MockedStatic<LionConfigHelper> mockedStatic = mockStatic(LionConfigHelper.class)) {
            ShelfQueryFetcher shelf = new ShelfQueryFetcher();
            mockedStatic.when(() -> LionConfigHelper.getSearchKeyScene()).thenReturn(null);
            HashMap<String, String> attrMap = new HashMap<>();
            shelf.buildSearchKeyScene(attrMap, "氧气嫩肤");
            System.out.println(JSON.toJSONString(attrMap));
            Assert.assertNotEquals("beautyColProduct", attrMap.get("searchKeyScene"));
        }
    }

    @Test
    public void test_buildSearchKeyScene_null(){
        try (MockedStatic<LionConfigHelper> mockedStatic = mockStatic(LionConfigHelper.class)) {
            ShelfQueryFetcher shelf = new ShelfQueryFetcher();
            Map<String, Map> stringStringMap = new HashMap<>();
            Map<String, List<String>> beautyColProductMap = new HashMap<>();
            beautyColProductMap.put("beautyColProduct", Arrays.asList( "氧气嫩肤", "氧气嫩", "科伊人","GeneoX氧气嫩肤"));
            stringStringMap.put("searchKeyScene",beautyColProductMap);
            mockedStatic.when(() -> LionConfigHelper.getSearchKeyScene()).thenReturn(stringStringMap);
            HashMap<String, String> attrMap = new HashMap<>();
            shelf.buildSearchKeyScene(attrMap, "关键词");
            System.out.println(JSON.toJSONString(attrMap));
            Assert.assertNotEquals("beautyColProduct", attrMap.get("searchKeyScene"));
        }
    }

    @Test
    public void test_addSpringFestivalNoClose(){
        ShelfQueryFetcher shelf = new ShelfQueryFetcher();
        ActivityContext ctx= new ActivityContext();
        Map<String, String> attrMap = new HashMap<>();
        ctx.addParam(ShelfActivityConstants.Params.springFestivalNoClose, true);
        shelf.addSpringFestivalNoClose(ctx,attrMap);
        String s = attrMap.get("isHolidayClosedShop");
        Assert.assertTrue(StringUtils.equals(s, "true"));
    }
}
