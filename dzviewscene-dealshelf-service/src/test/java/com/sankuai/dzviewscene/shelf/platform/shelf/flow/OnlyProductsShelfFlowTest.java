package com.sankuai.dzviewscene.shelf.platform.shelf.flow;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OnlyProductsShelfFlowTest {

    @Mock
    private AbstractActivity<?> activity;

    @Mock
    private ActivityContext ctx;

    @Mock
    private DouHuFetcher douHuFetcher;

    @Mock
    private QueryFetcher queryFetcher;

    @Mock
    private PaddingFetcher paddingFetcher;

    private OnlyProductsShelfFlow onlyProductsShelfFlow = new OnlyProductsShelfFlow();

    /**
     * Tests the execute method under normal conditions.
     */
    @Test
    public void testExecuteNormalCase() throws Throwable {
        // Arrange
        DouHuM douHuM = new DouHuM();
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroups.put("group1", productGroupM);
        CompletableFuture<DouHuM> douHuMFuture = CompletableFuture.completedFuture(douHuM);
        CompletableFuture<Map<String, ProductGroupM>> productGroupsFuture = CompletableFuture.completedFuture(productGroups);
        when(activity.findAbility(ctx, DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE)).thenReturn(douHuFetcher);
        when(douHuFetcher.build(ctx)).thenReturn(douHuMFuture);
        when(activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(queryFetcher);
        when(queryFetcher.build(ctx)).thenReturn(productGroupsFuture);
        when(activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE)).thenReturn(paddingFetcher);
        when(paddingFetcher.build(ctx)).thenReturn(CompletableFuture.completedFuture(productGroups));
        // Act
        CompletableFuture<ShelfGroupM> result = onlyProductsShelfFlow.execute(activity, ctx);
        // Assert
        assertNotNull(result);
        // Use join() to ensure the future is completed before accessing its result
        DouHuM resultDouHu = result.join().getDouHu();
        Map<String, ProductGroupM> resultProductGroups = result.join().getProductGroupMs();
        assertNotNull(resultDouHu);
        assertNotNull(resultProductGroups);
        assertEquals(douHuM, resultDouHu);
        assertEquals(productGroups, resultProductGroups);
    }

    /**
     * Tests the execute method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testExecuteExceptionCase() throws Throwable {
        // Arrange
        CompletableFuture<DouHuM> douHuMFuture = new CompletableFuture<>();
        douHuMFuture.completeExceptionally(new Exception());
        when(activity.findAbility(ctx, DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE)).thenReturn(douHuFetcher);
        when(douHuFetcher.build(ctx)).thenReturn(douHuMFuture);
        // Act
        onlyProductsShelfFlow.execute(activity, ctx);
    }
}
