package com.sankuai.dzviewscene.shelf.platform.shelf.flow;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.douhu.DouHuFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.mergequery.MergeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MergeQueryFilterLastShelfFlowTest {

    @Mock
    private AbstractActivity<?> activity;

    @Mock
    private ActivityContext ctx;

    @Mock
    private DouHuFetcher douHuFetcher;

    @Mock
    private MergeQueryFetcher mergeQueryFetcher;

    @Mock
    private FilterFetcher filterFetcher;

    private MergeQueryFilterLastShelfFlow flow;

    @Before
    public void setUp() {
        flow = new MergeQueryFilterLastShelfFlow();
    }

    /**
     * 测试execute方法正常情况
     */
    @Test
    public void testExecuteNormal() throws Throwable {
        // arrange
        DouHuM douHuM = new DouHuM();
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        Map<String, FilterM> filters = new HashMap<>();
        CompletableFuture<DouHuM> douHuMFuture = CompletableFuture.completedFuture(douHuM);
        CompletableFuture<Map<String, ProductGroupM>> productGroupsFuture = CompletableFuture.completedFuture(productGroups);
        CompletableFuture<Map<String, FilterM>> filtersFuture = CompletableFuture.completedFuture(filters);
        when(activity.findAbility(ctx, DouHuFetcher.ABILITY_FETCHER_DOU_HU_CODE)).thenReturn(douHuFetcher);
        when(douHuFetcher.build(ctx)).thenReturn(douHuMFuture);
        when(activity.findAbility(ctx, MergeQueryFetcher.ABILITY_MERGE_QUERY_CODE)).thenReturn(mergeQueryFetcher);
        when(mergeQueryFetcher.build(ctx)).thenReturn(productGroupsFuture);
        when(activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE)).thenReturn(filterFetcher);
        when(filterFetcher.build(ctx)).thenReturn(filtersFuture);
        // act
        CompletableFuture<ShelfGroupM> result = flow.execute(activity, ctx);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
    }

    /**
     * 测试execute方法异常情况
     */
    @Test(expected = Exception.class)
    public void testExecuteException() throws Throwable {
        // arrange
        when(douHuFetcher.build(ctx)).thenThrow(new Exception());
        // act
        flow.execute(activity, ctx);
    }
}
