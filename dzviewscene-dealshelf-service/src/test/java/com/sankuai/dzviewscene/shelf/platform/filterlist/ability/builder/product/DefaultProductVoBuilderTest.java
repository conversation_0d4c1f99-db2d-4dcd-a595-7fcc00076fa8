package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.product;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.ShopVO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductVoBuilderTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private FilterProductsM filterProductsM;

    @Mock
    private ProductGroupM productGroupM;

    private DefaultProductVoBuilder defaultProductVoBuilder;

    @Before
    public void setUp() {
        defaultProductVoBuilder = new DefaultProductVoBuilder();
    }

    @Test
    public void testBuildWhenFilterProductsIsNull() throws Throwable {
        // arrange
        FilterProductsM nonNullFilterProductsM = new FilterProductsM();
        // Assuming FilterProductsM has a method to set ProductGroup, if not, adjust accordingly
        // Explicitly setting to null to mimic the scenario
        nonNullFilterProductsM.setProductGroup(null);
        when(activityContext.getMainData()).thenReturn(CompletableFuture.completedFuture(nonNullFilterProductsM));
        // act
        CompletableFuture<List<DzProductVO>> result = defaultProductVoBuilder.build(activityContext);
        // assert
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWhenProductGroupIsNull() throws Throwable {
        // arrange
        when(activityContext.getMainData()).thenReturn(CompletableFuture.completedFuture(filterProductsM));
        when(filterProductsM.getProductGroup()).thenReturn(null);
        // act
        CompletableFuture<List<DzProductVO>> result = defaultProductVoBuilder.build(activityContext);
        // assert
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildWhenProductsIsEmpty() throws Throwable {
        // arrange
        when(activityContext.getMainData()).thenReturn(CompletableFuture.completedFuture(filterProductsM));
        when(filterProductsM.getProductGroup()).thenReturn(productGroupM);
        when(productGroupM.getProducts()).thenReturn(Collections.emptyList());
        // act
        CompletableFuture<List<DzProductVO>> result = defaultProductVoBuilder.build(activityContext);
        // assert
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }

    /**
     * Test buildShopVO with empty shop list
     */
    @Test
    public void testBuildShopVO_EmptyList() {
        // arrange
        DefaultProductVoBuilder builder = new DefaultProductVoBuilder();
        List<ShopM> emptyList = new ArrayList<>();
        // act
        ShopVO result = builder.buildShopVO(emptyList);
        // assert
        Assert.assertNull("Should return null for empty list", result);
    }

    /**
     * Test buildShopVO with null shop list
     */
    @Test
    public void testBuildShopVO_NullList() {
        // arrange
        DefaultProductVoBuilder builder = new DefaultProductVoBuilder();
        List<ShopM> nullList = null;
        // act
        ShopVO result = builder.buildShopVO(nullList);
        // assert
        Assert.assertNull("Should return null for null list", result);
    }

    /**
     * Test buildShopVO with list containing null as first element
     */
    @Test
    public void testBuildShopVO_NullFirstElement() {
        // arrange
        DefaultProductVoBuilder builder = new DefaultProductVoBuilder();
        List<ShopM> listWithNullFirst = Lists.newArrayList((ShopM) null);
        // act
        ShopVO result = builder.buildShopVO(listWithNullFirst);
        // assert
        Assert.assertNull("Should return null when first element is null", result);
    }
}
