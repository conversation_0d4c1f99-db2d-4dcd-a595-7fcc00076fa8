package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query;


import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@RunWith(MockitoJUnitRunner.class)
public class PlatformShelfCacheQueryFetcherTest {

    @InjectMocks
    PlatformShelfCacheQueryFetcher platformShelfCacheQueryFetcher;

    @Test
    @DisplayName("早教货架团单召回接口实验参数测试")
    public void buildQueryRequestExps_zao<PERSON>ao() throws Exception {
        ActivityContext ctx = new ActivityContext();
        Map<String, Object> parameters = new ConcurrentHashMap<>();
        ctx.setParameters(parameters);

        HashMap recallConfig = new HashMap<>();
        recallConfig.put(QueryFetcher.Params.earlyEducationFilterExps, "earlyEducationFilter0");
        parameters.put(QueryFetcher.Params.recallConfig, recallConfig);

        Method buildQueryRequestExps = platformShelfCacheQueryFetcher.getClass().getDeclaredMethod("buildQueryRequestExps", ActivityContext.class);
        buildQueryRequestExps.setAccessible(true);

        String exps = (String) buildQueryRequestExps.invoke(platformShelfCacheQueryFetcher, ctx);

        assert exps.equals("earlyEducationFilter0");
    }

    @Test
    @DisplayName("早教货架团单召回接口扩展参数测试")
    public void buildQueryRequestAttr_zaojiao() throws Exception {
        ActivityContext ctx = new ActivityContext();
        Map<String, Object> parameters = new ConcurrentHashMap<>();
        ctx.setParameters(parameters);

        HashMap recallConfig = new HashMap<>();
        recallConfig.put(QueryFetcher.Params.earlyEducationPriceFilterRange, "[0,10]");
        parameters.put(QueryFetcher.Params.recallConfig, recallConfig);

        Method buildQueryRequestAttr = platformShelfCacheQueryFetcher.getClass().getDeclaredMethod("buildQueryRequestAttr", ActivityContext.class);
        buildQueryRequestAttr.setAccessible(true);

        Map<String, String> attrMap = (Map<String, String>) buildQueryRequestAttr.invoke(platformShelfCacheQueryFetcher, ctx);

        assert attrMap.containsKey(QueryFetcher.Params.earlyEducationPriceFilterRange);
    }
}
