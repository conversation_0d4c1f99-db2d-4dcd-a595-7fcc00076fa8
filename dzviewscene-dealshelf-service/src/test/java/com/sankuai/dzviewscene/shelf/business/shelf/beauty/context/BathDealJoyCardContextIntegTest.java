package com.sankuai.dzviewscene.shelf.business.shelf.beauty.context;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.shelf.business.shelf.common.context.JoyCardShelfContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Map;

/**
 * 洗浴召回模板联合测试类
 *
 * @auther: liweilong06
 * @date: 2020/9/30 4:32 下午
 */
@Ignore
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.shelf.business.shelf.bathdeal.context", "com.sankuai.dzviewscene.nr.atom", "com.sankuai.dzviewscene.shelf.framework"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class BathDealJoyCardContextIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(BathDealJoyCardContextIntegTest.class);

    static {
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
    }

    @Resource
    private JoyCardShelfContext joyCardContext;

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_01_product() {
        ActivityContext ctx = new ActivityContext();
        ctx.addParam(ShelfActivityConstants.Params.platform, 1);
        ctx.addParam(ShelfActivityConstants.Params.dpPoiId, 955373666);
        ctx.addParam(ShelfActivityConstants.Params.dpCityId, 8);
        ctx.addParam(QueryFetcher.Params.groupNames, Arrays.asList("商家推荐", "团购", "浴资票", "套餐与服务", "充值套餐"));
        //

        Map<String, String> result = joyCardContext.contextExt(ctx).join();
        logger.info("返回结果为：" + JsonCodec.encode(result));
    }

}
