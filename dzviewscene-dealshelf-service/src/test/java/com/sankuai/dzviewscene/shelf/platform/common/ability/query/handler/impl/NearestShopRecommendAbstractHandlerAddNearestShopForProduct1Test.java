package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NearestShopRecommendAbstractHandlerAddNearestShopForProduct1Test {

    @Mock
    private AtomFacadeService atomFacadeService;

    @Mock
    private ActivityContext context;

    @Spy
    @InjectMocks
    private NearestShopRecommendAbstractHandler handler = new NearestShopRecommendAbstractHandler() {
    };

    @Test
    public void testAddNearestShopForProduct_WhenProduct2ShopIdMapEmpty() throws Throwable {
        Integer bizId = 1;
        ProductM product = new ProductM();
        product.setProductId(100);
        List<ProductM> products = Lists.newArrayList(product);
        doReturn(Maps.newHashMap()).when(handler).queryProduct2ShopIdMap(any(), eq(bizId), eq(products));
        handler.addNearestShopForProduct(context, bizId, products);
        assertNull(product.getShopIds());
        assertNull(product.getShopLongIds());
    }
}
