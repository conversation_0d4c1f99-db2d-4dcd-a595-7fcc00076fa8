package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.utils.ProductTitleHighLightUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductCouponM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractDefaultFloorsBuilderExtTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ProductM productM;

    @InjectMocks
    private AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = new AbstractDefaultFloorsBuilderExt() {
    };

    private static Set<String> originalShowPreSaleSceneCodes;

    @Mock
    private ProductPromoPriceM productPromoPriceM;

    private MockedStatic<PreSaleUtils> mockedPreSaleUtils;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private AbstractDefaultFloorsBuilderExt floorsBuilder = new AbstractDefaultFloorsBuilderExt() {

        @Override
        public List<DzTagVO> itemComponentPriceBottomTags(ActivityContext activityContext, String groupName, ProductM productM) {
            return super.itemComponentPriceBottomTags(activityContext, groupName, productM);
        }
    };

    @Before
    public void setUp() {
        mockedPreSaleUtils = mockStatic(PreSaleUtils.class);
    }

    @After
    public void tearDown() {
        mockedPreSaleUtils.close();
    }

    // Removed @After annotation to comply with the rules
    private void modifyShowPreSaleSceneCodes() throws Exception {
        Field showPreSaleSceneCodesField = AbstractDefaultFloorsBuilderExt.class.getDeclaredField("showPreSaleSceneCodes");
        showPreSaleSceneCodesField.setAccessible(true);
        originalShowPreSaleSceneCodes = (Set<String>) showPreSaleSceneCodesField.get(abstractDefaultFloorsBuilderExt);
        Set<String> showPreSaleSceneCodes = new HashSet<>();
        showPreSaleSceneCodes.add("inShowPreSaleSceneCodes");
        showPreSaleSceneCodesField.set(abstractDefaultFloorsBuilderExt, showPreSaleSceneCodes);
    }

    private void resetShowPreSaleSceneCodes() throws Exception {
        Field showPreSaleSceneCodesField = AbstractDefaultFloorsBuilderExt.class.getDeclaredField("showPreSaleSceneCodes");
        showPreSaleSceneCodesField.setAccessible(true);
        showPreSaleSceneCodesField.set(abstractDefaultFloorsBuilderExt, originalShowPreSaleSceneCodes);
    }

    private AbstractDefaultFloorsBuilderExt createTestSubject() {
        return new AbstractDefaultFloorsBuilderExt() {

            @Override
            public String itemComponentLabs(ActivityContext activityContext, String groupName, ProductM productM, int index) {
                return super.itemComponentLabs(activityContext, groupName, productM, index);
            }
        };
    }

    private void assertJsonEquals(String expectedJson, String actualJson) throws Exception {
        assertEquals(objectMapper.readTree(expectedJson), objectMapper.readTree(actualJson));
    }

    @Test
    public void testIsPreSaleBothNull() throws Throwable {
        assertFalse(abstractDefaultFloorsBuilderExt.isPreSale(null, null));
    }

    @Test
    public void testIsPreSaleActivityContextNull() throws Throwable {
        assertFalse(abstractDefaultFloorsBuilderExt.isPreSale(null, productM));
    }

    @Test
    public void testIsPreSaleProductMNull() throws Throwable {
        assertFalse(abstractDefaultFloorsBuilderExt.isPreSale(activityContext, null));
    }

    @Test
    public void testIsPreSaleNotPreSale() throws Throwable {
        assertFalse(abstractDefaultFloorsBuilderExt.isPreSale(activityContext, productM));
    }

    @Test
    public void testIsPreSalePreSaleButNotInShowPreSaleSceneCodes() throws Throwable {
        assertFalse(abstractDefaultFloorsBuilderExt.isPreSale(activityContext, productM));
    }

    /**
     * Test itemComponentSalePrice method when ProductM is null.
     * Expected behavior: Method handles null input gracefully and returns a default or empty value.
     * Note: As per the instructions, the method under test cannot be modified. Therefore, this test case
     * assumes the method under test is expected to handle null inputs gracefully, which it currently does not.
     * @throws Throwable
     */
    @Test(expected = NullPointerException.class)
    public void testItemComponentSalePriceWhenProductIsNull() throws Throwable {
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {
        };
        builder.itemComponentSalePrice(new ActivityContext(), "groupName", null);
    }

    /**
     * Test itemComponentSalePrice method when promo is null.
     * @throws Throwable
     */
    @Test
    public void testItemComponentSalePriceWhenPromoIsNull() throws Throwable {
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(null);
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {
        };
        String result = builder.itemComponentSalePrice(new ActivityContext(), "groupName", productM);
        assertEquals("Expected value for null promo", null, result);
    }

    /**
     * Test itemComponentSalePrice method when ProductPromoPrice is null.
     * @throws Throwable
     */
    @Test
    public void testItemComponentSalePriceWhenProductPromoPriceIsNull() throws Throwable {
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(productPromoPriceM);
        when(productPromoPriceM.getPromoPriceTag()).thenReturn(null);
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {
        };
        String result = builder.itemComponentSalePrice(new ActivityContext(), "groupName", productM);
        assertEquals(null, result);
    }

    /**
     * Test itemComponentSalePrice method when ProductPromoPrice is not null.
     * @throws Throwable
     */
    @Test
    public void testItemComponentSalePriceWhenProductPromoPriceIsNotNull() throws Throwable {
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(productPromoPriceM);
        when(productPromoPriceM.getPromoPriceTag()).thenReturn("promoPriceTag");
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {
        };
        String result = builder.itemComponentSalePrice(new ActivityContext(), "groupName", productM);
        assertEquals("promoPriceTag", result);
    }

    @Test
    public void testBuildDzPromoVOSNullContextOrProduct() throws Throwable {
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {

            @Override
            public List<DzPromoVO> buildDzPromoVOS(ActivityContext activityContext, ProductM productM, ProductPromoPriceM productPromoPriceM) {
                return super.buildDzPromoVOS(activityContext, productM, productPromoPriceM);
            }
        };
        List<DzPromoVO> result = builder.buildDzPromoVOS(null, null, productPromoPriceM);
        assertEquals(1, result.size());
    }

    @Test
    public void testBuildDzPromoVOSEmptyPromoItemList() throws Throwable {
        when(productPromoPriceM.getPromoItemList()).thenReturn(null);
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {

            @Override
            public List<DzPromoVO> buildDzPromoVOS(ActivityContext activityContext, ProductM productM, ProductPromoPriceM productPromoPriceM) {
                return super.buildDzPromoVOS(activityContext, productM, productPromoPriceM);
            }
        };
        List<DzPromoVO> result = builder.buildDzPromoVOS(activityContext, productM, productPromoPriceM);
        assertEquals(1, result.size());
    }

    @Test
    public void testBuildDzPromoVOSPreSalePopShowType() throws Throwable {
        mockedPreSaleUtils.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(true);
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {

            @Override
            public List<DzPromoVO> buildDzPromoVOS(ActivityContext activityContext, ProductM productM, ProductPromoPriceM productPromoPriceM) {
                return super.buildDzPromoVOS(activityContext, productM, productPromoPriceM);
            }
        };
        List<DzPromoVO> result = builder.buildDzPromoVOS(activityContext, productM, productPromoPriceM);
        assertEquals(1, result.size());
        assertNull(result.get(0).getDetail());
    }

    /**
     * 测试 itemComponentPurchase 方法，当 productM.getPurchase() 返回空字符串时，应返回 null
     */
    @Test
    public void testItemComponentPurchaseWhenPurchaseIsEmpty() throws Throwable {
        // arrange
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {

            @Override
            public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
                return super.itemComponentPurchase(activityContext, groupName, productM);
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        ProductM productM = new ProductM();
        productM.setPurchase("");
        // act
        RichLabelVO result = builder.itemComponentPurchase(activityContext, groupName, productM);
        // assert
        assertNull(result);
    }

    /**
     * 测试 itemComponentPurchase 方法，当 productM.getPurchase() 返回非空字符串时，应返回一个 RichLabelVO 对象，其 text 属性为 productM.getPurchase()
     */
    @Test
    public void testItemComponentPurchaseWhenPurchaseIsNotEmpty() throws Throwable {
        // arrange
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {

            @Override
            public RichLabelVO itemComponentPurchase(ActivityContext activityContext, String groupName, ProductM productM) {
                return super.itemComponentPurchase(activityContext, groupName, productM);
            }
        };
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroup";
        ProductM productM = new ProductM();
        productM.setPurchase("testPurchase");
        // act
        RichLabelVO result = builder.itemComponentPurchase(activityContext, groupName, productM);
        // assert
        assertNotNull(result);
        assertEquals("testPurchase", result.getText());
    }

    @Test
    public void testItemComponentLabsWhenPromoPricesAndPromoAreEmpty() throws Throwable {
        AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = createTestSubject();
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        String result = abstractDefaultFloorsBuilderExt.itemComponentLabs(activityContext, "groupName", productM, 1);
        assertJsonEquals("{\"category_id\":0,\"deal_id\":0,\"poi_id\":0,\"index\":1}", result);
    }

    // Removed @Before annotation to comply with the rules
    @Test
    public void testItemComponentLabsWhenPromoPricesIsNotEmptyButPromoIsNull() throws Throwable {
        AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = createTestSubject();
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(null);
        String result = abstractDefaultFloorsBuilderExt.itemComponentLabs(activityContext, "groupName", productM, 1);
        assertJsonEquals("{\"category_id\":0,\"deal_id\":0,\"poi_id\":0,\"index\":1}", result);
    }

    @Test
    public void testItemComponentLabsWhenPromoPricesAndPromoAreNotEmpty() throws Throwable {
        AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = createTestSubject();
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(productPromoPriceM);
        String result = abstractDefaultFloorsBuilderExt.itemComponentLabs(activityContext, "groupName", productM, 1);
        // Updated expected JSON to include "promotion_title":null
        assertJsonEquals("{\"category_id\":0,\"deal_id\":0,\"poi_id\":0,\"index\":1,\"promotion_title\":null}", result);
    }

    /**
     * 测试itemComponentSale方法，当productM.getSale()为null时
     */
    @Test
    public void testItemComponentSaleWhenSaleIsNull() throws Throwable {
        // arrange
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductM productM = mock(ProductM.class);
        when(productM.getSale()).thenReturn(null);
        // act
        String result = builder.itemComponentSale(activityContext, groupName, productM);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试itemComponentSale方法，当productM.getSale().getSaleTag()为空时
     */
    @Test
    public void testItemComponentSaleWhenSaleTagIsEmpty() throws Throwable {
        // arrange
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductM productM = mock(ProductM.class);
        ProductSaleM productSaleM = mock(ProductSaleM.class);
        when(productM.getSale()).thenReturn(productSaleM);
        when(productSaleM.getSaleTag()).thenReturn("");
        // act
        String result = builder.itemComponentSale(activityContext, groupName, productM);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试itemComponentSale方法，当productM.getSale().getSaleTag()不为空时
     */
    @Test
    public void testItemComponentSaleWhenSaleTagIsNotEmpty() throws Throwable {
        // arrange
        AbstractDefaultFloorsBuilderExt builder = new AbstractDefaultFloorsBuilderExt() {
        };
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductM productM = mock(ProductM.class);
        ProductSaleM productSaleM = mock(ProductSaleM.class);
        when(productM.getSale()).thenReturn(productSaleM);
        when(productSaleM.getSaleTag()).thenReturn("testSaleTag");
        // act
        String result = builder.itemComponentSale(activityContext, groupName, productM);
        // assert
        assertEquals("testSaleTag", result);
    }

    @Test
    public void testItemComponentPriceBottomTagsCouponsIsNull() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        String groupName = "test";
        ProductM productM = new ProductM();
        List<DzTagVO> result = floorsBuilder.itemComponentPriceBottomTags(activityContext, groupName, productM);
        assertNull(result);
    }

    @Test
    public void testItemComponentPriceBottomTagsAllCouponsAreNullOrEmpty() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        String groupName = "test";
        ProductM productM = new ProductM();
        productM.setCoupons(Arrays.asList(null, null));
        List<DzTagVO> result = floorsBuilder.itemComponentPriceBottomTags(activityContext, groupName, productM);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testItemComponentPriceBottomTagsAtLeastOneCouponIsNotNullAndNotEmpty() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        String groupName = "test";
        ProductM productM = new ProductM();
        // Adjusted to use the no-argument constructor and setter for couponTag
        ProductCouponM coupon1 = new ProductCouponM();
        coupon1.setCouponTag("couponTag1");
        ProductCouponM coupon2 = new ProductCouponM();
        coupon2.setCouponTag("couponTag2");
        productM.setCoupons(Arrays.asList(null, coupon1, coupon2));
        List<DzTagVO> result = floorsBuilder.itemComponentPriceBottomTags(activityContext, groupName, productM);
        assertEquals(2, result.size());
        assertEquals("#FF6633", result.get(0).getTextColor());
        assertFalse(result.get(0).isHasBorder());
        assertEquals("couponTag1", result.get(0).getName());
        assertEquals("#FF6633", result.get(1).getTextColor());
        assertFalse(result.get(1).isHasBorder());
        assertEquals("couponTag2", result.get(1).getName());
    }

    @Test(expected = NullPointerException.class)
    public void testItemComponentRichLabelsTitleWhenArgsIsNull() throws Throwable {
        AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = mock(AbstractDefaultFloorsBuilderExt.class, CALLS_REAL_METHODS);
        abstractDefaultFloorsBuilderExt.itemComponentRichLabelsTitle(null, null, null, 0);
    }

    @Test
    public void testItemComponentRichLabelsTitleWhenExceptionThrown() throws Throwable {
        AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = mock(AbstractDefaultFloorsBuilderExt.class, CALLS_REAL_METHODS);
        when(productM.getTitle()).thenReturn("title");
        String result = abstractDefaultFloorsBuilderExt.itemComponentRichLabelsTitle(activityContext, "groupName", productM, 0);
        assertNull(result);
    }

    @Test
    public void testItemComponentRichLabelsTitleWhenReturnNull() throws Throwable {
        AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = mock(AbstractDefaultFloorsBuilderExt.class, CALLS_REAL_METHODS);
        when(productM.getTitle()).thenReturn("title");
        String result = abstractDefaultFloorsBuilderExt.itemComponentRichLabelsTitle(activityContext, "groupName", productM, 0);
        assertNull(result);
    }

    @Test
    public void testItemComponentRichLabelsTitleWhenReturnNonNull() throws Throwable {
        AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt = mock(AbstractDefaultFloorsBuilderExt.class, CALLS_REAL_METHODS);
        when(productM.getTitle()).thenReturn("title");
        // Mock the static method call that leads to the expected result
        try (MockedStatic<ProductTitleHighLightUtils> mockedStatic = mockStatic(ProductTitleHighLightUtils.class)) {
            mockedStatic.when(() -> ProductTitleHighLightUtils.buildRichLabelsTitleWithKeyWordHighlight(activityContext, productM.getTitle(), ProductTitleHighLightUtils.LIGHT_TYPE_KEY_WORD)).thenReturn("expectedResult");
            String result = abstractDefaultFloorsBuilderExt.itemComponentRichLabelsTitle(activityContext, "groupName", productM, 0);
            assertEquals("expectedResult", result);
        }
    }
}
