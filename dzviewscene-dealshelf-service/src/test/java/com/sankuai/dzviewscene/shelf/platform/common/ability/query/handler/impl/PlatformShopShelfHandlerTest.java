package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.ShelfDTO;
import com.dianping.product.shelf.common.dto.ShelfNavTabProductList;
import com.dianping.product.shelf.common.request.ShelfNavTabProductRequest;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PlatformShopShelfHandlerTest {

    @Mock
    private ActivityContext activityContextMock;

    @Mock
    private CompositeAtomService compositeAtomServiceMock;

    @InjectMocks
    private PlatformShopShelfHandler handler;

    @Before
    public void setUp() {
        when(activityContextMock.getParam(ShelfActivityConstants.Params.deviceId)).thenReturn("testDeviceId");
        when(activityContextMock.getParam(ShelfActivityConstants.Params.appVersion)).thenReturn("testVersion");
        when(activityContextMock.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.selectedFilterId)).thenReturn(1L);
    }

    @After
    public void tearDown() {
        Mockito.reset(activityContextMock, compositeAtomServiceMock);
    }

    @Test
    public void testQueryValidationFails() throws Throwable {
        String groupName = "";
        HashMap<String, Object> params = new HashMap<>();
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        assertTrue(result.isDone());
        assertEquals(0, result.get().getTotal());
    }

    @Test
    public void testQuerySelectedTagIdZero() throws Throwable {
        String groupName = "testGroup";
        HashMap<String, Object> params = new HashMap<>();
        when(compositeAtomServiceMock.multiGetProductList(any())).thenReturn(null);
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        assertTrue(result.isDone());
        assertEquals(0, result.get().getTotal());
    }

    @Test
    public void testQueryCompositeAtomServiceReturnsNull() throws Throwable {
        String groupName = "testGroup";
        HashMap<String, Object> params = new HashMap<>();
        when(compositeAtomServiceMock.multiGetProductList(any())).thenReturn(null);
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        assertTrue(result.isDone());
        assertEquals(0, result.get().getTotal());
    }

    @Test
    public void testQueryCompositeAtomServiceReturnsFutureWithResponse() throws Throwable {
        String groupName = "testGroup";
        HashMap<String, Object> params = new HashMap<>();
        Response<ShelfNavTabProductList> response = new Response<>();
        response.setSuccess(true);
        ShelfNavTabProductList productList = new ShelfNavTabProductList();
        response.setContent(productList);
        when(compositeAtomServiceMock.multiGetProductList(any())).thenReturn(CompletableFuture.completedFuture(response));
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        assertTrue(result.isDone());
        assertNotNull(result.get());
        assertEquals(0, result.get().getTotal());
    }

    @Test
    public void testQueryCompositeAtomServiceReturnsFutureWithFailureResponse() throws Throwable {
        String groupName = "testGroup";
        HashMap<String, Object> params = new HashMap<>();
        Response<ShelfNavTabProductList> response = new Response<>();
        response.setSuccess(false);
        response.setMsg("Error");
        when(compositeAtomServiceMock.multiGetProductList(any())).thenReturn(CompletableFuture.completedFuture(response));
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        assertTrue(result.isDone());
        // Check if the result indicates a failure instead of expecting an ExecutionException
        assertFalse("Expected the response to indicate failure", result.get().getTotal() > 0);
    }

    /**
     * Test case: selectedTagId is 0 and updateSelectedId returns 0
     * Expected: Returns empty ProductGroupM
     */
    @Test
    public void testQuery_WhenSelectedTagIdIsZeroAndUpdateSelectedIdReturnsZero() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        when(activityContextMock.getParam(ShelfActivityConstants.Params.selectedFilterId)).thenReturn(null);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.dpPoiId)).thenReturn(1L);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        Response<ShelfDTO> shelfDTOResponse = new Response<>();
        shelfDTOResponse.setSuccess(true);
        ShelfDTO shelfDTO = new ShelfDTO();
        shelfDTOResponse.setContent(shelfDTO);
        CompletableFuture<Response<ShelfDTO>> futureResponse = CompletableFuture.completedFuture(shelfDTOResponse);
        when(compositeAtomServiceMock.multiGetShelfNav(any())).thenReturn(futureResponse);
        // act
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertEquals(0, result.get().getTotal());
    }

    /**
     * Test case: selectedTagId is not 0 initially
     * Expected: Proceeds to build request and call service
     */
    @Test
    public void testQuery_WhenSelectedTagIdIsNotZero() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        when(activityContextMock.getParam(ShelfActivityConstants.Params.selectedFilterId)).thenReturn(123L);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.dpPoiId)).thenReturn(1L);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.deviceId)).thenReturn("device1");
        when(activityContextMock.getParam(ShelfActivityConstants.Params.appVersion)).thenReturn("1.0");
        Response<ShelfNavTabProductList> response = new Response<>();
        response.setSuccess(true);
        ShelfNavTabProductList productList = new ShelfNavTabProductList();
        response.setContent(productList);
        when(compositeAtomServiceMock.multiGetProductList(any(ShelfNavTabProductRequest.class))).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        verify(compositeAtomServiceMock).multiGetProductList(any(ShelfNavTabProductRequest.class));
    }

    /**
     * Test case: compositeAtomService returns null
     * Expected: Returns empty ProductGroupM
     */
    @Test
    public void testQuery_WhenCompositeAtomServiceReturnsNull() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        when(activityContextMock.getParam(ShelfActivityConstants.Params.selectedFilterId)).thenReturn(123L);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.dpPoiId)).thenReturn(1L);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
        when(activityContextMock.getParam(ShelfActivityConstants.Params.deviceId)).thenReturn("device1");
        when(activityContextMock.getParam(ShelfActivityConstants.Params.appVersion)).thenReturn("1.0");
        when(compositeAtomServiceMock.multiGetProductList(any(ShelfNavTabProductRequest.class))).thenReturn(null);
        // act
        CompletableFuture<ProductGroupM> result = handler.query(activityContextMock, groupName, params);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertEquals(0, result.get().getTotal());
    }
}
