package com.sankuai.dzviewscene.shelf.platform.common.query.handler.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.sankuai.dzviewscene.product.ability.model.LeUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.LEUnCoopShopRecommendQueryHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(MockitoJUnitRunner.class)
public class LEUnCoopShopRecommendQueryHandlerTest {

    @Test
    public void test_buildRecommendParameters() {
        LEUnCoopShopRecommendQueryHandler leUnCoopShopRecommendQueryHandler = new LEUnCoopShopRecommendQueryHandler();
        ActivityContext context = new ActivityContext();
        context.addParam("platform", 1);
        context.addParam("mtPoiIdL", 1);
        context.addParam("dpPoiIdL", 1);
        context.addParam("pageNo", 1);
        context.addParam("pageSize", 1);
        context.addParam("genericCatIds", 1);
        Map<String, Object> params = new HashMap<>();
        params.put("flowFlag", "001");
        params.put("itemId", "1329840,1329841");
        LeUncoopShopShelfAttrM leUncoopShopShelfAttr = new LeUncoopShopShelfAttrM();
        RecommendParameters recommendParameters = leUnCoopShopRecommendQueryHandler.buildRecommendParameters(context, params, leUncoopShopShelfAttr);
        System.out.println(JSON.toJSONString(recommendParameters));
        Assert.assertNotNull(recommendParameters);
    }

    @Test
    public void test_convertPhotoRecommend2Products_null() {
        LEUnCoopShopRecommendQueryHandler leUnCoopShopRecommendQueryHandler = new LEUnCoopShopRecommendQueryHandler();
        List<RecommendDTO> recommendDTOList = new ArrayList<>();
        List<ProductM> productMS = leUnCoopShopRecommendQueryHandler.convertPhotoRecommend2Products(recommendDTOList, null);
        Assert.assertTrue(productMS.isEmpty());
    }

    @Test
    public void test_convertPhotoRecommend2Products() {
        LEUnCoopShopRecommendQueryHandler leUnCoopShopRecommendQueryHandler = new LEUnCoopShopRecommendQueryHandler();
        List<RecommendDTO> recommendDTOList = new ArrayList<>();
        RecommendDTO recommendDTO = new RecommendDTO();
        recommendDTO.setItem("1");
        Map <String, Object> bizData = new HashMap<>();
        bizData.put("productType", "1001");
        recommendDTO.setBizData(bizData);
        recommendDTOList.add(recommendDTO);
        List<ProductM> productMS = leUnCoopShopRecommendQueryHandler.convertPhotoRecommend2Products(recommendDTOList, null);
        Assert.assertTrue(productMS.size()==1);
    }
}
