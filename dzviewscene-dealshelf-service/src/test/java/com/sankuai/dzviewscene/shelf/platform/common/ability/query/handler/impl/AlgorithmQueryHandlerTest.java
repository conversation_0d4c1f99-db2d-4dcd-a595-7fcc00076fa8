package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class AlgorithmQueryHandlerTest {

    @InjectMocks
    private AlgorithmQueryHandler algorithmQueryHandler;

    @Mock
    private AtomFacadeService atomFacadeService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryAlgorithmQueryBizIdLessThanOrEqualToZero() throws Throwable {
        ActivityContext activityContext = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        params.put("algorithmQueryBizId", 0);
        CompletableFuture<ProductGroupM> result = algorithmQueryHandler.query(activityContext, "groupName", params);
        assertNull(result.get());
    }

    @Test
    public void testQueryQueryRecommendProductIdsReturnNull() throws Throwable {
        ActivityContext activityContext = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        params.put("algorithmQueryBizId", 1);
        when(atomFacadeService.queryRecommendProductIds(any(RecommendParameters.class), eq(RecommendDTO.class))).thenReturn(CompletableFuture.completedFuture(null));
        CompletableFuture<ProductGroupM> result = algorithmQueryHandler.query(activityContext, "groupName", params);
        assertNull(result.get());
    }
}
