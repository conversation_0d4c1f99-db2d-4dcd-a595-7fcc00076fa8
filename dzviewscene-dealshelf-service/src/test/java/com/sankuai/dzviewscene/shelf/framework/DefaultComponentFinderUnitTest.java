package com.sankuai.dzviewscene.shelf.framework;

import com.sankuai.dzviewscene.shelf.SpringConfiguration;
import com.sankuai.dzviewscene.shelf.framework.core.*;
import com.sankuai.dzviewscene.shelf.framework.exception.ComponentNotFoundException;
import com.sankuai.dzviewscene.shelf.platform.MockBeanTest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.cardbar.CardBarBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilder;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle.MainTitleBuilderExt;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by float on 2020/8/23.
 */
@Ignore
@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {SpringConfiguration.class})
public class DefaultComponentFinderUnitTest extends MockBeanTest {

    @Resource
    private ComponentFinder componentFinder;

    //@Test
    @DisplayName("查找上下文构造器, 找到")
    public void test_find_context_builder() throws Exception {
        IContextBuilder iContextBuilder = componentFinder.findContextBuilder(ShelfActivity.ACTIVITY_SHELF_CODE);
        Assert.assertTrue(iContextBuilder != null);
    }

    //@Test(expected = ComponentNotFoundException.class)
    @DisplayName("测试查找上下文构造器, 但是没找到")
    public void test_find_context_builder_but_not_found() throws Exception {
        componentFinder.findContextBuilder("activity_code_not_existed");
    }

    //@Test
    @DisplayName("测试查找场景识别器, 找到")
    public void test_find_scene_identifier_success() throws Exception {
        ISceneIdentifier sceneIdentifier = componentFinder.findSceneIdentifier(ShelfActivity.ACTIVITY_SHELF_CODE);
        Assert.assertTrue(sceneIdentifier != null);
    }

    //@Test(expected = ComponentNotFoundException.class)
    @DisplayName("测试查找场景识别器, 但是没找到")
    public void test_find_scene_identifier_but_not_found() throws Exception {
            componentFinder.findSceneIdentifier("scene_identifier_name_not_existed");
    }

    //@Test
    @DisplayName("测试查找活动, 找到")
    public void test_find_activity_and_found() throws Exception {
        IActivity activity = componentFinder.findActivity(ShelfActivity.ACTIVITY_SHELF_CODE);
        Assert.assertTrue(activity != null);
    }

    //@Test(expected = ComponentNotFoundException.class)
    @DisplayName("测试查找活动, 但是没找到")
    public void test_find_activity_but_not_found() throws Exception {
        componentFinder.findActivity("activity_name_not_existed");
    }

    //@Test
    @DisplayName("测试查找特定场景活动校验器, 找到")
    public void test_find_activity_validators() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        List<IActivityValidator> validators = componentFinder.findActivityValidators(activityContext);
        Assert.assertTrue(CollectionUtils.isNotEmpty(validators));
    }

    //@Test
    @DisplayName("测试查找特定场景活动校验器, 但是没找到")
    public void test_find_activity_validators_but_not_found() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("scene_code_not_existed");
        List<IActivityValidator> validators = componentFinder.findActivityValidators(activityContext);
        Assert.assertTrue(CollectionUtils.isEmpty(validators));
    }

    //@Test
    @DisplayName("测试查找特定场景上下文扩展器, 找到")
    public void test_find_ext_context_success() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        List<IContextExt> contextExts = componentFinder.findContextExt(activityContext);
        Assert.assertTrue(CollectionUtils.isNotEmpty(contextExts));
    }

    //@Test
    @DisplayName("测试查找特定场景上下文扩展器, 但是没找到")
    public void test_find_ext_context_but_not_found() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("scene_code_not_existed");
        List<IContextExt> contextExts = componentFinder.findContextExt(activityContext);
        Assert.assertTrue(CollectionUtils.isEmpty(contextExts));
    }

    //@Test
    @DisplayName("测试查找能力实例, 找到")
    public void test_find_ability_success() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        IAbility ability = componentFinder.findAbility(activityContext, FilterBuilder.ABILITY_FILTER_GEN_CODE);
        Assert.assertTrue(ability != null);
    }

    //@Test(expected = ComponentNotFoundException.class)
    @DisplayName("测试查找能力实例, 但是没找到")
    public void test_find_ability_but_not_found() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        componentFinder.findAbility(activityContext, MainTitleBuilder.ABILITY_MAIN_TITLE_CODE);
    }

    //@Test
    @DisplayName("测试查找扩展点, 找到")
    public void test_find_ext_point_success() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        CardBarBuilderExt cardBarBuilderExt = (CardBarBuilderExt) componentFinder.findExtPoint(activityContext, CardBarBuilderExt.EXT_POINT_CARD_BAR_CODE);
        Assert.assertTrue(cardBarBuilderExt != null);

    }

    //@Test(expected = ComponentNotFoundException.class)
    @DisplayName("测试查找扩展点, 但是没找到")
    public void test_find_ext_point_but_not_found() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        componentFinder.findExtPoint(activityContext, MainTitleBuilderExt.EXT_POINT_MAIN_TITLE_CODE);
    }

    //@Test
    @DisplayName("测试查找活动流程, 找到")
    public void test_find_activity_flow_success() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        IActivityFlow iActivityFlow = componentFinder.findActivityFlow(activityContext);
        Assert.assertTrue(iActivityFlow != null);
    }

    //@Test(expected = ComponentNotFoundException.class)
    @DisplayName("测试查找活动流程, 但是没找到")
    public void test_find_activity_flow_but_not_found() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode("activity_code_not_existed");
        componentFinder.findActivityFlow(activityContext);
    }

    //@Test
    public void name() throws Exception {
        ActivityContext activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        IAbility ability = componentFinder.findAbility(activityContext, FilterBuilder.ABILITY_FILTER_GEN_CODE);
        IActivity activity = componentFinder.findActivity(ShelfActivity.ACTIVITY_SHELF_CODE);
        Assert.assertTrue(activity != null);
    }
}
