package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.mock;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EmptyFilterBuilderTest {

    /**
     * 测试 build 方法是否能正确返回一个包含空 HashMap 的 CompletableFuture
     */
    @Test
    public void testBuildReturnsCorrectly() throws Throwable {
        // arrange
        ActivityContext ctx = mock(ActivityContext.class);
        EmptyFilterBuilder emptyFilterBuilder = new EmptyFilterBuilder();
        // act
        CompletableFuture<Map<String, com.sankuai.dzviewscene.productshelf.vu.vo.FilterComponentVO>> result = emptyFilterBuilder.build(ctx);
        // assert
        assertTrue(result.get().isEmpty());
    }

    /**
     * 测试 build 方法是否能正确处理 null 参数
     */
    @Test
    public void testBuildHandlesNullContext() throws Throwable {
        // arrange
        ActivityContext ctx = null;
        EmptyFilterBuilder emptyFilterBuilder = new EmptyFilterBuilder();
        // act
        CompletableFuture<Map<String, com.sankuai.dzviewscene.productshelf.vu.vo.FilterComponentVO>> result = emptyFilterBuilder.build(ctx);
        // assert
        assertTrue(result.get().isEmpty());
    }
}
