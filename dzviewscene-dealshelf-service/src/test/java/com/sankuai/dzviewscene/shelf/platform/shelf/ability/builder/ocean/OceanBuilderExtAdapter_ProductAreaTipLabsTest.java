package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.ocean;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OceanBuilderExtAdapter_ProductAreaTipLabsTest {

    @Mock
    private ActivityContext activityContext;

    /**
     * 测试 productAreaTipLabs 方法是否总是返回 null
     */
    @Test
    public void testProductAreaTipLabsReturnNull() throws Throwable {
        // arrange
        OceanBuilderExtAdapter oceanBuilderExtAdapter = new OceanBuilderExtAdapter() {

            @Override
            public String productAreaTipLabs(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = oceanBuilderExtAdapter.productAreaTipLabs(activityContext);
        // assert
        assertNull(result);
    }
}
