package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter;

import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.filter.FilterBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;

import java.util.List;

/**
 * Created by float.lu on 2020/8/24.
 */
@ExtPointInstance(name = "测试筛选组件构造器扩展")
public class TestFilterBuilderExt extends FilterBuilderExtAdapter {


    @Override
    public int showType(ActivityContext activityContext, String groupName) {
        return 0;
    }

    @Override
    public int minShowNum(ActivityContext activityContext, String groupName) {
        return 0;
    }

    @Override
    public int childrenMinShowNum(ActivityContext activityContext, String groupName) {
        return 0;
    }

    @Override
    public String labs(ActivityContext activityContext, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public RichLabelVO titleButton(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public RichLabelVO subTitle(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public String extra(ActivityContext activityContext, String groupName, FilterBtnM filterBtnM) {
        return null;
    }

    @Override
    public List<IconRichLabelVO> preFixedBtns(ActivityContext activityContext, String groupName) {
        return null;
    }

    @Override
    public boolean showWithNoProducts(ActivityContext activityContext) {
        return false;
    }

}
