package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import static org.junit.Assert.assertEquals;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfFilterFetcherExtTest {

    /**
     * 测试selected方法是否能正确返回0
     */
    @Test
    public void testSelectedReturnZero() throws Throwable {
        // arrange
        ShelfFilterFetcherExt shelfFilterFetcherExt = new ShelfFilterFetcherExt();
        ActivityContext activityContext = new ActivityContext();
        String groupName = "testGroupName";
        FilterM filterM = new FilterM();
        // act
        long result = shelfFilterFetcherExt.selected(activityContext, groupName, filterM);
        // assert
        assertEquals(0, result);
    }
}
