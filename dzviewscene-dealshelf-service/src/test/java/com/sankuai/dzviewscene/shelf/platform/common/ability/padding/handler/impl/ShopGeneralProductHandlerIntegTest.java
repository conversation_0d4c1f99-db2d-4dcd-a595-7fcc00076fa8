package com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl;

import com.dianping.beauty.zone.common.adapter.ItemSearchAdapter;
import com.dianping.beauty.zone.common.parse.ItemDefaultParser;
import com.dianping.beauty.zone.vc.service.base.ItemSearchService;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.google.common.collect.Lists;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.shelf.business.shelf.swimdeal.SwimContext;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl.ShopGeneralProductHandler;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2021/8/3 下午4:17
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl","com.sankuai.dzviewscene.nr.atom", "com.sankuai.dzviewscene.productshelf.nr.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class ShopGeneralProductHandlerIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(ShopGeneralProductHandlerIntegTest.class);

    @Resource
    private ShopGeneralProductHandler shopGeneralProductHandler;


    static {
        TestBeanFactory.registerBean("shopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
    }

    @Environment(AthenaEnv.Test)
    //@Test
    public void test_beta() {
        ActivityContext activityContext = buildContext();
        Map<String, Object> params = buildParams(null);
        ProductGroupM result = shopGeneralProductHandler.query(activityContext, "商品", params).join();
        Assert.assertTrue(result != null && CollectionUtils.isNotEmpty(result.getProducts()));
        logger.info("结果为：" + JsonCodec.encodeWithUTF8(result));

        int totalSize = result.getProducts().size();
        params = buildParams(Lists.newArrayList(405845779, 405982317));
        result = shopGeneralProductHandler.query(activityContext, "商品", params).join();
        Assert.assertTrue(result != null && CollectionUtils.isNotEmpty(result.getProducts()) && result.getProducts().size() + 2 == totalSize);
    }

    private ActivityContext buildContext() {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.platform, 1);
        activityContext.addParam(ShelfActivityConstants.Params.dpCityId, 1);
        activityContext.addParam(ShelfActivityConstants.Params.mtCityId, 10);
        activityContext.addParam(ShelfActivityConstants.Params.dpUserId, 0L);
        activityContext.addParam(ShelfActivityConstants.Params.mtUserId, 0L);
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, 3386251);
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiId, 0);
        activityContext.addParam(ShelfActivityConstants.Params.deviceId, "");
        activityContext.addParam(ShelfActivityConstants.Params.appVersion, "10.10.3");
        activityContext.addParam(ShelfActivityConstants.Params.clientType, 101);
        return activityContext;
    }

    private Map<String, Object> buildParams(List<Integer> productIdList) {
        Map<String, Object> params = new HashMap<>();
        params.put(PaddingFetcher.Params.planId, SwimContext.DEFAULT_DEAL_THEME_PLAN_ID);
        params.put("promoTemplateId", SwimContext.PROMO_TEMPLATE_ID);
        params.put("tcMergePromoTemplateId", SwimContext.TC_MERGE_PROMO_TEMPLATE_ID);
        params.put("scene", SwimContext.JOY_CARD_SCENE_ID);
        params.put(PaddingFetcher.Params.paddingType, PaddingFetcher.PaddingType.dealThemePadding.name());
        params.put(QueryFetcher.Params.excludeProductIds, productIdList);
        return params;
    }

}
