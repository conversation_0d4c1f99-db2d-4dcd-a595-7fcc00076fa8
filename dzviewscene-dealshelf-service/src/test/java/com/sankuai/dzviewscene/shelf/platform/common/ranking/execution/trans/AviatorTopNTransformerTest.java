package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.AviatorEvaluatorInstance;
import com.googlecode.aviator.Expression;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.core.RankingItem;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AviatorTopNTransformerTest {

    @Mock
    private RankingContext context;

    @Mock
    private RankingItem item;

    private AviatorTopNTransformer transformer;

    private AviatorEvaluatorInstance aviatorEvaluatorInstanceSpy;

    @Mock
    private Expression expression;

    @Before
    public void setUp() {
        transformer = new AviatorTopNTransformer("10");
    }

    /**
     * 测试 execute 方法，正常情况
     */
    @Test
    public void testExecuteNormal() throws Throwable {
        // arrange
        List<RankingItem> items = Arrays.asList(item, item, item);
        // act
        List<RankingItem> result = transformer.execute(context, items);
        // assert
        assertEquals(3, result.size());
    }

    /**
     * 测试 execute 方法，边界情况
     */
    @Test
    public void testExecuteBoundary() throws Throwable {
        // arrange
        List<RankingItem> items = Arrays.asList();
        // act
        List<RankingItem> result = transformer.execute(context, items);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 execute 方法，异常情况
     */
    @Test
    public void testExecuteException() throws Throwable {
        // arrange
        List<RankingItem> items = Arrays.asList(item, item, item);
        // act
        List<RankingItem> result = transformer.execute(context, items);
        // assert
        assertEquals(3, result.size());
    }

    @Test
    public void testTopNNormal() throws Throwable {
        try (MockedStatic<AviatorEvaluator> mocked = mockStatic(AviatorEvaluator.class)) {
            // arrange
            RankingContext context = new RankingContext();
            AviatorTopNTransformer transformer = new AviatorTopNTransformer("1+2");
            aviatorEvaluatorInstanceSpy = mock(AviatorEvaluatorInstance.class);
            mocked.when(AviatorEvaluator::getInstance).thenReturn(aviatorEvaluatorInstanceSpy);
            when(aviatorEvaluatorInstanceSpy.compile(anyString(), anyBoolean())).thenReturn(expression);
            when(expression.execute(anyMap())).thenReturn(3);
            // act
            int result = transformer.topN(context);
            // assert
            assertEquals(3, result);
        }
    }

    @Test(expected = NullPointerException.class)
    public void testTopNWithNullContext() throws Throwable {
        // arrange
        AviatorTopNTransformer transformer = new AviatorTopNTransformer("1+2");
        // act
        transformer.topN(null);
    }

    @Test(expected = RuntimeException.class)
    public void testTopNWithException() throws Throwable {
        try (MockedStatic<AviatorEvaluator> mocked = mockStatic(AviatorEvaluator.class)) {
            // arrange
            RankingContext context = new RankingContext();
            AviatorTopNTransformer transformer = new AviatorTopNTransformer("1+2");
            aviatorEvaluatorInstanceSpy = mock(AviatorEvaluatorInstance.class);
            mocked.when(AviatorEvaluator::getInstance).thenReturn(aviatorEvaluatorInstanceSpy);
            when(aviatorEvaluatorInstanceSpy.compile(anyString(), anyBoolean())).thenReturn(expression);
            when(expression.execute(anyMap())).thenThrow(new RuntimeException());
            // act
            transformer.topN(context);
        }
    }

    @Test
    public void testTopNWithNullResult() throws Throwable {
        try (MockedStatic<AviatorEvaluator> mocked = mockStatic(AviatorEvaluator.class)) {
            // arrange
            RankingContext context = new RankingContext();
            AviatorTopNTransformer transformer = new AviatorTopNTransformer("1+2");
            aviatorEvaluatorInstanceSpy = mock(AviatorEvaluatorInstance.class);
            mocked.when(AviatorEvaluator::getInstance).thenReturn(aviatorEvaluatorInstanceSpy);
            when(aviatorEvaluatorInstanceSpy.compile(anyString(), anyBoolean())).thenReturn(expression);
            when(expression.execute(anyMap())).thenReturn(null);
            // act
            int result = transformer.topN(context);
            // assert
            assertEquals(Integer.MAX_VALUE, result);
        }
    }
}
