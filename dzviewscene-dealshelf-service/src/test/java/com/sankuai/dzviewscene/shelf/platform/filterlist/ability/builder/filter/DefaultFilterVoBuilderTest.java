package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.filter;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.ArrayList;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultFilterVoBuilderTest {

    @Mock
    private ActivityContext ctx;

    @Mock
    private FilterProductsM filterProductsM;

    @Mock
    private FilterM // Mocking FilterM
    filterM;

    @Test
    public void testBuildMainDataIsNull() throws Throwable {
        // arrange
        DefaultFilterVoBuilder builder = new DefaultFilterVoBuilder();
        when(ctx.getMainData()).thenReturn(null);
        // act
        CompletableFuture<List<DzFilterVO>> result = builder.build(ctx);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildFilterProductsIsNull() throws Throwable {
        // arrange
        DefaultFilterVoBuilder builder = new DefaultFilterVoBuilder();
        when(ctx.getMainData()).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<List<DzFilterVO>> result = builder.build(ctx);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildFiltersIsEmpty() throws Throwable {
        // arrange
        DefaultFilterVoBuilder builder = new DefaultFilterVoBuilder();
        when(ctx.getMainData()).thenReturn(CompletableFuture.completedFuture(filterProductsM));
        when(filterProductsM.getFilters()).thenReturn(Collections.emptyList());
        // act
        CompletableFuture<List<DzFilterVO>> result = builder.build(ctx);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildAllFiltersAreNotEmpty() throws Throwable {
        // arrange
        DefaultFilterVoBuilder builder = new DefaultFilterVoBuilder();
        when(ctx.getMainData()).thenReturn(CompletableFuture.completedFuture(filterProductsM));
        // Corrected to use FilterM
        when(filterProductsM.getFilters()).thenReturn(Collections.singletonList(filterM));
        // act
        CompletableFuture<List<DzFilterVO>> result = builder.build(ctx);
        // assert
        assertFalse(result.get().isEmpty());
    }
}
