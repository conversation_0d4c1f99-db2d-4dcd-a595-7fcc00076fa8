package com.sankuai.dzviewscene.shelf.platform.common.query.queries;

import com.sankuai.dzviewscene.shelf.platform.common.ability.query.queries.BasePriceAseQuery;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.data.ProductMsMock;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;

/**
 * Created by float.lu on 2020/9/23.
 */
public class BasePriceAseQueryTest {

    private static QueryContext queryContext = new QueryContext();

    @Before
    public void setUp() throws Exception {
        queryContext.setPool(ProductMsMock.mockProductGroups());
    }


    //@Test
    @DisplayName("测试召回的商品是按照价格正序的")
    public void test_products_sorted_by_asc() throws Exception {
        List<ProductM> products = new BasePriceAseQuery().query(queryContext);
        Assert.assertThat(products, Matchers.notNullValue());
    }
}
