package com.sankuai.dzviewscene.shelf.platform.other.ability.flow;

import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.core.IAbility;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class NavAndProductShelfOtherActivityFlowTest {

    private AbstractActivity<?> activity;

    private ActivityContext ctx;

    private IAbility queryFetcherAbility;

    private IAbility filterFetcherAbility;

    private IAbility paddingFetcherAbility;

    @Before
    public void setUp() {
        activity = mock(AbstractActivity.class);
        ctx = new ActivityContext();
        queryFetcherAbility = mock(IAbility.class);
        filterFetcherAbility = mock(IAbility.class);
        paddingFetcherAbility = mock(IAbility.class);
        when(activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(queryFetcherAbility);
        when(activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE)).thenReturn(filterFetcherAbility);
        when(activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE)).thenReturn(paddingFetcherAbility);
    }

    @After
    public void tearDown() {
        // Reset mocks
        Mockito.reset(activity, queryFetcherAbility, filterFetcherAbility, paddingFetcherAbility);
    }

    /**
     * Test execute method when all futures complete normally.
     */
    @Test
    public void testExecuteAllFuturesCompleteNormally() throws Throwable {
        // arrange
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        Map<String, FilterM> filters = new HashMap<>();
        CompletableFuture<Map<String, ProductGroupM>> productGroupsFuture = CompletableFuture.completedFuture(productGroups);
        CompletableFuture<Map<String, FilterM>> filtersFuture = CompletableFuture.completedFuture(filters);
        when(queryFetcherAbility.build(any())).thenReturn(productGroupsFuture);
        when(filterFetcherAbility.build(any())).thenReturn(filtersFuture);
        when(paddingFetcherAbility.build(any())).thenReturn(productGroupsFuture);
        NavAndProductShelfOtherActivityFlow flow = new NavAndProductShelfOtherActivityFlow();
        // act
        CompletableFuture<ShelfGroupM> resultFuture = flow.execute(activity, ctx);
        // assert
        assertNotNull(resultFuture);
        assertTrue(resultFuture.isDone());
        ShelfGroupM result = resultFuture.get();
        assertNotNull(result);
        assertEquals(productGroups, result.getProductGroupMs());
        assertEquals(filters, result.getFilterMs());
    }

    /**
     * Test execute method when query fetcher future completes exceptionally.
     */
    @Test(expected = ExecutionException.class)
    public void testExecuteQueryFetcherFutureCompletesExceptionally() throws Throwable {
        // arrange
        CompletableFuture<Map<String, ProductGroupM>> productGroupsFuture = new CompletableFuture<>();
        productGroupsFuture.completeExceptionally(new RuntimeException("Query fetcher failed"));
        when(queryFetcherAbility.build(any())).thenReturn(productGroupsFuture);
        NavAndProductShelfOtherActivityFlow flow = new NavAndProductShelfOtherActivityFlow();
        // act
        CompletableFuture<ShelfGroupM> resultFuture = flow.execute(activity, ctx);
        // assert
        // This should throw an ExecutionException
        resultFuture.get();
    }

    /**
     * Test execute method when filter fetcher future completes exceptionally.
     */
    @Test(expected = ExecutionException.class)
    public void testExecuteFilterFetcherFutureCompletesExceptionally() throws Throwable {
        // arrange
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        CompletableFuture<Map<String, ProductGroupM>> productGroupsFuture = CompletableFuture.completedFuture(productGroups);
        CompletableFuture<Map<String, FilterM>> filtersFuture = new CompletableFuture<>();
        filtersFuture.completeExceptionally(new RuntimeException("Filter fetcher failed"));
        when(queryFetcherAbility.build(any())).thenReturn(productGroupsFuture);
        when(filterFetcherAbility.build(any())).thenReturn(filtersFuture);
        NavAndProductShelfOtherActivityFlow flow = new NavAndProductShelfOtherActivityFlow();
        // act
        CompletableFuture<ShelfGroupM> resultFuture = flow.execute(activity, ctx);
        // assert
        // This should throw an ExecutionException
        resultFuture.get();
    }

    /**
     * Test execute method when padding fetcher future completes exceptionally.
     */
    @Test(expected = ExecutionException.class)
    public void testExecutePaddingFetcherFutureCompletesExceptionally() throws Throwable {
        // arrange
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        CompletableFuture<Map<String, ProductGroupM>> productGroupsFuture = CompletableFuture.completedFuture(productGroups);
        CompletableFuture<Map<String, ProductGroupM>> paddingFuture = new CompletableFuture<>();
        paddingFuture.completeExceptionally(new RuntimeException("Padding fetcher failed"));
        when(queryFetcherAbility.build(any())).thenReturn(productGroupsFuture);
        when(paddingFetcherAbility.build(any())).thenReturn(paddingFuture);
        NavAndProductShelfOtherActivityFlow flow = new NavAndProductShelfOtherActivityFlow();
        // act
        CompletableFuture<ShelfGroupM> resultFuture = flow.execute(activity, ctx);
        // assert
        // This should throw an ExecutionException
        resultFuture.get();
    }
}
