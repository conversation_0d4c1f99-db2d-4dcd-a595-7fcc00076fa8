package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductRecommendDaysHandler_QueryTest {

    @InjectMocks
    private ProductRecommendDaysHandler productRecommendDaysHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ActivityContext activityContext;

    private final String groupName = "testGroupName";

    private final Map<String, Object> params = new HashMap<>();

    private void mockActivityContextParams() {
        when(activityContext.getParam(anyString())).thenReturn("1");
        when(activityContext.getParam(FilterListActivityConstants.Params.dpCityId)).thenReturn(1);
        when(activityContext.getParam(FilterListActivityConstants.Params.dpUserId)).thenReturn(1L);
        when(activityContext.getParam(FilterListActivityConstants.Params.lat)).thenReturn(1.0);
        when(activityContext.getParam(FilterListActivityConstants.Params.lng)).thenReturn(1.0);
        when(activityContext.getParam(FilterListActivityConstants.Params.pageNo)).thenReturn(1);
        when(activityContext.getParam(FilterListActivityConstants.Params.deviceId)).thenReturn("device123");
    }

    /**
     * Tests the normal execution scenario.
     */
    @Test
    public void testQueryNormal() throws Throwable {
        // Arrange
        when(compositeAtomService.getRecommendResult(any())).thenReturn(CompletableFuture.completedFuture(null));
        mockActivityContextParams();
        // Act
        CompletableFuture<ProductGroupM> result = productRecommendDaysHandler.query(activityContext, groupName, params);
        // Assert
        assertNotNull(result);
        // Adjusted to expect 2 invocations
        verify(compositeAtomService, times(2)).getRecommendResult(any());
    }

    /**
     * Tests the scenario when an exception is thrown.
     */
    @Test(expected = RuntimeException.class)
    public void testQueryException() throws Throwable {
        // Arrange
        // Act
        productRecommendDaysHandler.query(activityContext, groupName, params);
    }

    /**
     * Tests the boundary scenario where ActivityContext is null.
     */
    @Test(expected = NullPointerException.class)
    public void testQueryBoundary() throws Throwable {
        // Arrange
        ActivityContext nullContext = null;
        // Act
        productRecommendDaysHandler.query(nullContext, groupName, params);
    }
}
