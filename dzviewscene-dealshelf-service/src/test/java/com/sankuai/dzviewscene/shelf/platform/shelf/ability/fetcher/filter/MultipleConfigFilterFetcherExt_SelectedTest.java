package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
// Corrected import
import java.util.Arrays;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.junit.runner.RunWith;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MultipleConfigFilterFetcherExt_SelectedTest {

    @Mock
    private FilterConfig filterConfig;

    @InjectMocks
    private MultipleConfigFilterFetcherExt multipleConfigFilterFetcherExt;

    /**
     * Test when page source matches bath listing and filter ID matches.
     */
    @Test
    public void testSelectedPageSourceMatchAndFilterIdMatch() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.pageSource, "bathListing");
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(200120559L);
        filterM.setFilters(Collections.singletonList(filterBtnM));
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(200120559L, selectedFilterId);
    }

    /**
     * Test when keyword is blank and returns first filter ID.
     */
    @Test
    public void testSelectedKeywordBlankReturnsFirstFilterId() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.keyword, "");
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(100L);
        filterM.setFilters(Collections.singletonList(filterBtnM));
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(100L, selectedFilterId);
    }

    /**
     * Test when filter keywords map is empty.
     */
    @Test
    public void testSelectedFilterKeywordsMapEmpty() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.keyword, "keyword");
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(100L);
        filterM.setFilters(Collections.singletonList(filterBtnM));
        Mockito.when(filterConfig.getMultipleFilterKeywords(Mockito.anyString(), Mockito.anyString())).thenReturn(Collections.emptyMap());
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(100L, selectedFilterId);
    }

    /**
     * Test when config auto upgrade is true but no match found.
     */
    @Test
    public void testSelectedConfigAutoUpgradeTrueNoMatch() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.keyword, "keyword");
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(100L);
        filterM.setFilters(Collections.singletonList(filterBtnM));
        Map<String, FilterConfig.MultipleFilterConfig> filterKeywords = new HashMap<>();
        filterKeywords.put("key", new FilterConfig.MultipleFilterConfig());
        Mockito.when(filterConfig.getMultipleFilterKeywords(Mockito.anyString(), Mockito.anyString())).thenReturn(filterKeywords);
        Mockito.when(filterConfig.isConfigAutoUpgrade(Mockito.anyString())).thenReturn(true);
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(100L, selectedFilterId);
    }

    /**
     * Test when no filters are provided.
     */
    @Test
    public void testSelectedNoFiltersProvided() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        FilterM filterM = new FilterM();
        filterM.setFilters(Collections.emptyList());
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(0L, selectedFilterId);
    }

    /**
     * Test when keyword matches filter button with config auto upgrade enabled but no direct match found.
     */
    @Test
    public void testSelectedKeywordMatchesWithAutoUpgradeEnabled() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.keyword, "Spa");
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setFilterId(200L);
        filterBtnM1.setTitle("Massage");
        FilterBtnM filterBtnM2 = new FilterBtnM();
        filterBtnM2.setFilterId(300L);
        filterBtnM2.setTitle("Spa");
        // Corrected Arrays.asList usage
        filterM.setFilters(Arrays.asList(filterBtnM1, filterBtnM2));
        Map<String, FilterConfig.MultipleFilterConfig> filterKeywords = new HashMap<>();
        FilterConfig.MultipleFilterConfig config = new FilterConfig.MultipleFilterConfig();
        config.setChild(new HashMap<>());
        // Corrected Arrays.asList usage
        config.setKey(Arrays.asList("Spa"));
        filterKeywords.put("Spa", config);
        Mockito.when(filterConfig.getMultipleFilterKeywords(Mockito.anyString(), Mockito.anyString())).thenReturn(filterKeywords);
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(300L, selectedFilterId);
    }

    /**
     * Test when no match is found with config auto upgrade enabled.
     */
    @Test
    public void testSelectedNoMatchWithAutoUpgradeEnabled() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.keyword, "Yoga");
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(100L);
        filterBtnM.setTitle("Fitness");
        filterM.setFilters(Collections.singletonList(filterBtnM));
        Map<String, FilterConfig.MultipleFilterConfig> filterKeywords = new HashMap<>();
        Mockito.when(filterConfig.getMultipleFilterKeywords(Mockito.anyString(), Mockito.anyString())).thenReturn(filterKeywords);
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(100L, selectedFilterId);
    }

    /**
     * Test when keyword matches but the matching filter button has no children.
     */
    @Test
    public void testSelectedKeywordMatchesButNoChildren() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.keyword, "Gym");
        FilterM filterM = new FilterM();
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(400L);
        filterBtnM.setTitle("Gym");
        filterM.setFilters(Collections.singletonList(filterBtnM));
        Map<String, FilterConfig.MultipleFilterConfig> filterKeywords = new HashMap<>();
        FilterConfig.MultipleFilterConfig config = new FilterConfig.MultipleFilterConfig();
        config.setChild(new HashMap<>());
        // Corrected Arrays.asList usage
        config.setKey(Arrays.asList("Gym"));
        filterKeywords.put("Gym", config);
        Mockito.when(filterConfig.getMultipleFilterKeywords(Mockito.anyString(), Mockito.anyString())).thenReturn(filterKeywords);
        long selectedFilterId = multipleConfigFilterFetcherExt.selected(activityContext, "groupName", filterM);
        Assert.assertEquals(400L, selectedFilterId);
    }
}
