package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductListBuilderExtAdapter_SubtitleTest {

    @Mock
    private ActivityContext activityContext;

    /**
     * 测试 subtitle 方法是否总是返回 null
     */
    @Test
    public void testSubtitleReturnNull() throws Throwable {
        // arrange
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {

            @Override
            public String subtitle(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = productListBuilderExtAdapter.subtitle(activityContext);
        // assert
        assertNull(result);
    }

    /**
     * 测试 subtitle 方法在输入为 null 时是否返回 null
     */
    @Test
    public void testSubtitleWithNullInput() throws Throwable {
        // arrange
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {

            @Override
            public String subtitle(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = productListBuilderExtAdapter.subtitle(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 subtitle 方法在输入为非 null 时是否返回 null
     */
    @Test
    public void testSubtitleWithNonNullInput() throws Throwable {
        // arrange
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {

            @Override
            public String subtitle(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = productListBuilderExtAdapter.subtitle(activityContext);
        // assert
        assertNull(result);
    }

    /**
     * 测试 moreJumpText 方法在传入非空 ActivityContext 时的行为
     */
    @Test
    public void testMoreJumpTextWithNonNullInput() throws Throwable {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {
        };
        // act
        String result = productListBuilderExtAdapter.moreJumpText(activityContext);
        // assert
        assertNull(result);
    }

    /**
     * 测试 moreJumpText 方法在传入 null 时的行为
     */
    @Test
    public void testMoreJumpTextWithNullInput() throws Throwable {
        // arrange
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {
        };
        // act
        String result = productListBuilderExtAdapter.moreJumpText(null);
        // assert
        assertNull(result);
    }
}
