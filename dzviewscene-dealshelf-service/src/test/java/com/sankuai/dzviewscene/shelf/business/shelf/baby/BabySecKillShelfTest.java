package com.sankuai.dzviewscene.shelf.business.shelf.baby;

import com.dianping.baby.timeLimitTg.dto.BabyTimeLimeTgSpikeDTO;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import lombok.extern.slf4j.Slf4j;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/8
 */
@Ignore("没有可执行的方法")
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
@ContextConfig(packages = "com.sankuai.dzviewscene")
public class BabySecKillShelfTest {

    @Resource
    ActivityEngine activityEngine;


    @Environment(value = AthenaEnv.Test,swimlane = "3792-soihm")
    //@Test
    public void integrateTest() {
        for (int i = 0; i < 100; i++) {
            Object o = activityEngine.execute(buildActivityRequest());
            log.info("ooooooo :{}", JsonCodec.encode(o));
        }

    }

    private List<BabyTimeLimeTgSpikeDTO> mockBabyBackEnd() {
        List<BabyTimeLimeTgSpikeDTO> lists = new ArrayList<>();
        BabyTimeLimeTgSpikeDTO o1 = new BabyTimeLimeTgSpikeDTO();
        o1.setDpSkuId(405778752L);
        o1.setEndTime(System.currentTimeMillis()+100000);
        BabyTimeLimeTgSpikeDTO o2 = new BabyTimeLimeTgSpikeDTO();
        o2.setDpSkuId(405783178L);
        o2.setEndTime(System.currentTimeMillis()+100000);
        lists.add(o1);
        lists.add(o2);
        return lists;
    }

    private ActivityRequest buildActivityRequest() {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, "baby_play_sec_kill_deal_shelf");
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, 1);
        activityRequest.addParam(ShelfActivityConstants.Params.platform,1);
        activityRequest.addParam(ShelfActivityConstants.Params.unionId,"f62ba5df790542068604a7a369f8975fa162274986080698050");
        if (VCClientTypeEnum.isMtClientTypeByCode(1)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, 10);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, 6008479);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, 1);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, 6008479);
        }
        return activityRequest;
    }
}
