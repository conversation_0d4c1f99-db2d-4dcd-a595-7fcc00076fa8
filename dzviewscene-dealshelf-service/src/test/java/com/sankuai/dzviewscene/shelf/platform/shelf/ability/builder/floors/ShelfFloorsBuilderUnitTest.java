package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.productshelf.vu.vo.FilterBtnIdAndProAreasVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.data.ProductMsMock;
import com.sankuai.dzviewscene.shelf.platform.common.enums.ProductEnums;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.MultiplexGroupFilterFetcherExtMock;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfM;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/9/17.
 */
@Ignore("没有可执行的方法")
@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {SpringScope.class})
public class ShelfFloorsBuilderUnitTest {

    @MockBean
    private ComponentFinder componentFinder;
    @MockBean
    private AtomFacadeService atomFacadeService;
    @MockBean
    private CompositeAtomService compositeAtomService;
    @Resource
    private ShelfFloorsBuilder shelfFloorsBuilder;


    //@Test
    @DisplayName("测试有扩展实现的情况, 价格是80.00")
    public void test_has_sale_price_extension() throws Exception {
        // 有扩展售卖价格实现, 售卖价格为"80.00"
        Mockito.when(componentFinder.findExtPoint(Mockito.any(), Mockito.anyString())).thenReturn(new FloorsBuilderExtAdapter() {
            @Override
            public String itemComponentSalePrice(ActivityContext activityContext, String groupName, ProductM productM) {
                return "80.00";
            }
        });
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.cosmetic.name()));

        activityContext.addParam(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.cosmetic.name(), new HashMap<String, Object>() {{
                put(PaddingFetcher.Params.planId, "10100008");
                put(PaddingFetcher.Params.statisticSaleBizType, 12);
            }});
        }});
        activityContext.setMainData(CompletableFuture.completedFuture(mockProductGroups()));
        CompletableFuture<List<FilterBtnIdAndProAreasVO>> future = shelfFloorsBuilder.build(activityContext);
        Assert.assertThat(future, Matchers.notNullValue());
    }

    //@Test
    @DisplayName("测试没有扩展实现的情况, 价格是60.00")
    public void test_has_no_sale_price_extension() throws Exception {
        // 有扩展售卖价格实现, 售卖价格为"80.00"
        Mockito.when(componentFinder.findExtPoint(Mockito.any(), Mockito.anyString())).thenReturn(new FloorsBuilderExtAdapter() {
        });
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(QueryFetcher.Params.groupNames, Lists.newArrayList(ProductEnums.cosmetic.name()));

        activityContext.addParam(QueryFetcher.Params.groupParams, new HashMap<String, Object>() {{
            put(ProductEnums.cosmetic.name(), new HashMap<String, Object>() {{
                put(PaddingFetcher.Params.planId, "10100008");
                put(PaddingFetcher.Params.statisticSaleBizType, 12);
            }});
        }});
        activityContext.setMainData(CompletableFuture.completedFuture(mockProductGroups()));
        CompletableFuture<List<FilterBtnIdAndProAreasVO>> future = shelfFloorsBuilder.build(activityContext);
        Assert.assertThat(future, Matchers.notNullValue());
    }


    private ShelfGroupM mockProductGroups() {
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(new HashMap<>());
        shelfGroupM.getProductGroupMs().put(ProductEnums.cosmetic.name(), mockProductGroupM());
        return shelfGroupM;
    }

    protected ProductGroupM mockProductGroupM() {
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(ProductMsMock.mockProductGroups());
        return productGroupM;
    }

}
