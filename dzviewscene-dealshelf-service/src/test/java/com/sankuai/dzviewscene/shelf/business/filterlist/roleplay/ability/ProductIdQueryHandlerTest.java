package com.sankuai.dzviewscene.shelf.business.filterlist.roleplay.ability;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductIdQueryHandlerTest {

    @Mock
    private ActivityContext ctx;

    @InjectMocks
    private ProductIdQueryHandler handler;

    /**
     * 测试 ActivityContext 为 null 的情况
     */
    @Test
    public void testQueryActivityContextIsNull() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        // act
        CompletableFuture<ProductGroupM> result = handler.query(null, groupName, params);
        // assert
        assertNotNull(result);
        assertFalse(result.isCompletedExceptionally());
        assertTrue(result.get() == null);
    }

    /**
     * 测试 productId 小于等于 0 的情况
     */
    @Test
    public void testQueryProductIdIsLessThanOrEqualToZero() throws Throwable {
        // arrange
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        // act
        CompletableFuture<ProductGroupM> result = handler.query(ctx, groupName, params);
        // assert
        assertNotNull(result);
        assertFalse(result.isCompletedExceptionally());
        assertTrue(result.get() == null);
    }

    /**
     * 测试 productId 大于 0 的情况
     */
    @Test
    public void testQueryProductIdIsGreaterThanZero() throws Throwable {
        // arrange
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("productId", 1);
        when(ctx.getParameters()).thenReturn(parameters);
        String groupName = "testGroup";
        Map<String, Object> params = new HashMap<>();
        // act
        CompletableFuture<ProductGroupM> result = handler.query(ctx, groupName, params);
        // assert
        assertNotNull(result);
        assertFalse(result.isCompletedExceptionally());
        ProductGroupM productGroup = result.get();
        assertNotNull(productGroup);
        assertNotNull(productGroup.getProducts());
        assertEquals(1, productGroup.getTotal());
        assertEquals(1, productGroup.getProducts().size());
    }
}
