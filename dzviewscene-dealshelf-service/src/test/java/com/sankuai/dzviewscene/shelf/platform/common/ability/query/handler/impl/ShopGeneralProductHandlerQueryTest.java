package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.common.utils.PoiMigrateSceneEnum;
import com.sankuai.common.utils.PoiMigrateUtils;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShopGeneralProductHandlerQueryTest {

    @InjectMocks
    private ShopGeneralProductHandler shopGeneralProductHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ActivityContext activityContext;

    /**
     * Test case for empty shopIds in poi migrate mode
     */
    @Test
    public void testQueryWithEmptyShopIdsInPoiMigrateMode() throws Throwable {
        // arrange
        try (MockedStatic<PoiMigrateUtils> poiMigrateUtils = mockStatic(PoiMigrateUtils.class)) {
            poiMigrateUtils.when(() -> PoiMigrateUtils.needLongPoiProcess(PoiMigrateSceneEnum.ShopGeneralProductHandler.getScene())).thenReturn(true);
            Map<String, List<Long>> groupName2ShopIds = new HashMap<>();
            groupName2ShopIds.put("test", Collections.emptyList());
            when(activityContext.getParam(QueryFetcher.Params.groupName2ShopIds)).thenReturn(groupName2ShopIds);
            when(activityContext.getParam(ShelfActivityConstants.Params.platform)).thenReturn(1);
            Map<String, Object> params = new HashMap<>();
            params.put(QueryFetcher.Params.productType, 1);
            params.put(QueryFetcher.Params.spuType, 1L);
            params.put(QueryFetcher.Params.filterInvalid, true);
            // act
            CompletableFuture<ProductGroupM> result = shopGeneralProductHandler.query(activityContext, "test", params);
            // assert
            assertNotNull(result);
            ProductGroupM productGroupM = result.get();
            assertNotNull(productGroupM);
            assertEquals(0, productGroupM.getTotal());
        }
    }
}
