package com.sankuai.dzviewscene.shelf.platform.shelf.model;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShelfGroupMAddDouHuTest {

    private ShelfGroupM shelfGroupM = new ShelfGroupM();

    /**
     * 测试 addDouHu 方法，当输入的 douHuList 为 null 或者为空列表时
     */
    @Test
    public void testAddDouHuWithEmptyList() throws Throwable {
        // arrange
        // act
        shelfGroupM.addDouHu((List<DouHuM>) null);
        shelfGroupM.addDouHu(Collections.emptyList());
        // assert
        assertTrue(shelfGroupM.getDouHus().isEmpty());
    }

    /**
     * 测试 addDouHu 方法，当输入的 douHuList 不为空，但是其中的 DouHuM 对象为 null 时
     */
    @Test
    public void testAddDouHuWithNullDouHuM() throws Throwable {
        // arrange
        List<DouHuM> douHuList = Arrays.asList((DouHuM) null, (DouHuM) null);
        // act
        shelfGroupM.addDouHu(douHuList);
        // assert
        assertTrue(shelfGroupM.getDouHus().isEmpty());
    }

    /**
     * 测试 addDouHu 方法，当输入的 douHuList 不为空，其中的 DouHuM 对象也不为 null，但是 douHus 列表中包含相同的 expId 时
     */
    @Test
    public void testAddDouHuWithSameExpId() throws Throwable {
        // arrange
        DouHuM douHuM1 = Mockito.mock(DouHuM.class);
        Mockito.when(douHuM1.getExpId()).thenReturn("expId1");
        DouHuM douHuM2 = Mockito.mock(DouHuM.class);
        Mockito.when(douHuM2.getExpId()).thenReturn("expId1");
        // act
        shelfGroupM.addDouHu(Arrays.asList(douHuM1, douHuM2));
        // assert
        assertEquals(1, shelfGroupM.getDouHus().size());
    }

    /**
     * 测试 addDouHu 方法，当输入的 douList 不为空，其中的 DouHuM 对象也不为 null，且 douHus 列表中不包含相同的 expId 时
     */
    @Test
    public void testAddDouHuWithDifferentExpId() throws Throwable {
        // arrange
        DouHuM douHuM1 = Mockito.mock(DouHuM.class);
        Mockito.when(douHuM1.getExpId()).thenReturn("expId1");
        DouHuM douHuM2 = Mockito.mock(DouHuM.class);
        Mockito.when(douHuM2.getExpId()).thenReturn("expId2");
        // act
        shelfGroupM.addDouHu(Arrays.asList(douHuM1, douHuM2));
        // assert
        assertEquals(2, shelfGroupM.getDouHus().size());
    }
}
