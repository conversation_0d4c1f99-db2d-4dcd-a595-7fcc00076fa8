package com.sankuai.dzviewscene.shelf.platform.list;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.framework.monitor.ActivityWaterLevelMonitor;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.list.ability.builder.ProductListBuilder;
import com.sankuai.dzviewscene.shelf.platform.list.vo.ProductListVO;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

public class ProductListActivityTest {

    private ProductListActivity productListActivity;

    private ActivityWaterLevelMonitor activityWaterLevelMonitor;

    private ComponentFinder componentFinder;

    @Before
    public void setUp() throws Exception {
        productListActivity = new ProductListActivity();
        activityWaterLevelMonitor = mock(ActivityWaterLevelMonitor.class);
        componentFinder = mock(ComponentFinder.class);
        // Using reflection to inject the mock ComponentFinder into the ProductListActivity
        Field componentFinderField = AbstractActivity.class.getDeclaredField("componentFinder");
        componentFinderField.setAccessible(true);
        componentFinderField.set(productListActivity, componentFinder);
        // Injecting the mock ActivityWaterLevelMonitor into the ProductListActivity
        Field activityWaterLevelMonitorField = ProductListActivity.class.getDeclaredField("activityWaterLevelMonitor");
        activityWaterLevelMonitorField.setAccessible(true);
        activityWaterLevelMonitorField.set(productListActivity, activityWaterLevelMonitor);
    }

    /**
     * Tests the execute method under normal conditions.
     */
    @Test
    public void testExecuteNormal() throws Throwable {
        // Arrange
        ActivityContext ctx = mock(ActivityContext.class);
        QueryFetcher queryFetcher = mock(QueryFetcher.class);
        PaddingFetcher paddingFetcher = mock(PaddingFetcher.class);
        ProductListBuilder productListBuilder = mock(ProductListBuilder.class);
        CompletableFuture<Map<String, ProductGroupM>> futureMap = new CompletableFuture<>();
        futureMap.complete(new HashMap<>());
        CompletableFuture<ProductListVO> productListFuture = CompletableFuture.completedFuture(new ProductListVO());
        // Mocking the behavior of findAbility to return a non-null value
        when(componentFinder.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(queryFetcher);
        when(componentFinder.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE)).thenReturn(paddingFetcher);
        when(componentFinder.findAbility(ctx, ProductListBuilder.ABILITY_PRODUCT_LIST_CODE)).thenReturn(productListBuilder);
        when(queryFetcher.build(ctx)).thenReturn(futureMap);
        when(paddingFetcher.build(ctx)).thenReturn(futureMap);
        when(productListBuilder.build(ctx)).thenReturn(productListFuture);
        when(activityWaterLevelMonitor.doMonitor(ctx, productListFuture)).thenReturn(productListFuture);
        // Act
        CompletableFuture<ProductListVO> result = productListActivity.execute(ctx);
        // Assert
        assertNotNull(result);
        verify(activityWaterLevelMonitor, times(1)).doMonitor(any(ActivityContext.class), any(CompletableFuture.class));
    }

    /**
     * Tests the execute method under exception conditions.
     */
    @Test(expected = Exception.class)
    public void testExecuteException() throws Throwable {
        // Arrange
        ActivityContext ctx = mock(ActivityContext.class);
        // Simulate exception in the execution flow by throwing an exception when findAbility is called
        when(componentFinder.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenThrow(new Exception());
        // Act
        productListActivity.execute(ctx);
        // Assert
        // Exception is expected
    }
}
