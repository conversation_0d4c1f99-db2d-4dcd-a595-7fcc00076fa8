package com.sankuai.dzviewscene.shelf.platform.common.query.ranking;

import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.MultiplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.SimplexQuery;
import com.sankuai.dzviewscene.shelf.platform.common.data.ProductMsMock;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.hamcrest.Matchers;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by float.lu on 2020/9/17.
 */
public class MultiplexQueryUnitTest {

    private static QueryContext queryContext = new QueryContext();

    @Before
    public void setUp() throws Exception {
        queryContext.setPool(ProductMsMock.mockProductGroups());
    }

    //@Test
    @DisplayName("测试2组召回策略叠加, 第一组召回第二个和第四个商品, 第二组剩下池子召回第一个商品, 一共召回三个商品")
    public void test_two_simplex_query_output_product2_and_product4() throws Exception {
        List<ProductM> products = new MultiplexQuery() {
            @Override
            public List<SimplexQuery> loadQuery() {
                return new ArrayList<SimplexQuery>(){{
                    // 第一组策略, 召回第二个和第四个商品
                    add(new SimplexQuery() {
                        @Override
                        public boolean match(QueryContext context, ProductM product) {
                            return product.getProductId() == 2 || product.getProductId() == 4;
                        }
                    });
                    // 第二组策略, 剩下商品池中召回第一个商品
                    add(new SimplexQuery() {
                        @Override
                        public int topN(QueryContext context) {
                            return 1;
                        }
                    });
                }};
            }
        }.query(queryContext);
        Assert.assertThat(products, Matchers.hasSize(3)); // 一共召回三个商品
        Assert.assertThat(products.get(0).getProductId(), Matchers.equalTo(2)); // 第一个商品ID=2
        Assert.assertThat(products.get(1).getProductId(), Matchers.equalTo(4)); // 第二个商品ID为4
        Assert.assertThat(products.get(2).getProductId(), Matchers.equalTo(1)); // 第三个产品ID为1
        Assert.assertThat(queryContext.getMatched(), Matchers.hasSize(3)); // 一共召回三个商品
        Assert.assertThat(queryContext.getPool(), Matchers.hasSize(9));//剩下还有11个商品
    }

   // @Test
    @DisplayName("测试召回的时候传入负值被当做0处理")
    public void test_query_with_negative_topN() throws Exception {
        List<ProductM> products = new MultiplexQuery() {
            @Override
            public int topN(QueryContext context) {
                return -3;
            }

            @Override
            public List<SimplexQuery> loadQuery() {
                return new ArrayList<SimplexQuery>(){{
                    add(new SimplexQuery() {
                        @Override
                        public boolean match(QueryContext context, ProductM product) {
                            return true;
                        }
                    });
                }};

            }
        }.query(queryContext);
        Assert.assertThat(products, Matchers.hasSize(0));
    }
}
