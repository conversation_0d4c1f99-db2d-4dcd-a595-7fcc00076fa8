package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NullMainTitleBuilderExt_TagsTest {

    /**
     * 测试 tags 方法是否返回 null
     */
    @Test
    public void testTagsReturnNull() throws Throwable {
        // arrange
        NullMainTitleBuilderExt nullMainTitleBuilderExt = new NullMainTitleBuilderExt();
        ActivityContext activityContext = new ActivityContext();
        // act
        List<IconRichLabelVO> result = nullMainTitleBuilderExt.tags(activityContext);
        // assert
        assertNull(result);
    }
}
