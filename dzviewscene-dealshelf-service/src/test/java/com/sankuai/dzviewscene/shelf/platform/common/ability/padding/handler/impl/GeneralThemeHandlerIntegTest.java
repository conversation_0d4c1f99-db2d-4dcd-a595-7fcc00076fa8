package com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.shelf.platform.common.ability.padding.handler.impl","com.sankuai.dzviewscene.nr.atom"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class GeneralThemeHandlerIntegTest {

    private static final Logger logger = LoggerFactory.getLogger(GeneralThemeHandlerIntegTest.class);

    @Resource
    private GeneralProductThemeHandler generalProductThemeHandler;

    static {
        TestBeanFactory.registerBean("shopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-core.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
    }

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_online() {
        ActivityContext activityContext = buildContext();
        ProductGroupM productGroupM = buildProductGroupM();
        Map<String, Object> params = buildParams();
        ProductGroupM result = generalProductThemeHandler.padding(activityContext, productGroupM, params).join();
        logger.info("结果为：" + JsonCodec.encodeWithUTF8(result));
    }

    private Map<String, Object> buildParams() {
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.platform, 1);
        params.put(ShelfActivityConstants.Params.dpUserId, 0L);
        params.put(ShelfActivityConstants.Params.dpCityId, 1);

        params.put(ShelfActivityConstants.Params.deviceId, "sdfwrjhfqiuyjhjkahsdad");
        params.put(ShelfActivityConstants.Params.userAgent, 101);
        params.put(ShelfActivityConstants.Params.lat, 36.2);
        params.put(ShelfActivityConstants.Params.lng, 121.1);
        params.put(PaddingFetcher.Params.planId, "10100031");
        params.put(PaddingFetcher.Params.promoProductType, 4011);
        params.put(PaddingFetcher.Params.showTimeInterval, 30);
        params.put(PaddingFetcher.Params.statisticSaleBizType, 8);
        params.put(PaddingFetcher.Params.promoTemplateId, 0);
        params.put(PaddingFetcher.Params.productAttributeKeys, com.google.common.collect.Lists.newArrayList("bookingInfo","classCat2",
                "classChangeExplain","classDuration","classFeatureJsonArr","classHighlights","classNum",
                "featureTag","learningObjective","otherInfo","suitTargets","teacherNationality"));
        params.put(PaddingFetcher.Params.itemAttributeKeys, com.google.common.collect.Lists.newArrayList("classContentJson","classLaw",
                "classTotalHours"));
        return params;
    }

    private ProductGroupM buildProductGroupM() {
        ProductGroupM productGroupM = new ProductGroupM();
        List<ProductM> products = new ArrayList<>();
        products.add(getProductM(659232098));
        productGroupM.setProducts(products);
        return productGroupM;
    }

    private ProductM getProductM(int id) {
        ProductM productM = new ProductM();
        productM.setProductId(id);
        return productM;
    }

    private ActivityContext buildContext() {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.platform, 1);
        activityContext.addParam(ShelfActivityConstants.Params.dpCityId, 1);
        activityContext.addParam(ShelfActivityConstants.Params.mtCityId, 10);
        activityContext.addParam(ShelfActivityConstants.Params.dpUserId, 0L);
        activityContext.addParam(ShelfActivityConstants.Params.mtUserId, 0L);
        activityContext.addParam(ShelfActivityConstants.Params.dpPoiId, 1591904784);
        activityContext.addParam(ShelfActivityConstants.Params.mtPoiId, 0);
        activityContext.addParam(ShelfActivityConstants.Params.deviceId, "");
        activityContext.addParam(ShelfActivityConstants.Params.appVersion, "10.10.3");
        activityContext.addParam(ShelfActivityConstants.Params.clientType, 101);
        return activityContext;
    }
}
