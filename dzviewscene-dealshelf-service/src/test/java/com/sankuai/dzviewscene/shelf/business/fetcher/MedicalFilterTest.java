package com.sankuai.dzviewscene.shelf.business.fetcher;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.ranking.QueryContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

public class MedicalFilterTest {

    //@Test
    public void test_discount() {
        ProductM productM = new ProductM();
        productM.setProductId(11);
        productM.setBasePrice(new BigDecimal("11.23"));
        ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
        productPromoPriceM.setPromoPrice(new BigDecimal("3.12"));
        productM.setPromoPrices(Lists.newArrayList(productPromoPriceM));
        BigDecimal bigDecimal = getDiscount(productM);
    }



    //@Test
    public void test_sort() {
        List<ProductM> productMList = Lists.newArrayList();

        ProductM productM1 = new ProductM();
        productM1.setProductId(1);
        ProductSaleM productSaleM1 = new ProductSaleM();
        productSaleM1.setSale(9);
        productM1.setSale(productSaleM1);

        ProductPromoPriceM productPromoPriceM1 = new ProductPromoPriceM();
        productPromoPriceM1.setPromoPrice(new BigDecimal("10.0"));
        productM1.setBasePrice(new BigDecimal("100"));
        productM1.setPromoPrices(Lists.newArrayList(productPromoPriceM1));


        productMList.add(productM1);

        ProductM productM2 = new ProductM();
        productM2.setProductId(2);
        ProductSaleM productSaleM2 = new ProductSaleM();
        productSaleM2.setSale(10);
        productM2.setSale(productSaleM2);

        productM2.setAttr("shopRecommendAttr", "true");

        productM2.setBasePrice(new BigDecimal("100"));
        ProductPromoPriceM productPromoPriceM2 = new ProductPromoPriceM();
        productPromoPriceM2.setPromoPrice(new BigDecimal("30.0"));

        productM2.setPromoPrices(Lists.newArrayList(productPromoPriceM2));
        productMList.add(productM2);

        ProductM productM3 = new ProductM();
        productM3.setProductId(3);
        ProductSaleM productSaleM3 = new ProductSaleM();
        productSaleM3.setSale(9);
        productM3.setSale(productSaleM3);
        productM3.setAttr("shopRecommendAttr", "false");

        ProductPromoPriceM productPromoPriceM3 = new ProductPromoPriceM();
        productPromoPriceM3.setPromoPrice(new BigDecimal("40.0"));
        productM3.setBasePrice(new BigDecimal("100"));
        productM3.setPromoPrices(Lists.newArrayList(productPromoPriceM3));
        productMList.add(productM3);

//        List<ProductM> result = productMList.stream().sorted(comparator()).collect(Collectors.toList());

//        List<ProductM> res = sortSecKillProductsByInputScoreTop(productMList);

        List<ProductM> re = sortBySale(productMList);

    }

    public static List<ProductM> sortBySale(List<ProductM> productMList) {
        return productMList.stream().sorted(
                        Comparator.comparingInt(product -> getSaleSafely((ProductM) product)).reversed()
                                .thenComparing(product -> getDiscount((ProductM) product))
                )
                .collect(Collectors.toList());
    }

    public static int getSaleSafely(ProductM productM) {
        if(productM == null || productM.getSale() == null) {
            return 0;
        }
        return productM.getSale().getSale();
    }

    public static Comparator<ProductM> comparator() {
        List<Comparator> comparators = new LinkedList<>();
        comparators.add(recommendComparator());
        comparators.add(saleDescComparator());
        comparators.add(discountAscComparator());

        return ((o1, o2) -> {
            for (Comparator comparator : comparators) {
                if (comparator.compare(o1, o2) != 0) {
                    return comparator.compare(o1, o2);
                }
            }
            return 0;
        });
    }

    private static Comparator<ProductM> recommendComparator() {
        return ((o1, o2) -> {
            boolean attr1 = trueTag(o1.getAttr("shopRecommendAttr"));
            boolean attr2 = trueTag(o2.getAttr("shopRecommendAttr"));
            if(attr1 ^ attr2) {
                return attr1 ? -1 : 1;
            }
            return 0;
        });
    }

    private static Comparator<ProductM> saleDescComparator() {
        return ((o1, o2) -> {
            if(o1.getSale() == null && o2.getSale() == null) {
                return 0;
            }
            if (o1.getSale() == null) {
                return 1;
            }
            if (o2.getSale() == null) {
                return -1;
            }
            return o2.getSale().getSale() - o1.getSale().getSale();
        });
    }

    private static Comparator<ProductM> discountAscComparator() {
        return ((o1, o2) -> {
            BigDecimal discount1 = getDiscount(o1);
            BigDecimal discount2 = getDiscount(o2);
            return discount1.compareTo(discount2);
        });
    }

    public static List<ProductM> sortSecKillProductsByInputScoreTop(List<ProductM> productMS) {
        //商家推荐
        Collections.sort(productMS, (o1, o2) -> {
            return recommendTagByTrue(o1, o2);
        });
        //销量
        Collections.sort(productMS, (o1, o2) -> {
            return saleByDesc(o1, o2);
        });
////        //优惠力度
//        Collections.sort(productMS, (o1, o2) -> {
//            return discountCompare(o1, o2);
//        });
        return productMS;
    }

    private static int saleByDesc(ProductM o1, ProductM o2) {
        if(o1.getSale() == null && o2.getSale() == null) {
            return 0;
        }
        if (o1.getSale() == null) {
            return 1;
        }
        if (o2.getSale() == null) {
            return -1;
        }
        return o2.getSale().getSale() - o1.getSale().getSale();
    }


    private static int recommendTagByTrue(ProductM o1, ProductM o2) {
        if(trueTag(o1.getAttr("shopRecommendAttr")) ^ trueTag(o2.getAttr("shopRecommendAttr"))) {
            return trueTag(o1.getAttr("shopRecommendAttr")) ? -1 : 1;
        }
        return 0;
    }

    private static boolean trueTag(String attr) {
        return StringUtils.isNotEmpty(attr) && "true".equals(attr);
    }

    private static int discountCompare(ProductM o1, ProductM o2) {
        return getDiscount(o1).compareTo(getDiscount(o2));
    }

    private static BigDecimal getDiscount(ProductM productM) {
        ProductPromoPriceM productPromoPriceM = productM.getPromoPrice(0);
        if(productPromoPriceM == null || productPromoPriceM.getPromoPrice() == null || productM.getBasePrice() == null) {
            return new BigDecimal("1");
        }
        return productPromoPriceM.getPromoPrice().divide(productM.getBasePrice(), 2);
    }

}
