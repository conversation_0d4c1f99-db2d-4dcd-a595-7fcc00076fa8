package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.idmapper.api.dto.IdMapper;
import com.dianping.general.unified.search.api.generalsearchv2.dto.DealGroupDto;
import com.dianping.general.unified.search.api.generalsearchv2.request.DealGroupSearchRequest;
import com.dianping.general.unified.search.api.generalsearchv2.response.DealGroupSearchResponse;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealGroupSearchHandlerTest {

    @InjectMocks
    private DealGroupSearchHandler dealGroupSearchHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    private ActivityContext activityContext;

    private String groupName;

    private Map<String, Object> params;

    @Before
    public void setUp() {
        activityContext = createDefaultActivityContext();
        groupName = createDefaultGroupName();
        params = createDefaultParams();
    }

    private ActivityContext createDefaultActivityContext() {
        return new ActivityContext();
    }

    private String createDefaultGroupName() {
        return "testGroup";
    }

    private Map<String, Object> createDefaultParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("platform", 1);
        params.put("dpPoiId", 123L);
        params.put("searchterm", "testSearch");
        params.put("pageSize", 10);
        params.put("pageNo", 1);
        return params;
    }

    private CompletableFuture<DealGroupSearchResponse<DealGroupDto>> createSuccessfulFuture() {
        DealGroupSearchResponse<DealGroupDto> response = new DealGroupSearchResponse<>();
        DealGroupDto dealGroupDto = new DealGroupDto();
        dealGroupDto.setDealGroupId(123L);
        response.setSuccess(true);
        response.setResult(Collections.singletonList(dealGroupDto));
        return CompletableFuture.completedFuture(response);
    }

    private CompletableFuture<DealGroupSearchResponse<DealGroupDto>> createFailedFuture() {
        CompletableFuture<DealGroupSearchResponse<DealGroupDto>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Test Exception"));
        return failedFuture;
    }

    @Test
    public void testQueryNormal() throws Throwable {
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(createSuccessfulFuture());
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, groupName, params);
        assertNotNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testQueryActivityContextNull() throws Throwable {
        dealGroupSearchHandler.query(null, groupName, params);
    }

    @Test(expected = NullPointerException.class)
    public void testQueryGroupNameNull() throws Throwable {
        dealGroupSearchHandler.query(activityContext, null, params);
    }

    @Test(expected = NullPointerException.class)
    public void testQueryParamsNull() throws Throwable {
        dealGroupSearchHandler.query(activityContext, groupName, null);
    }

    @Test
    public void testQuerySearchResponseFail() throws Throwable {
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(createFailedFuture());
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, groupName, params);
        assertNotNull(result);
    }

    @Test
    public void testQueryNotMT() throws Throwable {
        // Modify platform to simulate the non-MT platform scenario
        params.put("platform", 2);
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(createSuccessfulFuture());
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, groupName, params);
        assertNotNull(result);
    }

    @Test
    public void testQuery_WhenPlatformIsMT_ShouldReturnProductGroup() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.platform, 2);
        activityContext.setParameters(params);
        DealGroupSearchResponse<DealGroupDto> searchResponse = new DealGroupSearchResponse<>();
        searchResponse.setSuccess(true);
        List<DealGroupDto> dealGroupDtos = new ArrayList<>();
        DealGroupDto dto = new DealGroupDto();
        dto.setDealGroupId(1L);
        dealGroupDtos.add(dto);
        searchResponse.setResult(dealGroupDtos);
        List<IdMapper> idMappers = new ArrayList<>();
        IdMapper idMapper = new IdMapper(100, 1, 200);
        idMappers.add(idMapper);
        CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchFuture = CompletableFuture.completedFuture(searchResponse);
        CompletableFuture<List<IdMapper>> idMapperFuture = CompletableFuture.completedFuture(idMappers);
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(searchFuture);
        when(compositeAtomService.batchGetDealIdByDpId(any())).thenReturn(idMapperFuture);
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, "test", params);
        ProductGroupM productGroupM = result.get();
        assertNotNull(productGroupM);
        assertEquals(1, productGroupM.getTotal());
        assertFalse(productGroupM.isHasNext());
        assertEquals(1, productGroupM.getProducts().size());
        assertEquals(100, productGroupM.getProducts().get(0).getProductId());
        verify(compositeAtomService).searchDealGroupBySearchTerm(any());
        verify(compositeAtomService).batchGetDealIdByDpId(any());
    }

    @Test
    public void testQuery_WhenPlatformIsMTAndEmptySearchResponse_ShouldReturnEmptyGroup() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.platform, 2);
        activityContext.setParameters(params);
        DealGroupSearchResponse<DealGroupDto> searchResponse = new DealGroupSearchResponse<>();
        searchResponse.setSuccess(true);
        searchResponse.setResult(new ArrayList<>());
        CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchFuture = CompletableFuture.completedFuture(searchResponse);
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(searchFuture);
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, "test", params);
        ProductGroupM productGroupM = result.get();
        assertNull(productGroupM);
        verify(compositeAtomService).searchDealGroupBySearchTerm(any());
        verify(compositeAtomService, never()).batchGetDealIdByDpId(any());
    }

    @Test
    public void testQuery_WhenPlatformIsMTAndEmptyIdMapping_ShouldReturnEmptyGroup() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.platform, 2);
        activityContext.setParameters(params);
        DealGroupSearchResponse<DealGroupDto> searchResponse = new DealGroupSearchResponse<>();
        searchResponse.setSuccess(true);
        List<DealGroupDto> dealGroupDtos = new ArrayList<>();
        DealGroupDto dto = new DealGroupDto();
        dto.setDealGroupId(1L);
        dealGroupDtos.add(dto);
        searchResponse.setResult(dealGroupDtos);
        List<IdMapper> emptyIdMappers = new ArrayList<>();
        CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchFuture = CompletableFuture.completedFuture(searchResponse);
        CompletableFuture<List<IdMapper>> idMapperFuture = CompletableFuture.completedFuture(emptyIdMappers);
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(searchFuture);
        when(compositeAtomService.batchGetDealIdByDpId(any())).thenReturn(idMapperFuture);
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, "test", params);
        ProductGroupM productGroupM = result.get();
        // Adjusted assertion to reflect expected behavior
        assertNull(productGroupM);
        verify(compositeAtomService).searchDealGroupBySearchTerm(any());
        verify(compositeAtomService).batchGetDealIdByDpId(any());
    }

    @Test
    public void testQuery_WhenPlatformIsMTAndSearchFails_ShouldReturnNull() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.platform, 2);
        activityContext.setParameters(params);
        DealGroupSearchResponse<DealGroupDto> searchResponse = new DealGroupSearchResponse<>();
        searchResponse.setSuccess(false);
        CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchFuture = CompletableFuture.completedFuture(searchResponse);
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(searchFuture);
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, "test", params);
        ProductGroupM productGroupM = result.get();
        assertNull(productGroupM);
        verify(compositeAtomService).searchDealGroupBySearchTerm(any());
        verify(compositeAtomService, never()).batchGetDealIdByDpId(any());
    }

    @Test
    public void testQuery_WhenPlatformIsMTWithBatchProcessing_ShouldHandleMultipleDeals() throws Throwable {
        ActivityContext activityContext = new ActivityContext();
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.platform, 2);
        activityContext.setParameters(params);
        List<DealGroupDto> dealGroupDtos = new ArrayList<>();
        for (int i = 1; i <= 150; i++) {
            DealGroupDto dto = new DealGroupDto();
            dto.setDealGroupId((long) i);
            dealGroupDtos.add(dto);
        }
        DealGroupSearchResponse<DealGroupDto> searchResponse = new DealGroupSearchResponse<>();
        searchResponse.setSuccess(true);
        searchResponse.setResult(dealGroupDtos);
        List<IdMapper> firstBatchMappers = new ArrayList<>();
        List<IdMapper> secondBatchMappers = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            firstBatchMappers.add(new IdMapper(i + 1000, i, i + 2000));
        }
        for (int i = 101; i <= 150; i++) {
            secondBatchMappers.add(new IdMapper(i + 1000, i, i + 2000));
        }
        CompletableFuture<DealGroupSearchResponse<DealGroupDto>> searchFuture = CompletableFuture.completedFuture(searchResponse);
        when(compositeAtomService.searchDealGroupBySearchTerm(any())).thenReturn(searchFuture);
        when(compositeAtomService.batchGetDealIdByDpId(anyList())).thenReturn(CompletableFuture.completedFuture(firstBatchMappers)).thenReturn(CompletableFuture.completedFuture(secondBatchMappers));
        CompletableFuture<ProductGroupM> result = dealGroupSearchHandler.query(activityContext, "test", params);
        ProductGroupM productGroupM = result.get();
        assertNotNull(productGroupM);
        assertEquals(150, productGroupM.getTotal());
        assertFalse(productGroupM.isHasNext());
        assertEquals(150, productGroupM.getProducts().size());
        verify(compositeAtomService).searchDealGroupBySearchTerm(any());
        verify(compositeAtomService, times(2)).batchGetDealIdByDpId(anyList());
    }
}
