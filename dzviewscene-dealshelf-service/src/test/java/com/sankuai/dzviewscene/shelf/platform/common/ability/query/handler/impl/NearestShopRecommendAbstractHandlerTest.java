package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NearestShopRecommendAbstractHandlerTest {

    @InjectMocks
    private NearestShopRecommendAbstractHandler handler = new NearestShopRecommendAbstractHandler() {

        @Override
        protected void addNearestShopForProduct(ActivityContext context, Integer bizId, List<ProductM> products) {
            // Do nothing for test
        }
    };

    /**
     * Test when products list is null
     */
    @Test
    public void testQueryProduct2ShopIdMap_NullProducts() {
        // arrange
        ActivityContext context = new ActivityContext();
        Integer bizId = 1;
        List<ProductM> products = null;
        // act
        Map<Long, Long> result = handler.queryProduct2ShopIdMap(context, bizId, products);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when products list is empty
     */
    @Test
    public void testQueryProduct2ShopIdMap_EmptyProducts() {
        // arrange
        ActivityContext context = new ActivityContext();
        Integer bizId = 1;
        List<ProductM> products = Lists.newArrayList();
        // act
        Map<Long, Long> result = handler.queryProduct2ShopIdMap(context, bizId, products);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when bizId is null
     */
    @Test
    public void testQueryProduct2ShopIdMap_NullBizId() {
        // arrange
        ActivityContext context = new ActivityContext();
        Integer bizId = null;
        List<ProductM> products = Lists.newArrayList(new ProductM());
        // act
        Map<Long, Long> result = handler.queryProduct2ShopIdMap(context, bizId, products);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when bizId is zero
     */
    @Test
    public void testQueryProduct2ShopIdMap_ZeroBizId() {
        // arrange
        ActivityContext context = new ActivityContext();
        Integer bizId = 0;
        List<ProductM> products = Lists.newArrayList(new ProductM());
        // act
        Map<Long, Long> result = handler.queryProduct2ShopIdMap(context, bizId, products);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when bizId is negative
     */
    @Test
    public void testQueryProduct2ShopIdMap_NegativeBizId() {
        // arrange
        ActivityContext context = new ActivityContext();
        Integer bizId = -1;
        List<ProductM> products = Lists.newArrayList(new ProductM());
        // act
        Map<Long, Long> result = handler.queryProduct2ShopIdMap(context, bizId, products);
        // assert
        assertTrue(result.isEmpty());
    }
}
