package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.vu.enums.DzItemShowTypeEnums;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractDefaultFloorsBuilderExtItemShowTypeTest {

    @Mock
    private ActivityContext activityContext;

    @Mock
    private ShopM shopM;

    private AbstractDefaultFloorsBuilderExt abstractDefaultFloorsBuilderExt;

    @Before
    public void setUp() throws Exception {
        abstractDefaultFloorsBuilderExt = new AbstractDefaultFloorsBuilderExt() {
        };
        // Use reflection to set the private itemShowTypeConfig field
        Field itemShowTypeConfigField = AbstractDefaultFloorsBuilderExt.class.getDeclaredField("itemShowTypeConfig");
        itemShowTypeConfigField.setAccessible(true);
        Object itemShowTypeConfig = createItemShowTypeConfig();
        itemShowTypeConfigField.set(abstractDefaultFloorsBuilderExt, itemShowTypeConfig);
    }

    private Object createItemShowTypeConfig() throws Exception {
        Class<?> itemShowTypeConfigClass = Class.forName("com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.AbstractDefaultFloorsBuilderExt$ItemShowTypeConfig");
        Object itemShowTypeConfig = itemShowTypeConfigClass.getDeclaredConstructor().newInstance();
        Field shopCategory2ShowTypeField = itemShowTypeConfigClass.getDeclaredField("shopCategory2ShowType");
        shopCategory2ShowTypeField.setAccessible(true);
        shopCategory2ShowTypeField.set(itemShowTypeConfig, new HashMap<>());
        Field sceneCode2ShowTypeField = itemShowTypeConfigClass.getDeclaredField("sceneCode2ShowType");
        sceneCode2ShowTypeField.setAccessible(true);
        sceneCode2ShowTypeField.set(itemShowTypeConfig, new HashMap<>());
        return itemShowTypeConfig;
    }

    /**
     * 场景1：showDefaultShowType 返回 true，直接返回 DzItemShowTypeEnums.DEFAULT.getType()
     */
    @Test
    public void testItemShowType_ShowDefaultShowTypeTrue() throws Throwable {
        // arrange
        when(activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(null);
        // act
        int result = abstractDefaultFloorsBuilderExt.itemShowType(activityContext, "groupName");
        // assert
        assertEquals(DzItemShowTypeEnums.DEFAULT.getType(), result);
    }

    /**
     * 场景2：showDefaultShowType 返回 false，ShopM 为 null，返回 DzItemShowTypeEnums.DEFAULT.getType()
     */
    @Test
    public void testItemShowType_ShowDefaultShowTypeFalseAndShopMNull() throws Throwable {
        // arrange
        when(activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(null);
        // act
        int result = abstractDefaultFloorsBuilderExt.itemShowType(activityContext, "groupName");
        // assert
        assertEquals(DzItemShowTypeEnums.DEFAULT.getType(), result);
    }

    /**
     * 场景5：showDefaultShowType 返回 false，ShopM 不为 null，itemShowTypeConfig.getShopCategory2ShowType() 不包含 category，itemShowTypeConfig.getSceneCode2ShowType() 不包含 sceneCode，返回 DzItemShowTypeEnums.DEFAULT.getType()
     */
    @Test
    public void testItemShowType_ShowDefaultShowTypeFalseAndShopCategory2ShowTypeAndSceneCode2ShowTypeNotContainsCategoryAndSceneCode() throws Throwable {
        // arrange
        when(activityContext.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);
        // Use reflection to set the shopCategory2ShowType and sceneCode2ShowType maps
        Field itemShowTypeConfigField = AbstractDefaultFloorsBuilderExt.class.getDeclaredField("itemShowTypeConfig");
        itemShowTypeConfigField.setAccessible(true);
        Object itemShowTypeConfig = itemShowTypeConfigField.get(abstractDefaultFloorsBuilderExt);
        Field shopCategory2ShowTypeField = itemShowTypeConfig.getClass().getDeclaredField("shopCategory2ShowType");
        shopCategory2ShowTypeField.setAccessible(true);
        Map<Integer, Integer> shopCategory2ShowType = new HashMap<>();
        shopCategory2ShowTypeField.set(itemShowTypeConfig, shopCategory2ShowType);
        Field sceneCode2ShowTypeField = itemShowTypeConfig.getClass().getDeclaredField("sceneCode2ShowType");
        sceneCode2ShowTypeField.setAccessible(true);
        Map<String, Integer> sceneCode2ShowType = new HashMap<>();
        sceneCode2ShowTypeField.set(itemShowTypeConfig, sceneCode2ShowType);
        // act
        int result = abstractDefaultFloorsBuilderExt.itemShowType(activityContext, "groupName");
        // assert
        assertEquals(DzItemShowTypeEnums.DEFAULT.getType(), result);
    }
}
