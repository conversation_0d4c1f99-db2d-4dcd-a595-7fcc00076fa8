package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.builder.productlist;

import static org.junit.Assert.assertNull;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductListBuilderExtAdapter_MoreJumpUrlTest {

    @Mock
    private ActivityContext activityContext;

    /**
     * 测试 moreJumpUrl 方法是否总是返回 null
     */
    @Test
    public void testMoreJumpUrlReturnNull() throws Throwable {
        // arrange
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {

            @Override
            public String moreJumpUrl(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = productListBuilderExtAdapter.moreJumpUrl(activityContext);
        // assert
        assertNull(result);
    }

    /**
     * 测试 moreJumpUrl 方法在输入为 null 时是否返回 null
     */
    @Test
    public void testMoreJumpUrlWithNullInput() throws Throwable {
        // arrange
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {

            @Override
            public String moreJumpUrl(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = productListBuilderExtAdapter.moreJumpUrl(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 moreJumpUrl 方法在输入为非 null 时是否返回 null
     */
    @Test
    public void testMoreJumpUrlWithNonNullInput() throws Throwable {
        // arrange
        ProductListBuilderExtAdapter productListBuilderExtAdapter = new ProductListBuilderExtAdapter() {

            @Override
            public String moreJumpUrl(ActivityContext activityContext) {
                return null;
            }
        };
        // act
        String result = productListBuilderExtAdapter.moreJumpUrl(activityContext);
        // assert
        assertNull(result);
    }
}
