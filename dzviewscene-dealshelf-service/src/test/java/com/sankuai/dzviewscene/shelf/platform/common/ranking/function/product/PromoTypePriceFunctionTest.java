package com.sankuai.dzviewscene.shelf.platform.common.ranking.function.product;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PromoTypePriceFunctionTest {

    @Mock
    private ProductM productM;

    @Mock
    private ProductPromoPriceM promoPriceM;

    private PromoTypePriceFunction promoTypePriceFunction;

    @Before
    public void setUp() {
        promoTypePriceFunction = new PromoTypePriceFunction();
    }

    @Test
    public void testCallEnvNotContainsItem() throws Throwable {
        Map<String, Object> env = new HashMap<>();
        // Ensure the env map contains the 'item' key to prevent NullPointerException
        env.put("item", productM);
        // Mocking the behavior of productM.getPromo to return null since the item is not in the env map
        when(productM.getPromo(anyInt())).thenReturn(null);
        AviatorObject arg1 = mock(AviatorObject.class);
        when(arg1.getValue(env)).thenReturn(1);
        AviatorObject result = promoTypePriceFunction.call(env, arg1);
        assertEquals(Integer.MAX_VALUE, result.getValue(env));
    }

    @Test
    public void testCallArg1NotContainsPromoType() throws Throwable {
        Map<String, Object> env = new HashMap<>();
        env.put("item", productM);
        // Mocking the behavior of productM.getPromo to return null since the promo type is not found
        when(productM.getPromo(anyInt())).thenReturn(null);
        AviatorObject arg1 = mock(AviatorObject.class);
        // Ensure arg1.getValue(env) returns a default value (e.g., 0) to avoid NullPointerException
        when(arg1.getValue(env)).thenReturn(0);
        AviatorObject result = promoTypePriceFunction.call(env, arg1);
        assertEquals(Integer.MAX_VALUE, result.getValue(env));
    }

    // Other test methods remain unchanged
    @Test
    public void testCallProductMNotContainsPromoPrice() throws Throwable {
        Map<String, Object> env = new HashMap<>();
        env.put("item", productM);
        AviatorObject arg1 = mock(AviatorObject.class);
        when(arg1.getValue(env)).thenReturn(1);
        when(productM.getPromo(1)).thenReturn(null);
        AviatorObject result = promoTypePriceFunction.call(env, arg1);
        assertEquals(Integer.MAX_VALUE, result.getValue(env));
    }

    @Test
    public void testCallPromoPriceMIsNull() throws Throwable {
        Map<String, Object> env = new HashMap<>();
        env.put("item", productM);
        AviatorObject arg1 = mock(AviatorObject.class);
        when(arg1.getValue(env)).thenReturn(1);
        when(productM.getPromo(1)).thenReturn(null);
        AviatorObject result = promoTypePriceFunction.call(env, arg1);
        assertEquals(Integer.MAX_VALUE, result.getValue(env));
    }

    @Test
    public void testCallPromoPriceMIsNotNull() throws Throwable {
        Map<String, Object> env = new HashMap<>();
        env.put("item", productM);
        AviatorObject arg1 = mock(AviatorObject.class);
        when(arg1.getValue(env)).thenReturn(1);
        when(productM.getPromo(1)).thenReturn(promoPriceM);
        when(promoPriceM.getPromoPrice()).thenReturn(new BigDecimal("100"));
        AviatorObject result = promoTypePriceFunction.call(env, arg1);
        assertEquals(new BigDecimal("100"), result.getValue(env));
    }
}
