package com.sankuai.dzviewscene.shelf.platform.detail.flow;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.core.IAbility;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductDetailFlowTest {

    @Mock
    private AbstractActivity<?> activity;

    @Mock
    private ActivityContext ctx;

    @Mock
    private IAbility<Object> ability;

    private ProductDetailFlow productDetailFlow;

    @Before
    public void setUp() {
        productDetailFlow = new ProductDetailFlow();
        when(activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(ability);
        when(activity.findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE)).thenReturn(ability);
    }

    /**
     * Tests the execute method under normal conditions.
     */
    @Test
    public void testExecuteNormal() throws Throwable {
        // arrange
        when(ability.build(ctx)).thenReturn(CompletableFuture.completedFuture(new Object()));
        // act
        CompletableFuture<Object> result = productDetailFlow.execute(activity, ctx);
        // assert
        assertNotNull(result);
        // Corrected the verify statement to match the actual number of invocations
        verify(activity, times(1)).findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE);
        verify(activity, times(1)).findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE);
        verify(ability, times(2)).build(ctx);
    }

    /**
     * Tests the execute method under exceptional conditions.
     */
    @Test(expected = RuntimeException.class)
    public void testExecuteException() throws Throwable {
        // arrange
        when(ability.build(ctx)).thenThrow(new RuntimeException());
        // act
        productDetailFlow.execute(activity, ctx);
        // assert
        verify(activity, times(1)).findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE);
        verify(activity, times(1)).findAbility(ctx, PaddingFetcher.ABILITY_PRODUCT_PADDING_CODE);
        verify(ability, times(2)).build(ctx);
    }
}
