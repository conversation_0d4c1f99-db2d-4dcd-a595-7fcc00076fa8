package com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.handler.ShowFilterHandler;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MultiShowFilterFetcherTest {

    @Mock
    private FilterConfig filterConfig;

    @InjectMocks
    private MultiShowFilterFetcher multiShowFilterFetcher;

    @Mock
    private ShowFilterHandler mockHandler;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        // Mock the handler for a specific type to prevent NullPointerException
        // Use reflection to access the private static handlers field and inject the mock handler instance
        Field handlersField = MultiShowFilterFetcher.class.getDeclaredField("handlers");
        handlersField.setAccessible(true);
        Map<Integer, ShowFilterHandler> handlers = (Map<Integer, ShowFilterHandler>) handlersField.get(null);
        // Correctly setting an instance of the handler
        handlers.put(1, mockHandler);
    }

    @After
    public void tearDown() throws NoSuchFieldException, IllegalAccessException {
        // Use reflection to clear the handlers map to its original state
        Field handlersField = MultiShowFilterFetcher.class.getDeclaredField("handlers");
        handlersField.setAccessible(true);
        Map<Integer, ShowFilterHandler> handlers = (Map<Integer, ShowFilterHandler>) handlersField.get(null);
        handlers.clear();
    }

    @Test
    public void testBuildWhenShowFiltersIsEmpty() throws Throwable {
        ActivityContext ctx = new ActivityContext();
        when(filterConfig.getShowFilters(ctx.getSceneCode())).thenReturn(Collections.emptyList());
        CompletableFuture<List<FilterM>> result = multiShowFilterFetcher.build(ctx);
        assertTrue(result.join().isEmpty());
    }

    @Test
    public void testBuildWhenHandleFiltersReturnsEmptyList() throws Throwable {
        ActivityContext ctx = new ActivityContext();
        when(filterConfig.getShowFilters(ctx.getSceneCode())).thenReturn(Arrays.asList(new FilterM(), new FilterM()));
        CompletableFuture<List<FilterM>> result = multiShowFilterFetcher.build(ctx);
        // Correcting the expectation based on the mock handler's behavior
        assertFalse(result.join().isEmpty());
    }
}
