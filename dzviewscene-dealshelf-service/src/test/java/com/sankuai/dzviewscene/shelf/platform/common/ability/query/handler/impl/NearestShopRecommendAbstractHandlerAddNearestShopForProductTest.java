package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import static org.mockito.Mockito.verify;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.Collections;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NearestShopRecommendAbstractHandlerAddNearestShopForProductTest {

    @InjectMocks
    private NearestShopRecommendAbstractHandler handler = new NearestShopRecommendAbstractHandler() {
    };

    @Mock
    private ActivityContext context;

    /**
     * Test early return when products list is empty
     */
    @Test
    public void testAddNearestShopForProduct_WhenProductsEmpty() {
        // arrange
        Integer bizId = 1;
        // act
        handler.addNearestShopForProduct(context, bizId, Collections.emptyList());
        // assert
        verify(context).addParam("dpPoiId", 0);
        verify(context).addParam("mtPoiId", 0);
        verify(context).addParam("dpPoiIdL", 0);
        verify(context).addParam("mtPoiIdL", 0);
    }

    /**
     * Test early return when bizId is null
     */
    @Test
    public void testAddNearestShopForProduct_WhenBizIdNull() {
        // arrange
        ArrayList<ProductM> products = Lists.newArrayList(new ProductM());
        // act
        handler.addNearestShopForProduct(context, null, products);
        // assert
        verify(context).addParam("dpPoiId", 0);
        verify(context).addParam("mtPoiId", 0);
        verify(context).addParam("dpPoiIdL", 0);
        verify(context).addParam("mtPoiIdL", 0);
    }

    /**
     * Test early return when bizId is less than or equal to 0
     */
    @Test
    public void testAddNearestShopForProduct_WhenBizIdInvalid() {
        // arrange
        ArrayList<ProductM> products = Lists.newArrayList(new ProductM());
        Integer bizId = 0;
        // act
        handler.addNearestShopForProduct(context, bizId, products);
        // assert
        verify(context).addParam("dpPoiId", 0);
        verify(context).addParam("mtPoiId", 0);
        verify(context).addParam("dpPoiIdL", 0);
        verify(context).addParam("mtPoiIdL", 0);
    }
}
