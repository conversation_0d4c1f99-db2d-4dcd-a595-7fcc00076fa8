package com.sankuai.dzviewscene.shelf.business.shelf.beauty.filter;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by float on 2020/9/13.
 */
public class ConfigMock {

    public static final FilterBtnM HOT_FILTER = buildFilterBtn(1L, "热门");
    public static final FilterBtnM ACTIVITY_FILTER = buildFilterBtn(2L, "大促");
    public static final FilterBtnM IDLE_PROMO_FILTER = buildFilterBtn(3L, "错峰X折");
    public static final FilterBtnM MEI_JIA_FILTER = buildFilterBtn(4L, "美甲");
    public static final FilterBtnM MEI_JIE_FILTER = buildFilterBtn(5L, "美睫");
    public static final FilterBtnM OTHER_FILTER = buildFilterBtn(6L, "其他");


    public static FilterM mockFilterM() {
        FilterM filterM = new FilterM();
        filterM.setFilters(Lists.newArrayList(HOT_FILTER, ACTIVITY_FILTER, IDLE_PROMO_FILTER, MEI_JIA_FILTER, MEI_JIE_FILTER, OTHER_FILTER));
        return filterM;
    }


    public static Map<String, List<String>> mockKeywords() {
        return new HashMap<>();
    }




    private static FilterBtnM buildFilterBtn(long filterId, String name) {
        FilterBtnM filterBtnM = new FilterBtnM();
        filterBtnM.setFilterId(filterId);
        filterBtnM.setTitle(name);
        return filterBtnM;
    }
}
