package com.sankuai.dzviewscene.shelf.platform.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ParamsUtilTest {

    private static final String PARAM_NAME = "testParam";

    private static final double DELTA = 0.0001;

    @Mock
    private ActivityContext ctx;

    /**
     * 测试 extractIdBySummarypids 方法，当 summaryStr 为空时
     */
    @Test
    public void testExtractIdBySummarypidsWhenSummaryStrIsNull() throws Throwable {
        // arrange
        String summaryStr = null;
        String key = "key";
        // act
        List<Integer> result = ParamsUtil.extractIdBySummarypids(summaryStr, key);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 extractIdBySummarypids 方法，当 summaryStr 不为空，但解码后的 maps 为空时
     */
    @Test
    public void testExtractIdBySummarypidsWhenMapsIsNull() throws Throwable {
        // arrange
        String summaryStr = "{}";
        String key = "key";
        // act
        List<Integer> result = ParamsUtil.extractIdBySummarypids(summaryStr, key);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 extractIdBySummarypids 方法，当 maps 不为空，但 key 对应的 pidStr 为空时
     */
    @Test
    public void testExtractIdBySummarypidsWhenPidStrIsNull() throws Throwable {
        // arrange
        String summaryStr = "{\"key\":\"\"}";
        String key = "key";
        // act
        List<Integer> result = ParamsUtil.extractIdBySummarypids(summaryStr, key);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 extractIdBySummarypids 方法，当 pidStr 不为空，但分割后的 pids 包含非数字字符串时
     */
    @Test
    public void testExtractIdBySummarypidsWhenPidsContainsNonNumericString() throws Throwable {
        // arrange
        String summaryStr = "{\"key\":\"1,2,a\"}";
        String key = "key";
        // act
        List<Integer> result = ParamsUtil.extractIdBySummarypids(summaryStr, key);
        // assert
        assertEquals(2, result.size());
        assertEquals(Integer.valueOf(1), result.get(0));
        assertEquals(Integer.valueOf(2), result.get(1));
    }

    /**
     * 测试 extractIdBySummarypids 方法，当 pids 包含数字字符串时
     */
    @Test
    public void testExtractIdBySummarypidsWhenPidsContainsNumericString() throws Throwable {
        // arrange
        String summaryStr = "{\"key\":\"1,2,3\"}";
        String key = "key";
        // act
        List<Integer> result = ParamsUtil.extractIdBySummarypids(summaryStr, key);
        // assert
        assertEquals(3, result.size());
        assertEquals(Integer.valueOf(1), result.get(0));
        assertEquals(Integer.valueOf(2), result.get(1));
        assertEquals(Integer.valueOf(3), result.get(2));
    }

    /**
     * Test case when extraMap is not empty and key exists
     */
    @Test
    public void testGetExtraParamFromMap_WhenKeyExists() {
        // arrange
        Map<String, Object> extParams = new HashMap<>();
        String extraJson = "{\"testKey\":\"testValue\"}";
        extParams.put(FilterListActivityConstants.Params.extra, extraJson);
        String defaultValue = "default";
        // act
        String result = ParamsUtil.getExtraParamFromMap(extParams, "testKey", defaultValue);
        // assert
        assertEquals("testValue", result);
    }

    /**
     * Test case when extraMap is not empty but key doesn't exist
     */
    @Test
    public void testGetExtraParamFromMap_WhenKeyNotExists() {
        // arrange
        Map<String, Object> extParams = new HashMap<>();
        String extraJson = "{\"anotherKey\":\"someValue\"}";
        extParams.put(FilterListActivityConstants.Params.extra, extraJson);
        String defaultValue = "default";
        // act
        String result = ParamsUtil.getExtraParamFromMap(extParams, "nonExistentKey", defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test case with different data type (Integer)
     */
    @Test
    public void testGetExtraParamFromMap_WithIntegerType() {
        // arrange
        Map<String, Object> extParams = new HashMap<>();
        String extraJson = "{\"numberKey\":42}";
        extParams.put(FilterListActivityConstants.Params.extra, extraJson);
        Integer defaultValue = 0;
        // act
        Integer result = ParamsUtil.getExtraParamFromMap(extParams, "numberKey", defaultValue);
        // assert
        assertEquals(Integer.valueOf(42), result);
    }

    /**
     * 测试getIntValue方法，当params为空的情况
     */
    @Test
    public void testGetIntValueParamsIsNull() throws Throwable {
        // arrange
        Map<String, Object> params = null;
        String name = "int";
        int defaultValue = -1;
        // act
        int result = ParamsUtil.getIntValue(params, name, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * 测试getIntValue方法，当params不为空，但name在params中不存在的情况
     */
    @Test
    public void testGetIntValueNameNotInParams() throws Throwable {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("float", 1.0);
        String name = "int";
        int defaultValue = -1;
        // act
        int result = ParamsUtil.getIntValue(params, name, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * 测试getIntValue方法，当params不为空，name在params中存在，但对应的值无法转换为int类型的情况
     */
    @Test
    public void testGetIntValueValueCannotBeConverted() throws Throwable {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("int", "abc");
        String name = "int";
        int defaultValue = -1;
        // act
        int result = ParamsUtil.getIntValue(params, name, defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * 测试getIntValue方法，当params不为空，name在params中存在，且对应的值可以转换为int类型的情况
     */
    @Test
    public void testGetIntValueValueCanBeConverted() throws Throwable {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("int", 1);
        String name = "int";
        int defaultValue = -1;
        // act
        int result = ParamsUtil.getIntValue(params, name, defaultValue);
        // assert
        assertEquals(1, result);
    }

    /**
     * Test when activityContext is null
     */
    @Test
    public void testGetDoubleSafely_NullActivityContext() {
        // arrange
        ActivityContext activityContext = null;
        // act
        double result = ParamsUtil.getDoubleSafely(activityContext, PARAM_NAME);
        // assert
        assertEquals(0, result, DELTA);
    }

    /**
     * Test when param value is a valid double string
     */
    @Test
    public void testGetDoubleSafely_ValidDoubleString() {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.getParam(PARAM_NAME)).thenReturn("123.45");
        // act
        double result = ParamsUtil.getDoubleSafely(activityContext, PARAM_NAME);
        // assert
        assertEquals(123.45, result, DELTA);
    }

    /**
     * Test when param value is null
     */
    @Test
    public void testGetDoubleSafely_NullParamValue() {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.getParam(PARAM_NAME)).thenReturn(null);
        // act
        double result = ParamsUtil.getDoubleSafely(activityContext, PARAM_NAME);
        // assert
        assertEquals(0, result, DELTA);
    }

    /**
     * Test when param value is an invalid double string
     */
    @Test
    public void testGetDoubleSafely_InvalidDoubleString() {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.getParam(PARAM_NAME)).thenReturn("invalid");
        // act
        double result = ParamsUtil.getDoubleSafely(activityContext, PARAM_NAME);
        // assert
        assertEquals(0, result, DELTA);
    }

    /**
     * Test getBooleanSafely when Map.get() throws exception
     * Should return false after catching exception
     */
    @Test
    public void testGetBooleanSafely_WhenMapGetThrowsException() {
        // arrange
        Map<String, Object> mockParams = Mockito.mock(Map.class);
        when(mockParams.isEmpty()).thenReturn(false);
        when(mockParams.get(anyString())).thenThrow(new RuntimeException("Test exception"));
        // act
        boolean result = ParamsUtil.getBooleanSafely(mockParams, "testKey");
        // assert
        assertFalse("Should return false when exception occurs", result);
    }

    /**
     * Test getBooleanSafely when value is non-Boolean type
     * Should return false for non-Boolean value
     */
    @Test
    public void testGetBooleanSafely_WhenValueIsNotBoolean() {
        // arrange
        Map<String, Object> mockParams = Mockito.mock(Map.class);
        when(mockParams.isEmpty()).thenReturn(false);
        // String instead of Boolean
        when(mockParams.get(anyString())).thenReturn("true");
        // act
        boolean result = ParamsUtil.getBooleanSafely(mockParams, "testKey");
        // assert
        assertFalse("Should return false for non-Boolean value", result);
    }

    /**
     * Test getBooleanSafely when value is null
     * Should return false for null value
     */
    @Test
    public void testGetBooleanSafely_WhenValueIsNull() {
        // arrange
        Map<String, Object> mockParams = Mockito.mock(Map.class);
        when(mockParams.isEmpty()).thenReturn(false);
        when(mockParams.get(anyString())).thenReturn(null);
        // act
        boolean result = ParamsUtil.getBooleanSafely(mockParams, "testKey");
        // assert
        assertFalse("Should return false when value is null", result);
    }

    /**
     * Test getBooleanSafely when value is Boolean.TRUE
     * Should return true for Boolean.TRUE value
     */
    @Test
    public void testGetBooleanSafely_WhenValueIsTrue() {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("testKey", Boolean.TRUE);
        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");
        // assert
        assertTrue("Should return true when value is Boolean.TRUE", result);
    }

    /**
     * Test getBooleanSafely when value is Boolean.FALSE
     * Should return false for Boolean.FALSE value
     */
    @Test
    public void testGetBooleanSafely_WhenValueIsFalse() {
        // arrange
        Map<String, Object> params = new HashMap<>();
        params.put("testKey", Boolean.FALSE);
        // act
        boolean result = ParamsUtil.getBooleanSafely(params, "testKey");
        // assert
        assertFalse("Should return false when value is Boolean.FALSE", result);
    }

    /**
     * 测试 getDoubleSafely 方法，当 params 为空的情况
     */
    @Test
    public void testGetDoubleSafelyParamsIsNull() throws Throwable {
        Map<String, Object> params = null;
        String name = "float";
        double result = ParamsUtil.getDoubleSafely(params, name);
        assertEquals(0.0, result, 0.0);
    }

    /**
     * 测试 getDoubleSafely 方法，当 params 不为空，但 name 在 params 中不存在的情况
     */
    @Test
    public void testGetDoubleSafelyNameNotInParams() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("double", "2.2");
        String name = "float";
        double result = ParamsUtil.getDoubleSafely(params, name);
        assertEquals(0.0, result, 0.0);
    }

    /**
     * 测试 getDoubleSafely 方法，当 params 不为空，name 在 params 中存在，但对应的值无法转换为 Double 类型的情况
     */
    @Test
    public void testGetDoubleSafelyValueNotDouble() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("float", "notDouble");
        String name = "float";
        double result = ParamsUtil.getDoubleSafely(params, name);
        assertEquals(0.0, result, 0.0);
    }

    /**
     * 测试 getDoubleSafely 方法，当 params 不为空，name 在 params 中存在，且对应的值可以转换为 Double 类型的情况
     */
    @Test
    public void testGetDoubleSafelyValueIsDouble() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("float", "2.2");
        String name = "float";
        double result = ParamsUtil.getDoubleSafely(params, name);
        assertEquals(2.2, result, 0.0);
    }

    /**
     * Test getDateSafely when activityContext is null
     */
    @Test
    public void testGetDateSafWithNullActivity() throws Throwable {
        // arrange
        // act
        Date result = ParamsUtil.getDateSafely((ActivityContext) null, "testParam");
        // assert
        assertNull(result);
    }

    /**
     * Test getDateSafely when activityContext is not null with valid parameters
     */
    @Test
    public void testGetDateSafelyWithValidParameters() throws Throwable {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        Map<String, Object> parameters = new HashMap<>();
        Date expectedDate = new Date();
        parameters.put("testParam", expectedDate);
        when(activityContext.getParameters()).thenReturn(parameters);
        // act
        Date result = ParamsUtil.getDateSafely(activityContext, "testParam");
        // assert
        assertEquals(expectedDate, result);
    }

    /**
     * Test getDateSafely when activityContext is not null but parameters map is empty
     */
    @Test
    public void testGetDateSafelyWithEmptyParameters() throws Throwable {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        Map<String, Object> parameters = new HashMap<>();
        when(activityContext.getParameters()).thenReturn(parameters);
        // act
        Date result = ParamsUtil.getDateSafely(activityContext, "testParam");
        // assert
        assertNull(result);
    }

    @Test
    public void testGetParamsFromExtraMapWhenExtraIsNull() throws Throwable {
        String extra = null;
        String key = "key";
        String defaultValue = "default";
        String result = ParamsUtil.getParamsFromExtraMap(extra, key, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetParamsFromExtraMapWhenExtraIsNotEmptyButDecodedMapIsNull() throws Throwable {
        String extra = "{}";
        String key = "key";
        String defaultValue = "default";
        String result = ParamsUtil.getParamsFromExtraMap(extra, key, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetParamsFromExtraMapWhenKeyNotInMap() throws Throwable {
        String extra = "{\"otherKey\":\"otherValue\"}";
        String key = "key";
        String defaultValue = "default";
        String result = ParamsUtil.getParamsFromExtraMap(extra, key, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetParamsFromExtraMapWhenKeyInMapButValueIsNull() throws Throwable {
        String extra = "{\"key\":null}";
        String key = "key";
        String defaultValue = "default";
        String result = ParamsUtil.getParamsFromExtraMap(extra, key, defaultValue);
        assertEquals(defaultValue, result);
    }

    @Test
    public void testGetParamsFromExtraMapWhenKeyInMapAndValueIsNotNull() throws Throwable {
        String extra = "{\"key\":\"value\"}";
        String key = "key";
        String defaultValue = "default";
        String result = ParamsUtil.getParamsFromExtraMap(extra, key, defaultValue);
        assertEquals("value", result);
    }

    /**
     * Test getCommonUserId when platform is MT
     * Should return mtUserId
     */
    @Test
    public void testGetCommonUserId_WhenPlatformIsMT() {
        // arrange
        ActivityContext ctx = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        // MT platform type
        params.put("platform", 2);
        params.put("mtUserId", 123456L);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("mtUserId")).thenReturn(123456L);
        // act
        long result = ParamsUtil.getCommonUserId(ctx);
        // assert
        assertEquals(123456L, result);
    }

    /**
     * Test getCommonUserId when platform is MT client type
     * Should return mtUserId
     */
    @Test
    public void testGetCommonUserId_WhenPlatformIsMTClientType() {
        // arrange
        ActivityContext ctx = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        // MT_APP client type
        params.put("platform", 200);
        params.put("mtUserId", 789123L);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("mtUserId")).thenReturn(789123L);
        // act
        long result = ParamsUtil.getCommonUserId(ctx);
        // assert
        assertEquals(789123L, result);
    }

    /**
     * Test getCommonUserId when platform is DP
     * Should return dpUserId
     */
    @Test
    public void testGetCommonUserId_WhenPlatformIsDP() {
        // arrange
        ActivityContext ctx = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        // DP platform type
        params.put("platform", 1);
        params.put("dpUserId", 456789L);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("dpUserId")).thenReturn(456789L);
        // act
        long result = ParamsUtil.getCommonUserId(ctx);
        // assert
        assertEquals(456789L, result);
    }

    /**
     * Test getCommonUserId when platform is DP client type
     * Should return dpUserId
     */
    @Test
    public void testGetCommonUserId_WhenPlatformIsDPClientType() {
        // arrange
        ActivityContext ctx = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        // DP_APP client type
        params.put("platform", 100);
        params.put("dpUserId", 345678L);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("dpUserId")).thenReturn(345678L);
        // act
        long result = ParamsUtil.getCommonUserId(ctx);
        // assert
        assertEquals(345678L, result);
    }

    /**
     * Test getCommonUserId when context is null
     * Should return 0
     */
    @Test
    public void testGetCommonUserId_WhenContextIsNull() {
        // arrange
        ActivityContext ctx = null;
        // act
        long result = ParamsUtil.getCommonUserId(ctx);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test getCommonUserId when userId is not present
     * Should return 0
     */
    @Test
    public void testGetCommonUserId_WhenUserIdNotPresent() {
        // arrange
        ActivityContext ctx = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        // MT platform
        params.put("platform", 2);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("mtUserId")).thenReturn(null);
        // act
        long result = ParamsUtil.getCommonUserId(ctx);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test getCommonUserId when platform parameter is invalid
     * Should return dpUserId as default
     */
    @Test
    public void testGetCommonUserId_WhenPlatformIsInvalid() {
        // arrange
        ActivityContext ctx = mock(ActivityContext.class);
        Map<String, Object> params = new HashMap<>();
        // Invalid platform
        params.put("platform", -1);
        params.put("dpUserId", 567890L);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("dpUserId")).thenReturn(567890L);
        // act
        long result = ParamsUtil.getCommonUserId(ctx);
        // assert
        assertEquals(567890L, result);
    }

    /**
     * Test getBooleanSafely method when ActivityContext is null
     * Should return false
     */
    @Test
    public void testGetBooleanSafely_WhenActivityContextNull() throws Throwable {
        // arrange
        ActivityContext activityContext = null;
        String paramName = "testParam";
        // act
        boolean result = ParamsUtil.getBooleanSafely(activityContext, paramName);
        // assert
        assertFalse("Should return false when ActivityContext is null", result);
    }

    /**
     * Test getBooleanSafely method when ActivityContext parameters map is empty
     * Should return false
     */
    @Test
    public void testGetBooleanSafely_WhenParametersEmpty() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        String paramName = "testParam";
        // act
        boolean result = ParamsUtil.getBooleanSafely(activityContext, paramName);
        // assert
        assertFalse("Should return false when parameters map is empty", result);
    }

    /**
     * Test getBooleanSafely method when parameter value is null
     * Should return false
     */
    @Test
    public void testGetBooleanSafely_WhenParameterValueNull() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        String paramName = "testParam";
        // No need to put null, simulating the absence of the parameter
        // act
        boolean result = ParamsUtil.getBooleanSafely(activityContext, paramName);
        // assert
        assertFalse("Should return false when parameter value is null", result);
    }

    /**
     * Test getBooleanSafely method when parameter value is Boolean true
     * Should return true
     */
    @Test
    public void testGetBooleanSafely_WhenParameterValueTrue() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        String paramName = "testParam";
        activityContext.getParameters().put(paramName, Boolean.TRUE);
        // act
        boolean result = ParamsUtil.getBooleanSafely(activityContext, paramName);
        // assert
        assertTrue("Should return true when parameter value is Boolean true", result);
    }

    /**
     * Test getBooleanSafely method when parameter value is Boolean false
     * Should return false
     */
    @Test
    public void testGetBooleanSafely_WhenParameterValueFalse() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        String paramName = "testParam";
        activityContext.getParameters().put(paramName, Boolean.FALSE);
        // act
        boolean result = ParamsUtil.getBooleanSafely(activityContext, paramName);
        // assert
        assertFalse("Should return false when parameter value is Boolean false", result);
    }

    /**
     * Test getBooleanSafely method when parameter value is non-Boolean type
     * Should return false
     */
    @Test
    public void testGetBooleanSafely_WhenParameterValueNonBoolean() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        String paramName = "testParam";
        activityContext.getParameters().put(paramName, "true");
        // act
        boolean result = ParamsUtil.getBooleanSafely(activityContext, paramName);
        // assert
        assertFalse("Should return false when parameter value is non-Boolean type", result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenContextIsNull() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(null);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenPlatformIsNull() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenUserIdIsNull() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenMtUserIdIsNotNullAndDpUserIdIsNull() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenMtUserIdIsNullAndDpUserIdIsNotNull() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenMtUserIdAndDpUserIdAreNotNull() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenMtUserIdIsNotNullAndDpUserIdIsNullAndMtUserIdIsZero() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenMtUserIdIsNullAndDpUserIdIsNotNullAndDpUserIdIsZero() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetUserIdForShelfActivityWhenMtUserIdAndDpUserIdAreNotNullAndMtUserIdAndDpUserIdAreZero() throws Throwable {
        long result = ParamsUtil.getUserIdForShelfActivity(ctx);
        assertEquals(0, result);
    }

    @Test
    public void testGetShopIdForShelfActivityWhenContextIsNull() throws Throwable {
        long result = ParamsUtil.getShopIdForShelfActivity(null);
        assertEquals(0, result);
    }

    @Test
    public void testGetShopIdForShelfActivityWhenPlatformIsNotMT() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("dpPoiId", 123L);
        // Correctly set the platform to a non-MT value
        // Assuming 100 is a non-MT platform
        params.put("platform", 100);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("dpPoiId")).thenReturn(123L);
        long result = ParamsUtil.getShopIdForShelfActivity(ctx);
        assertEquals(123L, result);
    }

    @Test
    public void testGetShopIdForShelfActivityWhenPlatformIsMTAndMtPoiIdExists() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("mtPoiId", 123L);
        // Correctly set the platform to an MT value
        // Assuming 200 is an MT platform
        params.put("platform", 200);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("mtPoiId")).thenReturn(123L);
        long result = ParamsUtil.getShopIdForShelfActivity(ctx);
        assertEquals(123L, result);
    }

    @Test
    public void testGetShopIdForShelfActivityWhenPlatformIsNotMTAndDpPoiIdExists() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("dpPoiId", 123L);
        // Correctly set the platform to a non-MT value
        // Assuming 100 is a non-MT platform
        params.put("platform", 100);
        when(ctx.getParameters()).thenReturn(params);
        when(ctx.getParam("dpPoiId")).thenReturn(123L);
        long result = ParamsUtil.getShopIdForShelfActivity(ctx);
        assertEquals(123L, result);
    }
}
