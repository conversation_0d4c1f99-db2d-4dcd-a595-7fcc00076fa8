package com.sankuai.dzviewscene.shelf.gateways.api;

import com.dianping.beauty.zone.common.adapter.ItemSearchAdapter;
import com.dianping.beauty.zone.common.parse.ItemDefaultParser;
import com.dianping.beauty.zone.common.parse.SingleParserFactory;
import com.dianping.beauty.zone.common.parse.reader.AnnotationReaderFactory;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import com.sankuai.athena.test.inf.TestBeanFactory;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfTypeEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzShelfResponseVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.framework.DefaultActivityEngine;
import com.sankuai.dzviewscene.shelf.platform.other.OtherActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene", "com.sankuai.dzviewscene.nr.atom","com.sankuai.dzviewscene.productshelf.nr", "com.sankuai.athena","com.dianping.vc.sdk", "com.dianping.beauty.zone.common.parse.reader"})
public class DealShelfApiTest {

    static {
        TestBeanFactory.registerBean("activityEngine", DefaultActivityEngine.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
        TestBeanFactory.registerBean("ShopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.registerBean("itemSearchAdapter", ItemSearchAdapter.class);
        TestBeanFactory.registerBean("itemDefaultParser", ItemDefaultParser.class);
        TestBeanFactory.registerBean("singleParserFactory", SingleParserFactory.class);
        TestBeanFactory.registerBean("annotationReaderFactory", AnnotationReaderFactory.class);
       // TestBeanFactory.registerBean("annotationReaders", List<AnnotationReader>.class);
    }

    @Resource
    private ActivityEngine activityEngine;

   @Environment(value = AthenaEnv.Product)
    //@Test
    public void test() {
       for (int i= 0 ; i<10 ;i++) {
            ActivityRequest activityRequest = buildActivityRequest();

            Object object = executeByActivityEngine(activityRequest);
            System.out.println(object);
        }
    }

    private ActivityRequest buildActivityRequest() {
        int platform = 100;
        int shopId = 114509925;
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, "12.35.6");
        activityRequest.addParam(ShelfActivityConstants.Params.unionId, "3e9f3d51f9f14b39b39fa0328f097ba3a155600291719487416");
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(platform) ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, 10);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, shopId);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, 1);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, shopId);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, "ios");
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, "edu_poi_double_floors_shelf_filter");
        activityRequest.addParam(ShelfActivityConstants.Params.shelfType, ShelfTypeEnums.OTHER.getType());
        activityRequest.addParam(ShelfActivityConstants.Params.channel, ShelfActivityConstants.ChannelType.productFilter);
        activityRequest.addParam(ShelfActivityConstants.Params.summaryProductIds, "{\"deal\":\"34306440\",\"spu\":\"7719601\"}");
        activityRequest.addParam(ShelfActivityConstants.Params.traceMark, "1");// 开启打点
        return activityRequest;
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse<DzShelfResponseVO> activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }

        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        return activityResponse.getResult().join();
    }
}
