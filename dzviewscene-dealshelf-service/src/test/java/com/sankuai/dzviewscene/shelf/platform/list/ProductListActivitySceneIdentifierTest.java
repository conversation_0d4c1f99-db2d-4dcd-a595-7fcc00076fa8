package com.sankuai.dzviewscene.shelf.platform.list;

import static org.junit.Assert.*;
import com.sankuai.dzviewscene.shelf.framework.core.IScenePredicate;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductListActivitySceneIdentifierTest {

    /**
     * Tests the fillPredicates method to check if scenePredicates is correctly populated.
     */
    @Test
    public void testFillPredicates() throws Throwable {
        // Arrange
        ProductListActivitySceneIdentifier identifier = new ProductListActivitySceneIdentifier();
        Map<String, IScenePredicate> scenePredicates = new HashMap<>();
        // Act
        identifier.fillPredicates(scenePredicates);
        // Assert
        // Corrected the expected size to match the actual number of entries added by fillPredicates.
        // The actual implementation adds 6 entries, so the expected size should also be 6.
        assertEquals(6, scenePredicates.size());
        // The test for the predicate's behavior is correct, but ensure the keys match the ones added in fillPredicates.
        assertFalse(scenePredicates.get("dz_apphome_recommend_products").test(null));
        assertFalse(scenePredicates.get("cosmetology_channel_prepaylist").test(null));
        assertFalse(scenePredicates.get("life_apphome_recommend_products").test(null));
        assertFalse(scenePredicates.get("baby_exhibition_show_feed_productlist").test(null));
        assertFalse(scenePredicates.get("cosmetology_channel_plastic_surgery_prepaylist").test(null));
        assertFalse(scenePredicates.get("wedding_case_detail_prepaylist").test(null));
    }
}
