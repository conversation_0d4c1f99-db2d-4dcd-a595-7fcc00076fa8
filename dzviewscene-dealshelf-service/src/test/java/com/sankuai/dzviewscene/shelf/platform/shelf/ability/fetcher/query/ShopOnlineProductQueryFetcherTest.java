package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.GroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Maps;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;


/**
 * @Author: zhouzhaoming02
 * @Create: 2024/6/5 17:29
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class ShopOnlineProductQueryFetcherTest {

    @Mock
    private GroupQueryFetcher groupQueryFetcher;

    @InjectMocks
    private ShopOnlineProductQueryFetcher shopOnlineProductQueryFetcher;

    @Test
    public void test_empty_groupConfig() {
        ActivityContext cxt = new ActivityContext();
        cxt.addParam(QueryFetcher.Params.groupNames, Collections.emptyList());
        Map<String, ProductGroupM> result = shopOnlineProductQueryFetcher.build(cxt).join();
        assert MapUtils.isEmpty(result);
    }

    @Test
    public void test_OnlineDealQuery() {
        ActivityContext cxt = new ActivityContext();
        cxt.addParam(QueryFetcher.Params.groupNames, Lists.newArrayList("团购"));
        Map<String, List<Map<String,Object>>> multiGroupParams = mockDealQuery();
        cxt.addParam(QueryFetcher.Params.multiGroupParams,multiGroupParams);
        Mockito.when(groupQueryFetcher.build(Mockito.any(),Mockito.any())).thenReturn(mockDealProductGroupM());
        Map<String, ProductGroupM> result = shopOnlineProductQueryFetcher.build(cxt).join();
        ProductGroupM productGroupM = result.get("团购");
        assert productGroupM != null && productGroupM.getProducts().size() == 2;
    }

    private Map<String, List<Map<String,Object>>> mockDealQuery() {
        Map<String,Object> objectMap = Maps.newHashMap();
        objectMap.put("queryName","团购");
        objectMap.put("queryType","poiSaleDealGroupQueryHandler");
        List<Map<String,Object>> mapList = Lists.newArrayList(objectMap);
        Map<String, List<Map<String,Object>>> multiGroupParams = new HashMap<>();
        multiGroupParams.put("团购",mapList);
        return multiGroupParams;
    }

    private CompletableFuture<Map<String, ProductGroupM>> mockDealProductGroupM() {
        Map<String,ProductGroupM> result = Maps.newHashMap();
        ProductGroupM productGroupM = new ProductGroupM();
        List<ProductM> products = Lists.newArrayList();
        products.add(new ProductM());
        products.add(new ProductM());
        productGroupM.setProducts(products);
        productGroupM.setTotal(products.size());
        result.put("团购",productGroupM);
        return CompletableFuture.completedFuture(result);
    }
}
