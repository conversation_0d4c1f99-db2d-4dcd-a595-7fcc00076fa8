package com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.maintitle;

import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.sankuai.dzviewscene.shelf.SpringConfiguration;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.platform.MockBeanTest;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Matchers;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * Created by float.lu on 2020/9/2.
 */
@Ignore("没有可执行的方法")
@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {SpringConfiguration.class})
public class ShelfMainTitleBuilderTest extends MockBeanTest {

    private ActivityContext activityContext;

    @Resource
    protected ComponentFinder componentFinder;

    @Before
    public void setUp() throws Exception {
        activityContext = new ActivityContext();
        activityContext.setActivityCode(ShelfActivity.ACTIVITY_SHELF_CODE);
        activityContext.setSceneCode("test_scene");
        activityContext.addParam("_activity_trace", "1");
    }

    //@Test
    public void test_main_title_builder() throws Exception {
        CompletableFuture<MainTitleComponentVO> mainTitleComponentVOCompletableFuture = componentFinder.findAbility(activityContext, MainTitleBuilder.ABILITY_MAIN_TITLE_CODE).build(activityContext);
        Assert.assertThat(mainTitleComponentVOCompletableFuture.join(), Matchers.isNotNull());
    }
}
