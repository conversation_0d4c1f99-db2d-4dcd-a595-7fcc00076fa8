package com.sankuai.dzviewscene.shelf.gateways.rpc;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.sankuai.dzviewscene.dealshelf.req.DealCustomStructSnapshotRequest;
import com.sankuai.dzviewscene.dealshelf.res.CommonResponse;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.shelf.faulttolerance.detail.DealDetailExecutor;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class DealCustomStructSnapshotServiceImplTest {

    @InjectMocks
    private DealCustomStructSnapshotServiceImpl service;

    @Mock
    private DealDetailExecutor dealDetailExecutor;

    @Before
    public void setUp() {
    }

    /**
     * 测试请求为null的情况
     */
    @Test
    public void testQueryDealCustomStructSnapshotRequestIsNull() {
        CommonResponse<String> response = service.queryDealCustomStructSnapshot(null);
        assertEquals("参数错误", response.getMsg());
        assertEquals(CommonResponse.FAIL, response.getCode());
    }

    /**
     * 测试dealGroupId小于等于0的情况
     */
    @Test
    public void testQueryDealCustomStructSnapshotDealGroupIdInvalid() {
        DealCustomStructSnapshotRequest request = new DealCustomStructSnapshotRequest();
        request.setDealGroupId(-1L);
        CommonResponse<String> response = service.queryDealCustomStructSnapshot(request);
        assertEquals("参数错误", response.getMsg());
        assertEquals(CommonResponse.FAIL, response.getCode());
    }

    /**
     * 测试dealDetailExecutor.execute抛出异常的情况
     */
    @Test
    public void testQueryDealCustomStructSnapshotExecuteThrowsException() throws Exception {
        DealCustomStructSnapshotRequest request = getDefaultRequest();
        when(dealDetailExecutor.execute(any())).thenThrow(new RuntimeException());
        CommonResponse<String> response = service.queryDealCustomStructSnapshot(request);
        assertEquals("系统异常", response.getMsg());
        assertEquals(CommonResponse.FAIL, response.getCode());
    }

    private DealCustomStructSnapshotRequest getDefaultRequest() {
        DealCustomStructSnapshotRequest request = new DealCustomStructSnapshotRequest();
        request.setDealGroupId(1L);
        request.setShopId(2L);
        request.setCityId(1);
        request.setClientTypeEnum(ClientTypeEnum.mt_mainApp_android);
        request.setRequestSource("trade_snapshot");
        return request;
    }

    /**
     * 测试未查询到自定义样式团购详情的情况
     */
    @Test
    public void testQueryDealCustomStructSnapshotDetailNotFound() throws Exception {
        DealCustomStructSnapshotRequest request = getDefaultRequest();
        when(dealDetailExecutor.execute(any())).thenReturn(null);
        CommonResponse<String> response = service.queryDealCustomStructSnapshot(request);
        assertEquals("未查询到自定义样式团购详情", response.getMsg());
        assertEquals(CommonResponse.SUCCESS, response.getCode());
    }

    /**
     * 测试查询成功的情况
     */
    @Test
    public void testQueryDealCustomStructSnapshotSuccess() throws Exception {
        DealCustomStructSnapshotRequest request = getDefaultRequest();
        DealModuleDetailVO mockDetailVO = new DealModuleDetailVO();
        when(dealDetailExecutor.execute(any())).thenReturn(mockDetailVO);
        CommonResponse<String> response = service.queryDealCustomStructSnapshot(request);
        assertNotNull(response.getData());
        assertEquals("查询成功", response.getMsg());
        assertEquals(CommonResponse.SUCCESS, response.getCode());
    }
}
