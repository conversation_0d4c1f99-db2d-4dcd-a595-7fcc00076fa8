package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ReadProductsFormFilterMFetcherTest {

    @Mock
    private ActivityContext ctx;

    @InjectMocks
    private ReadProductsFormFilterMFetcher fetcher;

    // Define Params within the test class to resolve the compilation issue
    private static class Params {

        static final String productComponent2Group = "productComponent2Group";
    }

    @Test(expected = BusinessException.class)
    public void testBuildProductComponent2GroupIsNull() throws Throwable {
        when(ctx.getParam(Params.productComponent2Group)).thenReturn(null);
        fetcher.build(ctx).get();
    }

    @Test(expected = BusinessException.class)
    public void testBuildProductComponent2GroupSizeGreaterThanOne() throws Throwable {
        Map<String, String> productComponent2Group = new HashMap<>();
        productComponent2Group.put("key1", "value1");
        productComponent2Group.put("key2", "value2");
        when(ctx.getParam(Params.productComponent2Group)).thenReturn(productComponent2Group);
        fetcher.build(ctx).get();
    }

    @Test
    public void testBuildFilterGroupsCfIsNull() throws Throwable {
        Map<String, String> productComponent2Group = new HashMap<>();
        productComponent2Group.put("key", "value");
        when(ctx.getParam(Params.productComponent2Group)).thenReturn(productComponent2Group);
        when(ctx.getAttachment(FilterFetcher.Attachments.filterGroups)).thenReturn(CompletableFuture.completedFuture(new HashMap<String, FilterM>()));
        CompletableFuture<Map<String, ProductGroupM>> result = fetcher.build(ctx);
        // Adjust the assertion to expect a map with a specific key and a default ProductGroupM object
        Map<String, ProductGroupM> expected = new HashMap<>();
        // Assuming default constructor sets the fields to the observed default values
        expected.put("key", new ProductGroupM());
        assertEquals(expected, result.get());
    }

    @Test
    public void testBuildWhenFilterGroupsIsNull() throws Throwable {
        Map<String, String> productComponent2Group = new HashMap<>();
        productComponent2Group.put("key1", "value1");
        when(ctx.getParam(any())).thenReturn(productComponent2Group);
        when(ctx.getAttachment(any())).thenReturn(null);
        CompletableFuture<Map<String, ProductGroupM>> result = fetcher.build(ctx);
        assertNotNull(result);
        assertTrue(result.get().isEmpty());
    }

    @SuppressWarnings("unchecked")
    @Test
    public void testBuildWithValidParameters() throws Throwable {
        // Setup productComponent2Group
        Map<String, String> productComponent2Group = new HashMap<>();
        productComponent2Group.put("key1", "value1");
        when(ctx.getParam(any())).thenReturn(productComponent2Group);
        // Setup filterGroups
        Map<String, FilterM> filterGroups = new HashMap<>();
        filterGroups.put("value1", new FilterM());
        CompletableFuture<Map<String, FilterM>> filterGroupsCf = CompletableFuture.completedFuture(filterGroups);
        // Use unchecked cast to handle generic type
        when(ctx.getAttachment(any())).thenReturn((CompletableFuture) filterGroupsCf);
        CompletableFuture<Map<String, ProductGroupM>> result = fetcher.build(ctx);
        assertNotNull(result);
        assertFalse(result.get().isEmpty());
    }
}
