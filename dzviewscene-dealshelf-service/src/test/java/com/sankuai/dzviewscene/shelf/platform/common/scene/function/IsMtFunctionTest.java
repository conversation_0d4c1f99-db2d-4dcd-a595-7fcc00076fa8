package com.sankuai.dzviewscene.shelf.platform.common.scene.function;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;
import com.googlecode.aviator.runtime.type.AviatorObject;
import com.googlecode.aviator.runtime.type.AviatorRuntimeJavaType;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import java.util.HashMap;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class IsMtFunctionTest {

    @Mock
    private PlatformUtil platformUtil;

    /**
     * 测试环境变量中包含平台参数，且平台为MT的情况
     */
    @Test
    public void testCallPlatformIsMT() throws Throwable {
        // arrange
        Map<String, Object> env = new HashMap<>();
        env.put(ShelfActivityConstants.Params.platform, 2);
        IsMtFunction isMtFunction = new IsMtFunction();
        // act
        AviatorObject result = isMtFunction.call(env);
        // assert
        assertTrue(result.booleanValue(env));
    }

    /**
     * 测试环境变量中包含平台参数，且平台不为MT的情况
     */
    @Test
    public void testCallPlatformIsNotMT() throws Throwable {
        // arrange
        Map<String, Object> env = new HashMap<>();
        env.put(ShelfActivityConstants.Params.platform, 1);
        IsMtFunction isMtFunction = new IsMtFunction();
        // act
        AviatorObject result = isMtFunction.call(env);
        // assert
        assertFalse(result.booleanValue(env));
    }

    /**
     * 测试环境变量中不包含平台参数的情况
     */
    @Test
    public void testCallPlatformNotSet() throws Throwable {
        // arrange
        Map<String, Object> env = new HashMap<>();
        IsMtFunction isMtFunction = new IsMtFunction();
        // act
        AviatorObject result = isMtFunction.call(env);
        // assert
        assertFalse(result.booleanValue(env));
    }
}
