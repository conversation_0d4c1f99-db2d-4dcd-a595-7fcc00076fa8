package com.sankuai.dzviewscene.shelf.platform.filterlist.flow;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.PaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.QueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter.FilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ParallelHandleFilterAndProductsFlowTest {

    @Mock
    private AbstractActivity<?> activity;

    @Mock
    private ActivityContext ctx;

    @Mock
    private FilterFetcher filterFetcher;

    @Mock
    private QueryFetcher queryFetcher;

    /**
     * 测试正常情况
     */
    @Test
    public void testExecuteNormal() throws Throwable {
        // arrange
        ParallelHandleFilterAndProductsFlow flow = new ParallelHandleFilterAndProductsFlow();
        Map<String, FilterM> filterMap = new HashMap<>();
        filterMap.put("filterKey", new FilterM());
        CompletableFuture<Map<String, FilterM>> filterFuture = CompletableFuture.completedFuture(filterMap);
        Map<String, ProductGroupM> productGroupMap = new HashMap<>();
        productGroupMap.put("productGroupKey", new ProductGroupM());
        CompletableFuture<Map<String, ProductGroupM>> productGroupFuture = CompletableFuture.completedFuture(productGroupMap);
        when(activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE)).thenReturn(filterFetcher);
        when(filterFetcher.build(ctx)).thenReturn(filterFuture);
        when(activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(queryFetcher);
        when(queryFetcher.build(ctx)).thenReturn(productGroupFuture);
        // act
        CompletableFuture<FilterProductsM> result = flow.execute(activity, ctx);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
    }

    /**
     * 测试异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testExecuteException() throws Throwable {
        // arrange
        ParallelHandleFilterAndProductsFlow flow = new ParallelHandleFilterAndProductsFlow();
        Map<String, FilterM> filterMap = new HashMap<>();
        CompletableFuture<Map<String, FilterM>> filterFuture = new CompletableFuture<>();
        filterFuture.completeExceptionally(new RuntimeException());
        when(activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE)).thenReturn(filterFetcher);
        when(filterFetcher.build(ctx)).thenReturn(filterFuture);
        // act
        flow.execute(activity, ctx);
    }

    /**
     * 测试边界情况
     */
    @Test
    public void testExecuteBoundary() throws Throwable {
        // arrange
        ParallelHandleFilterAndProductsFlow flow = new ParallelHandleFilterAndProductsFlow();
        Map<String, FilterM> filterMap = new HashMap<>();
        filterMap.put("filterKey", null);
        CompletableFuture<Map<String, FilterM>> filterFuture = CompletableFuture.completedFuture(filterMap);
        Map<String, ProductGroupM> productGroupMap = new HashMap<>();
        productGroupMap.put("productGroupKey", null);
        CompletableFuture<Map<String, ProductGroupM>> productGroupFuture = CompletableFuture.completedFuture(productGroupMap);
        when(activity.findAbility(ctx, FilterFetcher.ABILITY_FILTER_CODE)).thenReturn(filterFetcher);
        when(filterFetcher.build(ctx)).thenReturn(filterFuture);
        when(activity.findAbility(ctx, QueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(queryFetcher);
        when(queryFetcher.build(ctx)).thenReturn(productGroupFuture);
        // act
        CompletableFuture<FilterProductsM> result = flow.execute(activity, ctx);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
    }
}
