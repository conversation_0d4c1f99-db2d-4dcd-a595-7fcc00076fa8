package com.sankuai.dzviewscene.shelf.platform.filterlist.flow;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.AbstractActivity;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.ability.query.MultiGroupQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.filterlist.ability.fetcher.filter.ShowFilterFetcher;
import com.sankuai.dzviewscene.shelf.platform.filterlist.model.FilterProductsM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class StandardFilterProductsFlowTest {

    @Mock
    private AbstractActivity<?> activity;

    @Mock
    private ActivityContext ctx;

    @Mock
    private MultiGroupQueryFetcher multiGroupQueryFetcher;

    @Mock
    private MultiGroupPaddingFetcher multiGroupPaddingFetcher;

    @Mock
    private ShowFilterFetcher showFilterFetcher;

    @Test
    public void testExecuteNormal() throws Throwable {
        // arrange
        StandardFilterProductsFlow flow = new StandardFilterProductsFlow();
        CompletableFuture<Map<String, ProductGroupM>> productGroupCompletableFuture = CompletableFuture.completedFuture(null);
        CompletableFuture<List<FilterM>> showFilterCompletableFuture = CompletableFuture.completedFuture(null);
        when(activity.findAbility(ctx, MultiGroupQueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(multiGroupQueryFetcher);
        when(activity.findAbility(ctx, MultiGroupPaddingFetcher.ABILITY_PRODUCT_PADDING_CODE)).thenReturn(multiGroupPaddingFetcher);
        when(activity.findAbility(ctx, ShowFilterFetcher.ABILITY_MULTI_FILTER_FETCHER)).thenReturn(showFilterFetcher);
        when(multiGroupQueryFetcher.build(ctx)).thenReturn(productGroupCompletableFuture);
        when(multiGroupPaddingFetcher.build(ctx)).thenReturn(productGroupCompletableFuture);
        when(showFilterFetcher.build(ctx)).thenReturn(showFilterCompletableFuture);
        // act
        CompletableFuture<FilterProductsM> result = flow.execute(activity, ctx);
        // assert
        assertNotNull(result);
        verify(activity, times(1)).findAbility(ctx, MultiGroupQueryFetcher.ABILITY_PRODUCT_QUERY_CODE);
        verify(activity, times(1)).findAbility(ctx, MultiGroupPaddingFetcher.ABILITY_PRODUCT_PADDING_CODE);
        verify(activity, times(1)).findAbility(ctx, ShowFilterFetcher.ABILITY_MULTI_FILTER_FETCHER);
    }

    @Test(expected = Exception.class)
    public void testExecuteException() throws Throwable {
        // arrange
        StandardFilterProductsFlow flow = new StandardFilterProductsFlow();
        when(activity.findAbility(ctx, MultiGroupQueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenThrow(new Exception());
        // act
        flow.execute(activity, ctx);
        // assert
        // Exception is expected
    }

    @Test
    public void testExecuteNormalCase() throws Throwable {
        // arrange
        StandardFilterProductsFlow flow = new StandardFilterProductsFlow();
        CompletableFuture<Map<String, ProductGroupM>> productGroupCompletableFuture = CompletableFuture.completedFuture(null);
        CompletableFuture<List<FilterM>> showFilterCompletableFuture = CompletableFuture.completedFuture(null);
        when(activity.findAbility(ctx, MultiGroupQueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenReturn(multiGroupQueryFetcher);
        when(activity.findAbility(ctx, MultiGroupPaddingFetcher.ABILITY_PRODUCT_PADDING_CODE)).thenReturn(multiGroupPaddingFetcher);
        when(activity.findAbility(ctx, ShowFilterFetcher.ABILITY_MULTI_FILTER_FETCHER)).thenReturn(showFilterFetcher);
        when(multiGroupQueryFetcher.build(ctx)).thenReturn(productGroupCompletableFuture);
        when(multiGroupPaddingFetcher.build(ctx)).thenReturn(productGroupCompletableFuture);
        when(showFilterFetcher.build(ctx)).thenReturn(showFilterCompletableFuture);
        // act
        CompletableFuture<FilterProductsM> result = flow.execute(activity, ctx);
        // assert
        assertNotNull(result);
        verify(activity, times(1)).findAbility(ctx, MultiGroupQueryFetcher.ABILITY_PRODUCT_QUERY_CODE);
        verify(activity, times(1)).findAbility(ctx, MultiGroupPaddingFetcher.ABILITY_PRODUCT_PADDING_CODE);
        verify(activity, times(1)).findAbility(ctx, ShowFilterFetcher.ABILITY_MULTI_FILTER_FETCHER);
    }

    @Test(expected = Exception.class)
    public void testExecuteExceptionCase() throws Throwable {
        // arrange
        StandardFilterProductsFlow flow = new StandardFilterProductsFlow();
        when(activity.findAbility(ctx, MultiGroupQueryFetcher.ABILITY_PRODUCT_QUERY_CODE)).thenThrow(new Exception());
        // act
        flow.execute(activity, ctx);
        // assert
        // Exception is expected
    }
}
