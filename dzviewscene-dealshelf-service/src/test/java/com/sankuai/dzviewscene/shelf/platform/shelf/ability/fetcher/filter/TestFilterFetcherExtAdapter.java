package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.filter;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.annotation.ExtPointInstance;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;

/**
 * Created by float.lu on 2020/8/24.
 */
@ExtPointInstance(name = "测试筛选组件扩展点实例")
public class TestFilterFetcherExtAdapter extends FilterFetcherExtAdapter implements FilterFetcherExt {

    @Override
    public long selected(ActivityContext activityContext, String groupName, FilterM filterM) {
        return 0;
    }
}
