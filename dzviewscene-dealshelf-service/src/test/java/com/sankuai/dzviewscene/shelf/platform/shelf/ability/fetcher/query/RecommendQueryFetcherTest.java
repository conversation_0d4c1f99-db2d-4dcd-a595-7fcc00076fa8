package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.dianping.general.unified.search.api.dealgroupsearch.dto.DealGroupSearchDTO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import com.dianping.general.unified.search.api.dealgroupsearch.request.GeneralDealGroupSearchRequest;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RecommendQueryFetcherTest {

    @InjectMocks
    private RecommendQueryFetcher recommendQueryFetcher;

    @Mock
    private AtomFacadeService atomFacadeService;

    /**
     * Tests the normal scenario where a valid search result is returned.
     */
    @Test
    public void testBuildNormal() throws Throwable {
        ActivityContext ctx = mock(ActivityContext.class);
        List<DealGroupSearchDTO> dealGroupSearchDTOList = Collections.singletonList(mock(DealGroupSearchDTO.class));
        when(atomFacadeService.searchDealgroups(any())).thenReturn(CompletableFuture.completedFuture(dealGroupSearchDTOList));
        CompletableFuture<Map<String, ProductGroupM>> result = recommendQueryFetcher.build(ctx);
        assertNotNull(result);
        verify(atomFacadeService, times(1)).searchDealgroups(any());
    }

    /**
     * Tests the scenario where the ActivityContext is null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildWithNullActivityContext() throws Throwable {
        recommendQueryFetcher.build(null);
    }

    /**
     * Tests the scenario where atomFacadeService.searchDealgroups returns a null CompletableFuture.
     * This is an invalid case as the service should always return a CompletableFuture instance, even when no results are found.
     */
    @Test
    public void testBuildWithNullSearchResult() throws Throwable {
        ActivityContext ctx = mock(ActivityContext.class);
        when(atomFacadeService.searchDealgroups(any())).thenReturn(CompletableFuture.completedFuture(null));
        CompletableFuture<Map<String, ProductGroupM>> result = recommendQueryFetcher.build(ctx);
        assertNotNull(result);
        verify(atomFacadeService, times(1)).searchDealgroups(any());
    }

    /**
     * Tests the scenario where atomFacadeService.searchDealgroups throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testBuildWithException() throws Throwable {
        ActivityContext ctx = mock(ActivityContext.class);
        when(atomFacadeService.searchDealgroups(any())).thenThrow(new RuntimeException());
        recommendQueryFetcher.build(ctx);
    }
}
