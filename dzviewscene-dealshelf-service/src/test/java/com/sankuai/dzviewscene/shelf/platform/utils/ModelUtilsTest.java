package com.sankuai.dzviewscene.shelf.platform.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterProductListVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemAreaComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FilterBtnIdAndProAreasVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.ProductAreaComponentVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.list.vo.ProductListVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.hamcrest.Matchers;
import org.junit.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Created by float.lu on 2020/9/2.
 */
@RunWith(MockitoJUnitRunner.class)
public class ModelUtilsTest {

    //@Test
    @DisplayName("测试productGroupMs为null的情况")
    public void test_multi_product_group_is_null() throws Exception {
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(null);
        Assert.assertThat(ModelUtils.hasNoProducts(shelfGroupM), Matchers.is(true));
    }

    //@Test
    @DisplayName("测试productGroupMs不为null但是值为空map的情况")
    public void test_multi_product_group_is_empty() throws Exception {
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(new HashMap<>());
        Assert.assertThat(ModelUtils.hasNoProducts(shelfGroupM), Matchers.is(true));
    }

    //@Test
    @DisplayName("测试productGroupMs不为null, map值也不空, 但是商品组名关联的值为null的情况")
    public void test_product_group_is_null() throws Exception {
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(new HashMap<String, ProductGroupM>() {

            {
                put("group1", null);
                put("group2", null);
            }
        });
        Assert.assertThat(ModelUtils.hasNoProducts(shelfGroupM), Matchers.is(true));
    }

    //@Test
    @DisplayName("测试productGroupMs不为null, map值也不空, 但是商品组名关联的值不为null, 但是里面没有商品的情况")
    public void test_product_group_is_empty() throws Exception {
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(new HashMap<String, ProductGroupM>() {

            {
                put("group1", new ProductGroupM());
                put("group2", new ProductGroupM());
            }
        });
        Assert.assertThat(ModelUtils.hasNoProducts(shelfGroupM), Matchers.is(true));
    }

    //@Test
    @DisplayName("测试实际上有一组商品的情况")
    public void test_product_group_is_not_empty() throws Exception {
        ProductGroupM productM = new ProductGroupM();
        productM.setProducts(new ArrayList<>());
        productM.getProducts().add(new ProductM());
        productM.getProducts().add(new ProductM());
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        shelfGroupM.setProductGroupMs(new HashMap<String, ProductGroupM>() {

            {
                put("group1", productM);
            }
        });
        Assert.assertThat(ModelUtils.hasNoProducts(shelfGroupM), Matchers.is(false));
    }

    @Test
    @DisplayName("测试是否有神会员优惠")
    public void test_whether_has_magical_promo() throws Exception {
        DzProductVO dzProductVO = new DzProductVO();
        DzTagVO dzTagVO = new DzTagVO();
        dzTagVO.setName(DzPromoUtils.MAGICAL_MEMBER_TAG_NAME);
        dzProductVO.setPriceBottomPromoTagList(Lists.newArrayList(dzTagVO));
        Assert.assertThat(ModelUtils.hasMagicalMemberPromo(dzProductVO), Matchers.is(true));
    }

    /**
     * 测试 responseVO 为 null 的情况
     */
    @Test
    public void testHasFiltersResponseVONull() {
        // arrange
        DzFilterProductListVO responseVO = null;
        // act
        boolean result = ModelUtils.hasFilters(responseVO);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 responseVO 不为 null，但 filters 为空的情况
     */
    @Test
    public void testHasFiltersFiltersEmpty() {
        // arrange
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        responseVO.setFilters(Collections.emptyList());
        // act
        boolean result = ModelUtils.hasFilters(responseVO);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 responseVO 不为 null，filters 不为空，但第一个元素为 null 的情况
     */
    @Test
    public void testHasFiltersFirstElementNull() {
        // arrange
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        responseVO.setFilters(Collections.singletonList(null));
        // act
        boolean result = ModelUtils.hasFilters(responseVO);
        // assert
        Assert.assertFalse(result);
    }

    /**
     * 测试 responseVO 不为 null，filters 不为空，且第一个元素不为 null 的情况
     */
    @Test
    public void testHasFiltersFirstElementNotNull() {
        // arrange
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        DzFilterVO filter = new DzFilterVO();
        responseVO.setFilters(Collections.singletonList(filter));
        // act
        boolean result = ModelUtils.hasFilters(responseVO);
        // assert
        Assert.assertTrue(result);
    }

    @Test
    public void testExtractBackCatWhenDpPoiDTOIsNull() throws Throwable {
        DpPoiDTO dpPoiDTO = null;
        List<Integer> result = ModelUtils.extractBackCat(dpPoiDTO);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testExtractBackCatWhenBackMainCategoryPathIsEmpty() throws Throwable {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setBackMainCategoryPath(Collections.emptyList());
        List<Integer> result = ModelUtils.extractBackCat(dpPoiDTO);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testExtractBackCatWhenBackMainCategoryPathContainsNull() throws Throwable {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO = new DpPoiBackCategoryDTO();
        // Simulate a null value by setting categoryId to null
        dpPoiBackCategoryDTO.setCategoryId(null);
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(dpPoiBackCategoryDTO));
        List<Integer> result = ModelUtils.extractBackCat(dpPoiDTO);
        // Expecting an empty list since the categoryId is null
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testExtractBackCatWhenBackMainCategoryPathContainsNonNull() throws Throwable {
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        DpPoiBackCategoryDTO dpPoiBackCategoryDTO = new DpPoiBackCategoryDTO();
        dpPoiBackCategoryDTO.setCategoryId(1);
        dpPoiDTO.setBackMainCategoryPath(Arrays.asList(dpPoiBackCategoryDTO));
        List<Integer> result = ModelUtils.extractBackCat(dpPoiDTO);
        assertEquals(Arrays.asList(1), result);
    }

    /**
     * Test when input DzProductVO is null
     */
    @Test
    public void testHasMagicalMemberPromo_NullDzProductVO() {
        // arrange
        DzProductVO dzProductVO = null;
        // act
        boolean result = ModelUtils.hasMagicalMemberPromo(dzProductVO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when priceBottomPromoTagList is null
     */
    @Test
    public void testHasMagicalMemberPromo_NullPromoTagList() {
        // arrange
        DzProductVO dzProductVO = new DzProductVO();
        dzProductVO.setPriceBottomPromoTagList(null);
        // act
        boolean result = ModelUtils.hasMagicalMemberPromo(dzProductVO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when priceBottomPromoTagList is empty
     */
    @Test
    public void testHasMagicalMemberPromo_EmptyPromoTagList() {
        // arrange
        DzProductVO dzProductVO = new DzProductVO();
        dzProductVO.setPriceBottomPromoTagList(new ArrayList<>());
        // act
        boolean result = ModelUtils.hasMagicalMemberPromo(dzProductVO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when DzProductVO has magical member tag
     */
    @Test
    public void testHasMagicalMemberPromo_HasMagicalMemberTag() {
        // arrange
        DzProductVO dzProductVO = new DzProductVO();
        DzTagVO magicalTag = new DzTagVO();
        magicalTag.setName(DzPromoUtils.MAGICAL_MEMBER_TAG_NAME);
        dzProductVO.setPriceBottomPromoTagList(Collections.singletonList(magicalTag));
        // act
        boolean result = ModelUtils.hasMagicalMemberPromo(dzProductVO);
        // assert
        assertTrue(result);
    }

    /**
     * Test when DzProductVO doesn't have magical member tag
     */
    @Test
    public void testHasMagicalMemberPromo_NoMagicalMemberTag() {
        // arrange
        DzProductVO dzProductVO = new DzProductVO();
        DzTagVO normalTag = new DzTagVO();
        normalTag.setName("Normal Tag");
        dzProductVO.setPriceBottomPromoTagList(Collections.singletonList(normalTag));
        // act
        boolean result = ModelUtils.hasMagicalMemberPromo(dzProductVO);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 responseVO 为 null 的情况
     */
    @Test
    public void testHasProductsResponseVONull() {
        assertFalse(ModelUtils.hasProducts(null));
    }

    /**
     * 测试 responseVO 不为 null，但 products 列表为空的情况
     */
    @Test
    public void testHasProductsProductsEmpty() {
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        assertFalse(ModelUtils.hasProducts(responseVO));
    }

    /**
     * 测试 responseVO 不为 null，products 列表不为空，但列表中的第一个元素为 null 的情况
     */
    @Test
    public void testHasProductsProductsFirstElementNull() {
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        responseVO.setProducts(Arrays.asList(null, new DzProductVO()));
        assertFalse(ModelUtils.hasProducts(responseVO));
    }

    /**
     * 测试 responseVO 不为 null，products 列表不为空，且列表中的第一个元素不为 null 的情况
     */
    @Test
    public void testHasProductsProductsFirstElementNotNull() {
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        responseVO.setProducts(Arrays.asList(new DzProductVO(), new DzProductVO()));
        assertTrue(ModelUtils.hasProducts(responseVO));
    }

    /**
     * 测试getProductExtAttrValue方法，当extAttrs为空时
     */
    @Test
    public void testGetProductExtAttrValueWhenExtAttrsIsNull() throws Throwable {
        // arrange
        String key = "testKey";
        // act
        String result = ModelUtils.getProductExtAttrValue(null, key);
        // assert
        assertNull(result);
    }

    /**
     * 测试getProductExtAttrValue方法，当extAttrs不为空，但没有name与key相同的AttrM对象时
     */
    @Test
    public void testGetProductExtAttrValueWhenNoMatchedAttr() throws Throwable {
        // arrange
        String key = "testKey";
        AttrM attr = new AttrM("otherKey", "testValue");
        // act
        String result = ModelUtils.getProductExtAttrValue(Arrays.asList(attr), key);
        // assert
        assertNull(result);
    }

    /**
     * 测试getProductExtAttrValue方法，当extAttrs不为空，且有name与key相同的AttrM对象时
     */
    @Test
    public void testGetProductExtAttrValueWhenMatchedAttrExists() throws Throwable {
        // arrange
        String key = "testKey";
        AttrM attr = new AttrM(key, "testValue");
        // act
        String result = ModelUtils.getProductExtAttrValue(Arrays.asList(attr), key);
        // assert
        assertEquals("testValue", result);
    }

    /**
     * 测试 hasProducts 方法，当 responseVO 是 DzFilterProductListVO 类型，且 products 不为空时，应返回 true
     */
    @Test
    public void testHasProductsWhenResponseVOIsDzFilterProductListVOAndProductsIsNotEmpty() {
        // arrange
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        responseVO.setProducts(Collections.singletonList(new com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO()));
        // act
        boolean result = ModelUtils.hasProducts(responseVO);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 hasProducts 方法，当 responseVO 是 DzFilterProductListVO 类型，但 products 为空时，应返回 false
     */
    @Test
    public void testHasProductsWhenResponseVOIsDzFilterProductListVOAndProductsIsEmpty() {
        // arrange
        DzFilterProductListVO responseVO = new DzFilterProductListVO();
        responseVO.setProducts(Collections.emptyList());
        // act
        boolean result = ModelUtils.hasProducts(responseVO);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 hasProducts 方法，当 responseVO 是 ProductListVO 类型，且 products 不为空时，应返回 true
     */
    @Test
    public void testHasProductsWhenResponseVOIsProductListVOAndProductsIsNotEmpty() {
        // arrange
        ProductListVO responseVO = new ProductListVO();
        responseVO.setProducts(Collections.singletonList(new com.sankuai.dzviewscene.shelf.platform.list.vo.ProductVO()));
        // act
        boolean result = ModelUtils.hasProducts(responseVO);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 hasProducts 方法，当 responseVO 是 ProductListVO 类型，但 products 为空时，应返回 false
     */
    @Test
    public void testHasProductsWhenResponseVOIsProductListVOAndProductsIsEmpty() {
        // arrange
        ProductListVO responseVO = new ProductListVO();
        responseVO.setProducts(Collections.emptyList());
        // act
        boolean result = ModelUtils.hasProducts(responseVO);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 hasProducts 方法，当 responseVO 不是 DzFilterProductListVO 或 ProductListVO 类型，且不为 null 时，应返回 true
     */
    @Test
    public void testHasProductsWhenResponseVOIsNotDzFilterProductListVOOrProductListVOAndIsNotNull() {
        // arrange
        Object responseVO = new Object();
        // act
        boolean result = ModelUtils.hasProducts(responseVO);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 hasProducts 方法，当 responseVO 不是 DzFilterProductListVO 或 ProductListVO 类型，且为 null 时，应返回 false
     */
    @Test
    public void testHasProductsWhenResponseVOIsNotDzFilterProductListVOOrProductListVOAndIsNull() {
        // arrange
        Object responseVO = null;
        // act
        boolean result = ModelUtils.hasProducts(responseVO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when filterBtnIdAndProAreasVO has product areas with non-empty items
     */
    @Test
    public void testProductAreasIsNotEmpty_WithNonEmptyProductArea() {
        // arrange
        FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO = new FilterBtnIdAndProAreasVO();
        ProductAreaComponentVO productArea = new ProductAreaComponentVO();
        DzItemAreaComponentVO itemArea = new DzItemAreaComponentVO();
        itemArea.setProductItems(Arrays.asList(new DzItemVO()));
        productArea.setItemArea(itemArea);
        filterBtnIdAndProAreasVO.setProductAreas(Arrays.asList(productArea));
        // act
        boolean result = ModelUtils.productAreasIsNotEmpty(filterBtnIdAndProAreasVO);
        // assert
        assertTrue(result);
    }

    /**
     * Test when filterBtnIdAndProAreasVO has product areas but all are empty
     */
    @Test
    public void testProductAreasIsNotEmpty_WithEmptyProductAreas() {
        // arrange
        FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO = new FilterBtnIdAndProAreasVO();
        ProductAreaComponentVO productArea = new ProductAreaComponentVO();
        DzItemAreaComponentVO itemArea = new DzItemAreaComponentVO();
        itemArea.setProductItems(Collections.emptyList());
        productArea.setItemArea(itemArea);
        filterBtnIdAndProAreasVO.setProductAreas(Arrays.asList(productArea));
        // act
        boolean result = ModelUtils.productAreasIsNotEmpty(filterBtnIdAndProAreasVO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when filterBtnIdAndProAreasVO has empty product areas list
     */
    @Test
    public void testProductAreasIsNotEmpty_WithEmptyProductAreasList() {
        // arrange
        FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO = new FilterBtnIdAndProAreasVO();
        filterBtnIdAndProAreasVO.setProductAreas(Collections.emptyList());
        // act
        boolean result = ModelUtils.productAreasIsNotEmpty(filterBtnIdAndProAreasVO);
        // assert
        assertFalse(result);
    }

    /**
     * Test when filterBtnIdAndProAreasVO is null
     */
    @Test
    public void testProductAreasIsNotEmpty_WithNullInput() {
        // arrange
        FilterBtnIdAndProAreasVO filterBtnIdAndProAreasVO = null;
        // act
        boolean result = ModelUtils.productAreasIsNotEmpty(filterBtnIdAndProAreasVO);
        // assert
        assertFalse(result);
    }

    /**
     * Tests hasMagicalMemberPromo method when shelfItemVO is null, should return false.
     */
    @Test
    public void testHasMagicalMemberPromoWhenShelfItemVOIsNull() throws Throwable {
        assertFalse(ModelUtils.hasMagicalMemberPromo((ShelfItemVO) null));
    }

    /**
     * Tests hasMagicalMemberPromo method when shelfItemVO is not null, but priceBottomTags is empty, should return false.
     */
    @Test
    public void testHasMagicalMemberPromoWhenPriceBottomTagsIsEmpty() throws Throwable {
        ShelfItemVO shelfItemVO = new ShelfItemVO();
        assertFalse(ModelUtils.hasMagicalMemberPromo(shelfItemVO));
    }

    /**
     * Tests hasMagicalMemberPromo method when shelfItemVO is not null, priceBottomTags is not empty, but priceBottomTags does not contain a ShelfTagVO object named MAGICAL_MEMBER_TAG_NAME, should return false.
     */
    @Test
    public void testHasMagicalMemberPromoWhenPriceBottomTagsDoesNotContainMagicalMemberTag() throws Throwable {
        ShelfItemVO shelfItemVO = new ShelfItemVO();
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setName("OTHER_TAG_NAME");
        shelfItemVO.setPriceBottomTags(Arrays.asList(shelfTagVO));
        assertFalse(ModelUtils.hasMagicalMemberPromo(shelfItemVO));
    }

    /**
     * Tests hasMagicalMemberPromo method when shelfItemVO is not null, priceBottomTags is not empty, and priceBottomTags contains a ShelfTagVO object named MAGICAL_MEMBER_TAG_NAME, should return true.
     */
    @Test
    public void testHasMagicalMemberPromoWhenPriceBottomTagsContainsMagicalMemberTag() throws Throwable {
        ShelfItemVO shelfItemVO = new ShelfItemVO();
        ShelfTagVO shelfTagVO = new ShelfTagVO();
        shelfTagVO.setName(DzPromoUtils.MAGICAL_MEMBER_TAG_NAME);
        shelfItemVO.setPriceBottomTags(Arrays.asList(shelfTagVO));
        assertTrue(ModelUtils.hasMagicalMemberPromo(shelfItemVO));
    }

    @Test
    public void testHasDegradeProductsWhenMapIsNull() throws Throwable {
        Map<String, ProductGroupM> groupNameProductGroupMMap = null;
        boolean result = ModelUtils.hasDegradeProducts(groupNameProductGroupMMap);
        assertFalse(result);
    }

    @Test
    public void testHasDegradeProductsWhenMapIsEmpty() throws Throwable {
        Map<String, ProductGroupM> groupNameProductGroupMMap = new HashMap<>();
        boolean result = ModelUtils.hasDegradeProducts(groupNameProductGroupMMap);
        assertFalse(result);
    }

    @Test
    public void testHasDegradeProductsWhenProductGroupMIsNull() throws Throwable {
        Map<String, ProductGroupM> groupNameProductGroupMMap = new HashMap<>();
        groupNameProductGroupMMap.put("key1", null);
        boolean result = ModelUtils.hasDegradeProducts(groupNameProductGroupMMap);
        assertFalse(result);
    }

    @Test
    public void testHasDegradeProductsWhenProductsIsEmpty() throws Throwable {
        Map<String, ProductGroupM> groupNameProductGroupMMap = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(null);
        groupNameProductGroupMMap.put("key1", productGroupM);
        boolean result = ModelUtils.hasDegradeProducts(groupNameProductGroupMMap);
        assertFalse(result);
    }

    @Test
    public void testHasDegradeProductsWhenProductsIsNotEmpty() throws Throwable {
        Map<String, ProductGroupM> groupNameProductGroupMMap = new HashMap<>();
        ProductGroupM productGroupM = new ProductGroupM();
        // Ensure the products list is not empty
        productGroupM.setProducts(new ArrayList<>());
        // Add a product to the list
        productGroupM.getProducts().add(new com.sankuai.dzviewscene.shelf.platform.common.model.ProductM());
        groupNameProductGroupMMap.put("key1", productGroupM);
        boolean result = ModelUtils.hasDegradeProducts(groupNameProductGroupMMap);
        assertTrue(result);
    }

    /**
     * Test scenario: ShelfGroupM has multiple product groups but all have empty products
     * Expected: Should return true when all product groups have empty products
     */
    @Test
    public void testHasNoProducts_AllProductGroupsEmpty() {
        // arrange
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        // Create first product group with null products list
        ProductGroupM group1 = new ProductGroupM();
        productGroups.put("group1", group1);
        // Create second product group with empty products list
        ProductGroupM group2 = new ProductGroupM();
        group2.setProducts(new ArrayList<>());
        productGroups.put("group2", group2);
        shelfGroupM.setProductGroupMs(productGroups);
        // act
        boolean result = ModelUtils.hasNoProducts(shelfGroupM);
        // assert
        Assert.assertTrue("Should return true when all product groups have empty products", result);
    }

    /**
     * Test scenario: ShelfGroupM has null ProductGroupM value in map
     * Expected: Should return true when all product groups are null or empty
     */
    @Test
    public void testHasNoProducts_NullProductGroupValue() {
        // arrange
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        productGroups.put("group1", null);
        shelfGroupM.setProductGroupMs(productGroups);
        // act
        boolean result = ModelUtils.hasNoProducts(shelfGroupM);
        // assert
        Assert.assertTrue("Should return true when product group value is null", result);
    }

    /**
     * Test scenario: ShelfGroupM has at least one product group with products
     * Expected: Should return false when any product group has products
     */
    @Test
    public void testHasNoProducts_HasProductsInGroup() {
        // arrange
        ShelfGroupM shelfGroupM = new ShelfGroupM();
        Map<String, ProductGroupM> productGroups = new HashMap<>();
        // Create first product group with products
        ProductGroupM group1 = new ProductGroupM();
        List<ProductM> products = new ArrayList<>();
        products.add(new ProductM());
        group1.setProducts(products);
        productGroups.put("group1", group1);
        // Create second product group with empty products
        ProductGroupM group2 = new ProductGroupM();
        group2.setProducts(new ArrayList<>());
        productGroups.put("group2", group2);
        shelfGroupM.setProductGroupMs(productGroups);
        // act
        boolean result = ModelUtils.hasNoProducts(shelfGroupM);
        // assert
        Assert.assertFalse("Should return false when any product group has products", result);
    }
}
