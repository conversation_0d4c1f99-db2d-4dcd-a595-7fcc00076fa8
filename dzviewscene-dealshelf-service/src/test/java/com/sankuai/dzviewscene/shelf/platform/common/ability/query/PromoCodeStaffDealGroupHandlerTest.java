package com.sankuai.dzviewscene.shelf.platform.common.ability.query;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.event.api.promoqrcode.PromoQRCodeResponse;
import com.dianping.gmkt.event.api.promoqrcode.dto.staffcode.StaffCodeDTO;
import com.dianping.technician.common.api.domain.TechnicianResp;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.technician.trade.api.product.dto.ShelfProductCategoryModel;
import com.sankuai.technician.trade.api.product.dto.ShelfProductItem;
import com.sankuai.technician.trade.api.product.dto.TechStandardShelfModule;
import com.sankuai.technician.trade.api.product.service.TechProductQueryService;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * @description :
 * @date : 2024/9/14
 */
@RunWith(MockitoJUnitRunner.class)
public class PromoCodeStaffDealGroupHandlerTest {

    @InjectMocks
    private PromoCodeStaffDealGroupHandler promoCodeStaffDealGroupHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ActivityContext getActivityContext() {
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam("entityId", "123");
        activityContext.addParam("platform", 2);
        activityContext.addParam("mtCityId", 1);
        activityContext.addParam("lng", 1.0);
        activityContext.addParam("lat", 1.0);
        activityContext.addParam("clientType", 1);
        activityContext.addParam("clientVersion", "1.0");
        activityContext.addParam("deviceId", "123");
        activityContext.addParam("userId", 1L);
        activityContext.addParam("shopId", 1L);
        activityContext.addParam("mtUserId", 1L);
        activityContext.addParam("dpUserId", 1L);
        activityContext.addParam("userAgent", 100);
        return activityContext;
    }

    /**
     * 测试query方法，当promoQRCodeCService返回的StaffCodeDTO为null时
     */
    @Test
    public void testQueryStaffCodeDTONull() throws ExecutionException, InterruptedException {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam("entityId", "123");
        when(compositeAtomService.getStaffCodeDTOByCodeId(anyLong())).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<ProductGroupM> result = promoCodeStaffDealGroupHandler.query(activityContext, "groupName", new HashMap<>());
        // assert
        assertNull(result.get());
    }

    /**
     * 测试query方法，当compositeAtomService返回的TechStandardShelfModule为null时
     */
    @Test
    public void testQueryCompositeAtomServiceReturnsNull() throws Throwable {
        // arrange
        ActivityContext activityContext = getActivityContext();
        PromoQRCodeResponse<StaffCodeDTO> response = new PromoQRCodeResponse<>();
        StaffCodeDTO staffCodeDTO = new StaffCodeDTO();
        staffCodeDTO.setTechnicianId(1L);
        response.setData(staffCodeDTO);
        when(compositeAtomService.getStaffCodeDTOByCodeId(anyLong())).thenReturn(CompletableFuture.completedFuture(response));
        when(compositeAtomService.queryStaffShelf(any())).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<ProductGroupM> result = promoCodeStaffDealGroupHandler.query(activityContext, "groupName", new HashMap<>());
        // assert
        assertNull(result.get());
    }

    /**
     * 测试query方法，当compositeAtomService返回的TechStandardShelfModule不为null，但getData()为null时
     */
    @Test
    public void testQueryCompositeAtomServiceReturnsNonNullButGetDataNull() throws Throwable {
        // arrange
        ActivityContext activityContext = getActivityContext();
        PromoQRCodeResponse<StaffCodeDTO> response = new PromoQRCodeResponse<>();
        StaffCodeDTO staffCodeDTO = new StaffCodeDTO();
        staffCodeDTO.setTechnicianId(1L);
        response.setData(staffCodeDTO);
        when(compositeAtomService.getStaffCodeDTOByCodeId(anyLong())).thenReturn(CompletableFuture.completedFuture(response));
        CompletableFuture<TechStandardShelfModule> future = CompletableFuture.completedFuture(new TechStandardShelfModule());
        when(compositeAtomService.queryStaffShelf(any())).thenReturn((CompletableFuture) future);
        // act
        CompletableFuture<ProductGroupM> result = promoCodeStaffDealGroupHandler.query(activityContext, "groupName", new HashMap<>());
        // assert
        try {
            ProductGroupM productGroupM = result.get();
            assertNull(productGroupM);
        } catch (InterruptedException | ExecutionException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试query方法，当compositeAtomService.queryStaffShelf返回的future异常，返回数据失败的情况
     */
    @Test
    public void testQueryFutureExceptionDataFail() throws Throwable {
        // arrange
        ActivityContext activityContext = getActivityContext();
        StaffCodeDTO staffCodeDTO = new StaffCodeDTO();
        staffCodeDTO.setTechnicianId(1L);
        PromoQRCodeResponse<StaffCodeDTO> response = new PromoQRCodeResponse<>();
        response.setData(staffCodeDTO);
        when(compositeAtomService.getStaffCodeDTOByCodeId(anyLong())).thenReturn(CompletableFuture.completedFuture(response));
        TechStandardShelfModule techStandardShelfModule = new TechStandardShelfModule();
        techStandardShelfModule.setTotalCount(1);
        TechnicianResp<TechStandardShelfModule> technicianResp = new TechnicianResp<>(TechnicianResp.SUCCESS, "OK", techStandardShelfModule);
        when(compositeAtomService.queryStaffShelf(any())).thenThrow(new RuntimeException());
        // act
        CompletableFuture<ProductGroupM> result = promoCodeStaffDealGroupHandler.query(activityContext, "groupName", new HashMap<>());
        // assert
        assertNull(result.get());
    }

    /**
     * 测试query方法，当compositeAtomService.queryStaffShelf返回的future成功，并且getData也成功返回数据时
     */
    @Test
    public void testQueryFutureSuccessDataSuccess() throws Throwable {
        // arrange
        ActivityContext activityContext = getActivityContext();
        StaffCodeDTO staffCodeDTO = new StaffCodeDTO();
        staffCodeDTO.setTechnicianId(1L);
        PromoQRCodeResponse<StaffCodeDTO> response = new PromoQRCodeResponse<>();
        response.setData(staffCodeDTO);
        when(compositeAtomService.getStaffCodeDTOByCodeId(anyLong())).thenReturn(CompletableFuture.completedFuture(response));
        TechStandardShelfModule techStandardShelfModule = new TechStandardShelfModule();
        techStandardShelfModule.setTotalCount(1);
        ShelfProductCategoryModel shelfProductCategoryModel = new ShelfProductCategoryModel();
        ShelfProductItem shelfProductItem = new ShelfProductItem();
        shelfProductItem.setProductId("1");
        shelfProductCategoryModel.setShelfProductItems(Lists.newArrayList(shelfProductItem));
        techStandardShelfModule.setTechProductCategoryModels(Lists.newArrayList(shelfProductCategoryModel));
        TechnicianResp<TechStandardShelfModule> technicianResp = new TechnicianResp<>(TechnicianResp.SUCCESS, "OK", techStandardShelfModule);
        CompletableFuture<TechnicianResp<TechStandardShelfModule>> future = CompletableFuture.completedFuture(technicianResp);
        when(compositeAtomService.queryStaffShelf(any())).thenReturn(future);
        // act
        CompletableFuture<ProductGroupM> result = promoCodeStaffDealGroupHandler.query(activityContext, "groupName", new HashMap<>());
        // assert
        assertNotNull(result);
        ProductGroupM productGroupM = result.get();
        assertNotNull(productGroupM);
        assertEquals(1, productGroupM.getTotal());
        assertEquals(1, productGroupM.getProducts().get(0).getProductId());
    }

    /**
     * Test when codeId is blank
     */
    @Test
    public void testQuery_WhenCodeIdIsBlank() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.entityId, "");
        // act
        CompletableFuture<ProductGroupM> result = promoCodeStaffDealGroupHandler.query(activityContext, "groupName", new HashMap<>());
        // assert
        assertNotNull(result);
        assertNull(result.get());
    }

    /**
     * Test exception handling
     */
    @Test
    public void testQuery_WhenExceptionOccurs() throws Throwable {
        // arrange
        ActivityContext activityContext = new ActivityContext();
        activityContext.addParam(ShelfActivityConstants.Params.entityId, "123");
        when(compositeAtomService.getStaffCodeDTOByCodeId(123L)).thenThrow(new RuntimeException("Test exception"));
        // act
        CompletableFuture<ProductGroupM> result = promoCodeStaffDealGroupHandler.query(activityContext, "groupName", new HashMap<>());
        // assert
        assertNull(result);
        verify(compositeAtomService).getStaffCodeDTOByCodeId(123L);
    }
}
