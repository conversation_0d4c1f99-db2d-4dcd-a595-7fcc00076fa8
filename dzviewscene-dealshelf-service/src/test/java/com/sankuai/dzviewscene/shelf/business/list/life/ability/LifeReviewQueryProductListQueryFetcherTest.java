package com.sankuai.dzviewscene.shelf.business.list.life.ability;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.exception.BusinessException;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class LifeReviewQueryProductListQueryFetcherTest {

    @Mock
    private ActivityContext ctx;

    private LifeReviewQueryProductListQueryFetcher fetcher;

    @Before
    public void setUp() {
        fetcher = new LifeReviewQueryProductListQueryFetcher();
    }

    /**
     * 测试 groupNames 参数为空的情况
     */
    @Test(expected = BusinessException.class)
    public void testBuildGroupNamesIsNull() throws Throwable {
        // arrange
        when(ctx.getParam("groupNames")).thenReturn(null);
        // act
        fetcher.build(ctx);
    }

    /**
     * 测试 productIdList 参数为空的情况
     */
    @Test
    public void testBuildProductIdListIsNull() throws Throwable {
        // arrange
        when(ctx.getParam("groupNames")).thenReturn(Arrays.asList("group1"));
        when(ctx.getParam("productIdList")).thenReturn(null);
        // act
        CompletableFuture<Map<String, ProductGroupM>> result = fetcher.build(ctx);
        // assert
        assertTrue(result.get().isEmpty());
    }

    /**
     * 测试 groupNames 和 productIdList 参数都不为空的情况
     */
    @Test
    public void testBuildNormal() throws Throwable {
        // arrange
        when(ctx.getParam("groupNames")).thenReturn(Arrays.asList("group1"));
        List<Integer> productIdList = Arrays.asList(1, 2, 3);
        when(ctx.getParam("productIdList")).thenReturn(productIdList);
        // act
        CompletableFuture<Map<String, ProductGroupM>> result = fetcher.build(ctx);
        // assert
        Map<String, ProductGroupM> map = result.get();
        assertEquals(1, map.size());
        assertTrue(map.containsKey("group1"));
    }
}
