package com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query;

import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.dianping.product.shelf.common.request.ShelfNavTabProductRequest;
import com.sankuai.dzviewscene.product.filterlist.model.FlagshipStoreM;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class ShelfQueryFetcher_PaddingShelfTypeAndStoreIdOfFlagshipStoreShelfTest {

    @InjectMocks
    private ShelfQueryFetcher shelfQueryFetcher;

    @Mock
    private AtomFacadeService atomFacadeService;

    public ShelfQueryFetcher_PaddingShelfTypeAndStoreIdOfFlagshipStoreShelfTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testPaddingShelfTypeAndStoreIdOfFlagshipStoreShelfWithEmptyStoreUUID() throws Throwable {
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.getSceneCode()).thenReturn("joy_flagship_store_hot_deal_module");
        when(activityContext.getParam(ShelfActivityConstants.Params.pageNo)).thenReturn(1);
        when(activityContext.getParam(ShelfActivityConstants.Params.pageSize)).thenReturn(10);
        ShelfNavTabProductRequest shelfRequest = new ShelfNavTabProductRequest();
        shelfQueryFetcher.paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(shelfRequest, activityContext);
        verify(activityContext, times(1)).getSceneCode();
    }

    @Test
    public void testPaddingShelfTypeAndStoreIdOfFlagshipStoreShelfWithException() throws Throwable {
        ActivityContext activityContext = mock(ActivityContext.class);
        FlagshipStoreM flagshipStoreM = new FlagshipStoreM();
        flagshipStoreM.setStoreId(1L);
        when(activityContext.getSceneCode()).thenReturn("joy_flagship_store_hot_deal_module");
        when(activityContext.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStore)).thenReturn(flagshipStoreM);
        when(activityContext.getParam(ShelfActivityConstants.Params.pageNo)).thenReturn(1);
        when(activityContext.getParam(ShelfActivityConstants.Params.pageSize)).thenReturn(10);
        when(atomFacadeService.getStoreIdByUUID(anyString())).thenThrow(new RuntimeException());
        ShelfNavTabProductRequest shelfRequest = new ShelfNavTabProductRequest();
        shelfQueryFetcher.paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(shelfRequest, activityContext);
        verify(activityContext, times(1)).getSceneCode();
    }

    @Test
    public void testPaddingShelfTypeAndStoreIdOfFlagshipStoreShelfSuccess() throws Throwable {
        ActivityContext activityContext = mock(ActivityContext.class);
        FlagshipStoreM flagshipStoreM = new FlagshipStoreM();
        flagshipStoreM.setStoreId(1L);
        when(activityContext.getSceneCode()).thenReturn("joy_flagship_store_hot_deal_module");
        when(activityContext.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStore)).thenReturn(flagshipStoreM);
        when(activityContext.getParam(ShelfActivityConstants.Params.pageNo)).thenReturn(1);
        when(activityContext.getParam(ShelfActivityConstants.Params.pageSize)).thenReturn(10);
        when(atomFacadeService.getStoreIdByUUID(anyString())).thenReturn(CompletableFuture.completedFuture(1L));
        ShelfNavTabProductRequest shelfRequest = new ShelfNavTabProductRequest();
        shelfQueryFetcher.paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(shelfRequest, activityContext);
        verify(activityContext, times(1)).getSceneCode();
    }

    @Test
    public void testPaddingShelfTypeAndStoreIdOfFlagshipStoreShelfNotFlagshipStoreReq() throws Throwable {
        ActivityContext activityContext = mock(ActivityContext.class);
        when(activityContext.getSceneCode()).thenReturn("not_flagship_store_scene");
        when(activityContext.getParam(anyString())).thenReturn(null);
        ShelfNavTabProductRequest shelfRequest = new ShelfNavTabProductRequest();
        shelfQueryFetcher.paddingShelfTypeAndStoreIdOfFlagshipStoreShelf(shelfRequest, activityContext);
        // No verification needed as the method is expected to return early for non-flagship store requests
        assertNotNull(activityContext);
    }
}
