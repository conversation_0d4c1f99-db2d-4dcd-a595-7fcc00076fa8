package com.sankuai.dzviewscene.shelf.platform.common.ability.query.handler.impl;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.lang.reflect.Field;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class TimesDealRelationHandler_BuildProductMTest {

    private TimesDealRelationHandler timesDealRelationHandler = new TimesDealRelationHandler();

    private boolean getTimesDealQueryFlagUsingReflection(ProductM productM) throws NoSuchFieldException, IllegalAccessException {
        Field timesDealQueryFlagField = ProductM.class.getDeclaredField("timesDealQueryFlag");
        timesDealQueryFlagField.setAccessible(true);
        return (boolean) timesDealQueryFlagField.get(productM);
    }

    /**
     * Tests the buildProductM method with a normal Long type value for dealId.
     */
    @Test
    public void testBuildProductM_NormalDealId() throws Throwable {
        Long dealId = 123L;
        ProductM productM = timesDealRelationHandler.buildProductM(dealId);
        Assert.assertNotNull(productM);
        Assert.assertEquals(dealId.intValue(), productM.getProductId());
        Assert.assertTrue(getTimesDealQueryFlagUsingReflection(productM));
    }

    /**
     * Tests the buildProductM method with null for dealId.
     * Expects a NullPointerException to be thrown.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildProductM_NullDealId() throws Throwable {
        Long dealId = null;
        timesDealRelationHandler.buildProductM(dealId);
    }
}
