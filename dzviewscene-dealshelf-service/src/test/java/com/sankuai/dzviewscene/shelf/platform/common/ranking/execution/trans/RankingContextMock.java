package com.sankuai.dzviewscene.shelf.platform.common.ranking.execution.trans;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.RankingContext;
import org.testng.collections.Maps;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by float.lu on 2020/11/7.
 */
public class RankingContextMock {

    public static RankingContext buildRankContext() {
        RankingContext rankingContext = new RankingContext();
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> map = Maps.newHashMap();
        map.put("tagName", Lists.newArrayList("疫苗"));
        params.put("extParams", map);
        rankingContext.setParams(params);
        return rankingContext;
    }
}
