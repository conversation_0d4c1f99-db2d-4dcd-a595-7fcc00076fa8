package com.sankuai.dzviewscene.shelf.business.shelf.maintitle;

import com.dianping.poi.relation.service.api.PoiRelationService;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.MedicalPoiMainTitleOpt;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.nibmp.decorate.media.query.thrift.api.HeadPicService;
import org.junit.Assert;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * Date:2024/5/13
 * Author：xzq
 * Description:
 */
public class MedicalPoiMainTitleOptTest {

    @Spy
    private HeadPicService headPicService;
    @Spy
    private PoiRelationService poiRelationService;
    @InjectMocks
    private MedicalPoiMainTitleOpt opt;



    @BeforeMethod
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test_compute() {
        ActivityCxt activityCxt = new ActivityCxt();
        MedicalPoiMainTitleOpt.Config config = new MedicalPoiMainTitleOpt.Config();
        Map<String,Object> map = new HashMap<>();
        ShopM shopM = new ShopM();
        shopM.setShopName("!11");
        map.put("platform",1);
        map.put("mtPoiIdL",1111L);
        map.put("dpPoiIdL",1111L);
        map.put("ctxShop",shopM);
        activityCxt.setParameters(map);
        config.setTitle("title");
        config.setTopTitle("subTitle");
        config.setTagColor("111");
        config.setTitleTagIcon("1111");
        MainTitleComponentVO compute = opt.compute(activityCxt, null, config);
        Assert.assertNotNull(compute);
    }


}
