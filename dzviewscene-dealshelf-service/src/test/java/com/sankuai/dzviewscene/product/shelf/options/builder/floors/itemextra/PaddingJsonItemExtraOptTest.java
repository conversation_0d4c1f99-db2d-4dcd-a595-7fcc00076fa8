package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemextra;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemExtraVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.apache.commons.collections.MapUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @since 2024/4/12 12:55
 */
public class PaddingJsonItemExtraOptTest {

    private PaddingJsonItemExtraOpt.Config config;
    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private ProductM productM;

    private PaddingJsonItemExtraOpt paddingJsonItemExtraOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        paddingJsonItemExtraOpt = new PaddingJsonItemExtraOpt();
        when(param.getProductM()).thenReturn(productM);
        config = new PaddingJsonItemExtraOpt.Config();
    }

    /**
     * 测试 compute 方法，当 config.getExtra() 不为空时，应返回 config.getExtra()
     */
    @Test
    public void testComputeConfigExtraIsNotEmpty() {
        // arrange
        String expected = "{\"a\":\"extra\"}";
        config.setExtra(expected);

        // act
        String result = paddingJsonItemExtraOpt.compute(activityCxt, param, config);

        // assert
        assertEquals(expected, result);
    }

    /**
     * 测试 compute 方法，当 config 中的 paddingDealCategoryId 为 true 时
     */
    @Test
    public void testComputePaddingDealCategoryIdTrue() {
        // arrange
        config.setPaddingDealCategoryId(true);
        when(productM.getCategoryId()).thenReturn(123);

        // act
        String result = paddingJsonItemExtraOpt.compute(activityCxt, param, config);

        // assert
        assertEquals("{\"dealCategoryId\":123}", result);
    }

    /**
     * 测试 compute 方法，当 config 中的 paddingSaleNum 为 true 且 productM.getSale() 不为 null 时
     */
    @Test
    public void testComputePaddingSaleNumTrueAndSaleNotNull() {
        // arrange
        config.setPaddingSaleNum(true);
        ProductSaleM sale = new ProductSaleM();
        sale.setSale(100);
        when(productM.getSale()).thenReturn(sale);

        // act
        String result = paddingJsonItemExtraOpt.compute(activityCxt, param, config);

        // assert
        assertEquals("{\"dealSaleNum\":100}", result);
    }

    /**
     * 测试 compute 方法，当 config 中的 productAttrKeys 不为空且 productM 中有对应的属性值时
     */
    @Test
    public void testComputeProductAttrKeysNotEmptyAndProductMHasAttr() {
        // arrange
        config.setProductAttrKeys(Arrays.asList("attr1", "attr2"));
        when(productM.getAttr("attr1")).thenReturn("value1");
        when(productM.getAttr("attr2")).thenReturn("value2");

        // act
        String result = paddingJsonItemExtraOpt.compute(activityCxt, param, config);

        // assert
        Map<String, Object> extraMap = JsonCodec.decode(result, Map.class);
        assertTrue("value1".equals(MapUtils.getString(extraMap, "attr1")));
        assertTrue("value2".equals(MapUtils.getString(extraMap, "attr2")));
    }

    /**
     * 测试 compute 方法，当 config 中的 appendMap 不为空时
     */
    @Test
    public void testComputeAppendMapNotEmpty() {
        // arrange
        HashMap<String, String> appendMap = new HashMap<>();
        appendMap.put("key1", "value1");
        appendMap.put("key2", "value2");
        config.setAppendMap(appendMap);

        // act
        String result = paddingJsonItemExtraOpt.compute(activityCxt, param, config);

        // assert
        Map<String, Object> extraMap = JsonCodec.decode(result, Map.class);
        assertTrue("value1".equals(MapUtils.getString(extraMap, "key1")));
        assertTrue("value2".equals(MapUtils.getString(extraMap, "key2")));
    }
}