package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;
import org.junit.Before;

@RunWith(MockitoJUnitRunner.class)
public class MassageMemberPromoTagBuildStrategyTest {

    @InjectMocks
    private MassageMemberPromoTagBuildStrategy massageMemberPromoTagBuildStrategy;

    @Mock
    private PriceBottomTagBuildReq req;

    @Mock
    private ProductM productM;

    @Mock
    private CardM cardM;

    @Mock
    private ProductPromoPriceM memberPromoM;

    @Mock
    private ProductPromoPriceM noMemberPromoM;

    @Test
    public void testBuildTagWhenCardMIsNull() throws Throwable {
        when(req.getCardM()).thenReturn(null);
        when(req.getProductM()).thenReturn(productM);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(memberPromoM, noMemberPromoM));
        DzTagVO result = massageMemberPromoTagBuildStrategy.buildTag(req);
        assertNull("Expected null result when CardM is null", result);
    }

    @Test
    public void testBuildTagWhenPromoPricesIsEmpty() throws Throwable {
        when(req.getProductM()).thenReturn(productM);
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        DzTagVO result = massageMemberPromoTagBuildStrategy.buildTag(req);
        assertNull("Expected null result when PromoPrices is empty", result);
    }

    @Test
    public void testBuildTagWhenMemberPromoPriceIsGreaterThanNoMemberPromoPrice() throws Throwable {
        when(req.getProductM()).thenReturn(productM);
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(memberPromoM, noMemberPromoM));
        DzTagVO result = massageMemberPromoTagBuildStrategy.buildTag(req);
        assertNull("Expected null result when MemberPromoPrice is greater than NoMemberPromoPrice", result);
    }
}
