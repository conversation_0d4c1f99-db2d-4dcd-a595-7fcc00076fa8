package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class ProductListConfigPriceAboveTagsOpt_ComputeTest {

    @InjectMocks
    private ProductListConfigPriceAboveTagsOpt opt;

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductPriceAboveTagVP.Param param;

    private ProductListConfigPriceAboveTagsOpt.Config config;

    @Mock
    private ProductM productM;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        config = new ProductListConfigPriceAboveTagsOpt.Config();
        when(param.getProductM()).thenReturn(productM);
    }

    @Test
    public void testComputeProductIsTopDisplayAndAttrKeysIsEmpty() throws Throwable {
        when(productM.getAttr("TOP_DISPLAY_PRODUCT")).thenReturn("true");
        config.setTopDisplayAttrKeys(Arrays.asList());
        List<DzTagVO> result = opt.compute(context, param, config);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeProductIsTopDisplayAndAttrValueIsEmpty() throws Throwable {
        when(productM.getAttr("TOP_DISPLAY_PRODUCT")).thenReturn("true");
        config.setTopDisplayAttrKeys(Arrays.asList("attrKey1", "attrKey2"));
        when(productM.getAttr("attrKey1")).thenReturn("");
        when(productM.getAttr("attrKey2")).thenReturn("");
        List<DzTagVO> result = opt.compute(context, param, config);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeProductIsNotTopDisplayAndAttrKeysIsEmpty() throws Throwable {
        when(productM.getAttr("TOP_DISPLAY_PRODUCT")).thenReturn("false");
        config.setAttrKeys(Arrays.asList());
        List<DzTagVO> result = opt.compute(context, param, config);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeProductIsNotTopDisplayAndAttrValueIsEmpty() throws Throwable {
        when(productM.getAttr("TOP_DISPLAY_PRODUCT")).thenReturn("false");
        config.setAttrKeys(Arrays.asList("attrKey1", "attrKey2"));
        when(productM.getAttr("attrKey1")).thenReturn("");
        when(productM.getAttr("attrKey2")).thenReturn("");
        List<DzTagVO> result = opt.compute(context, param, config);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testComputeProductIsNotTopDisplayAndAttrValueIsNotEmpty() throws Throwable {
        when(productM.getAttr("TOP_DISPLAY_PRODUCT")).thenReturn("false");
        config.setAttrKeys(Arrays.asList("attrKey1", "attrKey2"));
        when(productM.getAttr("attrKey1")).thenReturn("attrValue1");
        when(productM.getAttr("attrKey2")).thenReturn("attrValue2");
        Map<String, ProductListConfigPriceAboveTagsOpt.TagCfg> attrKey2Cfg = new HashMap<>();
        attrKey2Cfg.put("attrKey1", new ProductListConfigPriceAboveTagsOpt.TagCfg());
        attrKey2Cfg.put("attrKey2", new ProductListConfigPriceAboveTagsOpt.TagCfg());
        config.setAttrKey2Cfg(attrKey2Cfg);
        List<DzTagVO> result = opt.compute(context, param, config);
        assertEquals(2, result.size());
    }
}
