package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicalItemPriceAboveTagsOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ItemPriceAboveTagsVP.Param param;

    @Mock
    private MedicalItemPriceAboveTagsOpt.Config config;

    @Mock
    private DzTagVO configTag;

    @Mock
    private ProductM productM;

    @Test
    public void testComputeProductIsNull() throws Throwable {
        // arrange
        MedicalItemPriceAboveTagsOpt opt = new MedicalItemPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(null);
        // act
        List<DzTagVO> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeConfigTagIsNull() throws Throwable {
        // arrange
        MedicalItemPriceAboveTagsOpt opt = new MedicalItemPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(config.getConfigTag()).thenReturn(null);
        // act
        List<DzTagVO> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeMatchProductTypesIsEmpty() throws Throwable {
        // arrange
        MedicalItemPriceAboveTagsOpt opt = new MedicalItemPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        // act
        List<DzTagVO> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeMatchProductTypesNotContainsProductType() throws Throwable {
        // arrange
        MedicalItemPriceAboveTagsOpt opt = new MedicalItemPriceAboveTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        // act
        List<DzTagVO> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        MedicalItemPriceAboveTagsOpt opt = new MedicalItemPriceAboveTagsOpt();
        when(productM.getProductType()).thenReturn(1);
        when(param.getProductM()).thenReturn(productM);
        when(config.getMatchProductTypes()).thenReturn(Collections.singletonList(1));
        // Ensure configTag is not null
        when(config.getConfigTag()).thenReturn(configTag);
        // Mock necessary data for configTag
        // Mock necessary data for productM
        when(productM.getProductTags()).thenReturn(Collections.singletonList("Sample Tag"));
        // Corrected: Mock necessary data for productM to return a List<String> instead of String
        when(productM.getObjAttr(anyString())).thenReturn(Collections.singletonList("Sample Picture"));
        // act
        List<DzTagVO> result = opt.compute(context, param, config);
        // assert
        assertNotNull(result);
        // Ensure the result is not only non-null but also contains elements
        assertFalse(result.isEmpty());
    }
}
