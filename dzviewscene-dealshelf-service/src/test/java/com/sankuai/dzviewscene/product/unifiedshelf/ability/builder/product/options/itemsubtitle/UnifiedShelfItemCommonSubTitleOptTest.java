package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsubtitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ItemSubTitleVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.UnifiedShelfItemCommonSubTitleOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemSubTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.Assert;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

public class UnifiedShelfItemCommonSubTitleOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private UnifiedShelfItemCommonSubTitleOpt.Config config;
    @Mock
    private UnifiedShelfItemSubTitleVP.Param param;
    @Mock
    private ProductM productM;

    private UnifiedShelfItemCommonSubTitleOpt target;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        target = new UnifiedShelfItemCommonSubTitleOpt();
    }

    /**
     * 测试 compute 方法，当 config.isForceNull() 返回 true 时，应返回 null。
     */
    @Test
    public void testComputeWhenConfigIsForceNull() {
        // arrange
        when(config.isForceNull()).thenReturn(true);

        // act
        ItemSubTitleVO result = target.compute(context, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试 compute 方法，当 productTags 为空时，应返回 null。
     */
    @Test
    public void testComputeWhenProductTagsIsEmpty() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(Collections.emptyList());

        // act
        ItemSubTitleVO result = target.compute(context, param, config);

        // assert
        assertNull(result);
    }

    /**
     * 测试 compute 方法，当 productTags 非空时，应返回非 null。
     */
    @Test
    public void testComputeWhenProductTagsIsNotEmpty() {
        // arrange
        when(config.isForceNull()).thenReturn(false);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(Arrays.asList("Tag1", "Tag2"));

        // act
        ItemSubTitleVO result = target.compute(context, param, config);

        Assert.assertNotNull(result);
    }


}
