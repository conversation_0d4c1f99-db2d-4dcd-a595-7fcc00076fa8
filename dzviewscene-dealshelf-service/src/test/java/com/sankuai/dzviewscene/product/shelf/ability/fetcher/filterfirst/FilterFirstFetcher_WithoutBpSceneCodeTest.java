package com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class FilterFirstFetcher_WithoutBpSceneCodeTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private Config mockConfig;

    @Mock
    private DouHuM mockDouHuM;

    @Mock
    private ShopM mockShopM;

    private FilterFirstFetcher filterFirstFetcher;

    @Before
    public void setUp() {
        filterFirstFetcher = new FilterFirstFetcher();
        when(mockActivityCxt.getParam(PmfConstants.Ctx.ctxShop)).thenReturn(mockShopM);
    }

    @Test
    public void testWithoutBpSceneCode_Category2SceneIsEmpty() throws Throwable {
        // arrange
        when(mockConfig.getCategory2Scene()).thenReturn(null);
        // act
        boolean result = filterFirstFetcher.withoutBpSceneCode(mockActivityCxt, mockConfig);
        // assert
        assertFalse(result);
    }

    @Test
    public void testWithoutBpSceneCode_Category2SceneNotContainsCategoryId() throws Throwable {
        // arrange
        Map<Integer, String> category2Scene = new HashMap<>();
        category2Scene.put(2, "someScene");
        when(mockConfig.getCategory2Scene()).thenReturn(category2Scene);
        // Ensure categoryId does not match the key in category2Scene
        when(mockShopM.getCategory()).thenReturn(1);
        // act
        boolean result = filterFirstFetcher.withoutBpSceneCode(mockActivityCxt, mockConfig);
        // assert
        assertTrue(result);
    }

    @Test
    public void testWithoutBpSceneCode_Category2SceneContainsCategoryId() throws Throwable {
        // arrange
        Map<Integer, String> category2Scene = new HashMap<>();
        category2Scene.put(1, "someScene");
        when(mockConfig.getCategory2Scene()).thenReturn(category2Scene);
        // Ensure categoryId matches the key in category2Scene
        when(mockShopM.getCategory()).thenReturn(1);
        // act
        boolean result = filterFirstFetcher.withoutBpSceneCode(mockActivityCxt, mockConfig);
        // assert
        assertFalse(result);
    }
}
