package com.sankuai.dzviewscene.product.shelf.utils;

import org.junit.Assert;
import org.junit.Test;

public class PicRatioMathUtilsTest {

    @Test
    public void testConvertRatioToString() {
        // 测试 1:1
        Assert.assertEquals("1:1", PicRatioMathUtils.convertRatioToString(1.0));

        // 测试 3:4
        Assert.assertEquals("3:4", PicRatioMathUtils.convertRatioToString(3.0 / 4.0));

        // 测试 4:3
        Assert.assertEquals("4:3", PicRatioMathUtils.convertRatioToString(4.0 / 3.0));

        // 测试 16:9
        Assert.assertEquals("16:9", PicRatioMathUtils.convertRatioToString(16.0 / 9.0));

        // 测试 9:16
        Assert.assertEquals("9:16", PicRatioMathUtils.convertRatioToString(9.0 / 16.0));

        // 测试未知比例，返回默认值 "1:1"
        Assert.assertEquals("1:1", PicRatioMathUtils.convertRatioToString(2.0));

        // 测试接近 1:1 的值
        Assert.assertEquals("1:1", PicRatioMathUtils.convertRatioToString(1.01));
        Assert.assertEquals("1:1", PicRatioMathUtils.convertRatioToString(0.99));

        // 测试接近 3:4 的值
        Assert.assertEquals("3:4", PicRatioMathUtils.convertRatioToString(0.751));
        Assert.assertEquals("3:4", PicRatioMathUtils.convertRatioToString(0.749));

        // 测试接近 4:3 的值
        Assert.assertEquals("4:3", PicRatioMathUtils.convertRatioToString(1.333));
        Assert.assertEquals("4:3", PicRatioMathUtils.convertRatioToString(1.334));

        // 测试接近 16:9 的值
        Assert.assertEquals("16:9", PicRatioMathUtils.convertRatioToString(1.777));
        Assert.assertEquals("16:9", PicRatioMathUtils.convertRatioToString(1.778));

        // 测试接近 9:16 的值
        Assert.assertEquals("9:16", PicRatioMathUtils.convertRatioToString(0.562));
        Assert.assertEquals("9:16", PicRatioMathUtils.convertRatioToString(0.563));
    }
}
