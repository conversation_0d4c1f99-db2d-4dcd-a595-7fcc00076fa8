package com.sankuai.dzviewscene.product.shelf.options.maintitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.maintitle.vp.DealMainTitleVP.Param;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.MedicalPoiMainTitleOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.MainTitleComponentVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.nibmp.decorate.media.query.thrift.api.HeadPicService;
import com.sankuai.nibmp.decorate.media.query.thrift.dto.GetPCHeadPicReqDTO;
import com.sankuai.nibmp.decorate.media.query.thrift.dto.GetPCHeadPicRespDTO;
import com.sankuai.nibmp.decorate.media.query.thrift.dto.part.HeadPicInfo;
import com.sankuai.nibmp.decorate.media.query.thrift.dto.part.HeadPicItemInfo;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicalPoiMainTitleOptTest {

    @InjectMocks
    private MedicalPoiMainTitleOpt opt;

    @Mock
    private HeadPicService headPicService;

    /**
     * Test compute method under normal conditions.
     */
    @Test
    public void testComputeNormal() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        Param param = Mockito.mock(Param.class);
        Config config = Mockito.mock(Config.class);
        ShopM shopM = Mockito.mock(ShopM.class);
        Mockito.when(context.getParam("platform")).thenReturn(1);
        Mockito.when(context.getParam("mtPoiIdL")).thenReturn(1111L);
        Mockito.when(context.getParam("dpPoiIdL")).thenReturn(1111L);
        Mockito.when(context.getParam("ctxShop")).thenReturn(shopM);
        Mockito.when(config.getTitle()).thenReturn("title");
        Mockito.when(config.getTopTitle()).thenReturn("subTitle");
        Mockito.when(config.getTagColor()).thenReturn("111");
        Mockito.when(config.getTitleTagIcon()).thenReturn("1111");
        GetPCHeadPicRespDTO headPicRespDTO = new GetPCHeadPicRespDTO();
        HeadPicInfo headPicInfo = new HeadPicInfo();
        HeadPicItemInfo headPicItemInfo = new HeadPicItemInfo();
        headPicItemInfo.setUrl("http://example.com/pic.jpg");
        headPicInfo.setList(Collections.singletonList(headPicItemInfo));
        headPicRespDTO.setHeadPic(headPicInfo);
        Mockito.when(headPicService.getPCHeadPicInfo(any(GetPCHeadPicReqDTO.class))).thenReturn(headPicRespDTO);
        MainTitleComponentVO result = opt.compute(context, param, config);
        Assert.assertNotNull(result);
        Assert.assertEquals("title", result.getTitle());
    }

    /**
     * Test compute method with null parameters, expecting a NullPointerException.
     */
    @Test(expected = NullPointerException.class)
    public void testComputeException() throws Throwable {
        opt.compute(null, null, null);
    }

    /**
     * Test compute method with boundary conditions.
     */
    @Test
    public void testComputeBoundary() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        Param param = Mockito.mock(Param.class);
        Config config = Mockito.mock(Config.class);
        ShopM shopM = Mockito.mock(ShopM.class);
        Mockito.when(context.getParam("platform")).thenReturn(1);
        Mockito.when(context.getParam("mtPoiIdL")).thenReturn(1111L);
        Mockito.when(context.getParam("dpPoiIdL")).thenReturn(1111L);
        Mockito.when(context.getParam("ctxShop")).thenReturn(shopM);
        Mockito.when(config.getTitle()).thenReturn(null);
        Mockito.when(config.getTopTitle()).thenReturn("subTitle");
        Mockito.when(config.getTagColor()).thenReturn("111");
        Mockito.when(config.getTitleTagIcon()).thenReturn("1111");
        GetPCHeadPicRespDTO headPicRespDTO = new GetPCHeadPicRespDTO();
        HeadPicInfo headPicInfo = new HeadPicInfo();
        HeadPicItemInfo headPicItemInfo = new HeadPicItemInfo();
        headPicItemInfo.setUrl("http://example.com/pic.jpg");
        headPicInfo.setList(Collections.singletonList(headPicItemInfo));
        headPicRespDTO.setHeadPic(headPicInfo);
        Mockito.when(headPicService.getPCHeadPicInfo(any(GetPCHeadPicReqDTO.class))).thenReturn(headPicRespDTO);
        MainTitleComponentVO result = opt.compute(context, param, config);
        Assert.assertNotNull(result);
        Assert.assertNull(result.getTitle());
    }
}
