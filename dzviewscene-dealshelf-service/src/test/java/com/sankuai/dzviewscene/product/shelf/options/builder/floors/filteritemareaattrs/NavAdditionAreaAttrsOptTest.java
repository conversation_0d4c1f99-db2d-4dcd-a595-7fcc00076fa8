package com.sankuai.dzviewscene.product.shelf.options.builder.floors.filteritemareaattrs;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.FilterItemAreaAttrsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.AttrVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class NavAdditionAreaAttrsOptTest {

    private NavAdditionAreaAttrsOpt navAdditionAreaAttrsOpt;

    private ActivityCxt context;

    private FilterItemAreaAttrsVP.Param param;

    @Before
    public void setUp() {
        navAdditionAreaAttrsOpt = new NavAdditionAreaAttrsOpt();
        context = mock(ActivityCxt.class);
        param = mock(FilterItemAreaAttrsVP.Param.class);
    }

    @Test
    public void testComputeProductListIsNull() {
        when(param.getProducts()).thenReturn(null);
        List<AttrVO> result = navAdditionAreaAttrsOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeAdditionAttrIsNull() {
        List<ProductM> productList = new ArrayList<>();
        productList.add(mock(ProductM.class));
        when(param.getProducts()).thenReturn(productList);
        List<AttrVO> result = navAdditionAreaAttrsOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeAdditionAttrMapIsNull() {
        List<ProductM> productList = new ArrayList<>();
        ProductM product = mock(ProductM.class);
        when(product.getAttr(anyString())).thenReturn("{}");
        productList.add(product);
        when(param.getProducts()).thenReturn(productList);
        List<AttrVO> result = navAdditionAreaAttrsOpt.compute(context, param, null);
        assertNull(result);
    }

    @Test
    public void testComputeNormalCase() {
        List<ProductM> productList = new ArrayList<>();
        ProductM product = mock(ProductM.class);
        when(product.getAttr(anyString())).thenReturn("{\"key\":\"value\"}");
        productList.add(product);
        when(param.getProducts()).thenReturn(productList);
        List<AttrVO> result = navAdditionAreaAttrsOpt.compute(context, param, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("key：", result.get(0).getName());
        assertEquals("value", result.get(0).getValue());
    }
}
