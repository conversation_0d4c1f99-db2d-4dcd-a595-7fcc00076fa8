package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsale;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSaleVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ResvItemSaleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param param;

    @Mock
    private ProductM productM;

    @Mock
    private ProductSaleM productSaleM;

    @Test
    public void testComputeParamInvalid() {
        ResvItemSaleOpt resvItemSaleOpt = new ResvItemSaleOpt();
        assertNull(resvItemSaleOpt.compute(context, null, new ResvItemSaleOpt.Config()));
    }

    @Test
    public void testComputeProductMNull() {
        ResvItemSaleOpt resvItemSaleOpt = new ResvItemSaleOpt();
        when(param.getProductM()).thenReturn(null);
        assertNull(resvItemSaleOpt.compute(context, param, new ResvItemSaleOpt.Config()));
    }

    @Test
    public void testComputeProductSaleMNull() {
        ResvItemSaleOpt resvItemSaleOpt = new ResvItemSaleOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getSale()).thenReturn(null);
        assertNull(resvItemSaleOpt.compute(context, param, new ResvItemSaleOpt.Config()));
    }

    @Test
    public void testComputeSaleLessThanShowMin() {
        ResvItemSaleOpt resvItemSaleOpt = new ResvItemSaleOpt();
        ResvItemSaleOpt.Config config = new ResvItemSaleOpt.Config();
        config.setShowMin(10);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getSale()).thenReturn(productSaleM);
        when(productSaleM.getSale()).thenReturn(5);
        assertNull(resvItemSaleOpt.compute(context, param, config));
    }

    @Test
    public void testComputeSaleGreaterThanShowMin() {
        ResvItemSaleOpt resvItemSaleOpt = new ResvItemSaleOpt();
        ResvItemSaleOpt.Config config = new ResvItemSaleOpt.Config();
        config.setShowMin(5);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getSale()).thenReturn(productSaleM);
        when(productSaleM.getSale()).thenReturn(10);
        when(productSaleM.getSaleTag()).thenReturn("saleTag");
        assertEquals("saleTag", resvItemSaleOpt.compute(context, param, config));
    }

    @Test
    public void testComputeWithGroupName2SuffixMap() {
        ResvItemSaleOpt resvItemSaleOpt = new ResvItemSaleOpt();
        ResvItemSaleOpt.Config config = new ResvItemSaleOpt.Config();
        int sale = 100;
        String groupName = "testGroup";
        String suffix = "已获价";
        Map<String, String> map = Maps.newHashMap();
        map.put(groupName, suffix);
        config.setGroupName2SuffixMap(map);
        when(param.getProductM()).thenReturn(productM);
        when(param.getGroupName()).thenReturn(groupName);
        when(productM.getSale()).thenReturn(productSaleM);
        when(productSaleM.getSale()).thenReturn(sale);

        String result = resvItemSaleOpt.compute(context, param, config);

        assertEquals(sale + suffix, result);
    }
}
