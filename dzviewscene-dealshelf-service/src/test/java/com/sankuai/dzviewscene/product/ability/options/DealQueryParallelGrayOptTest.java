package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryParallelGrayOptTest {

    @Mock
    private ActivityCxt activityCxt;

    private DealQueryParallelGrayOpt dealQueryParallelGrayOpt;
    private DealQueryParallelGrayOpt.Config config;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        dealQueryParallelGrayOpt = new DealQueryParallelGrayOpt();
        config = new DealQueryParallelGrayOpt.Config();
    }

    /**
     * 测试灰度百分比内命中
     */
    @Test
    public void testCompute_GrayPercentHit() {
        // arrange
        config.setGrayPercent(50);
        when(activityCxt.getParam(ShelfActivityConstants.Params.dpPoiIdL)).thenReturn(25L);

        // act
        CompletableFuture<Object> result = dealQueryParallelGrayOpt.compute(activityCxt, null, config);

        // assert
        assertTrue((Boolean) result.join());
    }

    /**
     * 测试灰度百分比外未命中
     */
    @Test
    public void testCompute_GrayPercentMiss() {
        // arrange
        config.setGrayPercent(50);
        when(activityCxt.getParam(ShelfActivityConstants.Params.dpPoiIdL)).thenReturn(75L);

        // act
        CompletableFuture<Object> result = dealQueryParallelGrayOpt.compute(activityCxt, null, config);

        // assert
        assertFalse((Boolean) result.join());
    }

    /**
     * 测试白名单内命中
     */
    @Test
    public void testCompute_WhiteListHit() {
        // arrange
        config.setWhiteShopIds(Arrays.asList(100L, 200L));
        when(activityCxt.getParam(ShelfActivityConstants.Params.dpPoiIdL)).thenReturn(100L);

        // act
        CompletableFuture<Object> result = dealQueryParallelGrayOpt.compute(activityCxt, null, config);

        // assert
        assertTrue((Boolean) result.join());
    }

    /**
     * 测试白名单外未命中
     */
    @Test
    public void testCompute_WhiteListMiss() {
        // arrange
        config.setWhiteShopIds(Arrays.asList(100L, 200L));
        when(activityCxt.getParam(ShelfActivityConstants.Params.dpPoiIdL)).thenReturn(300L);

        // act
        CompletableFuture<Object> result = dealQueryParallelGrayOpt.compute(activityCxt, null, config);

        // assert
        assertFalse((Boolean) result.join());
    }

    /**
     * 测试参数为空时的行为
     */
    @Test
    public void testCompute_NullParams() {
        // arrange
        //when(activityCxt.getParameters()).thenReturn(null);

        // act
        CompletableFuture<Object> result = dealQueryParallelGrayOpt.compute(activityCxt, null, config);

        // assert
        assertFalse((Boolean) result.join());
    }
}
