package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModel;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrsModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.struct.DealDetailStructAttrListBuilder;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.model.StructAttrItemModel;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class StructAttrStrategyTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assambleParam;

    private StructAttrStrategy structAttrStrategy = new StructAttrStrategy();

    @Test
    public void testBuildModelVONullStructAttrModel() throws Throwable {
        when(activityCxt.getSource(DealDetailStructAttrListBuilder.CODE)).thenReturn(null);
        DealDetailModuleVO result = structAttrStrategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVONullStructAttrsModel() throws Throwable {
        List<DealDetailStructAttrModel> structAttrModels = Collections.singletonList(new DealDetailStructAttrModel());
        when(activityCxt.getSource(DealDetailStructAttrListBuilder.CODE)).thenReturn(structAttrModels);
        DealDetailModuleVO result = structAttrStrategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVONullStructAttrModels() throws Throwable {
        DealDetailStructAttrModel structAttrModel = new DealDetailStructAttrModel();
        // Simulate empty StructAttrsModels
        structAttrModel.setStructAttrsModels(Collections.emptyList());
        when(activityCxt.getSource(DealDetailStructAttrListBuilder.CODE)).thenReturn(Collections.singletonList(structAttrModel));
        DealDetailModuleVO result = structAttrStrategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVONullMainAttrMap() throws Throwable {
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        // Simulate empty StructAttrModels
        structAttrsModel.setStructAttrModels(Collections.emptyList());
        DealDetailStructAttrModel structAttrModel = new DealDetailStructAttrModel();
        structAttrModel.setStructAttrsModels(Collections.singletonList(structAttrsModel));
        when(activityCxt.getSource(DealDetailStructAttrListBuilder.CODE)).thenReturn(Collections.singletonList(structAttrModel));
        DealDetailModuleVO result = structAttrStrategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVONullStructAttrModuleVOS() throws Throwable {
        // This test case is effectively the same as testBuildModelVONullMainAttrMap due to the setup
        // It's included for completeness but highlights the need for distinct test scenarios
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        // Simulate empty StructAttrModels
        structAttrsModel.setStructAttrModels(Collections.emptyList());
        DealDetailStructAttrModel structAttrModel = new DealDetailStructAttrModel();
        structAttrModel.setStructAttrsModels(Collections.singletonList(structAttrsModel));
        when(activityCxt.getSource(DealDetailStructAttrListBuilder.CODE)).thenReturn(Collections.singletonList(structAttrModel));
        DealDetailModuleVO result = structAttrStrategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull(result);
    }

    @Test
    public void testBuildModelVONonNullStructAttrModuleVOS() throws Throwable {
        // Assuming a valid setup that would lead to a non-null result
        // This setup is hypothetical and needs to be adjusted based on actual conditions that lead to a non-null result
        StructAttrsModel structAttrsModel = new StructAttrsModel();
        // Simulate non-empty StructAttrModels
        structAttrsModel.setStructAttrModels(Collections.singletonList(new StructAttrItemModel()));
        DealDetailStructAttrModel structAttrModel = new DealDetailStructAttrModel();
        structAttrModel.setStructAttrsModels(Collections.singletonList(structAttrsModel));
        when(activityCxt.getSource(DealDetailStructAttrListBuilder.CODE)).thenReturn(Collections.singletonList(structAttrModel));
        DealDetailModuleVO result = structAttrStrategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNotNull(result);
    }
}
