package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import java.util.Collections;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduClassItemFromSkuOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private EduClassItemFromSkuOpt.Param param;

    @Mock
    private EduClassItemFromSkuOpt.Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @InjectMocks
    private EduClassItemFromSkuOpt opt;

    @Test
    public void testCompute_paramIsNull() throws Throwable {
        // Setup
        // Test and Verify
        try {
            opt.compute(context, null, config);
            fail("Expected NullPointerException when param is null");
        } catch (NullPointerException e) {
            // Expected exception
        }
    }

    @Test
    public void testCompute_dealDetailInfoModelIsNull() throws Throwable {
        // Setup
        when(config.isJustShowOnShortCourse()).thenReturn(true);
        when(param.getDealDetailInfoModel()).thenReturn(null);
        // Test and Verify
        try {
            opt.compute(context, param, config);
            fail("Expected NullPointerException when dealDetailInfoModel is null");
        } catch (NullPointerException e) {
            // Expected exception
        }
    }
}
