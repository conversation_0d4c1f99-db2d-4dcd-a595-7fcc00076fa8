package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceBottomTagsVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildStrategyFactory;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo.*;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.*;
import com.sankuai.dzviewscene.product.shelf.utils.ParamsUtil;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.*;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ModelUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Maps;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConfigItemPriceBottomTagsOptTest {

    @InjectMocks
    private ConfigItemPriceBottomTagsOpt configItemPriceBottomTagsOpt;

    @Mock
    private PriceBottomTagBuildStrategyFactory strategyFactory;

    private ActivityCxt activityCxt;

    private ItemPriceBottomTagsVP.Param param;

    private MockedStatic<PromoSimplifyUtils> mockedStatic;

    @Before
    public void setUp() {
        activityCxt = new ActivityCxt();
        activityCxt.addParam(ShelfActivityConstants.Params.userAgent, 200);
        mockedStatic = Mockito.mockStatic(PromoSimplifyUtils.class);

        param =  mockParam();
        when(strategyFactory.getBuildStrategy("神会员")).thenReturn(new MagicalMemberPromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("预售")).thenReturn(new PreSalePromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("足疗会员")).thenReturn(new MassageMemberPromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("彩虹活动秒杀")).thenReturn(new SecKillPromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("营销曝光秒杀")).thenReturn(new ExposureSecKillPromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("限时秒杀")).thenReturn(new GeneralSecKillPromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("其他优惠")).thenReturn(new OtherPromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("会员专属")).thenReturn(new MemberExclusivePromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("丽人会员")).thenReturn(new BeautyMemberPromoTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("商家会员")).thenReturn(new MerchantMemberPromoTagBuildStrategy());

        when(strategyFactory.getBuildStrategy("买贵必赔")).thenReturn(new PriceGuaranteeTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("全网低价")).thenReturn(new LowestPriceTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("空间比价")).thenReturn(new SpacePriceTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("时间比价")).thenReturn(new TimePriceTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("价保")).thenReturn(new PriceProtectionTagBuildStrategy());
        when(strategyFactory.getBuildStrategy("价格力标签")).thenReturn(new PricePowerTagBuildStrategy());
    }

    @After
    public void tearDown() {
        mockedStatic.close();
    }

    @Test
    public void test_magicalMember() {
        ConfigItemPriceBottomTagsOpt.Config config = mockConfig1();
        List<DzTagVO> dzTagVOS = configItemPriceBottomTagsOpt.compute(activityCxt, param, config);
        Assert.assertNotNull(dzTagVOS);
        DzItemVO dzItemVO = new DzItemVO();
        dzItemVO.setPriceBottomTags(dzTagVOS);
        boolean hasMagicalMemberPromo = ModelUtils.hasMagicalMemberPromo(dzItemVO);
        Assert.assertTrue(hasMagicalMemberPromo);
    }

    @Test
    public void test_otherPromo() {
        ConfigItemPriceBottomTagsOpt.Config config = mockConfig2();
        List<DzTagVO> dzTagVOS = configItemPriceBottomTagsOpt.compute(activityCxt, param, config);
        Assert.assertNotNull(dzTagVOS);
    }

    @Test
    public void test_presale() {
        param = mockParam1();
        ConfigItemPriceBottomTagsOpt.Config config = mockConfig2();
        List<DzTagVO> dzTagVOS = configItemPriceBottomTagsOpt.compute(activityCxt, param, config);
        Assert.assertNotNull(dzTagVOS);
    }

    @Test
    public void test_skill() {
        param = mockParam2();
        mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplifyV2(activityCxt)).thenReturn(true);
        List<DzTagVO> dzTagVOS = configItemPriceBottomTagsOpt.compute(activityCxt, param, mockConfig3());
        Assert.assertNotNull(dzTagVOS);
    }

    @Test
    public void test_param_object_util() {
        Map<String, Object> params = Maps.newHashMap();
        params.put("magicMemberFilterGrayConfig", new MagicMemberFilterGrayConfig());
        MagicMemberFilterGrayConfig config = ParamsUtil.getObjectSafely(params, "magicMemberFilterGrayConfig", new TypeReference<MagicMemberFilterGrayConfig>(){});
        Assert.assertNotNull(config);
    }

    private ConfigItemPriceBottomTagsOpt.Param mockParam() {
        String str = "{\"actProductId\":1022025664,\"actProductType\":1,\"activities\":[],\"basePrice\":200,\"basePriceTag\":\"200\",\"beginDate\":0,\"categoryId\":0,\"compositeScore\":0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"attr_shopRecommend\",\"value\":\"false\"},{\"name\":\"common_consumer_coupon_assign\",\"value\":\"false\"},{\"name\":\"service_type\",\"value\":\"足疗\"},{\"name\":\"calcName\",\"value\":\"60分钟足疗\"},{\"name\":\"standardNameDealGroup\",\"value\":\"1\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1022025664&poiid=607472195&shopuuid=&nailexhibitid=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoU_fCgm4_weIw0ZVKB2sV1PDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO364LrYkUB-Ndn82LZtHADKYFEJoJLg-lgPm1vYsV66q0lKTGwhtXpN9pvOQBXHyPAxIgLKrYCfszqixfxyF2QNXIzyzErchegwjEYFbPCNJjCgxiUjgMoMRyWEj6oj0_-F27dMz-0iCCldPPuSZiAIzPzDm6W8J8-Y3MroozTDaLJCwBZk-_nfQZnVtrzasZYbOB_r75nGKE8hO7845RkQYqnmEFiNKbFm2PR1DZKAy1zFr_EjRiPw6COP-IPrDse70-voeMWFdxJ6_dSKSw7z4Iw1zpmIBuYGlNZfcryV11n64Zxo8oPsUysjVDndNDFwfmzLdRogX16P-22NOtPaDvbPF28TYbaEIY7DaTMp7sI0hapyH9J0JcWH0Sg4jYeXqNuyNjY6a8X5K8X9huhKyf8U_DNjXqB-oElMFIJDM7mh8o-cnSMbIYTqbKSgJbSlmGSFqzfMCjEw6DxdTo8uiGFQVrXvXnAGZBYkOmyQAtsKroPaRL07-VDsIt6Kn5S7jl6pnQvXlsPvOFH5Zj97JKyL42yIY5TpiNY2ToWpSMKOLrbZ_Vi37uDpORRcVWTymZoQsC_rFGJJanDUwiS7lSMivyGl-mtVA6PQbXHfww\",\"marketPrice\":\"250\",\"orderUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage&dealid=1022025664&shopid=607472195&pagesource=poiShelf&expid=directPage&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoU_fCgm4_weIw0ZVKB2sV1PDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO364LrYkUB-Ndn82LZtHADKYFEJoJLg-lgPm1vYsV66q0lKTGwhtXpN9pvOQBXHyPAxIgLKrYCfszqixfxyF2QNXIzyzErchegwjEYFbPCNJjCgxiUjgMoMRyWEj6oj0_-F27dMz-0iCCldPPuSZiAIzPzDm6W8J8-Y3MroozTDaLJCwBZk-_nfQZnVtrzasZYbOB_r75nGKE8hO7845RkQYqnmEFiNKbFm2PR1DZKAy1zFr_EjRiPw6COP-IPrDse70-voeMWFdxJ6_dSKSw7z4Iw1zpmIBuYGlNZfcryV11n64Zxo8oPsUysjVDndNDFwfmzLdRogX16P-22NOtPaDvbPF28TYbaEIY7DaTMp7sI0hapyH9J0JcWH0Sg4jYeXqNuyNjY6a8X5K8X9huhKyf8U_DNjXqB-oElMFIJDM7mh8o-cnSMbIYTqbKSgJbSlmGSFqzfMCjEw6DxdTo8uiGFQVrXvXnAGZBYkOmyQAtsKroPaRL07-VDsIt6Kn5S7jl6pnQvXlsPvOFH5Zj97JKyL42yIY5TpiNY2ToWpSMKOLrbZ_Vi37uDpORRcVWTymZoQsC_rFGJJanDUwiS7lSMivyGl-mtVA6PQbXHfww\",\"orderUsers\":[],\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/6196cb24739d3a2c6915614d76dfe63162745.png\",\"productId\":1022025664,\"productTagList\":[],\"productTags\":[\"60分钟\",\"足部\"],\"productType\":1,\"promoPrices\":[{\"discount\":0.76,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"https://p1.meituan.net/travelcube/83518c6e14ec54a491bac580cb43d2fe4222.png\",\"marketPrice\":\"250\",\"pricePromoInfoMap\":{\"1\":[{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046393\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046392\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390308\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046391\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390307\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046390\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390306\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046389\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390305\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046388\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390304\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046387\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473619231\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046386\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"icon\":\"神会员券icon\",\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoItemTextDTO\":{\"icon\":\"神会员券icon\",\"promoDivideType\":\"PLATFORM_COUPON\",\"subTitle\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"title\":\"美团优惠券\"},\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0}]},\"promoItemList\":[{\"amount\":50,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":1022025664,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":50,\"promoTag\":\"-¥50\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1},{\"amount\":9,\"canAssign\":false,\"desc\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"PLATFORM_COUPON\",\"subTitle\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"title\":\"美团优惠券\"},\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\",\"CAN_INFLATE\":\"true\",\"AFTER_INFLATE\":\"false\",\"BIZ_TOKEN\":\"1111\"},\"remainStock\":0,\"sourceType\":1},{\"amount\":3,\"canAssign\":false,\"desc\":\"满25元减3元\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"newUser\":false,\"promoId\":66051,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"GOVERNMENT_CONSUME_COUPON\",\"subTitle\":\"满25元减3元\",\"title\":\"政府消费券\"},\"promoPrice\":3,\"promoTag\":\"-¥3\",\"promoType\":\"政府消费券\",\"promoTypeCode\":14,\"remainStock\":0,\"sourceType\":0}],\"promoPrice\":188,\"promoPriceTag\":\"188\",\"promoQuantityLimit\":0,\"promoTag\":\"共省¥62\",\"promoTagType\":60,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":62,\"totalPromoPriceTag\":\"-¥62\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":20,\"saleTag\":\"半年消费20\"},\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"stock\":{\"remainStock\":99999956,\"soldOut\":false,\"totalStock\":*********},\"timesDeal\":false,\"title\":\"【神会员】200｜60分钟足疗\",\"top\":false,\"tradeType\":3,\"unifyProduct\":false,\"userSubscribe\":false}";
        ProductM productM = JSONObject.parseObject(str, ProductM.class);
        return ItemPriceBottomTagsVP.Param.builder().platform(1).salePrice("188").productM(productM).build();
    }

    private ConfigItemPriceBottomTagsOpt.Param mockParam1() {
        String str = "{\"actProductId\":1022025664,\"actProductType\":1,\"activities\":[],\"basePrice\":200,\"basePriceTag\":\"200\",\"beginDate\":0,\"categoryId\":0,\"compositeScore\":0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"attr_shopRecommend\",\"value\":\"false\"},{\"name\":\"common_consumer_coupon_assign\",\"value\":\"false\"},{\"name\":\"service_type\",\"value\":\"足疗\"},{\"name\":\"calcName\",\"value\":\"60分钟足疗\"},{\"name\":\"standardNameDealGroup\",\"value\":\"1\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1022025664&poiid=607472195&shopuuid=&nailexhibitid=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoU_fCgm4_weIw0ZVKB2sV1PDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO364LrYkUB-Ndn82LZtHADKYFEJoJLg-lgPm1vYsV66q0lKTGwhtXpN9pvOQBXHyPAxIgLKrYCfszqixfxyF2QNXIzyzErchegwjEYFbPCNJjCgxiUjgMoMRyWEj6oj0_-F27dMz-0iCCldPPuSZiAIzPzDm6W8J8-Y3MroozTDaLJCwBZk-_nfQZnVtrzasZYbOB_r75nGKE8hO7845RkQYqnmEFiNKbFm2PR1DZKAy1zFr_EjRiPw6COP-IPrDse70-voeMWFdxJ6_dSKSw7z4Iw1zpmIBuYGlNZfcryV11n64Zxo8oPsUysjVDndNDFwfmzLdRogX16P-22NOtPaDvbPF28TYbaEIY7DaTMp7sI0hapyH9J0JcWH0Sg4jYeXqNuyNjY6a8X5K8X9huhKyf8U_DNjXqB-oElMFIJDM7mh8o-cnSMbIYTqbKSgJbSlmGSFqzfMCjEw6DxdTo8uiGFQVrXvXnAGZBYkOmyQAtsKroPaRL07-VDsIt6Kn5S7jl6pnQvXlsPvOFH5Zj97JKyL42yIY5TpiNY2ToWpSMKOLrbZ_Vi37uDpORRcVWTymZoQsC_rFGJJanDUwiS7lSMivyGl-mtVA6PQbXHfww\",\"marketPrice\":\"250\",\"orderUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage&dealid=1022025664&shopid=607472195&pagesource=poiShelf&expid=directPage&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoU_fCgm4_weIw0ZVKB2sV1PDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO364LrYkUB-Ndn82LZtHADKYFEJoJLg-lgPm1vYsV66q0lKTGwhtXpN9pvOQBXHyPAxIgLKrYCfszqixfxyF2QNXIzyzErchegwjEYFbPCNJjCgxiUjgMoMRyWEj6oj0_-F27dMz-0iCCldPPuSZiAIzPzDm6W8J8-Y3MroozTDaLJCwBZk-_nfQZnVtrzasZYbOB_r75nGKE8hO7845RkQYqnmEFiNKbFm2PR1DZKAy1zFr_EjRiPw6COP-IPrDse70-voeMWFdxJ6_dSKSw7z4Iw1zpmIBuYGlNZfcryV11n64Zxo8oPsUysjVDndNDFwfmzLdRogX16P-22NOtPaDvbPF28TYbaEIY7DaTMp7sI0hapyH9J0JcWH0Sg4jYeXqNuyNjY6a8X5K8X9huhKyf8U_DNjXqB-oElMFIJDM7mh8o-cnSMbIYTqbKSgJbSlmGSFqzfMCjEw6DxdTo8uiGFQVrXvXnAGZBYkOmyQAtsKroPaRL07-VDsIt6Kn5S7jl6pnQvXlsPvOFH5Zj97JKyL42yIY5TpiNY2ToWpSMKOLrbZ_Vi37uDpORRcVWTymZoQsC_rFGJJanDUwiS7lSMivyGl-mtVA6PQbXHfww\",\"orderUsers\":[],\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/6196cb24739d3a2c6915614d76dfe63162745.png\",\"productId\":1022025664,\"productTagList\":[],\"productTags\":[\"60分钟\",\"足部\"],\"productType\":1,\"promoPrices\":[{\"discount\":0.76,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"https://p1.meituan.net/travelcube/83518c6e14ec54a491bac580cb43d2fe4222.png\",\"marketPrice\":\"250\",\"pricePromoInfoMap\":{\"1\":[{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046393\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046392\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390308\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046391\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390307\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046390\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390306\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046389\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390305\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046388\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390304\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046387\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473619231\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046386\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"icon\":\"神会员券icon\",\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoItemTextDTO\":{\"icon\":\"神会员券icon\",\"promoDivideType\":\"PLATFORM_COUPON\",\"subTitle\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"title\":\"美团优惠券\"},\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0}]},\"promoItemList\":[{\"amount\":50,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":1022025664,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":50,\"promoTag\":\"-¥50\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1},{\"amount\":9,\"canAssign\":false,\"desc\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"PLATFORM_COUPON\",\"subTitle\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"title\":\"美团优惠券\"},\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\",\"CAN_INFLATE\":\"true\",\"AFTER_INFLATE\":\"false\",\"BIZ_TOKEN\":\"1111\"},\"remainStock\":0,\"sourceType\":1},{\"amount\":3,\"canAssign\":false,\"desc\":\"满25元减3元\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"newUser\":false,\"promoId\":66051,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"GOVERNMENT_CONSUME_COUPON\",\"subTitle\":\"满25元减3元\",\"title\":\"政府消费券\"},\"promoPrice\":3,\"promoTag\":\"-¥3\",\"promoType\":\"政府消费券\",\"promoTypeCode\":14,\"remainStock\":0,\"sourceType\":0}],\"promoPrice\":188,\"promoPriceTag\":\"188\",\"promoQuantityLimit\":0,\"promoTag\":\"共省¥62\",\"promoTagType\":20,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":62,\"totalPromoPriceTag\":\"-¥62\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":20,\"saleTag\":\"半年消费20\"},\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"stock\":{\"remainStock\":99999956,\"soldOut\":false,\"totalStock\":*********},\"timesDeal\":false,\"title\":\"【神会员】200｜60分钟足疗\",\"top\":false,\"tradeType\":3,\"unifyProduct\":false,\"userSubscribe\":false}";
        ProductM productM = JSONObject.parseObject(str, ProductM.class);
        return ItemPriceBottomTagsVP.Param.builder().platform(1).salePrice("188").productM(productM).build();
    }

    private ConfigItemPriceBottomTagsOpt.Param mockParam2() {
        String str = "{\"actProductId\":1022025664,\"actProductType\":1,\"activities\":[],\"basePrice\":200,\"basePriceTag\":\"200\",\"beginDate\":0,\"categoryId\":0,\"compositeScore\":0,\"dealSpuId\":0,\"dealThemePadded\":true,\"endDate\":0,\"extAttrs\":[{\"name\":\"attr_recommend\",\"value\":\"false\"},{\"name\":\"attr_shopRecommend\",\"value\":\"false\"},{\"name\":\"common_consumer_coupon_assign\",\"value\":\"false\"},{\"name\":\"service_type\",\"value\":\"足疗\"},{\"name\":\"calcName\",\"value\":\"60分钟足疗\"},{\"name\":\"standardNameDealGroup\",\"value\":\"1\"},{\"name\":\"preSaleTag\",\"value\":\"false\"}],\"extendImages\":[],\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1022025664&poiid=607472195&shopuuid=&nailexhibitid=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoU_fCgm4_weIw0ZVKB2sV1PDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO364LrYkUB-Ndn82LZtHADKYFEJoJLg-lgPm1vYsV66q0lKTGwhtXpN9pvOQBXHyPAxIgLKrYCfszqixfxyF2QNXIzyzErchegwjEYFbPCNJjCgxiUjgMoMRyWEj6oj0_-F27dMz-0iCCldPPuSZiAIzPzDm6W8J8-Y3MroozTDaLJCwBZk-_nfQZnVtrzasZYbOB_r75nGKE8hO7845RkQYqnmEFiNKbFm2PR1DZKAy1zFr_EjRiPw6COP-IPrDse70-voeMWFdxJ6_dSKSw7z4Iw1zpmIBuYGlNZfcryV11n64Zxo8oPsUysjVDndNDFwfmzLdRogX16P-22NOtPaDvbPF28TYbaEIY7DaTMp7sI0hapyH9J0JcWH0Sg4jYeXqNuyNjY6a8X5K8X9huhKyf8U_DNjXqB-oElMFIJDM7mh8o-cnSMbIYTqbKSgJbSlmGSFqzfMCjEw6DxdTo8uiGFQVrXvXnAGZBYkOmyQAtsKroPaRL07-VDsIt6Kn5S7jl6pnQvXlsPvOFH5Zj97JKyL42yIY5TpiNY2ToWpSMKOLrbZ_Vi37uDpORRcVWTymZoQsC_rFGJJanDUwiS7lSMivyGl-mtVA6PQbXHfww\",\"marketPrice\":\"250\",\"orderUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=dealsubmitorderpage&dealid=1022025664&shopid=607472195&pagesource=poiShelf&expid=directPage&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoU_fCgm4_weIw0ZVKB2sV1PDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO364LrYkUB-Ndn82LZtHADKYFEJoJLg-lgPm1vYsV66q0lKTGwhtXpN9pvOQBXHyPAxIgLKrYCfszqixfxyF2QNXIzyzErchegwjEYFbPCNJjCgxiUjgMoMRyWEj6oj0_-F27dMz-0iCCldPPuSZiAIzPzDm6W8J8-Y3MroozTDaLJCwBZk-_nfQZnVtrzasZYbOB_r75nGKE8hO7845RkQYqnmEFiNKbFm2PR1DZKAy1zFr_EjRiPw6COP-IPrDse70-voeMWFdxJ6_dSKSw7z4Iw1zpmIBuYGlNZfcryV11n64Zxo8oPsUysjVDndNDFwfmzLdRogX16P-22NOtPaDvbPF28TYbaEIY7DaTMp7sI0hapyH9J0JcWH0Sg4jYeXqNuyNjY6a8X5K8X9huhKyf8U_DNjXqB-oElMFIJDM7mh8o-cnSMbIYTqbKSgJbSlmGSFqzfMCjEw6DxdTo8uiGFQVrXvXnAGZBYkOmyQAtsKroPaRL07-VDsIt6Kn5S7jl6pnQvXlsPvOFH5Zj97JKyL42yIY5TpiNY2ToWpSMKOLrbZ_Vi37uDpORRcVWTymZoQsC_rFGJJanDUwiS7lSMivyGl-mtVA6PQbXHfww\",\"orderUsers\":[],\"picUrl\":\"https://p0.inf.test.sankuai.com/dpmerchantpic/6196cb24739d3a2c6915614d76dfe63162745.png\",\"productId\":1022025664,\"productTagList\":[],\"productTags\":[\"60分钟\",\"足部\"],\"productType\":1,\"promoPrices\":[{\"discount\":0.76,\"endTime\":0,\"extendDisplayInfo\":{},\"icon\":\"https://p1.meituan.net/travelcube/83518c6e14ec54a491bac580cb43d2fe4222.png\",\"marketPrice\":\"250\",\"pricePromoInfoMap\":{\"1\":[{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046393\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046392\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390308\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046391\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390307\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046390\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390306\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046389\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390305\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046388\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473390304\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046387\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2, 减9元\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"473619231\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0},{\"amount\":9,\"couponGroupId\":\"1146584764\",\"couponId\":\"402366100011046386\",\"couponTitle\":\"qll基础券-家店-餐综酒-1V2\",\"desc\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"icon\":\"神会员券icon\",\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoItemTextDTO\":{\"icon\":\"神会员券icon\",\"promoDivideType\":\"PLATFORM_COUPON\",\"subTitle\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"title\":\"美团优惠券\"},\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionDisplayTextMap\":{},\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\"},\"remainStock\":0,\"sourceType\":1,\"startTime\":1711952842000,\"totalStock\":0}]},\"promoItemList\":[{\"amount\":50,\"canAssign\":false,\"desc\":\"\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":false,\"promoId\":1022025664,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"subTitle\":\"\",\"title\":\"团购优惠\"},\"promoPrice\":50,\"promoTag\":\"-¥50\",\"promoType\":\"团购优惠\",\"promoTypeCode\":11,\"remainStock\":0,\"sourceType\":1},{\"amount\":9,\"canAssign\":false,\"desc\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"effectiveEndTime\":0,\"endTime\":1712851199000,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"minConsumptionAmount\":0,\"newUser\":false,\"promoId\":1146584764,\"promoIdentity\":\"magicalMemberCoupon\",\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"PLATFORM_COUPON\",\"subTitle\":\"qll基础券-家店-餐综酒-1V2，9元无门槛优惠券\",\"title\":\"美团优惠券\"},\"promoPrice\":9,\"promoTag\":\"-¥9\",\"promoType\":\"美团优惠券\",\"promoTypeCode\":5,\"promotionExplanatoryTags\":[3],\"promotionOtherInfoMap\":{\"ASSET_TYPE\":\"1\",\"TSP_COUPON_GROUP_ID\":\"9007353591038985\",\"TSP_COUPON_ID\":\"*********\",\"CAN_INFLATE\":\"true\",\"AFTER_INFLATE\":\"false\",\"BIZ_TOKEN\":\"1111\"},\"remainStock\":0,\"sourceType\":1},{\"amount\":3,\"canAssign\":false,\"desc\":\"满25元减3元\",\"effectiveEndTime\":0,\"endTime\":0,\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"newUser\":false,\"promoId\":66051,\"promoItemText\":{\"icon\":\"https://p0.meituan.net/travelcube/167e4c4bff8f33e221b69214e827bbc44455.png\",\"promoDivideType\":\"SECOND_KILL\",\"subTitle\":\"满25元减3元\",\"title\":\"限时秒杀\"},\"promoPrice\":3,\"promoTag\":\"-¥3\",\"promoType\":\"限时秒杀\",\"promoTypeCode\":14,\"remainStock\":0,\"sourceType\":0}],\"promoPrice\":188,\"promoPriceTag\":\"188\",\"promoQuantityLimit\":0,\"promoTag\":\"共省¥62\",\"promoTagType\":60,\"promoType\":0,\"startTime\":0,\"totalPromoPrice\":62,\"totalPromoPriceTag\":\"-¥62\",\"userHasCard\":false}],\"resourceRank\":{\"rankLinkUrl\":\"\",\"rankName\":\"\"},\"sale\":{\"sale\":20,\"saleTag\":\"半年消费20\"},\"shopMs\":[],\"shopNum\":0,\"spuType\":0,\"stock\":{\"remainStock\":99999956,\"soldOut\":false,\"totalStock\":*********},\"timesDeal\":false,\"title\":\"【神会员】200｜60分钟足疗\",\"top\":false,\"tradeType\":3,\"unifyProduct\":false,\"userSubscribe\":false}";
        ProductM productM = JSONObject.parseObject(str, ProductM.class);
        productM.setBestPromoPrice(productM.getPromoPrices().get(0));
        return ItemPriceBottomTagsVP.Param.builder().platform(1).salePrice("188").productM(productM).build();
    }

    private ConfigItemPriceBottomTagsOpt.Config mockConfig1() {
        ConfigItemPriceBottomTagsOpt.Config config = new ConfigItemPriceBottomTagsOpt.Config();
        List<List<String>> strategyConfig = Lists.newArrayList();
        strategyConfig.add(Lists.newArrayList("神会员","预售","足疗会员","彩虹活动秒杀","其他优惠"));
        strategyConfig.add(Lists.newArrayList("买贵必赔","全网低价","空间比价","时间比价","价保","价格力标签"));
        config.setStrategies(strategyConfig);
        config.setPopType(3);
        return config;
    }

    private ConfigItemPriceBottomTagsOpt.Config mockConfig2() {
        ConfigItemPriceBottomTagsOpt.Config config = new ConfigItemPriceBottomTagsOpt.Config();
        List<List<String>> strategyConfig = Lists.newArrayList();
        strategyConfig.add(Lists.newArrayList("预售","会员专属","丽人会员","商家会员","足疗会员","彩虹活动秒杀","营销曝光秒杀","其他优惠"));
        strategyConfig.add(Lists.newArrayList("买贵必赔","价格力标签"));
        config.setStrategies(strategyConfig);
        config.setPopType(3);
        return config;
    }

    private ConfigItemPriceBottomTagsOpt.Config mockConfig3() {
        ConfigItemPriceBottomTagsOpt.Config config = new ConfigItemPriceBottomTagsOpt.Config();
        List<List<String>> strategyConfig = Lists.newArrayList();
        strategyConfig.add(Lists.newArrayList("限时秒杀","其他优惠"));
        config.setStrategies(strategyConfig);
        config.setPopType(3);
        return config;
    }
}
