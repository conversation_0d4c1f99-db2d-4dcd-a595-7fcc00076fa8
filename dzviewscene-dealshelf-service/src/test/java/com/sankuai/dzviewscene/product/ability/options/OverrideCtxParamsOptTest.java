package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;
import java.util.List;
import org.mockito.InjectMocks;
import java.lang.reflect.Constructor;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OverrideCtxParamsOptTest {

    @Mock
    private OverrideCtxParamsOpt.Config config;

    @Test
    public void testComputeWhenOverrideMapIsEmpty() throws Throwable {
        // arrange
        OverrideCtxParamsOpt overrideCtxParamsOpt = new OverrideCtxParamsOpt();
        ActivityCxt ctx = new ActivityCxt();
        List<Integer> needFields = Arrays.asList(1, 2, 3);
        int platform = 1;
        // Use reflection to create an instance of PreSyncHandlerVP.Param
        Constructor<PreSyncHandlerVP.Param> constructor = PreSyncHandlerVP.Param.class.getDeclaredConstructor(ActivityCxt.class, List.class, int.class);
        constructor.setAccessible(true);
        PreSyncHandlerVP.Param param = constructor.newInstance(ctx, needFields, platform);
        when(config.getOverrideMap()).thenReturn(null);
        // act
        Map<String, Object> result = overrideCtxParamsOpt.compute(ctx, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenOverrideMapIsNotEmpty() throws Throwable {
        // arrange
        OverrideCtxParamsOpt overrideCtxParamsOpt = new OverrideCtxParamsOpt();
        ActivityCxt ctx = new ActivityCxt();
        List<Integer> needFields = Arrays.asList(1, 2, 3);
        int platform = 1;
        // Use reflection to create an instance of PreSyncHandlerVP.Param
        Constructor<PreSyncHandlerVP.Param> constructor = PreSyncHandlerVP.Param.class.getDeclaredConstructor(ActivityCxt.class, List.class, int.class);
        constructor.setAccessible(true);
        PreSyncHandlerVP.Param param = constructor.newInstance(ctx, needFields, platform);
        Map<String, Object> overrideMap = new HashMap<>();
        overrideMap.put("key", "value");
        when(config.getOverrideMap()).thenReturn(overrideMap);
        // act
        Map<String, Object> result = overrideCtxParamsOpt.compute(ctx, param, config);
        // assert
        assertEquals(overrideMap, result);
    }
}
