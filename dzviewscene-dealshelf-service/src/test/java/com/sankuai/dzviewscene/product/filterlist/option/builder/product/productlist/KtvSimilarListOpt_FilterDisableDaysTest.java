package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.DisableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.UseRuleM;
import java.util.Arrays;
import java.util.Collections;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class KtvSimilarListOpt_FilterDisableDaysTest {

    private KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();

    private ProductM createProductWithDisableDays(java.util.List<Integer> disableDays) {
        ProductM productM = new ProductM();
        UseRuleM useRuleM = new UseRuleM();
        DisableDateM disableDateM = new DisableDateM();
        disableDateM.setDisableDays(disableDays);
        useRuleM.setDisableDate(disableDateM);
        productM.setUseRuleM(useRuleM);
        return productM;
    }

    @Test
    public void testFilterDisableDaysWhenUseRuleIsNull() throws Throwable {
        ProductM productM = new ProductM();
        // Expecting true because the method should handle null UseRule gracefully
        Assert.assertTrue("Expect true when UseRule is null", ktvSimilarListOpt.filterDisableDays(Collections.emptyList(), productM));
    }

    @Test
    public void testFilterDisableDaysWhenDisableDateIsNull() throws Throwable {
        ProductM productM = new ProductM();
        productM.setUseRuleM(new UseRuleM());
        // Expecting true because the method should handle null DisableDate gracefully
        Assert.assertTrue("Expect true when DisableDate is null", ktvSimilarListOpt.filterDisableDays(Collections.emptyList(), productM));
    }

    @Test
    public void testFilterDisableDaysWhenDisableDaysIsEmptyAndCurrentIsEmpty() throws Throwable {
        ProductM productM = createProductWithDisableDays(Collections.emptyList());
        // Expecting true because both lists are empty
        Assert.assertTrue("Expect true when both lists are empty", ktvSimilarListOpt.filterDisableDays(Collections.emptyList(), productM));
    }

    @Test
    public void testFilterDisableDaysWhenDisableDaysIsEmptyAndCurrentIsNotEmpty() throws Throwable {
        ProductM productM = createProductWithDisableDays(Collections.emptyList());
        // Expecting true because disableDays is empty and currentDisableDays is not
        Assert.assertTrue("Expect true when disableDays is empty and currentDisableDays is not", ktvSimilarListOpt.filterDisableDays(Arrays.asList(1, 2, 3), productM));
    }

    @Test
    public void testFilterDisableDaysWhenDisableDaysIsEmpty() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        ProductM productM = new ProductM();
        productM.setUseRuleM(new UseRuleM());
        productM.getUseRuleM().setDisableDate(new DisableDateM());
        Assert.assertTrue(ktvSimilarListOpt.filterDisableDays(Collections.emptyList(), productM));
    }
}
