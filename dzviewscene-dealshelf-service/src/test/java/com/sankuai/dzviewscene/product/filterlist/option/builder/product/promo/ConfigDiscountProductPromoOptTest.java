package com.sankuai.dzviewscene.product.filterlist.option.builder.product.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ConfigDiscountProductPromoOptTest {

    @InjectMocks
    private ConfigDiscountProductPromoOpt configDiscountProductPromoOpt;

    @Mock
    private ActivityCxt mockContext;

    @Mock
    private ConfigDiscountProductPromoOpt.Param mockParam;

    @Mock
    private ConfigDiscountProductPromoOpt.Config mockConfig;

    @Mock
    private ProductM mockProductM;

    @Before
    public void setUp() {
        // Ensure that the ProductM object is not null and its marketPrice is set
        when(mockParam.getProductM()).thenReturn(mockProductM);
        // Set a default market price
        when(mockProductM.getMarketPrice()).thenReturn("100");
    }

    @Test
    public void testCompute_WhenDiscountGreaterThanTopLimitAndNoAppendValue() throws Throwable {
        // arrange
        when(mockParam.getSalePrice()).thenReturn("1000");
        when(mockConfig.isShowAppendValueWhenNoDiscount()).thenReturn(false);
        when(mockConfig.getMaxDiscount()).thenReturn(9.9);
        // act
        List<DzPromoVO> result = configDiscountProductPromoOpt.compute(mockContext, mockParam, mockConfig);
        // assert
        assertTrue(result.isEmpty());
        verify(mockConfig).isShowAppendValueWhenNoDiscount();
    }

    @Test
    public void testCompute_WhenSalePriceZeroAndNoAppendValue() throws Throwable {
        // arrange
        when(mockParam.getSalePrice()).thenReturn("0");
        when(mockConfig.isShowAppendValueWhenNoDiscount()).thenReturn(false);
        // act
        List<DzPromoVO> result = configDiscountProductPromoOpt.compute(mockContext, mockParam, mockConfig);
        // assert
        assertTrue(result.isEmpty());
        verify(mockConfig).isShowAppendValueWhenNoDiscount();
    }

    @Test
    public void testCompute_WhenMarketPriceZeroAndNoAppendValue() throws Throwable {
        // arrange
        when(mockParam.getSalePrice()).thenReturn("100");
        // Set market price to zero
        when(mockProductM.getMarketPrice()).thenReturn("0");
        when(mockConfig.isShowAppendValueWhenNoDiscount()).thenReturn(false);
        // act
        List<DzPromoVO> result = configDiscountProductPromoOpt.compute(mockContext, mockParam, mockConfig);
        // assert
        assertTrue(result.isEmpty());
        verify(mockConfig).isShowAppendValueWhenNoDiscount();
    }
}
