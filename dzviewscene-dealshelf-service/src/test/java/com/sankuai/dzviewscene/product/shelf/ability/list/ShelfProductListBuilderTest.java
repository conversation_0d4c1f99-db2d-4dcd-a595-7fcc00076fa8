package com.sankuai.dzviewscene.product.shelf.ability.list;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.list.ShelfProductListCfg;
import com.sankuai.dzviewscene.product.shelf.ability.list.ShelfProductListParam;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.ShelfFloorsBuilder;
import com.sankuai.dzviewscene.productshelf.vu.vo.FilterBtnIdAndProAreasVO;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShelfProductListBuilderTest {

    @Mock
    private ShelfFloorsBuilder shelfFloorsBuilder;

    @InjectMocks
    private ShelfProductListBuilder shelfProductListBuilder;

    @Test
    public void testBuildWhenLoadProductGroupMReturnEmpty() throws Throwable {
        // arrange
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ShelfProductListParam shelfFloorParam = mock(ShelfProductListParam.class);
        ShelfProductListCfg shelfFloorCfg = mock(ShelfProductListCfg.class);
        when(activityCxt.getSource(anyString())).thenReturn(null);
        // act
        CompletableFuture<List<FilterBtnIdAndProAreasVO>> result = shelfProductListBuilder.build(activityCxt, shelfFloorParam, shelfFloorCfg);
        // assert
        verify(shelfFloorsBuilder, times(1)).build(ActivityCtxtUtils.toActivityContext(activityCxt));
    }

    @Test
    public void testBuildWhenLoadProductGroupMReturnNotEmpty() throws Throwable {
        // arrange
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ShelfProductListParam shelfFloorParam = mock(ShelfProductListParam.class);
        ShelfProductListCfg shelfFloorCfg = mock(ShelfProductListCfg.class);
        Map<String, ProductGroupM> productGroupMs = mock(Map.class);
        when(activityCxt.getSource(anyString())).thenReturn(productGroupMs);
        // act
        CompletableFuture<List<FilterBtnIdAndProAreasVO>> result = shelfProductListBuilder.build(activityCxt, shelfFloorParam, shelfFloorCfg);
        // assert
        verify(shelfFloorsBuilder, times(1)).build(ActivityCtxtUtils.toActivityContext(activityCxt));
    }

    @Test(expected = RuntimeException.class)
    public void testBuildWhenBuildThrowException() throws Throwable {
        // arrange
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        ShelfProductListParam shelfFloorParam = mock(ShelfProductListParam.class);
        ShelfProductListCfg shelfFloorCfg = mock(ShelfProductListCfg.class);
        when(shelfFloorsBuilder.build(ActivityCtxtUtils.toActivityContext(activityCxt))).thenThrow(new RuntimeException());
        // act
        shelfProductListBuilder.build(activityCxt, shelfFloorParam, shelfFloorCfg);
    }
}
