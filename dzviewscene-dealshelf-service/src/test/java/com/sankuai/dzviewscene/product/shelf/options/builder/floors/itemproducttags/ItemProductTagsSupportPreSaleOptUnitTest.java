package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.DealProductSpuAttrDTO;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags.ProductTagsSupportPreSaleOpt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
/**
 * @auther: liweilong06
 * @date: 2023/3/14 上午11:12
 */
@Ignore("没有可执行的方法")
@RunWith(MockitoJUnitRunner.class)
public class ItemProductTagsSupportPreSaleOptUnitTest {

    private ItemProductTagsSupportPreSaleOpt opt;
    private ItemProductTagsVP.Param ItemProductTagsVPParam;
    private ItemProductTagsSupportPreSaleOpt.Config config;
    @Mock
    private ProductM mockProductM;
    private ProductTagsSupportPreSaleOpt.Param param;
    private MockedStatic<PreSaleUtils> preSaleUtilsMockedStatic;
    private MockedStatic<ItemProductTagsSupportPreSaleOpt> itemProductTagsSupportPreSaleOptMockedStatic;
    @Mock
    private ActivityCxt context;

    @Before
    public void init() {
        opt= new ItemProductTagsSupportPreSaleOpt();
        config = new ItemProductTagsSupportPreSaleOpt.Config();
        MockitoAnnotations.initMocks(this);
        param = Mockito.mock(ProductTagsSupportPreSaleOpt.Param.class);
        ItemProductTagsVPParam = Mockito.mock(ItemProductTagsVP.Param.class);
        mockProductM = Mockito.mock(ProductM.class);
        preSaleUtilsMockedStatic = mockStatic(PreSaleUtils.class);
        itemProductTagsSupportPreSaleOptMockedStatic = mockStatic(ItemProductTagsSupportPreSaleOpt.class);
    }

    @After
    public void tearDown() {
        preSaleUtilsMockedStatic.close();
        itemProductTagsSupportPreSaleOptMockedStatic.close();
    }

    // 测空
    @Test
    public void TestCompute1(){
        ItemProductTagsSupportPreSaleOpt.Config config = new ItemProductTagsSupportPreSaleOpt.Config();
        config.setEnableHotSpuSpecialTag(false);
        ProductM productM1 = new ProductM();
        productM1.setProductTags(Lists.newArrayList());
        productM1.setAttr("preSaleTag", "false");
        List<String> result = opt.compute(context, ItemProductTagsVP.Param.builder().productM(productM1).build(), config);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    // 测爆品
    @Test
    public void TestCompute2(){
        ItemProductTagsSupportPreSaleOpt.Config config = new ItemProductTagsSupportPreSaleOpt.Config();
        config.setEnableHotSpuSpecialTag(true);
        ProductM productM1 = new ProductM();
        productM1.setProductTags(Lists.newArrayList());
        productM1.setAttr("topPerformingProduct", "1");
        DealProductSpuDTO spuDTO = new DealProductSpuDTO();
        spuDTO.setAttrList(Lists.newArrayList(new DealProductSpuAttrDTO("attr_hot_spu_shelf_sub_tag", "[\"test\"]")));
        productM1.setSpuM(spuDTO);
        List<String> result = opt.compute(context, ItemProductTagsVP.Param.builder().productM(productM1).build(), config);
        Assert.assertNotNull(result);
    }

    // 测预售单
    @Test
    public void TestCompute3(){
        ItemProductTagsSupportPreSaleOpt.Config config = new ItemProductTagsSupportPreSaleOpt.Config();
        config.setEnableHotSpuSpecialTag(true);
        ProductM productM1 = new ProductM();
        productM1.setAttr("preSaleTag", "true");
        productM1.setAttr("preSaleStartDate", "今天");
        productM1.setProductTags(Lists.newArrayList());
        DealProductSpuDTO spuDTO = new DealProductSpuDTO();
        spuDTO.setAttrList(Lists.newArrayList(new DealProductSpuAttrDTO("attr_hot_spu_shelf_sub_tag", "[\"test\"]")));
        productM1.setSpuM(spuDTO);
        List<String> result = opt.compute(context, ItemProductTagsVP.Param.builder().productM(productM1).build(), config);
        assertTrue(CollectionUtils.isEmpty(result));
    }

    /**
     * 测试商品为null时，返回null的情况
     */
    @Test
    public void testComputeWithNullProduct() {
        // arrange
        //when(param.getProductM()).thenReturn(null);

        // act
        List<String> result = opt.compute(context, ItemProductTagsVPParam, config);

        // assert
        assertEquals(null, result);
    }

    //@Test
    public void test_nullParam() {
        ItemProductTagsVP.Param param = ItemProductTagsVP.Param.builder().build();
        Assert.assertTrue(CollectionUtils.isEmpty(opt.compute(null, param, config)));
    }

    //@Test
    public void test_notNullParam() {
        String origTag = "游玩时长：3小时";
        ProductM productM = new ProductM();
        productM.setAttr("preSaleTag", "true");
        productM.setProductTags(Lists.newArrayList("1", "2"));
        productM.setAttr("preSaleStartDate", "2023.02.06");
        productM.setProductTags(Lists.newArrayList(origTag));
        ItemProductTagsVP.Param param = ItemProductTagsVP.Param.builder().build();
        param.setProductM(productM);
        List<String> productTags = opt.compute(null, param, config);
        Assert.assertTrue(CollectionUtils.size(productTags) == 1);
        Assert.assertTrue(productTags.get(0).equals("2023.02.06后可用"));

        productM.setExtAttrs(Lists.newArrayList());
        productM.setAttr("preSaleTag", "false");
        productM.setProductTags(Lists.newArrayList("1", "2"));
        productM.setAttr("preSaleStartDate", "2023.02.06");
        productTags = opt.compute(null, param, config);
        Assert.assertTrue(CollectionUtils.size(productTags) == 2);
        Assert.assertTrue(productTags.get(0).equals("1"));
    }

}
