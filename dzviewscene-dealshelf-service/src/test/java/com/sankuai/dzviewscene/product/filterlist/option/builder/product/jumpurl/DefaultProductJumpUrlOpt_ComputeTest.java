package com.sankuai.dzviewscene.product.filterlist.option.builder.product.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductJumpUrlOpt_ComputeTest {

    @Mock
    private ActivityCxt mockContext;

    @Mock
    private ProductJumpUrlVP.Param mockParam;

    @Mock
    private ProductM mockProductM;

    /**
     * 测试ProductM为null的情况
     */
    @Test
    public void testComputeProductMIsNull() throws Throwable {
        // arrange
        when(mockParam.getProductM()).thenReturn(null);
        DefaultProductJumpUrlOpt opt = new DefaultProductJumpUrlOpt();
        // act
        String result = opt.compute(mockContext, mockParam, null);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试ProductM不为null但jumpUrl为空的情况
     */
    @Test
    public void testComputeProductMNotNullButJumpUrlEmpty() throws Throwable {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getJumpUrl()).thenReturn("");
        DefaultProductJumpUrlOpt opt = new DefaultProductJumpUrlOpt();
        // act
        String result = opt.compute(mockContext, mockParam, null);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试ProductM不为null且jumpUrl有值的情况
     */
    @Test
    public void testComputeProductMNotNullAndJumpUrlNotEmpty() throws Throwable {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockProductM.getJumpUrl()).thenReturn("http://example.com");
        DefaultProductJumpUrlOpt opt = new DefaultProductJumpUrlOpt();
        // act
        String result = opt.compute(mockContext, mockParam, null);
        // assert
        assertEquals("http://example.com", result);
    }
}
