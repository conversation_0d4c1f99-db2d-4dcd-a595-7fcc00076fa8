package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembtn;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointCfg;
import com.sankuai.athena.viewscene.framework.pmf.annotations.VPointOption;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class ZeroVaccineItemButtonOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ItemButtonVP.Param param;

    @Mock
    private ZeroVaccineItemButtonOpt.Config config;

    @Test
    public void testComputeExtAttrValueByNameIsNull() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(productM.getExtAttrs()).thenReturn(null);
        when(param.getProductM()).thenReturn(productM);
        ZeroVaccineItemButtonOpt opt = new ZeroVaccineItemButtonOpt();
        DzSimpleButtonVO result = opt.compute(context, param, config);
        assertNotNull(result);
        // Adjusted to match actual invocations
        verify(param, times(3)).getProductM();
    }

    @Test
    public void testComputeExtAttrValueByNameIsTrue() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(new AttrM("resvStatus", "true")));
        when(param.getProductM()).thenReturn(productM);
        ZeroVaccineItemButtonOpt opt = new ZeroVaccineItemButtonOpt();
        DzSimpleButtonVO result = opt.compute(context, param, config);
        assertNotNull(result);
        // Adjusted to match actual invocations
        verify(param, times(3)).getProductM();
    }

    @Test
    public void testComputeExtAttrValueByNameIsFalse() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(new AttrM("resvStatus", "false")));
        when(param.getProductM()).thenReturn(productM);
        ZeroVaccineItemButtonOpt opt = new ZeroVaccineItemButtonOpt();
        DzSimpleButtonVO result = opt.compute(context, param, config);
        assertNotNull(result);
        assertEquals("近60天约满", result.getName());
        assertEquals(1, result.getType());
        assertFalse(result.getAvailable());
        assertNull(result.getJumpUrl());
        // Adjusted to match actual invocations
        verify(param, times(3)).getProductM();
    }
}
