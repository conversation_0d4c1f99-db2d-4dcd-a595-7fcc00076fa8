package com.sankuai.dzviewscene.product.shelf.options.builder.floors.defaultshownum;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemAreaDefaultShowNumVP.Param;
import com.sankuai.dzviewscene.shelf.business.utils.DouHuUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefaultItemAreaDefaultShowNumOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param param;

    @Mock
    private DefaultItemAreaDefaultShowNumOpt.Config config;

    @InjectMocks
    private DefaultItemAreaDefaultShowNumOpt defaultItemAreaDefaultShowNumOpt;

    @Test
    public void testComputeTimesDealAndAdditionalDefaultNumIsZero() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testComputeNoDefaultShowNumByProductType() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        Map<Integer, Integer> productType2floorDefaultShowNum = new HashMap<>();
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testComputeDouHu2floorDefaultShowNumIsNull() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        Map<String, Integer> douHu2floorDefaultShowNum = new HashMap<>();
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testComputeDouHu2floorDefaultShowNumIsNotNullButDouHuListIsNull() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        Map<String, Integer> douHu2floorDefaultShowNum = new HashMap<>();
        douHu2floorDefaultShowNum.put("sk", 5);
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testComputeDouHu2floorDefaultShowNumIsNotNullAndDouHuListIsNotNullButNoHit() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        Map<String, Integer> douHu2floorDefaultShowNum = new HashMap<>();
        douHu2floorDefaultShowNum.put("sk", 5);
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testComputeFilterId2floorDefaultShowNumIsNull() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        Map<Long, Integer> filterId2floorDefaultShowNum = new HashMap<>();
        when(config.getFilterId2floorDefaultShowNum()).thenReturn(filterId2floorDefaultShowNum);
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testComputeFilterId2floorDefaultShowNumIsNotNullButFilterIdNotExist() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        Map<Long, Integer> filterId2floorDefaultShowNum = new HashMap<>();
        filterId2floorDefaultShowNum.put(2L, 5);
        when(config.getFilterId2floorDefaultShowNum()).thenReturn(filterId2floorDefaultShowNum);
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(0), result);
    }

    @Test
    public void testComputeFilterId2floorDefaultShowNumIsNotNullAndFilterIdExist() throws Throwable {
        List<ProductM> products = Arrays.asList(mock(ProductM.class));
        when(param.getProducts()).thenReturn(products);
        when(param.getFilterId()).thenReturn(1L);
        Map<Long, Integer> filterId2floorDefaultShowNum = new HashMap<>();
        filterId2floorDefaultShowNum.put(1L, 5);
        when(config.getFilterId2floorDefaultShowNum()).thenReturn(filterId2floorDefaultShowNum);
        Integer result = defaultItemAreaDefaultShowNumOpt.compute(context, param, config);
        assertEquals(Integer.valueOf(5), result);
    }
}
