package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import java.util.ArrayList;
import java.util.List;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.enums.MassageServiceTypeEnum;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils.OverNightModuleUtils;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.ranking.util.NumberUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import java.util.Objects;
import java.util.stream.Collectors;
import org.junit.runner.RunWith;
import org.junit.*;

public class AbstractFootMessageModuleOpt_GetServiceFlowParseModelsTest {

    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt;

    // Test case to verify behavior when 'serviceProcessArrayNew' does not map to a ServiceFlowParseModel list
    @Test
    public void testGetServiceFlowParseModelsWhenServiceProcessArrayNewIsNotServiceFlowParseModelList() throws Throwable {
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = mock(AbstractFootMessageModuleOpt.class, CALLS_REAL_METHODS);
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        SkuAttrItemDto item = new SkuAttrItemDto();
        item.setAttrName("serviceProcessArrayNew");
        item.setAttrValue("[]");
        skuAttrs.add(item);
        List<ServiceFlowParseModel> result = abstractFootMessageModuleOpt.getServiceFlowParseModels(skuAttrs);
        assertNotNull("Result should not be null", result);
        assertTrue("Result should be empty", result.isEmpty());
    }

    // Test case to verify behavior when 'serviceProcessArrayNew' correctly maps to a ServiceFlowParseModel list
    @Test
    public void testGetServiceFlowParseModelsWhenServiceProcessArrayNewIsServiceFlowParseModelListJson() throws Throwable {
        AbstractFootMessageModuleOpt abstractFootMessageModuleOpt = mock(AbstractFootMessageModuleOpt.class, CALLS_REAL_METHODS);
        List<SkuAttrItemDto> skuAttrs = new ArrayList<>();
        SkuAttrItemDto item = new SkuAttrItemDto();
        item.setAttrName("serviceProcessArrayNew");
        item.setAttrValue("[{\"bodyPart\":\"Back\",\"servicemethod\":\"Massage\",\"stepTime\":30}]");
        skuAttrs.add(item);
        List<ServiceFlowParseModel> result = abstractFootMessageModuleOpt.getServiceFlowParseModels(skuAttrs);
        assertNotNull("Result should not be null", result);
        assertFalse("Result should not be empty", result.isEmpty());
    }
}
