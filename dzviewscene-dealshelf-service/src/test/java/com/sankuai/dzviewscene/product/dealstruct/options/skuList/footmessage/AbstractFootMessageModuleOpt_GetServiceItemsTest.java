package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.SkuAttrAttrItemVO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractFootMessageModuleOpt_GetServiceItemsTest {

    @Mock
    private AbstractFootMessageModuleOpt abstractFootMessageModuleOpt;

    private List<SkuAttrItemDto> skuAttrs = new ArrayList<>();

    // This matches one of the SPECIAL_PRODUCT_CATEGORY_ID_LIST
    private Long productCategory = 2104617L;

    @InjectMocks
    private TestableAbstractFootMessageModuleOpt moduleOpt = new TestableAbstractFootMessageModuleOpt();

    @Mock
    private SkuAttrItemDto skuAttrItemDto;

    @Test
    public void testGetServiceItemsWhenSkuAttrsIsNull() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(null, productCategory);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetServiceItemsWhenProductCategoryIsNotInSpecialProductCategoryIdList() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(skuAttrs, 2104616L);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetServiceItemsWhenAllMethodsReturnNull() throws Throwable {
        List<DealSkuItemVO> result = abstractFootMessageModuleOpt.getServiceItems(skuAttrs, productCategory);
        assertEquals(0, result.size());
    }

    // Implement a minimal stub for the compute method to satisfy the compiler
    private static class TestableAbstractFootMessageModuleOpt extends AbstractFootMessageModuleOpt {

        @Override
        public Object compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, Object source, Object target) {
            // Stub implementation
            return null;
        }
    }

    /**
     * 测试 getServingPosture 方法，当 skuAttrs 为空时
     */
    @Test
    public void testGetServingPostureWhenSkuAttrsIsNull() throws Throwable {
        // arrange
        // act
        DealSkuItemVO result = moduleOpt.getServingPosture(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getServingPosture 方法，当 skuAttrs 不包含 "servingPosture" 属性时
     */
    @Test
    public void testGetServingPostureWhenSkuAttrsNotContainsServingPosture() throws Throwable {
        // arrange
        when(skuAttrItemDto.getAttrName()).thenReturn("other");
        // act
        DealSkuItemVO result = moduleOpt.getServingPosture(Collections.singletonList(skuAttrItemDto));
        // assert
        assertNull(result);
    }

    /**
     * 测试 getServingPosture 方法，当 skuAttrs 包含 "servingPosture" 属性时
     */
    @Test
    public void testGetServingPostureWhenSkuAttrsContainsServingPosture() throws Throwable {
        // arrange
        when(skuAttrItemDto.getAttrName()).thenReturn("servingPosture");
        when(skuAttrItemDto.getAttrValue()).thenReturn("value");
        // act
        DealSkuItemVO result = moduleOpt.getServingPosture(Collections.singletonList(skuAttrItemDto));
        // assert
        assertNotNull(result);
        assertEquals("服务姿势", result.getName());
        assertEquals(0, result.getType());
        assertNull(result.getValueAttrs());
        assertEquals("value", result.getValue());
    }
}
