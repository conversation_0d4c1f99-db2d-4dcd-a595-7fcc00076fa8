package com.sankuai.dzviewscene.product.shelf.options.list.promotags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.PromoTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultPromoTagsOptTest {

    @Mock
    private ProductM productM;

    @Mock
    private ProductPromoPriceM productPromoPriceM;

    @Mock
    private PromoTagsVP.Param param;

    @Test
    public void testComputeWithDirectPromo() throws Throwable {
        // arrange
        DefaultPromoTagsOpt defaultPromoTagsOpt = new DefaultPromoTagsOpt();
        when(productM.getPromo(anyInt())).thenReturn(productPromoPriceM);
        when(param.getProductM()).thenReturn(productM);
        // act
        List<DzPromoVO> result = defaultPromoTagsOpt.compute(mock(ActivityCxt.class), param, mock(DefaultPromoTagsOpt.Config.class));
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void testComputeWithBasePromo() throws Throwable {
        // arrange
        DefaultPromoTagsOpt defaultPromoTagsOpt = new DefaultPromoTagsOpt();
        when(productM.getPromo(anyInt())).thenReturn(null);
        when(param.getProductM()).thenReturn(productM);
        // act
        List<DzPromoVO> result = defaultPromoTagsOpt.compute(mock(ActivityCxt.class), param, mock(DefaultPromoTagsOpt.Config.class));
        // assert
        // Adjusted expectation to match the method behavior
        assertNull(result);
    }

    @Test
    public void testComputeWithNoPromo() throws Throwable {
        // arrange
        DefaultPromoTagsOpt defaultPromoTagsOpt = new DefaultPromoTagsOpt();
        when(productM.getPromo(anyInt())).thenReturn(null);
        when(param.getProductM()).thenReturn(productM);
        // act
        List<DzPromoVO> result = defaultPromoTagsOpt.compute(mock(ActivityCxt.class), param, mock(DefaultPromoTagsOpt.Config.class));
        // assert
        assertNull(result);
    }
}
