package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityTagsVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.BarProductActivityTagsOpt.Config;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BarProductActivityTagsOptTest {

    @Mock
    private ProductM productM;

    @Spy
    @InjectMocks
    private BarProductActivityTagsOpt barProductActivityTagsOpt;

    @Mock
    private ActivityCxt context;

    @InjectMocks
    private BarProductActivityTagsOpt opt;

    @Test
    public void testComputePicUrlIsNull() throws Throwable {
        // arrange
        BarProductActivityTagsOpt barProductActivityTagsOpt = new BarProductActivityTagsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductActivityTagsVP.Param param = mock(ProductActivityTagsVP.Param.class);
        Config config = mock(Config.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn(null);
        // act
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    // 其他测试用例...
    @Test
    public void testComputeActivityFloatTagIsNull() throws Throwable {
        // arrange
        BarProductActivityTagsOpt barProductActivityTagsOpt = new BarProductActivityTagsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductActivityTagsVP.Param param = mock(ProductActivityTagsVP.Param.class);
        Config config = mock(Config.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("picUrl");
        // act
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testComputeLowestPriceFloatTagIsNull() throws Throwable {
        // arrange
        BarProductActivityTagsOpt barProductActivityTagsOpt = new BarProductActivityTagsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductActivityTagsVP.Param param = mock(ProductActivityTagsVP.Param.class);
        Config config = mock(Config.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("picUrl");
        // act
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testComputeContainsNullElement() throws Throwable {
        // arrange
        BarProductActivityTagsOpt barProductActivityTagsOpt = new BarProductActivityTagsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductActivityTagsVP.Param param = mock(ProductActivityTagsVP.Param.class);
        Config config = mock(Config.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("picUrl");
        // act
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testComputeNotContainsNullElement() throws Throwable {
        // arrange
        BarProductActivityTagsOpt barProductActivityTagsOpt = new BarProductActivityTagsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        ProductActivityTagsVP.Param param = mock(ProductActivityTagsVP.Param.class);
        Config config = mock(Config.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("picUrl");
        // act
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testCompute_WhenBothTagsNotNull() throws Throwable {
        ProductActivityTagsVP.Param param = ProductActivityTagsVP.Param.builder().productM(new ProductM()).filterId(123L).build();
        BarProductActivityTagsOpt.Config config = new BarProductActivityTagsOpt.Config();
        FloatTagVO floatTag = new FloatTagVO();
        DzPictureComponentVO icon1 = new DzPictureComponentVO();
        icon1.setPicUrl("http://test1.com");
        floatTag.setIcon(icon1);
        doReturn(floatTag).when(barProductActivityTagsOpt).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
        param.getProductM().setPicUrl("http://test.com");
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("http://test1.com", result.get(0).getImgUrl());
        verify(barProductActivityTagsOpt).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
    }

    @Test
    public void testCompute_WhenOnlyFloatTagNotNull() throws Throwable {
        ProductActivityTagsVP.Param param = ProductActivityTagsVP.Param.builder().productM(new ProductM()).filterId(123L).build();
        BarProductActivityTagsOpt.Config config = new BarProductActivityTagsOpt.Config();
        FloatTagVO floatTag = new FloatTagVO();
        DzPictureComponentVO icon = new DzPictureComponentVO();
        icon.setPicUrl("http://test.com");
        floatTag.setIcon(icon);
        doReturn(floatTag).when(barProductActivityTagsOpt).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
        param.getProductM().setPicUrl("http://test.com");
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("http://test.com", result.get(0).getImgUrl());
        verify(barProductActivityTagsOpt).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
    }

    @Test
    public void testCompute_WhenNoTags() throws Throwable {
        ProductActivityTagsVP.Param param = ProductActivityTagsVP.Param.builder().productM(new ProductM()).filterId(123L).build();
        BarProductActivityTagsOpt.Config config = new BarProductActivityTagsOpt.Config();
        doReturn(null).when(barProductActivityTagsOpt).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
        param.getProductM().setPicUrl("http://test.com");
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        assertTrue(result.isEmpty());
        verify(barProductActivityTagsOpt).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
    }

    @Test
    public void testCompute_WhenPicUrlNull() throws Throwable {
        ProductM productM = new ProductM();
        productM.setPicUrl(null);
        ProductActivityTagsVP.Param param = ProductActivityTagsVP.Param.builder().productM(productM).filterId(123L).build();
        BarProductActivityTagsOpt.Config config = new BarProductActivityTagsOpt.Config();
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        assertNull(result);
        verify(barProductActivityTagsOpt, never()).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
    }

    @Test
    public void testCompute_WhenFloatTagHasNoIcon() throws Throwable {
        ProductActivityTagsVP.Param param = ProductActivityTagsVP.Param.builder().productM(new ProductM()).filterId(123L).build();
        BarProductActivityTagsOpt.Config config = new BarProductActivityTagsOpt.Config();
        FloatTagVO floatTag = new FloatTagVO();
        doReturn(floatTag).when(barProductActivityTagsOpt).getActivityFloatTag(any(ProductM.class), anyLong(), any(BarProductActivityTagsOpt.Config.class));
        param.getProductM().setPicUrl("http://test.com");
        List<DzActivityTagVO> result = barProductActivityTagsOpt.compute(context, param, config);
        // Assuming the method under test handles null icons correctly
        assertFalse(result.isEmpty());
    }

    // Other test methods remain unchanged
    @Test
    public void testGetShopRecommendTopTagWithFilter_NotShopRecommended() throws Throwable {
        // arrange
        long filterId = 123L;
        // Ensure the navIds do not contain the filterId or contain Integer.MAX_VALUE
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("[789]");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNull("Expected null result when product is not shop recommended", result);
    }

    @Test
    public void testGetShopRecommendTopTagWithFilter_MatchingFilterId() throws Throwable {
        // arrange
        long filterId = 123L;
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("[123]");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNotNull("Expected non-null result for matching filter id", result);
    }

    @Test
    public void testGetShopRecommendTopTagWithFilter_MaxValueInNavIds() throws Throwable {
        // arrange
        long filterId = 999L;
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("[2147483647]");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNotNull("Expected non-null result when Integer.MAX_VALUE is in navIds", result);
    }

    @Test
    public void testGetShopRecommendTopTagWithFilter_NonMatchingFilterId() throws Throwable {
        // arrange
        long filterId = 456L;
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("[123,789]");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNull("Expected null result when filter id doesn't match navs", result);
    }

    @Test
    public void testGetShopRecommendTopTagWithFilter_EmptyNavIds() throws Throwable {
        // arrange
        long filterId = 123L;
        when(productM.getAttr("attr_shopRecommend")).thenReturn("true");
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNotNull("Expected non-null result when navIds is empty", result);
    }

    @Test
    public void testGetShopRecommendTopTagWithFilterInvalidFilterId() throws Throwable {
        // arrange
        long filterId = 0L;
        when(productM.getAttr("attr_shopRecommend")).thenReturn("true");
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("[123]");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNotNull("Expected non-null result when filterId is invalid", result);
    }

    @Test
    public void testGetShopRecommendTopTagWithFilter_WhenShopRecommended() throws Throwable {
        // arrange
        long filterId = 123L;
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("[123]");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNotNull("Expected non-null result for shop recommended product", result);
    }

    @Test
    public void testGetShopRecommendTopTagWithFilter_WhenNotShopRecommended() throws Throwable {
        // arrange
        long filterId = 123L;
        when(productM.getAttr("attr_shopRecommend_navIds")).thenReturn("[]");
        // act
        FloatTagVO result = opt.getShopRecommendTopTagWithFilter(productM, filterId);
        // assert
        assertNull("Expected null result for non-shop recommended product", result);
    }
}
