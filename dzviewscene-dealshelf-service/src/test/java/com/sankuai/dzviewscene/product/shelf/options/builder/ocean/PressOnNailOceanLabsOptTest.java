package com.sankuai.dzviewscene.product.shelf.options.builder.ocean;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.ocean.vp.OceanLabsVP.Param;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * PressOnNailOceanLabsOpt.compute 方法的测试类
 */
@RunWith(MockitoJUnitRunner.class)
public class PressOnNailOceanLabsOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    private PressOnNailOceanLabsOpt pressOnNailOceanLabsOpt;
    private PressOnNailOceanLabsOpt.Config configWithFields;
    private PressOnNailOceanLabsOpt.Config configWithoutFields;

    private MockedStatic<PressOnNailUtils> pressOnNailUtilsMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailOceanLabsOpt = new PressOnNailOceanLabsOpt();
        configWithFields = new PressOnNailOceanLabsOpt.Config();
        configWithFields.setFields(Lists.newArrayList("testField"));
        configWithoutFields = new PressOnNailOceanLabsOpt.Config();
        pressOnNailUtilsMockedStatic = Mockito.mockStatic(PressOnNailUtils.class);
    }

    @After
    public void tearDown(){
        pressOnNailUtilsMockedStatic.close();
    }

    /**
     * 测试 compute 方法，当 config 为 null 时
     */
    @Test
    public void testComputeConfigIsNull() {
        // arrange
        Param param = Param.builder().build();

        // act
        Map<String, String> result = pressOnNailOceanLabsOpt.compute(mockActivityCxt, param, null);

        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试 compute 方法，当 config 中的 fields 为空时
     */
    @Test
    public void testComputeFieldsIsEmpty() {
        // arrange
        Param param = Param.builder().build();

        // act
        Map<String, String> result = pressOnNailOceanLabsOpt.compute(mockActivityCxt, param, configWithoutFields);

        // assert
        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试 compute 方法，正常情况
     */
    @Test
    public void testComputeNormal() {
        // arrange
        Param param = Param.builder().build();
        pressOnNailUtilsMockedStatic.when(()->PressOnNailUtils.buildItemOceanMap(any(), any())).thenReturn(new HashMap<>());

        // act
        Map<String, String> result = pressOnNailOceanLabsOpt.compute(mockActivityCxt, param, configWithFields);

        // assert
        assertFalse("结果不应为空", result.isEmpty());
        assertEquals("结果应包含指定字段", "{\"customize_array\":[{}]}", result.get("testField"));
    }
}
