package com.sankuai.dzviewscene.product.shelf.options.builder.filter.image;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ActivityStyle;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import java.lang.reflect.Constructor;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class FilterIdMapFilterBtnImageOptComputeTest {

    @InjectMocks
    private FilterIdMapFilterBtnImageOpt filterIdMapFilterBtnImageOpt;

    private FilterIdMapFilterBtnImageOpt.Param createParam() {
        try {
            Constructor<FilterIdMapFilterBtnImageOpt.Param> constructor = FilterIdMapFilterBtnImageOpt.Param.class.getDeclaredConstructor();
            constructor.setAccessible(true);
            FilterIdMapFilterBtnImageOpt.Param param = constructor.newInstance();
            FilterBtnM filterBtnM = new FilterBtnM();
            filterBtnM.setFilterId(1L);
            param.setFilterBtnM(filterBtnM);
            return param;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create Param instance", e);
        }
    }

    @Test
    public void testComputeReturnActivityStyle() throws Throwable {
        ActivityCxt activityCxt = new ActivityCxt();
        FilterIdMapFilterBtnImageOpt.Param param = createParam();
        FilterBtnM filterBtnM = param.getFilterBtnM();
        filterBtnM.setActivity(true);
        ActivityStyle activityStyle = new ActivityStyle();
        activityStyle.setIconUrl("activity-url");
        filterBtnM.setActivityStyle(activityStyle);
        FilterIdMapFilterBtnImageOpt.Config config = new FilterIdMapFilterBtnImageOpt.Config();
        config.setFilterId2ImageCfg(Maps.newHashMap());
        DzPictureComponentVO result = filterIdMapFilterBtnImageOpt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals("activity-url", result.getPicUrl());
    }

    @Test
    public void testComputeReturnActivityStyleWithoutDimensions() throws Throwable {
        ActivityCxt activityCxt = new ActivityCxt();
        FilterIdMapFilterBtnImageOpt.Param param = createParam();
        FilterBtnM filterBtnM = param.getFilterBtnM();
        filterBtnM.setActivity(true);
        ActivityStyle activityStyle = new ActivityStyle();
        activityStyle.setIconUrl("activity-url");
        filterBtnM.setActivityStyle(activityStyle);
        FilterIdMapFilterBtnImageOpt.Config config = new FilterIdMapFilterBtnImageOpt.Config();
        config.setFilterId2ImageCfg(Maps.newHashMap());
        config.setActivityPicHeight(16);
        DzPictureComponentVO result = filterIdMapFilterBtnImageOpt.compute(activityCxt, param, config);
        assertNotNull(result);
        assertEquals("activity-url", result.getPicUrl());
    }

    @Test
    public void testComputeWithNullConfig() throws Throwable {
        ActivityCxt activityCxt = new ActivityCxt();
        FilterIdMapFilterBtnImageOpt.Param param = createParam();
        try {
            DzPictureComponentVO result = filterIdMapFilterBtnImageOpt.compute(activityCxt, param, null);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // Expected exception
        }
    }

    @Test
    public void testComputeWithNullParam() throws Throwable {
        ActivityCxt activityCxt = new ActivityCxt();
        FilterIdMapFilterBtnImageOpt.Config config = new FilterIdMapFilterBtnImageOpt.Config();
        config.setFilterId2ImageCfg(Maps.newHashMap());
        try {
            DzPictureComponentVO result = filterIdMapFilterBtnImageOpt.compute(activityCxt, null, config);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // Expected exception
        }
    }
}
