package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;

public class KtvSimilarListOpt_GetSimilarDealListTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    private ActivityCxt context;

    private ProductM currentProduct;

    private List<ProductM> list;

    @Before
    public void setUp() {
        ktvSimilarListOpt = new KtvSimilarListOpt();
        context = Mockito.mock(ActivityCxt.class);
        currentProduct = Mockito.mock(ProductM.class);
        list = new ArrayList<>();
    }

    /**
     * 测试getSimilarDealList方法，当pageSource等于GUESS时
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsGuess() {
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("GUESS");
        List<ProductM> result = ktvSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        Assert.assertNotNull(result);
    }

    /**
     * 测试getSimilarDealList方法，当pageSource等于SHELF时
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsShelf() {
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("SHELF");
        List<ProductM> result = ktvSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        Assert.assertNotNull(result);
    }

    /**
     * 测试getSimilarDealList方法，当pageSource既不等于GUESS也不等于SHELF时
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsNotGuessOrShelf() {
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("OTHER");
        List<ProductM> result = ktvSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        Assert.assertTrue(result.isEmpty());
    }
}
