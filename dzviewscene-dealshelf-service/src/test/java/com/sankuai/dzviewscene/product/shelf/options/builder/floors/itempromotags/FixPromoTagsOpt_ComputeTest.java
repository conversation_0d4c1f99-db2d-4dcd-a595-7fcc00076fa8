package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.List;
import static org.junit.Assert.*;

public class FixPromoTagsOpt_ComputeTest {

    private FixPromoTagsOpt fixPromoTagsOpt;

    @Before
    public void setUp() {
        fixPromoTagsOpt = new FixPromoTagsOpt();
    }

    /**
     * 测试config为null的情况
     */
    @Test
    public void testComputeConfigIsNull() {
        List<DzPromoVO> result = fixPromoTagsOpt.compute(null, null, null);
        assertNull(result);
    }

    /**
     * 测试config的text属性为空的情况
     */
    @Test
    public void testComputeConfigTextIsEmpty() {
        FixPromoTagsOpt.Config config = new FixPromoTagsOpt.Config();
        config.setText("");
        List<DzPromoVO> result = fixPromoTagsOpt.compute(null, null, config);
        assertNull(result);
    }

    /**
     * 测试config的text属性不为空，showBorder为true的情况
     */
    @Test
    public void testComputeConfigTextIsNotEmptyAndShowBorderIsTrue() {
        FixPromoTagsOpt.Config config = new FixPromoTagsOpt.Config();
        config.setText("优惠");
        config.setShowBorder(true);
        List<DzPromoVO> result = fixPromoTagsOpt.compute(null, null, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("优惠", result.get(0).getName());
        assertEquals(1, result.get(0).getPosition());
        assertEquals(1, result.get(0).getShowType());
    }

    /**
     * 测试config的text属性不为空，showBorder为false的情况
     */
    @Test
    public void testComputeConfigTextIsNotEmptyAndShowBorderIsFalse() {
        FixPromoTagsOpt.Config config = new FixPromoTagsOpt.Config();
        config.setText("优惠");
        config.setShowBorder(false);
        List<DzPromoVO> result = fixPromoTagsOpt.compute(null, null, config);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("优惠", result.get(0).getName());
        assertEquals(1, result.get(0).getPosition());
        assertEquals(0, result.get(0).getShowType());
    }
}
