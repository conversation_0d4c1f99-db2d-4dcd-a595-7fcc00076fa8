package com.sankuai.dzviewscene.product.shelf.options.builder.filter.showtype;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterShowTypeVP.Param;
import com.sankuai.dzviewscene.product.shelf.options.builder.filter.showtype.MedicalSpecialtyFilterShowTypeOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants.Style;
import org.junit.Test;

import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class MedicalSpecialtyFilterShowTypeOptTest {
    @Test
    public void compute() {
        int expectShowType = 1;
        ActivityCxt ctx = new ActivityCxt();
        ctx.attach(Style.showType, CompletableFuture.completedFuture(expectShowType));

        MedicalSpecialtyFilterShowTypeOpt opt = new MedicalSpecialtyFilterShowTypeOpt();
        Integer actual = opt.compute(ctx, Param.builder().build(), new Config());
        assertEquals(expectShowType, actual);
    }
}