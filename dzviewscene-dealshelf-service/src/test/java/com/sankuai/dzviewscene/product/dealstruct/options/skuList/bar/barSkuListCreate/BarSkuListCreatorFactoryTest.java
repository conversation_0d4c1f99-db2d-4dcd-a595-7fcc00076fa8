package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuListCreate;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.runner.RunWith;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BarSkuListCreatorFactoryTest {

    private void setBarSkuListBuilderList(List<AbstractBarSkuListCreator> newList) throws NoSuchFieldException, IllegalAccessException {
        Field field = BarSkuListCreatorFactory.class.getDeclaredField("barSkuListBuilderList");
        field.setAccessible(true);
        field.set(null, newList);
    }

    @Test
    public void testCreadSkuListBuilderWhenListIsEmpty() throws Throwable {
        List<AbstractBarSkuListCreator> barSkuListBuilderList = new ArrayList<>();
        setBarSkuListBuilderList(barSkuListBuilderList);
        AbstractBarSkuListCreator result = BarSkuListCreatorFactory.creadSkuListBuilder(true, new ArrayList<>(), new BarDealDetailSkuListModuleOpt.Config());
        assertNull(result);
    }

    @Test
    public void testCreadSkuListBuilderWhenNoMatchedObject() throws Throwable {
        List<AbstractBarSkuListCreator> barSkuListBuilderList = new ArrayList<>();
        barSkuListBuilderList.add(mock(AbstractBarSkuListCreator.class));
        when(barSkuListBuilderList.get(0).ideantify(anyBoolean(), anyList(), any())).thenReturn(false);
        setBarSkuListBuilderList(barSkuListBuilderList);
        AbstractBarSkuListCreator result = BarSkuListCreatorFactory.creadSkuListBuilder(true, new ArrayList<>(), new BarDealDetailSkuListModuleOpt.Config());
        assertNull(result);
    }

    @Test
    public void testCreadSkuListBuilderWhenMatchedObjectExists() throws Throwable {
        List<AbstractBarSkuListCreator> barSkuListBuilderList = new ArrayList<>();
        AbstractBarSkuListCreator mockCreator = mock(AbstractBarSkuListCreator.class);
        barSkuListBuilderList.add(mockCreator);
        when(mockCreator.ideantify(anyBoolean(), anyList(), any())).thenReturn(true);
        setBarSkuListBuilderList(barSkuListBuilderList);
        AbstractBarSkuListCreator result = BarSkuListCreatorFactory.creadSkuListBuilder(true, new ArrayList<>(), new BarDealDetailSkuListModuleOpt.Config());
        assertEquals(mockCreator, result);
    }
}
