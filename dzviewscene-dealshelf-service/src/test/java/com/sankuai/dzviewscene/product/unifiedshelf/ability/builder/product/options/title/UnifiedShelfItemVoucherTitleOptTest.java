package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.StyleTextModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * UnifiedShelfItemVoucherTitleOpt.compute 方法的单元测试
 */
@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemVoucherTitleOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @InjectMocks
    private UnifiedShelfItemVoucherTitleOpt voucherTitleOpt;
    @Mock
    private UnifiedShelfItemVoucherTitleOpt.Config config;
    @Mock
    private ProductM productM;
    @Mock
    private UnifiedShelfItemVoucherTitleOpt.Param param;

    /**
     * 测试正常场景：marketPrice 和 voucherTitleFormat 都不为空
     */
    @Test
    public void testComputeNormalScenario() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(config.getVoucherTitleFormat()).thenReturn("%s元代金券");
        when(productM.getMarketPrice()).thenReturn("100");

        // act
        List<StyleTextModel> result = voucherTitleOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertEquals("100元代金券", result.get(0).getText());
    }

    /**
     * 测试边界场景：marketPrice 为空
     */
    @Test
    public void testComputeMarketPriceEmpty() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(config.getVoucherTitleFormat()).thenReturn("%s元代金券");
        when(productM.getMarketPrice()).thenReturn("");

        // act
        List<StyleTextModel> result = voucherTitleOpt.compute(activityCxt, param, config);

        // assert
        assertTrue(StringUtils.isBlank(result.get(0).getText()));
    }

    /**
     * 测试边界场景：voucherTitleFormat 为空
     */
    @Test
    public void testComputeVoucherTitleFormatEmpty() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(config.getVoucherTitleFormat()).thenReturn("");

        // act
        List<StyleTextModel> result = voucherTitleOpt.compute(activityCxt, param, config);

        // assert
        assertTrue(StringUtils.isBlank(result.get(0).getText()));
    }
}
