package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonCarouselMsgVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonCarouselMsgVP.Param;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;

public class ReservationStockStrategyTest {

    private ReservationStockStrategy strategy;
    private ItemButtonCarouselMsgVP.Param param;
    private CarouselBuilderContext context;
    private ProductM productM;

    @Before
    public void setUp() {
        strategy = new ReservationStockStrategy();
        param = Mockito.mock(Param.class);
        context = Mockito.mock(CarouselBuilderContext.class);
        productM = Mockito.mock(ProductM.class);
    }

//    /**
//     * 测试场景：参数为null
//     */
//    @Test
//    public void testBuildParamIsNull() {
//        RichLabelVO result = strategy.build(null);
//        assertNull(result);
//    }

    /**
     * 测试场景：ProductM为null
     */
    @Test
    public void testBuildProductMIsNull() {
        Mockito.when(context.getProductM()).thenReturn(null);
        RichLabelVO result = strategy.build(context);
        assertNull(result);
    }

    /**
     * 测试场景：ProductM的extAttrs为空
     */
    @Test
    public void testBuildExtAttrsIsEmpty() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getExtAttrs()).thenReturn(new ArrayList<>());
        RichLabelVO result = strategy.build(context);
        assertNull(result);
    }

    /**
     * 测试场景：reservation_stock属性不存在
     */
    @Test
    public void testBuildReservationStockNotExists() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("not_reservation_stock", "10"));
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getExtAttrs()).thenReturn(attrs);
        RichLabelVO result = strategy.build(context);
        assertNull(result);
    }

    /**
     * 测试场景：reservation_stock属性存在
     */
    @Test
    public void testBuildReservationStockExists() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("reservation_stock", "10"));
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getExtAttrs()).thenReturn(attrs);
        Mockito.when(productM.getAttr("reservation_stock")).thenReturn("10");
        RichLabelVO result = strategy.build(context);
        assertNotNull(result);
        assertEquals("10", result.getText());
        assertEquals("#FF4B10", result.getTextColor());
        assertEquals(10, result.getTextSize());
    }
}
