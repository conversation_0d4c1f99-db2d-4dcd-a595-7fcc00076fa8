package com.sankuai.dzviewscene.product.ability.options;

import com.dianping.product.shelf.common.enums.NavRouterTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ProductAnchorVP;
import com.sankuai.dzviewscene.product.shelf.common.ProductAnchorInfo;
import com.sankuai.dzviewscene.product.shelf.options.filter.anchor.ConfigKeyAnchorOpt;
import com.sankuai.dzviewscene.product.shelf.options.filter.anchor.ConfigKeyAnchorOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.config.FilterConfig.MultipleFilterConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Lists;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

/**
 * 测试ConfigKeyAnchorOpt的compute方法
 */
@RunWith(MockitoJUnitRunner.class)
public class ConfigKeyAnchorOptTest {

    @InjectMocks
    private ConfigKeyAnchorOpt configKeyAnchorOpt;

    @Mock
    private FilterConfig filterConfig;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试搜索词为空时返回首tab
     */
    @Test
    public void testComputeWithBlankKeywordReturnsFirstNav() {
        ActivityCxt context = mock(ActivityCxt.class);
        when(context.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("");

        ProductAnchorVP.Param param = ProductAnchorVP.Param.builder().ctx(context).build();
        ProductAnchorInfo result = configKeyAnchorOpt.compute(context, param, new Config());

        assertEquals(NavRouterTypeEnum.DEAL_FIRST_NAV_TYPE.getType(), result.getRouterType());
    }

    /**
     * 测试filterKeywords为空时返回首tab
     */
    @Test
    public void testComputeWithEmptyFilterKeywordsReturnsFirstNav() {
        ActivityCxt context = mock(ActivityCxt.class);
        when(context.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("keyword");
        //when(filterConfig.getMultipleFilterKeywords(anyString(), anyString())).thenReturn(new HashMap<>());
        ProductAnchorVP.Param param = ProductAnchorVP.Param.builder().ctx(context).build();
        ProductAnchorInfo result = configKeyAnchorOpt.compute(context, param, new Config());

        assertEquals(NavRouterTypeEnum.DEAL_FIRST_NAV_TYPE.getType(), result.getRouterType());
    }

    /**
     * 测试匹配到关键词时返回正确的导航信息
     */
    @Test
    public void testComputeWithMatchedKeywordReturnsCorrectNav() {
        ActivityCxt context = mock(ActivityCxt.class);
        when(context.getSceneCode()).thenReturn("sceneCode");
        when(context.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("keyword");
        Map<String, MultipleFilterConfig> filterKeywords = new HashMap<>();
        MultipleFilterConfig config = new MultipleFilterConfig();
        config.setKey(Lists.newArrayList("keyword"));
        filterKeywords.put("matchedKey", config);
        when(filterConfig.getMultipleFilterKeywords(anyString(), anyString())).thenReturn(filterKeywords);
        ProductAnchorVP.Param param = ProductAnchorVP.Param.builder().ctx(context).build();
        ProductAnchorInfo result = configKeyAnchorOpt.compute(context, param, new Config());

        assertEquals(NavRouterTypeEnum.DEAL_NAV_NAME_TYPE.getType(), result.getRouterType());
        assertEquals("matchedKey", result.getMatchKey());
    }

    /**
     * 测试未匹配到关键词时返回首tab
     */
    @Test
    public void testComputeWithNoMatchedKeywordReturnsFirstNav() {
        ActivityCxt context = mock(ActivityCxt.class);
        when(context.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("keyword");
        //when(filterConfig.getMultipleFilterKeywords(anyString(), anyString())).thenReturn(new HashMap<>());
        ProductAnchorVP.Param param = ProductAnchorVP.Param.builder().ctx(context).build();
        ProductAnchorInfo result = configKeyAnchorOpt.compute(context, param, new Config());

        assertEquals(NavRouterTypeEnum.DEAL_FIRST_NAV_TYPE.getType(), result.getRouterType());
    }
}

