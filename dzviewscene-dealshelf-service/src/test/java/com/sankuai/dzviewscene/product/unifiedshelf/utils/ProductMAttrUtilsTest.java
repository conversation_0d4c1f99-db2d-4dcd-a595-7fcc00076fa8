package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.shoppadding.ContextHandlerAbility;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Lists;

import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

public class ProductMAttrUtilsTest {

    @Mock
    private ProductM mockProductM;

    @Mock
    private DealProductSpuDTO mockSpuM;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试standardSpu方法，当productM为null时
     */
    @Test
    public void testStandardSpu_WithNullProductM() {
        // arrange
        ProductM productM = null;

        // act
        boolean result = ProductMAttrUtils.standardSpu(productM);

        // assert
        assertFalse("当productM为null时，应返回false", result);
    }

    /**
     * 测试standardSpu方法，当productM不为null，但既不是爆品也不是爆品检查通过的情况
     */
    @Test
    public void testStandardSpu_WithNonHotSpuAndNonHotSpuCheck() {
        // arrange
        when(mockProductM.getAttr("topPerformingProduct")).thenReturn("0");
        when(mockProductM.getAttr(ProductMAttrUtils.DEAL_HOT_SPU_CHECK_ATTR)).thenReturn("false");

        // act
        boolean result = ProductMAttrUtils.standardSpu(mockProductM);

        // assert
        assertFalse("当productM不为null，但既不是爆品也不是爆品检查通过的情况，应返回false", result);
    }

    /**
     * 测试standardSpu方法，当productM不为null且是爆品的情况
     */
    @Test
    public void testStandardSpu_WithHotSpu() {
        // arrange
        when(mockProductM.getAttr("topPerformingProduct")).thenReturn("1");

        // act
        boolean result = ProductMAttrUtils.standardSpu(mockProductM);

        // assert
        assertTrue("当productM不为null且是爆品的情况，应返回true", result);
    }

    /**
     * 测试standardSpu方法，当productM不为null且爆品检查通过的情况
     */
    @Test
    public void testStandardSpu_WithHotSpuCheck() {
        // arrange
        when(mockProductM.getAttr(ProductMAttrUtils.DEAL_HOT_SPU_CHECK_ATTR)).thenReturn("true");

        // act
        boolean result = ProductMAttrUtils.standardSpu(mockProductM);

        // assert
        assertTrue("当productM不为null且爆品检查通过的情况，应返回true", result);
    }


    /**
     * 测试getStandardSpuName方法，当productM为null时
     */
    @Test
    public void testGetStandardSpuName_WithNullProductM() {
        String result = ProductMAttrUtils.getStandardSpuName(null);
        assertNull("当productM为null时，应返回null", result);
    }

    /**
     * 测试getStandardSpuName方法，当productM的spuM为null时
     */
    @Test
    public void testGetStandardSpuName_WithNullSpuM() {
        when(mockProductM.getSpuM()).thenReturn(null);
        String result = ProductMAttrUtils.getStandardSpuName(mockProductM);
        assertNull("当productM的spuM为null时，应返回null", result);
    }

    /**
     * 测试getStandardSpuName方法，当productM和spuM都不为null时
     */
    @Test
    public void testGetStandardSpuName_WithValidProductMAndSpuM() {
        when(mockProductM.getSpuM()).thenReturn(mockSpuM);
        when(mockSpuM.getSpuName()).thenReturn("标准SPU名称");
        String result = ProductMAttrUtils.getStandardSpuName(mockProductM);
        assertNotNull("当productM和spuM都不为null时，不应返回null", result);
        assertEquals("标准SPU名称", result);
    }

    /**
     * 测试getStandardSpuName方法，当productM和spuM都不为null时
     */
    @Test
    public void testGetStandardSpuName_getStandardSpuPic() {
        when(mockProductM.getSpuM()).thenReturn(mockSpuM);
        when(mockSpuM.getHeadPic()).thenReturn("标准SPU名称");
        String result = ProductMAttrUtils.getStandardSpuPic(mockProductM);
        assertNotNull("当productM和spuM都不为null时，不应返回null", result);
        assertEquals("标准SPU名称", result);
    }

    @Test
    public void test_getSuperDealProductTags() {
        List<String> strings = Lists.newArrayList("富强", "民主");
        ProductM productM = new ProductM();
        DealProductSpuDTO spuDTO = new DealProductSpuDTO();
        spuDTO.setSpuKeyInformation(strings);
        productM.setSpuM(spuDTO);
        List<String> superDealProductTags = ProductMAttrUtils.getSuperDealProductTags(productM);
        Assert.assertTrue(CollectionUtils.isEqualCollection(strings, superDealProductTags));
    }

    @Test
    public void test_isUniteStarProductnull(){
        ProductM productM = new ProductM();
        productM.setProductId(12345);
        boolean uniteStarProduct = ProductMAttrUtils.isUniteStarProduct(productM);
        assertFalse(uniteStarProduct);
    }

}
