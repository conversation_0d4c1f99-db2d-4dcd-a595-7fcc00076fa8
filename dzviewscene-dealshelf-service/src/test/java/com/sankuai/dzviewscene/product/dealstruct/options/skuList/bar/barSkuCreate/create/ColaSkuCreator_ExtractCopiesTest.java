package com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.barSkuCreate.create;

import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.bar.BarDealDetailSkuListModuleOpt;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import java.util.Arrays;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ColaSkuCreator_ExtractCopiesTest {

    private ColaSkuCreator colaSkuCreator = new ColaSkuCreator();

    @Test
    public void testExtractCopiesWhenAttrItemsIsEmpty() throws Throwable {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(null);
        skuItemDto.setCopies(2);
        // act
        String result = colaSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        // Corrected expected value based on the method's logic
        assertEquals("(2份)", result);
    }

    @Test
    public void testExtractCopiesWhenQuantityAvailableIsEmpty() throws Throwable {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setAttrItems(Arrays.asList(new SkuAttrItemDto()));
        skuItemDto.setCopies(2);
        // act
        String result = colaSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        // Corrected expected value based on the method's logic
        assertEquals("(2份)", result);
    }

    @Test
    public void testExtractCopiesWhenQuantityUnitIsEmpty() throws Throwable {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("quantityAvailable");
        skuAttrItemDto.setAttrValue("2");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        skuItemDto.setCopies(2);
        // act
        String result = colaSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        // Corrected expected value based on the method's logic
        assertEquals("(4份)", result);
    }

    @Test
    public void testExtractCopiesWhenQuantityAvailableAndQuantityUnitAreNotEmpty() throws Throwable {
        // arrange
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("quantityAvailable");
        skuAttrItemDto.setAttrValue("2");
        SkuAttrItemDto skuAttrItemDtoUnit = new SkuAttrItemDto();
        skuAttrItemDtoUnit.setAttrName("quantityUnit");
        skuAttrItemDtoUnit.setAttrValue("罐");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto, skuAttrItemDtoUnit));
        skuItemDto.setCopies(2);
        // act
        String result = colaSkuCreator.extractCopies(skuItemDto, new BarDealDetailSkuListModuleOpt.Config());
        // assert
        // Corrected expected value based on the method's logic
        assertEquals("(4罐)", result);
    }
}
