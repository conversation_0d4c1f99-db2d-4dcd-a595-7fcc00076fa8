package com.sankuai.dzviewscene.product.shelf.options.list.producttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AttrProductTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    @Test
    public void testComputeProductIsNull() throws Throwable {
        // arrange
        AttrProductTagsOpt attrProductTagsOpt = new AttrProductTagsOpt();
        AttrProductTagsOpt.Param param = mock(AttrProductTagsOpt.Param.class);
        when(param.getProductM()).thenReturn(null);
        // act
        List<String> result = attrProductTagsOpt.compute(activityCxt, param, new AttrProductTagsOpt.Config());
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeAttrIsEmpty() throws Throwable {
        // arrange
        AttrProductTagsOpt attrProductTagsOpt = new AttrProductTagsOpt();
        AttrProductTagsOpt.Param param = mock(AttrProductTagsOpt.Param.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(anyString())).thenReturn("");
        // act
        List<String> result = attrProductTagsOpt.compute(activityCxt, param, new AttrProductTagsOpt.Config());
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeAttrIsNotEmpty() throws Throwable {
        // arrange
        AttrProductTagsOpt attrProductTagsOpt = new AttrProductTagsOpt();
        AttrProductTagsOpt.Param param = mock(AttrProductTagsOpt.Param.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(anyString())).thenReturn("testAttr");
        // act
        List<String> result = attrProductTagsOpt.compute(activityCxt, param, new AttrProductTagsOpt.Config());
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testAttr", result.get(0));
    }
}
