package com.sankuai.dzviewscene.product.filterlist.option.builder.product.purchase;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPurchaseVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductPurchaseOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductPurchaseVP.Param param;

    @Mock
    private ProductM productM;

    /**
     * 测试 compute 方法，当 ProductM 的 purchase 属性为空时，应返回 null
     */
    @Test
    public void testComputePurchaseIsNull() throws Throwable {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPurchase()).thenReturn(null);
        ProductPurchaseOpt productPurchaseOpt = new ProductPurchaseOpt();
        // act
        RichLabelVO result = productPurchaseOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }

    /**
     * 测试 compute 方法，当 ProductM 的 purchase 属性不为空时，应返回一个 RichLabelVO 对象
     */
    @Test
    public void testComputePurchaseIsNotNull() throws Throwable {
        // arrange
        String purchase = "100";
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPurchase()).thenReturn(purchase);
        ProductPurchaseOpt productPurchaseOpt = new ProductPurchaseOpt();
        // act
        RichLabelVO result = productPurchaseOpt.compute(context, param, null);
        // assert
        assertNotNull(result);
        assertEquals(purchase, result.getText());
    }
}
