package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.aggregate.posthandler.process.AggregatePostHandlerFactory;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.aggregate.AggregatePostHandlerVP;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class CommonAggregatePostHandlerOptTest {

    @InjectMocks
    private CommonAggregatePostHandlerOpt postHandlerOpt;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private AggregatePostHandlerFactory aggregatePostHandlerFactory;

    private AggregatePostHandlerVP.Param param;
    private CommonAggregatePostHandlerOpt.Config config;

    @Before
    public void setUp() {
        // 初始化配置
        config = new CommonAggregatePostHandlerOpt.Config();
        param = AggregatePostHandlerVP.Param.builder().build();
        param.setShelfItemVO(new ShelfItemVO()); // 初始化ShelfItemVO
    }

    /**
     * 测试执行后置处理策略
     */
    @Test
    public void testComputeWithHandlers() {
        // arrange
        config.setHandlerConfig(Lists.newArrayList("MassageTitleProcessHandler", "SpuProcessHandler"));
        ShelfItemVO result = new ShelfItemVO();


        // act
        ShelfItemVO finalResult = postHandlerOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(finalResult);
        // 根据处理逻辑进行其他断言
    }

    /**
     * 测试没有配置后置处理策略的场景
     */
    @Test
    public void testComputeWithoutHandlers() {
        // arrange
        config.setHandlerConfig(Lists.newArrayList());

        // act
        ShelfItemVO finalResult = postHandlerOpt.compute(activityCxt, param, config);

        // assert
        assertNotNull(finalResult);
        // 验证返回的结果与输入一致
        assertEquals(param.getShelfItemVO(), finalResult);
    }
}