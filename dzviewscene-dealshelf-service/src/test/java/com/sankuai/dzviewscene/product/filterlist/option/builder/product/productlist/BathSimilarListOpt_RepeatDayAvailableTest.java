package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AvailableDateM;
import com.sankuai.dzviewscene.shelf.platform.common.model.CycleAvailableDateM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import java.util.Arrays;

public class BathSimilarListOpt_RepeatDayAvailableTest {

    private BathSimilarListOpt bathSimilarListOpt;

    @Before
    public void setUp() {
        bathSimilarListOpt = new BathSimilarListOpt();
    }

    /**
     * 测试两个AvailableDateM对象都为空时的情况
     */
    @Test
    public void testRepeatDayAvailableBothNull() throws Throwable {
        AvailableDateM currentAvailableDateM = new AvailableDateM();
        AvailableDateM availableDateM = new AvailableDateM();
        Assert.assertFalse(bathSimilarListOpt.repeatDayAvailable(currentAvailableDateM, availableDateM));
    }

    /**
     * 测试两个AvailableDateM对象中有一个为空时的情况
     */
    @Test
    public void testRepeatDayAvailableOneNull() throws Throwable {
        AvailableDateM availableDateM = new AvailableDateM();
        // Simulates null by being empty
        AvailableDateM emptyAvailableDateM = new AvailableDateM();
        Assert.assertFalse(bathSimilarListOpt.repeatDayAvailable(emptyAvailableDateM, availableDateM));
        Assert.assertFalse(bathSimilarListOpt.repeatDayAvailable(availableDateM, emptyAvailableDateM));
    }

    /**
     * 测试两个AvailableDateM对象都不为空，但没有交集时的情况
     */
    @Test
    public void testRepeatDayAvailableNoIntersection() throws Throwable {
        AvailableDateM currentAvailableDateM = new AvailableDateM();
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(Arrays.asList(1, 2, 3));
        currentAvailableDateM.setCycleAvailableDateList(Arrays.asList(cycleAvailableDateM));
        AvailableDateM availableDateM = new AvailableDateM();
        CycleAvailableDateM cycleAvailableDateM2 = new CycleAvailableDateM();
        cycleAvailableDateM2.setAvailableDays(Arrays.asList(4, 5, 6));
        availableDateM.setCycleAvailableDateList(Arrays.asList(cycleAvailableDateM2));
        Assert.assertFalse(bathSimilarListOpt.repeatDayAvailable(currentAvailableDateM, availableDateM));
    }

    /**
     * 测试两个AvailableDateM对象都不为空，且有交集时的情况
     */
    @Test
    public void testRepeatDayAvailableWithIntersection() throws Throwable {
        AvailableDateM currentAvailableDateM = new AvailableDateM();
        CycleAvailableDateM cycleAvailableDateM = new CycleAvailableDateM();
        cycleAvailableDateM.setAvailableDays(Arrays.asList(1, 2, 3));
        currentAvailableDateM.setCycleAvailableDateList(Arrays.asList(cycleAvailableDateM));
        AvailableDateM availableDateM = new AvailableDateM();
        CycleAvailableDateM cycleAvailableDateM2 = new CycleAvailableDateM();
        cycleAvailableDateM2.setAvailableDays(Arrays.asList(2, 3, 4));
        availableDateM.setCycleAvailableDateList(Arrays.asList(cycleAvailableDateM2));
        Assert.assertTrue(bathSimilarListOpt.repeatDayAvailable(currentAvailableDateM, availableDateM));
    }
}
