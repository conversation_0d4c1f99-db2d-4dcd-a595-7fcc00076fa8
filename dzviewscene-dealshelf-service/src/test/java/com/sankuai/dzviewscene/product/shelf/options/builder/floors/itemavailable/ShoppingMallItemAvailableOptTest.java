package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemavailable;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemAvailableVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Before;

@RunWith(MockitoJUnitRunner.class)
public class ShoppingMallItemAvailableOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ItemAvailableVP.Param param;

    @Mock
    private ProductM productM;

    @InjectMocks
    private ShoppingMallItemAvailableOpt opt;

    @Test(expected = NullPointerException.class)
    public void testComputeWhenProductIsNull() throws Throwable {
        // Setup
        when(param.getProductM()).thenReturn(null);
        // Execution
        // Expecting NullPointerException
        opt.compute(context, param, null);
    }

    @Test
    public void testComputeWhenStageIsNotInUnavailableStages() throws Throwable {
        // Setup
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr(WarmUpStageEnum.ATTRIBUTE_KEY_WARM_UP_START_TIME)).thenReturn("someStartTime");
        when(productM.getAttr(WarmUpStageEnum.ATTRIBUTE_KEY_TIME_STOCK)).thenReturn("someTimeStock");
        // Execution
        Boolean actual = opt.compute(context, param, null);
        // Verification
        assertTrue("The item should be available when stage is not in unavailable stages", actual);
    }
}
