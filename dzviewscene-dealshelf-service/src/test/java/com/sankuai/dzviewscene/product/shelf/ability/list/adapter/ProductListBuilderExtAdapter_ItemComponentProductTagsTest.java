package com.sankuai.dzviewscene.product.shelf.ability.list.adapter;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.ProductTagsVP;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class ProductListBuilderExtAdapter_ItemComponentProductTagsTest {

    @InjectMocks
    private ProductListBuilderExtAdapter productListBuilderExtAdapter;

    @Mock
    private PmfExecutionHelper pmfExecutionHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试itemComponentProductTags方法，正常情况
     */
    @Test
    public void testItemComponentProductTagsNormal() throws Throwable {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductM productM = mock(ProductM.class);
        long filterId = 1L;
        ProductTagsVP productTagsVP = mock(ProductTagsVP.class);
        List<String> expected = Arrays.asList("tag1", "tag2");
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(productTagsVP);
        when(productTagsVP.execute(any(ActivityCxt.class), any())).thenReturn(expected);
        // act
        List<String> result = productListBuilderExtAdapter.itemComponentProductTags(activityContext, groupName, productM, filterId);
        // assert
        assertEquals(expected, result);
    }

    /**
     * 测试itemComponentProductTags方法，异常情况
     */
    @Test(expected = RuntimeException.class)
    public void testItemComponentProductTagsException() throws Throwable {
        // arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductM productM = mock(ProductM.class);
        long filterId = 1L;
        ProductTagsVP productTagsVP = mock(ProductTagsVP.class);
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(productTagsVP);
        when(productTagsVP.execute(any(ActivityCxt.class), any())).thenThrow(new RuntimeException());
        // act
        productListBuilderExtAdapter.itemComponentProductTags(activityContext, groupName, productM, filterId);
        // assert
        // Exception is expected
    }
}
