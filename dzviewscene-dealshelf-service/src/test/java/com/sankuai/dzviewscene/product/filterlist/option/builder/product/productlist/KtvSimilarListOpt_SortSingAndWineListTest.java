package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class KtvSimilarListOpt_SortSingAndWineListTest {

    private KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();

    private void simulateProductServiceTypeAndSale(ProductM product, String serviceType, int sale) {
        AttrM attrM = new AttrM("SERVICE_TYPE", serviceType);
        ProductSaleM saleM = new ProductSaleM();
        saleM.setSale(sale);
        // Correctly return the list of AttrM objects when getExtAttrs() is called
        Mockito.when(product.getExtAttrs()).thenReturn(Arrays.asList(attrM));
        // Simulate the sale value
        Mockito.when(product.getSale()).thenReturn(saleM);
    }

    @Test(expected = NullPointerException.class)
    public void testSortSingAndWineListWhenFilterListIsNull() throws Throwable {
        ktvSimilarListOpt.sortSingAndWineList(null);
    }
}
