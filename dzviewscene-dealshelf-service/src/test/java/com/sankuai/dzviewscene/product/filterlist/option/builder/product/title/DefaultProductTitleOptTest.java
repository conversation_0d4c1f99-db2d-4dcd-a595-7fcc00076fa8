package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;

/**
 * @author: created by hang.yu on 2024/3/4 19:13
 */
@RunWith(MockitoJUnitRunner.class)
public class DefaultProductTitleOptTest {

    @Test
    public void testCompute() {
        ActivityCxt context = mock(ActivityCxt.class);

        DefaultProductTitleOpt defaultProductTitleOpt = new DefaultProductTitleOpt();

        ProductM productM = new ProductM();
        productM.setTitle("模拟名称");
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();

        String title = defaultProductTitleOpt.compute(context, param, null);
        Assert.assertEquals(title, "模拟名称");
    }

}