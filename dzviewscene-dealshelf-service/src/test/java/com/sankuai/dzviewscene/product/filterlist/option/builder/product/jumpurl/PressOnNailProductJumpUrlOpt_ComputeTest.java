package com.sankuai.dzviewscene.product.filterlist.option.builder.product.jumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class PressOnNailProductJumpUrlOpt_ComputeTest {

    private PressOnNailProductJumpUrlOpt pressOnNailProductJumpUrlOpt;

    private ActivityCxt context;

    private PressOnNailProductJumpUrlOpt.Param param;

    private PressOnNailProductJumpUrlOpt.Config config;

    @Before
    public void setUp() {
        pressOnNailProductJumpUrlOpt = new PressOnNailProductJumpUrlOpt();
        context = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(PressOnNailProductJumpUrlOpt.Param.class);
        config = Mockito.mock(PressOnNailProductJumpUrlOpt.Config.class);
    }

    /**
     * 测试 ProductM 为 null 的情况
     */
    @Test
    public void testCompute_ProductMIsNull_ReturnEmptyString() {
        when(param.getProductM()).thenReturn(null);
        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);
        assertEquals("", result);
    }

    /**
     * 测试 ProductM 不为 null，但 jumpUrl 为空的情况
     */
    @Test
    public void testCompute_JumpUrlIsEmpty_ReturnEmptyString() {
        ProductM productM = new ProductM();
        when(param.getProductM()).thenReturn(productM);
        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);
        assertEquals("", result);
    }

    /**
     * 测试 ProductM 不为 null，jumpUrl 不为空，但 skuIdList 为空的情况
     */
    @Test
    public void testCompute_SkuIdListIsEmpty_ReturnJumpUrl() {
        ProductM productM = new ProductM();
        productM.setJumpUrl("http://example.com");
        when(param.getProductM()).thenReturn(productM);
        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);
        assertEquals("http://example.com", result);
    }

    /**
     * 测试 ProductM 不为 null，jumpUrl 不为空，skuIdList 不为空的情况
     */
    @Test
    public void testCompute_SkuIdListIsNotEmpty_ReturnJumpUrlWithSkuInitIndex() {
        ProductM productM = new ProductM();
        productM.setJumpUrl("http://example.com");
        productM.setSkuIdList(Arrays.asList("SKU123"));
        when(param.getProductM()).thenReturn(productM);
        String result = pressOnNailProductJumpUrlOpt.compute(context, param, config);
        assertEquals("http://example.com&skuinitindex=SKU123", result);
    }
}
