package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
// Correct import for DealDetailSkuUniStructuredDto
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Config config;

    private DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Param createParamInstance() throws Exception {
        Constructor<DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Param> constructor = DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Param.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    private DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Config createConfigWithAttrListGroupModels() {
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Config config = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Config();
        List<DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        config.setAttrListGroupModels(attrListGroupModels);
        return config;
    }

    @Test
    public void testComputeConfigOrParamIsNull() throws Throwable {
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        assertNull(opt.compute(context, null, config));
        assertNull(opt.compute(context, param, null));
    }

    @Test
    public void testComputeDealAttrsIsEmpty() throws Throwable {
        when(param.getDealAttrs()).thenReturn(new ArrayList<>());
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeDealDetailDtoModelIsNull() throws Throwable {
        when(param.getDealDetailDtoModel()).thenReturn(null);
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeMustGroupsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = mock(DealDetailDtoModel.class);
        when(dealDetailDtoModel.getSkuUniStructuredDto()).thenReturn(null);
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeSkuItemsIsEmpty() throws Throwable {
        DealDetailDtoModel dealDetailDtoModel = mock(DealDetailDtoModel.class);
        // Correctly mock the expected return type using its fully qualified name
        when(dealDetailDtoModel.getSkuUniStructuredDto()).thenReturn(mock(DealDetailSkuUniStructuredDto.class));
        when(param.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeAttrListGroupModelsIsEmpty() throws Throwable {
        when(config.getAttrListGroupModels()).thenReturn(new ArrayList<>());
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeAttrListGroupModelsIsNotEmpty() throws Throwable {
        when(config.getAttrListGroupModels()).thenReturn(new ArrayList<>());
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeWithBothDealAttrsAndFirstSkuAttrs() throws Throwable {
        // arrange
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        ActivityCxt context = new ActivityCxt();
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Param param = createParamInstance();
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Config config = createConfigWithAttrListGroupModels();
        // Set up dealAttrs
        List<AttrM> dealAttrs = Arrays.asList(new AttrM("name1", "value1"));
        param.setDealAttrs(dealAttrs);
        // Set up firstSkuAttrs through DealDetailDtoModel
        DealDetailDtoModel dealDetailDtoModel = mock(DealDetailDtoModel.class);
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testComputeWithOnlyFirstSkuAttrs() throws Throwable {
        // arrange
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        ActivityCxt context = new ActivityCxt();
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Param param = createParamInstance();
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Config config = createConfigWithAttrListGroupModels();
        // Set up empty dealAttrs
        param.setDealAttrs(new ArrayList<>());
        // Set up firstSkuAttrs through DealDetailDtoModel
        DealDetailDtoModel dealDetailDtoModel = mock(DealDetailDtoModel.class);
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testComputeWithOnlyDealAttrs() throws Throwable {
        // arrange
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt opt = new DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt();
        ActivityCxt context = new ActivityCxt();
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Param param = createParamInstance();
        DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.Config config = createConfigWithAttrListGroupModels();
        // Set up dealAttrs
        List<AttrM> dealAttrs = Arrays.asList(new AttrM("name1", "value1"));
        param.setDealAttrs(dealAttrs);
        // Set up empty DealDetailDtoModel
        param.setDealDetailDtoModel(new DealDetailDtoModel());
        // Set up config
        List<DealAttrVOListBuiltByDealAttrAndFirstSkuAttrOpt.AttrListGroupModel> attrListGroupModels = new ArrayList<>();
        config.setAttrListGroupModels(attrListGroupModels);
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }
}
