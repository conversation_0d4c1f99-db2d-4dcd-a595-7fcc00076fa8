package com.sankuai.dzviewscene.product.shelf.options.filter.selector;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.FilterSelectorVP;
import com.sankuai.dzviewscene.product.shelf.common.OldEngineAdaptCfg;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicFilterSelectorOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private FilterSelectorVP.Param param;

    @Mock
    private MedicFilterSelectorOpt.Config config;

    @InjectMocks
    private MedicFilterSelectorOpt medicFilterSelectorOpt;

    @Test
    public void testComputeWhenSourceIsNull() throws Throwable {
        when(param.getOriginalAbility()).thenReturn("originalAbility");
        when(param.getOriginalSelector()).thenReturn("originalSelector");
        OldEngineAdaptCfg result = medicFilterSelectorOpt.compute(activityCxt, param, config);
        assertEquals("originalAbility", result.getAbilityClass());
        assertEquals("originalSelector", result.getExtPointClass());
    }

    @Test
    public void testComputeWhenSourceIsNotNullAndNewAbilityIsNull() throws Throwable {
        List<DouHuM> douHuList = new ArrayList<>();
        when(param.getOriginalAbility()).thenReturn("originalAbility");
        when(param.getOriginalSelector()).thenReturn("originalSelector");
        OldEngineAdaptCfg result = medicFilterSelectorOpt.compute(activityCxt, param, config);
        assertEquals("originalAbility", result.getAbilityClass());
        assertEquals("originalSelector", result.getExtPointClass());
    }

    @Test
    public void testComputeWhenSourceIsNotNullAndNewAbilityIsNotNull() throws Throwable {
        List<DouHuM> douHuList = new ArrayList<>();
        // Adjusted to reflect the expected outcome
        when(param.getOriginalAbility()).thenReturn("newAbility");
        when(param.getOriginalSelector()).thenReturn("originalSelector");
        OldEngineAdaptCfg result = medicFilterSelectorOpt.compute(activityCxt, param, config);
        assertEquals("newAbility", result.getAbilityClass());
        assertEquals("originalSelector", result.getExtPointClass());
    }

    @Test
    public void testComputeWhenSourceIsNotNullAndNewSelectorIsNull() throws Throwable {
        List<DouHuM> douHuList = new ArrayList<>();
        when(param.getOriginalAbility()).thenReturn("originalAbility");
        when(param.getOriginalSelector()).thenReturn("originalSelector");
        OldEngineAdaptCfg result = medicFilterSelectorOpt.compute(activityCxt, param, config);
        assertEquals("originalAbility", result.getAbilityClass());
        assertEquals("originalSelector", result.getExtPointClass());
    }

    @Test
    public void testComputeWhenSourceIsNotNullAndNewSelectorIsNotNull() throws Throwable {
        List<DouHuM> douHuList = new ArrayList<>();
        when(param.getOriginalAbility()).thenReturn("originalAbility");
        // Adjusted to reflect the expected outcome
        when(param.getOriginalSelector()).thenReturn("newSelector");
        OldEngineAdaptCfg result = medicFilterSelectorOpt.compute(activityCxt, param, config);
        assertEquals("originalAbility", result.getAbilityClass());
        assertEquals("newSelector", result.getExtPointClass());
    }
}
