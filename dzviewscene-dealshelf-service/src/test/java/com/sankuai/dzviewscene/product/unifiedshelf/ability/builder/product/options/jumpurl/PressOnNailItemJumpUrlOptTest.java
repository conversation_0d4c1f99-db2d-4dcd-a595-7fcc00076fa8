package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl.PressOnNailItemJumpUrlOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.UnifiedShelfItemJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailItemJumpUrlOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductM productM;

    private PressOnNailItemJumpUrlOpt.Param param;

    @Before
    public void setUp() {
        param = PressOnNailItemJumpUrlOpt.Param.builder().productM(productM).build();
    }

    @Test
    public void testCompute_JumpUrlBlankAndSkuListNotEmpty() throws Throwable {
        // arrange
        PressOnNailItemJumpUrlOpt opt = new PressOnNailItemJumpUrlOpt();
        when(productM.getJumpUrl()).thenReturn("");
        // act
        String result = opt.compute(context, param, null);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testCompute_JumpUrlNotBlankAndSkuListEmpty() throws Throwable {
        // arrange
        PressOnNailItemJumpUrlOpt opt = new PressOnNailItemJumpUrlOpt();
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(Arrays.asList());
        // act
        String result = opt.compute(context, param, null);
        // assert
        assertEquals("http://example.com", result);
    }

    @Test
    public void testCompute_JumpUrlBlankAndSkuListEmpty() throws Throwable {
        // arrange
        PressOnNailItemJumpUrlOpt opt = new PressOnNailItemJumpUrlOpt();
        when(productM.getJumpUrl()).thenReturn("");
        // act
        String result = opt.compute(context, param, null);
        // assert
        assertEquals("", result);
    }

    @Test
    public void testCompute_JumpUrlNotBlankAndSkuListNotEmpty() throws Throwable {
        // arrange
        PressOnNailItemJumpUrlOpt opt = new PressOnNailItemJumpUrlOpt();
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(Arrays.asList("1", "2", "3"));
        // act
        String result = opt.compute(context, param, null);
        // assert
        assertEquals("http://example.com&skuinitindex=1", result);
    }
}
