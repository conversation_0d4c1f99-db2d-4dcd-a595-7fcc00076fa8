package com.sankuai.dzviewscene.product.dealstruct.ability.fetcher;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.uniform.UniformStructContentModel;
import com.sankuai.dzviewscene.shelf.platform.common.ability.padding.MultiGroupPaddingFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import org.junit.rules.ExpectedException;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class DealDetailFetcherTest {

    // Using ExpectedException Rule to verify that the code throws a specific exception
    @Rule
    public ExpectedException thrown = ExpectedException.none();

    private DealDetailFetcher createDealDetailFetcher() {
        return new DealDetailFetcher();
    }

    @InjectMocks
    private DealDetailFetcher dealDetailFetcher;

    @Mock
    private MultiGroupPaddingFetcher multiGroupPaddingFetcher;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetDealModuleAttrsProductMIsNull() throws Throwable {
        DealDetailFetcher dealDetailFetcher = createDealDetailFetcher();
        List<UniformStructContentModel> result = dealDetailFetcher.getDealModuleAttrs(null);
        assertNull(result);
    }

    @Test
    public void testGetDealModuleAttrsAttrIsEmpty() throws Throwable {
        DealDetailFetcher dealDetailFetcher = createDealDetailFetcher();
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.getAttr(anyString())).thenReturn("");
        List<UniformStructContentModel> result = dealDetailFetcher.getDealModuleAttrs(productM);
        assertNull(result);
    }

    @Test
    public void testGetDealModuleAttrsNormal() throws Throwable {
        DealDetailFetcher dealDetailFetcher = createDealDetailFetcher();
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.getAttr(anyString())).thenReturn("[{\"type\":\"test\",\"data\":null}]");
        List<UniformStructContentModel> result = dealDetailFetcher.getDealModuleAttrs(productM);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test", result.get(0).getType());
        assertNull(result.get(0).getData());
    }

    @Test
    public void testBuildAndConvertProductGroupM2DealDetailInfoModel() {
        // 准备测试数据
        ActivityCxt activityCxt = new ActivityCxt();
        DealDetailFecherParam fecherParam = new DealDetailFecherParam();
        DealDetailFetcherCfg fetcherCfg = new DealDetailFetcherCfg();
        fetcherCfg.setPlanId("testPlanId");
        fetcherCfg.setAttributeKeys(Arrays.asList("attr1", "attr2"));

        // 模拟 ProductM
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setTitle("Test Deal");
        productM.setMarketPrice(new BigDecimal("100").toString());
        productM.setBasePrice(new BigDecimal("80"));

        // 模拟 ProductGroupM
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(Collections.singletonList(productM));

        // 模拟 MultiGroupPaddingFetcher 的行为
        Map<String, ProductGroupM> groupName2ProductGroupMMap = new HashMap<>();
        groupName2ProductGroupMMap.put("deal", productGroupM);
        activityCxt.addParam(ProductDetailActivityConstants.Params.productId, 1);
        when(multiGroupPaddingFetcher.build(any())).thenReturn(CompletableFuture.completedFuture(groupName2ProductGroupMMap));

        // 执行测试
        CompletableFuture<List<DealDetailInfoModel>> future = dealDetailFetcher.build(activityCxt, fecherParam, fetcherCfg);

        // 验证结果
        List<DealDetailInfoModel> result = future.join();
        assertNotNull(result);
        assertEquals(1, result.size());

        DealDetailInfoModel dealDetailInfoModel = result.get(0);
        assertEquals(1, dealDetailInfoModel.getDealId());
        assertEquals("Test Deal", dealDetailInfoModel.getDealTitle());
        assertEquals("100", dealDetailInfoModel.getMarketPrice());
        assertEquals("80", dealDetailInfoModel.getSalePrice());

        // 验证 MultiGroupPaddingFetcher 被调用
        verify(multiGroupPaddingFetcher).build(any());
    }
}
