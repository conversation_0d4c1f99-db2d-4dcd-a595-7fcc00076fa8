package com.sankuai.dzviewscene.product.filterlist.option.builder.product.producttags;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductTagsSupportPreSaleOpt_GetPreSaleProductTagsTest {

    @Mock
    private ProductM productM;

    @Test
    public void testGetPreSaleProductTagsWhenTemplateIsEmptyAndDateIsNotEmpty() {
        // arrange
        ProductTagsSupportPreSaleOpt productTagsSupportPreSaleOpt = new ProductTagsSupportPreSaleOpt();
        ProductTagsSupportPreSaleOpt.Config config = new ProductTagsSupportPreSaleOpt.Config();
        config.setPreSaleTemplate("");
        when(productM.getAttr(config.getPreSaleDateAttrKey())).thenReturn("2022-01-01");
        when(productM.getProductTags()).thenReturn(Arrays.asList("tag1", "tag2"));
        // act
        List<String> result = productTagsSupportPreSaleOpt.getPreSaleProductTags(productM, config);
        // assert
        assertEquals(Arrays.asList("tag1", "tag2"), result);
    }

    @Test
    public void testGetPreSaleProductTagsWhenTemplateIsNotEmptyAndDateIsEmpty() {
        // arrange
        ProductTagsSupportPreSaleOpt productTagsSupportPreSaleOpt = new ProductTagsSupportPreSaleOpt();
        ProductTagsSupportPreSaleOpt.Config config = new ProductTagsSupportPreSaleOpt.Config();
        config.setPreSaleTemplate("%s后可用");
        when(productM.getAttr(config.getPreSaleDateAttrKey())).thenReturn("");
        when(productM.getProductTags()).thenReturn(Arrays.asList("tag1", "tag2"));
        // act
        List<String> result = productTagsSupportPreSaleOpt.getPreSaleProductTags(productM, config);
        // assert
        assertEquals(Arrays.asList("tag1", "tag2"), result);
    }

    @Test
    public void testGetPreSaleProductTagsWhenTemplateAndDateAreEmpty() {
        // arrange
        ProductTagsSupportPreSaleOpt productTagsSupportPreSaleOpt = new ProductTagsSupportPreSaleOpt();
        ProductTagsSupportPreSaleOpt.Config config = new ProductTagsSupportPreSaleOpt.Config();
        config.setPreSaleTemplate("");
        when(productM.getAttr(config.getPreSaleDateAttrKey())).thenReturn("");
        when(productM.getProductTags()).thenReturn(Arrays.asList("tag1", "tag2"));
        // act
        List<String> result = productTagsSupportPreSaleOpt.getPreSaleProductTags(productM, config);
        // assert
        assertEquals(Arrays.asList("tag1", "tag2"), result);
    }

    @Test
    public void testGetPreSaleProductTagsWhenTemplateAndDateAreNotEmpty() {
        // arrange
        ProductTagsSupportPreSaleOpt productTagsSupportPreSaleOpt = new ProductTagsSupportPreSaleOpt();
        ProductTagsSupportPreSaleOpt.Config config = new ProductTagsSupportPreSaleOpt.Config();
        config.setPreSaleTemplate("%s后可用");
        when(productM.getAttr(config.getPreSaleDateAttrKey())).thenReturn("2022-01-01");
        // act
        List<String> result = productTagsSupportPreSaleOpt.getPreSaleProductTags(productM, config);
        // assert
        assertEquals(Arrays.asList("2022-01-01后可用"), result);
    }
}
