package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListGroupTitleVP;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSkuListGroupTitleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuListGroupTitleVP.Param param;

    @Mock
    private DefaultSkuListGroupTitleOpt.Config config;

    @Test
    public void testComputeMustGroup() {
        // arrange
        when(param.isMustGroup()).thenReturn(true);
        DefaultSkuListGroupTitleOpt opt = new DefaultSkuListGroupTitleOpt();
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeFormatEmpty() {
        // arrange
        when(param.isMustGroup()).thenReturn(false);
        when(config.getFormat()).thenReturn("");
        when(param.getTotalNum()).thenReturn(2);
        when(param.getOptionalNum()).thenReturn(3);
        DefaultSkuListGroupTitleOpt opt = new DefaultSkuListGroupTitleOpt();
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertEquals("以下2选3", result);
    }

    @Test
    public void testComputeFormatNotEmpty() {
        // arrange
        when(param.isMustGroup()).thenReturn(false);
        when(config.getFormat()).thenReturn("自定义格式：%d选%d");
        when(param.getTotalNum()).thenReturn(2);
        when(param.getOptionalNum()).thenReturn(3);
        DefaultSkuListGroupTitleOpt opt = new DefaultSkuListGroupTitleOpt();
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertEquals("自定义格式：2选3", result);
    }
}
