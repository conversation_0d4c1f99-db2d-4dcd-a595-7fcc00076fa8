package com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.options.DealQueryParallelGrayOpt;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.ProductParallelQueryPostHandler.Config;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery.ProductParallelQueryPostHandler.Request;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.filterfirst.FilterFirstFetcher;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.framework.ComponentFinder;
import com.sankuai.dzviewscene.shelf.platform.common.ability.degrade.query.ShopOnlineDealDegradeQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.fetcher.query.ShelfQueryFetcher;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductParallelQueryPostHandlerTest {

    @InjectMocks
    private ProductParallelQueryPostHandler productParallelQueryPostHandler;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ShopOnlineDealDegradeQueryFetcher shopOnlineDealDegradeQueryFetcher;

    @Mock
    private ComponentFinder componentFinder;

    @Mock
    private ShelfQueryFetcher shelfQueryFetcher;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试并行灰度关闭时，返回null的情况
     */
    @Test
    public void testBuildParallelGrayOff() throws Throwable {
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(DealQueryParallelGrayOpt.CODE, false);
        // arrange
        when(activityCxt.getParameters()).thenReturn(parameters);

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = productParallelQueryPostHandler.build(activityCxt, new Request(), new Config());

        // assert
        assertNull(result.get());
    }

    /**
     * 测试并行灰度开启，但没有商品也没有导航召回时，返回空结果的情况
     */
    @Test
    public void testBuildNoProductNoFilter() throws Throwable {
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(DealQueryParallelGrayOpt.CODE, true);
        // arrange
        when(activityCxt.getParameters()).thenReturn(parameters);
        when(activityCxt.getSource(anyString())).thenReturn(null);

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = productParallelQueryPostHandler.build(activityCxt, new Request(), new Config());

        // assert
        assertTrue(result.get().isEmpty());
    }

    /**
     * 测试并行灰度开启，有商品召回但没有导航召回的情况
     */
    @Test
    public void testBuildWithProductNoFilter() throws Throwable {
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(DealQueryParallelGrayOpt.CODE, true);
        // arrange
        when(activityCxt.getParameters()).thenReturn(parameters);
        when(activityCxt.getSource(ProductParallelQueryFetcher.CODE)).thenReturn(getProductGroupMMap());
        when(shopOnlineDealDegradeQueryFetcher.build(any(ActivityContext.class))).thenReturn(CompletableFuture.completedFuture(getProductGroupMMap()));

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = productParallelQueryPostHandler.build(activityCxt, new Request(), new Config());

        // assert
        assertNotNull(result.get());
        assertFalse(result.get().isEmpty());
    }

    /**
     * 测试并行灰度开启，没有商品召回但有导航召回的情况
     */
    @Test
    public void testBuildNoProductWithFilter() throws Throwable {
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(DealQueryParallelGrayOpt.CODE, true);
        // arrange
        when(activityCxt.getParameters()).thenReturn(parameters);
        when(activityCxt.getSource(ProductParallelQueryFetcher.CODE)).thenReturn(null);
        when(activityCxt.getSource(FilterFirstFetcher.CODE)).thenReturn(getFilterMMap());
        when(componentFinder.findAbility(any(ActivityContext.class), anyString())).thenReturn(shelfQueryFetcher);
        when(shelfQueryFetcher.build(any(ActivityContext.class))).thenReturn(CompletableFuture.completedFuture(getProductGroupMMap()));

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = productParallelQueryPostHandler.build(activityCxt, new Request(), new Config());

        // assert
        assertNotNull(result.get());
        assertFalse(result.get().isEmpty());
    }

    /**
     * 测试并行灰度开启，有商品召回且有导航召回，筛选id能匹配的情况
     */
    @Test
    public void testBuildWithProductAndFilterMatch() throws Throwable {
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(DealQueryParallelGrayOpt.CODE, true);
        // arrange
        when(activityCxt.getParameters()).thenReturn(parameters);
        when(activityCxt.getSource(ProductParallelQueryFetcher.CODE)).thenReturn(getProductGroupMMap());
        when(activityCxt.getSource(FilterFirstFetcher.CODE)).thenReturn(getFilterMMap());

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = productParallelQueryPostHandler.build(activityCxt, new Request(), new Config());

        // assert
        assertNotNull(result.get());
        assertFalse(result.get().isEmpty());
    }

    /**
     * 测试并行灰度开启，有商品召回且有导航召回，筛选id不能匹配的情况
     */
    @Test
    public void testBuildWithProductAndFilterNoMatch() throws Throwable {
        Map<String, Object> parameters = Maps.newHashMap();
        parameters.put(DealQueryParallelGrayOpt.CODE, true);
        parameters.put(ShelfActivityConstants.Params.recalledFilterId, 1);
        parameters.put(ShelfActivityConstants.Params.selectedFilterId, 2);
        // arrange
        Map<String, FilterM> filterMMap = Maps.newHashMap();
        FilterM filterM = new FilterM();
        FilterBtnM child2 = new FilterBtnM();
        child2.setFilterId(1);
        FilterBtnM child1 = new FilterBtnM();
        child1.setFilterId(5);
        child1.setChildren(Lists.newArrayList(child2));
        FilterBtnM filterBtnM1 = new FilterBtnM();
        filterBtnM1.setFilterId(10);
        filterBtnM1.setChildren(Lists.newArrayList(child1));
        FilterBtnM filterBtnM2 = new FilterBtnM();
        filterBtnM2.setFilterId(2);
        filterM.setFilters(Lists.newArrayList(filterBtnM1, filterBtnM2));
        filterMMap.put("团购", filterM);
        Map<String, ProductGroupM> productGroupMMap = Maps.newHashMap();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(Lists.newArrayList(new ProductM()));
        productGroupMMap.put("团购", productGroupM);
        when(activityCxt.getParameters()).thenReturn(parameters);
        when(activityCxt.getSource(ProductParallelQueryFetcher.CODE)).thenReturn(productGroupMMap);
        when(activityCxt.getSource(FilterFirstFetcher.CODE)).thenReturn(filterMMap);

        // act
        CompletableFuture<Map<String, ProductGroupM>> result = productParallelQueryPostHandler.build(activityCxt, new Request(), new Config());

        // assert
        assertNotNull(result.get());
        assertFalse(result.get().isEmpty());
    }

    private Map<String, FilterM> getFilterMMap() {
        Map<String, FilterM> filterMMap = Maps.newHashMap();
        FilterM filterM = new FilterM();
        filterM.setFilters(Lists.newArrayList(new FilterBtnM()));
        filterMMap.put("团购", filterM);
        return filterMMap;
    }

    private Map<String, ProductGroupM> getProductGroupMMap() {
        Map<String, ProductGroupM> productGroupMMap = Maps.newHashMap();
        ProductGroupM productGroupM = new ProductGroupM();
        productGroupM.setProducts(Lists.newArrayList(new ProductM()));
        productGroupMMap.put("团购", productGroupM);
        return productGroupMMap;
    }
}
