package com.sankuai.dzviewscene.product.productdetail.options.productintroduction;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.productintroduction.vpoints.DetailIntroductionVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.Content;
import org.junit.runner.RunWith;
import java.util.Collections;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultIntroductionOptTest {

    /**
     * Tests that the compute method returns null.
     */
    @Test
    public void testComputeReturnsNull() throws Throwable {
        // arrange
        DefaultIntroductionOpt defaultIntroductionOpt = new DefaultIntroductionOpt();
        ActivityCxt context = new ActivityCxt();
        // Using the builder pattern to create an instance of Param
        DetailIntroductionVP.Param param = DetailIntroductionVP.Param.builder().productMs(Collections.emptyList()).build();
        // Using an instance of DefaultIntroductionOpt to create Cfg
        DefaultIntroductionOpt.Cfg cfg = defaultIntroductionOpt.new Cfg();
        // act
        Content result = defaultIntroductionOpt.compute(context, param, cfg);
        // assert
        assertNull(result);
    }
}
