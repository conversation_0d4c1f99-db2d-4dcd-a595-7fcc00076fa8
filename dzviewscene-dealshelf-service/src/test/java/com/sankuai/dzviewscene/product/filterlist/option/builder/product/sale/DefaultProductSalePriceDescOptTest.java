package com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceDescVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceDescVP.Param;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale.DefaultProductSalePriceDescOpt;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.sale.DefaultProductSalePriceDescOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultProductSalePriceDescOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DefaultProductSalePriceDescOpt.Param param;

    @Mock
    private DefaultProductSalePriceDescOpt.Config config;

    @Mock
    private ProductM // Assuming ProductM is the correct type to be returned by param.getProductM()
    productM;

    /**
     * Tests the compute method under normal conditions.
     */
    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        DefaultProductSalePriceDescOpt opt = new DefaultProductSalePriceDescOpt();
        when(param.getProductM()).thenReturn(productM);
        // Assuming getBasePriceDesc() is a method of ProductM
        when(productM.getBasePriceDesc()).thenReturn("basePriceDesc");
        // act
        String result = opt.compute(activityCxt, param, config);
        // assert
        assertEquals("basePriceDesc", result);
    }

    /**
     * Tests the compute method when ProductM object is null.
     */
    @Test(expected = NullPointerException.class)
    public void testComputeProductMIsNull() throws Throwable {
        // arrange
        DefaultProductSalePriceDescOpt opt = new DefaultProductSalePriceDescOpt();
        when(param.getProductM()).thenReturn(null);
        // act
        opt.compute(activityCxt, param, config);
    }

    /**
     * Tests the compute method when getBasePriceDesc returns an empty string.
     */
    @Test
    public void testComputeBasePriceDescIsEmpty() throws Throwable {
        // arrange
        DefaultProductSalePriceDescOpt opt = new DefaultProductSalePriceDescOpt();
        when(param.getProductM()).thenReturn(productM);
        // Assuming getBasePriceDesc() is a method of ProductM
        when(productM.getBasePriceDesc()).thenReturn("");
        // act
        String result = opt.compute(activityCxt, param, config);
        // assert
        assertEquals("", result);
    }
}
