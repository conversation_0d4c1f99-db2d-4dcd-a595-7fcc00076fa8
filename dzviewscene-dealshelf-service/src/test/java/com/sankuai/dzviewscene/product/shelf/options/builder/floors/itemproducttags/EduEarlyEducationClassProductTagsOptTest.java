package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;

@RunWith(MockitoJUnitRunner.class)
public class EduEarlyEducationClassProductTagsOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ItemProductTagsVP.Param param;

    @Mock
    private ProductM productM;

    @Test
    public void testComputeProductIsNull() throws Throwable {
        EduEarlyEducationClassProductTagsOpt opt = new EduEarlyEducationClassProductTagsOpt();
        when(param.getProductM()).thenReturn(null);
        List<String> result = opt.compute(context, param, null);
        assertNull(result);
    }

//    @Test
//    public void testComputeProductIsPreSale() throws Throwable {
//        try (MockedStatic<PreSaleUtils> mockedStatic = Mockito.mockStatic(PreSaleUtils.class)) {
//            EduEarlyEducationClassProductTagsOpt opt = new EduEarlyEducationClassProductTagsOpt();
//            mockedStatic.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(true);
//            // Additional setup and assertions as needed for pre-sale scenario
//        }
//    }

    @Test
    public void testComputeProductIsNotPreSaleButTagsIsEmpty() throws Throwable {
        try (MockedStatic<PreSaleUtils> mockedStatic = Mockito.mockStatic(PreSaleUtils.class)) {
            EduEarlyEducationClassProductTagsOpt opt = new EduEarlyEducationClassProductTagsOpt();
            when(param.getProductM()).thenReturn(productM);
            mockedStatic.when(() -> PreSaleUtils.isPreSaleDeal(productM)).thenReturn(false);
            List<String> result = opt.compute(context, param, null);
            assertNull(result);
        }
    }
}
