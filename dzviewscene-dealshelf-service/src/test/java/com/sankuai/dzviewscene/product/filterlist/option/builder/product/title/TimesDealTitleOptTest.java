package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductTitleVP;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;

/**
 * @author: created by hang.yu on 2024/3/4 19:18
 */
@RunWith(MockitoJUnitRunner.class)
public class TimesDealTitleOptTest {

    @Test
    public void testCompute() {
        ActivityCxt context = mock(ActivityCxt.class);

        TimesDealTitleOpt timesDealTitleOpt = new TimesDealTitleOpt();

        ProductM productM = new ProductM();
        productM.setTradeType(19);
        productM.setAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER, "2");
        productM.setSalePrice(new BigDecimal("100"));
        productM.setTimesDealQueryFlag(true);
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();


        TimesDealTitleOpt.Config config = new TimesDealTitleOpt.Config();


        String title = timesDealTitleOpt.compute(context, param, config);

        Assert.assertEquals(title, "2次 单次¥50");
    }

    @Test
    public void testCompute2() {
        ActivityCxt context = mock(ActivityCxt.class);

        TimesDealTitleOpt timesDealTitleOpt = new TimesDealTitleOpt();

        ProductM productM = new ProductM();
        productM.setTradeType(19);
        productM.setSalePrice(new BigDecimal("100"));
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();

        TimesDealTitleOpt.Config config = new TimesDealTitleOpt.Config();

        String title = timesDealTitleOpt.compute(context, param, config);

        Assert.assertNull(title);
    }


    @Test
    public void testCompute3() {
        ActivityCxt context = mock(ActivityCxt.class);

        TimesDealTitleOpt timesDealTitleOpt = new TimesDealTitleOpt();

        ProductM productM = new ProductM();
        productM.setTradeType(20);
        productM.setSalePrice(new BigDecimal("100"));
        productM.setTimesDealQueryFlag(true);
        ProductTitleVP.Param param = ProductTitleVP.Param.builder().productM(productM).build();

        TimesDealTitleOpt.Config config = new TimesDealTitleOpt.Config();

        String title = timesDealTitleOpt.compute(context, param, config);

        Assert.assertEquals(title, "1次 单次¥100");
    }

}