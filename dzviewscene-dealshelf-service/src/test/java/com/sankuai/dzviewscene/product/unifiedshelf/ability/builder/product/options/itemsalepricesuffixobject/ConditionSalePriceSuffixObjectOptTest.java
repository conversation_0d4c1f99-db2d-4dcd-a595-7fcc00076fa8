package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemsalepricesuffixobject;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfSalePriceSuffixVO;
import com.sankuai.dzviewscene.product.shelf.utils.ApplianceShelfUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试ConditionSalePriceSuffixObjectOpt.compute方法
 */
@RunWith(MockitoJUnitRunner.class)
public class ConditionSalePriceSuffixObjectOptTest {

    private static final String CAMPAIGN_PRICE_DESC = "campaginPriceDesc";

    private static final String IS_MULTI_SKU_KEY = "dealMultiSkuFieldAttr";

    @Mock
    private ActivityCxt mockContext;
    @Mock
    private ConditionSalePriceSuffixObjectOpt.Param mockParam;
    @Mock
    private ProductM productM;
    @Mock
    private ConditionSalePriceSuffixObjectOpt.Config mockConfig;
    @InjectMocks
    private ConditionSalePriceSuffixObjectOpt optUnderTest;

    /**
     * 测试配置为空时返回null
     */
    @Test
    public void testComputeConfigEmptyReturnsNull() {
        // arrange
        when(mockConfig.getSuffixTypes()).thenReturn(null);

        // act
        ShelfSalePriceSuffixVO result = optUnderTest.compute(mockContext, mockParam, mockConfig);

        // assert
        assertNull(result);
    }

    /**
     * 测试未找到匹配的类型时返回null
     */
    @Test
    public void testComputeNoMatchingTypeReturnsNull() {
        // arrange
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("nonExistingType"));

        // act
        ShelfSalePriceSuffixVO result = optUnderTest.compute(mockContext, mockParam, mockConfig);

        // assert
        assertNull(result);
    }

    /**
     * 测试正常场景，返回非null的ShelfSalePriceSuffixVO对象
     */
    @Test
    public void testComputeValidTypeReturnsNonNull() {
        // arrange
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("times"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(mockParam.getProductM().getAttr("sys_multi_sale_number")).thenReturn("3");

        // act
        ShelfSalePriceSuffixVO result = optUnderTest.compute(mockContext, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertEquals("/3次", result.getSalePriceSuffix());
    }

    @Test
    public void testComputeValidTypeReturnsCampaign() {
        // arrange
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("campaign"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(mockParam.getProductM().getAttr(CAMPAIGN_PRICE_DESC)).thenReturn("test");

        // act
        ShelfSalePriceSuffixVO result = optUnderTest.compute(mockContext, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertEquals("test", result.getSalePriceSuffix());
    }

    @Test
    public void testComputeValidTypeReturnsMultiSku() {
        // arrange
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("multiSku"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(mockParam.getProductM().getAttr(IS_MULTI_SKU_KEY)).thenReturn("true");

        // act
        ShelfSalePriceSuffixVO result = optUnderTest.compute(mockContext, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertEquals("起", result.getSalePriceSuffix());
    }

    @Test
    public void testComputeValidTypeReturnsMiniprogram() {
        // arrange
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("miniprogram"));
        when(mockParam.getProductM()).thenReturn(productM);
        when(productM.getAttr(ApplianceShelfUtils.ATTR_MINIPROGRAM_LINKED)).thenReturn("是");

        // act
        ShelfSalePriceSuffixVO result = optUnderTest.compute(mockContext, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertEquals("起", result.getSalePriceSuffix());
    }

    @Test
    public void testComputeValidTypeReturnsRecycleMiniprogram() {
        // arrange
        when(mockConfig.getSuffixTypes()).thenReturn(Arrays.asList("recycleMiniprogram"));

        // act
        ShelfSalePriceSuffixVO result = optUnderTest.compute(mockContext, mockParam, mockConfig);

        // assert
        assertNotNull(result);
        assertEquals("免费估价", result.getSalePriceSuffix());
    }
}
