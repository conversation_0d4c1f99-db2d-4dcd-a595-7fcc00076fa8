package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import org.junit.After;
import org.junit.Before;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;

@RunWith(MockitoJUnitRunner.class)
public class KtvSimilarListOpt_GetPeriodListTest {

    @Mock
    private ProductM productM;

    @Test
    public void testGetPeriodListWhenSkuItemListIsEmpty() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        List<String> result = ktvSimilarListOpt.getPeriodList(productM);
        assertEquals("The result list should be empty when the SKU item list is empty", 0, result.size());
    }

    @Test
    public void testGetPeriodListWhenPeriodAttrIsNull() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        List<String> result = ktvSimilarListOpt.getPeriodList(productM);
        assertEquals("The result list should be empty when the period attribute is null", 0, result.size());
    }

    @Test
    public void testGetPeriodListWhenPeriodAttrIsEmpty() throws Throwable {
        KtvSimilarListOpt ktvSimilarListOpt = new KtvSimilarListOpt();
        List<String> result = ktvSimilarListOpt.getPeriodList(productM);
        assertEquals("The result list should be empty when the period attribute is empty", 0, result.size());
    }
}
