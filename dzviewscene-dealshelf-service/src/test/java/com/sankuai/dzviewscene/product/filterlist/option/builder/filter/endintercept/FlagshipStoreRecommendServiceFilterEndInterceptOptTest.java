package com.sankuai.dzviewscene.product.filterlist.option.builder.filter.endintercept;

import com.alibaba.fastjson.JSON;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreJumpUrlVP;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.filter.vp.FilterEndInterceptVP;
import com.sankuai.dzviewscene.product.filterlist.model.FlagshipStoreM;
import com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl.EnvConfigurableMoreJumpUrlOpt;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterVO;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterBtnVO;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.mtstore.aggregate.thrift.dto.decorate.StoreRecommendProjectItemDTO;
import com.sankuai.mtstore.aggregate.thrift.dto.resp.GetStoreRecommendProjectRespDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.*;
import org.junit.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class FlagshipStoreRecommendServiceFilterEndInterceptOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private FilterEndInterceptVP.Param param;

    @Mock
    private FlagshipStoreRecommendServiceFilterEndInterceptOpt.Config config;

    private FlagshipStoreRecommendServiceFilterEndInterceptOpt flagshipStoreRecommendServiceFilterEndInterceptOpt;

    public FlagshipStoreRecommendServiceFilterEndInterceptOptTest() {
        flagshipStoreRecommendServiceFilterEndInterceptOpt = new FlagshipStoreRecommendServiceFilterEndInterceptOpt();
    }

    @Test
    public void testComputeChildrenIsNull() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        filterList.add(filterVO);
        when(param.getFilterList()).thenReturn(filterList);
        flagshipStoreRecommendServiceFilterEndInterceptOpt.compute(activityCxt, param, config);
        // Adjusted to times(2)
        verify(param, times(2)).getFilterList();
    }

    @Test
    public void testComputeChildrenSizeLessThan3() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            children.add(new DzFilterBtnVO());
        }
        filterVO.setChildren(children);
        filterList.add(filterVO);
        when(param.getFilterList()).thenReturn(filterList);
        flagshipStoreRecommendServiceFilterEndInterceptOpt.compute(activityCxt, param, config);
        // Adjusted to times(2)
        verify(param, times(2)).getFilterList();
    }

    @Test
    public void testComputeChildrenSizeGreaterThan6() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            children.add(new DzFilterBtnVO());
        }
        filterVO.setChildren(children);
        filterList.add(filterVO);
        when(param.getFilterList()).thenReturn(filterList);
        flagshipStoreRecommendServiceFilterEndInterceptOpt.compute(activityCxt, param, config);
        // Adjusted to times(2)
        verify(param, times(2)).getFilterList();
    }

    @Test
    public void testComputeChildrenSizeBetween3And6() throws Throwable {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            children.add(new DzFilterBtnVO());
        }
        filterVO.setChildren(children);
        filterList.add(filterVO);
        when(param.getFilterList()).thenReturn(filterList);
        flagshipStoreRecommendServiceFilterEndInterceptOpt.compute(activityCxt, param, config);
        // Adjusted to times(2)
        verify(param, times(2)).getFilterList();
    }

    @Test
    public void test_ctxFlagshipStoreUrl() {
        EnvConfigurableMoreJumpUrlOpt jumpUrlOpt = new EnvConfigurableMoreJumpUrlOpt();
        EnvConfigurableMoreJumpUrlOpt.Param param = DealFilterListMoreJumpUrlVP.Param.builder()
                .userAgent(100).platform(1).build();
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl,"https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html");
        Assert.assertTrue(jumpUrlOpt.compute(context, param, null).equals("https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html"));
    }

    @Test
    public void test_ctxFlagshipStoreUrl1() {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO dzFilterBtnVO = new DzFilterBtnVO();
        dzFilterBtnVO.setFilterId(200128514L);
        children.add(dzFilterBtnVO);
        DzFilterBtnVO dzFilterBtnVO1 = new DzFilterBtnVO();
        dzFilterBtnVO1.setFilterId(200128522L);
        children.add(dzFilterBtnVO1);
        DzFilterBtnVO dzFilterBtnVO2 = new DzFilterBtnVO();
        dzFilterBtnVO2.setFilterId(200128523L);
        children.add(dzFilterBtnVO2);
        filterVO.setChildren(children);
        filterList.add(filterVO);
        Map<Long, String> filterId2Img = new HashMap<>();
        filterId2Img.put(200128514L,"https://p0.meituan.net/travelcube/36ef018adcfc6a292f17d79f2ad7af6c25861.jpg");
        filterId2Img.put(200128522L,"https://p0.meituan.net/travelcube/a35857aeccaa85275d874b8ac0451a4628343.jpg");
        filterId2Img.put(200128523L,"https://p0.meituan.net/travelcube/a35857aeccaa85275d874b8ac0451a4628343.jpg");
        when(config.getFilterId2Img()).thenReturn(filterId2Img);
        when(param.getFilterList()).thenReturn(filterList);
        when(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl)).thenReturn("https://g.dianping.com/csr/biz-product-list/play-brand-group-list.html");
        flagshipStoreRecommendServiceFilterEndInterceptOpt.compute(activityCxt, param, config);
        Assert.assertNotNull(param.getFilterList());
    }

    @Test
    public void test_ctxFlagshipStoreUrl2() {
        List<DzFilterVO> filterList = new ArrayList<>();
        DzFilterVO filterVO = new DzFilterVO();
        List<DzFilterBtnVO> children = new ArrayList<>();
        DzFilterBtnVO dzFilterBtnVO = new DzFilterBtnVO();
        dzFilterBtnVO.setFilterId(200128514L);
        children.add(dzFilterBtnVO);
        DzFilterBtnVO dzFilterBtnVO1 = new DzFilterBtnVO();
        dzFilterBtnVO1.setFilterId(200128522L);
        children.add(dzFilterBtnVO1);
        DzFilterBtnVO dzFilterBtnVO2 = new DzFilterBtnVO();
        dzFilterBtnVO2.setFilterId(200128523L);
        children.add(dzFilterBtnVO2);
        filterVO.setChildren(children);
        filterList.add(filterVO);
        Map<Long, String> filterId2Img = new HashMap<>();
        filterId2Img.put(200128514L,"https://p0.meituan.net/travelcube/36ef018adcfc6a292f17d79f2ad7af6c25861.jpg");
        filterId2Img.put(200128522L,"https://p0.meituan.net/travelcube/a35857aeccaa85275d874b8ac0451a4628343.jpg");
        filterId2Img.put(200128523L,"https://p0.meituan.net/travelcube/a35857aeccaa85275d874b8ac0451a4628343.jpg");
        when(config.getFilterId2Img()).thenReturn(filterId2Img);
        HashMap<Integer, FlagshipStoreRecommendServiceFilterEndInterceptOpt.UrlCfg> platform2Url = new HashMap<>();
        FlagshipStoreRecommendServiceFilterEndInterceptOpt.UrlCfg urlCfg = new FlagshipStoreRecommendServiceFilterEndInterceptOpt.UrlCfg();
        urlCfg.setUrlFormat("https:/mapi.51ping.com/Apppage?ApplicationPageTypeId=10009&ModuleKey=FlagMRN/module/mrnmodule_FlagAllGoodsShelf&filterbtnid=%s");
        urlCfg.setPrefixWithURLEncode("dianping://web?url=");
        platform2Url.put(100,urlCfg);
        //when(config.getPlatform2Url()).thenReturn(platform2Url);
        when(config.doGetPlatformUrlCfgByUserAgent(100)).thenReturn(urlCfg);
        when(param.getFilterList()).thenReturn(filterList);
        when(param.getUserAgent()).thenReturn(100);
        flagshipStoreRecommendServiceFilterEndInterceptOpt.compute(activityCxt, param, config);
        Assert.assertNotNull(param.getFilterList());
    }

    @Test
    public void test_GetStoreRecommendProjectRespDTO(){
        String filterJson = "[{\"children\":[{\"filterId\":200128513,\"id\":200128513,\"multiSelect\":false,\"name\":\"推荐\",\"selectable\":true,\"selected\":true,\"type\":0},{\"children\":[{\"filterId\":200128574,\"id\":200128574,\"multiSelect\":false,\"name\":\"全部\",\"selectable\":true,\"selected\":false,\"type\":0},{\"filterId\":200128515,\"id\":200128515,\"multiSelect\":false,\"name\":\"深层清洁\",\"selectable\":true,\"selected\":false,\"type\":0}],\"filterId\":200128514,\"id\":200128514,\"multiSelect\":false,\"name\":\"皮肤管理\",\"selectable\":true,\"selected\":false,\"type\":0},{\"filterId\":200128535,\"id\":200128535,\"multiSelect\":false,\"name\":\"美胸美臀\",\"selectable\":true,\"selected\":false,\"type\":0},{\"children\":[{\"filterId\":200128577,\"id\":200128577,\"multiSelect\":false,\"name\":\"全部\",\"selectable\":true,\"selected\":false,\"type\":0},{\"filterId\":200128538,\"id\":200128538,\"multiSelect\":false,\"name\":\"纯色美甲\",\"selectable\":true,\"selected\":false,\"type\":0}],\"filterId\":200128537,\"id\":200128537,\"multiSelect\":false,\"name\":\"美甲\",\"selectable\":true,\"selected\":false,\"type\":0},{\"filterId\":200128542,\"id\":200128542,\"multiSelect\":false,\"name\":\"美甲美睫\",\"selectable\":true,\"selected\":false,\"type\":0},{\"children\":[{\"filterId\":200128578,\"id\":200128578,\"multiSelect\":false,\"name\":\"全部\",\"selectable\":true,\"selected\":false,\"type\":0},{\"filterId\":200128548,\"id\":200128548,\"multiSelect\":false,\"name\":\"烫发\",\"selectable\":true,\"selected\":false,\"type\":0},{\"filterId\":200128549,\"id\":200128549,\"multiSelect\":false,\"name\":\"染发\",\"selectable\":true,\"selected\":false,\"type\":0},{\"filterId\":200128550,\"id\":200128550,\"multiSelect\":false,\"name\":\"潮色漂染\",\"selectable\":true,\"selected\":false,\"type\":0}],\"filterId\":200128547,\"id\":200128547,\"multiSelect\":false,\"name\":\"烫染\",\"selectable\":true,\"selected\":false,\"type\":0}],\"extra\":\"{\\\"shelfTagsFromPlatform\\\":[\\\"\\\\u968f\\\\u65f6\\\\u9000\\\",\\\"\\\\u8fc7\\\\u671f\\\\u9000\\\"]}\",\"id\":0,\"minShowNum\":0,\"selected\":false,\"size\":0,\"type\":0}]";
        List<DzFilterVO> dzFilterBtnVOS = JsonCodec.converseList(filterJson, DzFilterVO.class);
        when(param.getFilterList()).thenReturn(dzFilterBtnVOS);
        when(config.isUseSecondFilter()).thenReturn(true);
        FlagshipStoreM flagshipStoreM = new FlagshipStoreM();
        List<StoreRecommendProjectItemDTO> projectList = new ArrayList<>();
        StoreRecommendProjectItemDTO storeRecommendProjectItemDTO = new StoreRecommendProjectItemDTO();
        storeRecommendProjectItemDTO.setProjectId(200128514L);
        storeRecommendProjectItemDTO.setBackgroundPic("https://p0.meituan.net/travelcube/36ef018adcfc6a292f17d79f2ad7af6c25861.jpg");
        projectList.add(storeRecommendProjectItemDTO);
        StoreRecommendProjectItemDTO storeRecommendProjectItemDTO3 = new StoreRecommendProjectItemDTO();
        storeRecommendProjectItemDTO3.setProjectId(200128538L);
        storeRecommendProjectItemDTO3.setBackgroundPic("https://p0.meituan.net/travelcube/a35857aeccaa85275d874b8ac0451a4628343.jpg");
        projectList.add(storeRecommendProjectItemDTO3);
        StoreRecommendProjectItemDTO storeRecommendProjectItemDTO1 = new StoreRecommendProjectItemDTO();
        storeRecommendProjectItemDTO1.setProjectId(200128535L);
        storeRecommendProjectItemDTO1.setBackgroundPic("https://p0.meituan.net/travelcube/a35857aeccaa85275d874b8ac0451a4628343.jpg");
        projectList.add(storeRecommendProjectItemDTO1);
        StoreRecommendProjectItemDTO storeRecommendProjectItemDTO2 = new StoreRecommendProjectItemDTO();
        storeRecommendProjectItemDTO2.setProjectId(200128542L);
        storeRecommendProjectItemDTO2.setBackgroundPic("https://p0.meituan.net/travelcube/a35857aeccaa85275d874b8ac0451a4628343.jpg");
        projectList.add(storeRecommendProjectItemDTO2);
        flagshipStoreM.setProjectList(projectList);
        flagshipStoreM.setStoreId(123213123213L);
        flagshipStoreM.setModuleType(1);
        when(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStore)).thenReturn(flagshipStoreM);
        HashMap<Integer, FlagshipStoreRecommendServiceFilterEndInterceptOpt.UrlCfg> platform2Url = new HashMap<>();
        FlagshipStoreRecommendServiceFilterEndInterceptOpt.UrlCfg urlCfg = new FlagshipStoreRecommendServiceFilterEndInterceptOpt.UrlCfg();
        urlCfg.setUrlFormat("https:/mapi.51ping.com/Apppage?ApplicationPageTypeId=10009&ModuleKey=FlagMRN/module/mrnmodule_FlagAllGoodsShelf&filterbtnid=%s");
        urlCfg.setPrefixWithURLEncode("dianping://web?url=");
        platform2Url.put(100,urlCfg);
        when(config.doGetPlatformUrlCfgByUserAgent(100)).thenReturn(urlCfg);
        when(param.getUserAgent()).thenReturn(100);
        flagshipStoreRecommendServiceFilterEndInterceptOpt.compute(activityCxt, param, config);
        System.out.println(JSON.toJSONString(param.getFilterList()));
        Assert.assertNotNull(param.getFilterList());
    }
}
