package com.sankuai.dzviewscene.product.filterlist.option.assembler.responseAssemble.moreJumpUrl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.model.CarAndPetUncoopShopShelfAttrM;
import com.sankuai.dzviewscene.product.ability.options.CarAndPetUnCoopCommonInfoOpt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.vp.DealFilterListMoreJumpUrlVP;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CarAndPetUnCoopMoreJumpUrlOptTest {

    @Mock
    private ActivityCxt activityCxt;

    private CarAndPetUnCoopMoreJumpUrlOpt opt;
    private CarAndPetUnCoopMoreJumpUrlOpt.Config config;
    @Mock
    private DealFilterListMoreJumpUrlVP.Param  param;

    @Before
    public void setUp() {
        opt = new CarAndPetUnCoopMoreJumpUrlOpt();
        config = new CarAndPetUnCoopMoreJumpUrlOpt.Config();
    }

    @Test
    public void compute_whenHasJumpUrl_thenReturnUrl() {
        // given
        String expectedUrl = "https://test.meituan.com";
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setJumpUrl(expectedUrl);
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals(expectedUrl, result);
    }

    @Test
    public void compute_whenAttrIsNull_thenReturnEmpty() {
        // given
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(null);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals("", result);
    }

    @Test
    public void compute_whenJumpUrlIsEmpty_thenReturnEmpty() {
        // given
        CarAndPetUncoopShopShelfAttrM attr = new CarAndPetUncoopShopShelfAttrM();
        attr.setJumpUrl("");
        when(activityCxt.getParam(CarAndPetUnCoopCommonInfoOpt.CODE)).thenReturn(attr);

        // when
        String result = opt.compute(activityCxt, param, config);

        // then
        assertEquals("", result);
    }
}
