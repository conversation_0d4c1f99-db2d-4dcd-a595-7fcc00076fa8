package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfButtonVO;
import com.sankuai.dzviewscene.product.shelf.utils.DealSecKillUtils;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn.UnifiedShelfItemConfigureBtnOpt.Config;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.BtnTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfItemConfigureBtnOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    @Mock
    private UnifiedShelfItemConfigureBtnOpt.Param mockParam;

    @Mock
    private Config mockConfig;

    @Mock
    private ProductM mockProductM;

    @InjectMocks
    private UnifiedShelfItemConfigureBtnOpt underTest;

    @Test
    public void testCompute_Normal() throws Throwable {
        when(mockParam.getProductM()).thenReturn(mockProductM);
        when(mockConfig.getType()).thenReturn(1);
        when(mockConfig.getName()).thenReturn("Test Button");
        when(mockConfig.isDisable()).thenReturn(false);
        when(mockConfig.getJumpUrl()).thenReturn("http://example.com");
        ShelfButtonVO result = underTest.compute(mockActivityCxt, mockParam, mockConfig);
        assertEquals("类型应为默认按钮", BtnTypeEnum.COMMON_BUTTON.getCode(), result.getType());
        assertEquals("名称应为 Test Button", "Test Button", result.getName());
        assertFalse("按钮不应被禁用", result.isDisable());
        assertEquals("跳转 URL 应为 http://example.com", "http://example.com", result.getJumpUrl());
    }

    @Test(expected = Exception.class)
    public void testCompute_ExceptionInRewriteByShopCategoryConfig() throws Throwable {
        // Simulate the exception scenario by setting up the necessary conditions
        when(mockConfig.getJumpUrl()).thenThrow(new Exception());
        underTest.compute(mockActivityCxt, mockParam, mockConfig);
    }

    @Test(expected = Exception.class)
    public void testCompute_ExceptionInGetJumpUrl() throws Throwable {
        when(mockConfig.getJumpUrl()).thenThrow(new Exception());
        underTest.compute(mockActivityCxt, mockParam, mockConfig);
    }

    @Test(expected = Exception.class)
    public void testCompute_ExceptionInGetButtonName() throws Throwable {
        when(mockConfig.getName()).thenThrow(new Exception());
        underTest.compute(mockActivityCxt, mockParam, mockConfig);
    }

    @Test(expected = Exception.class)
    public void testCompute_ExceptionInGetType() throws Throwable {
        when(mockConfig.getType()).thenThrow(new Exception());
        underTest.compute(mockActivityCxt, mockParam, mockConfig);
    }

    @Test(expected = Exception.class)
    public void testCompute_ExceptionInGetDisable() throws Throwable {
        when(mockConfig.isDisable()).thenThrow(new Exception());
        underTest.compute(mockActivityCxt, mockParam, mockConfig);
    }
}
