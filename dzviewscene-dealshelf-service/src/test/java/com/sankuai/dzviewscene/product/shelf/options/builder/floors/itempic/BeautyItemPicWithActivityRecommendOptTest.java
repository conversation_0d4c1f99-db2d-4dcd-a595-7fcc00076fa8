package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic;

import cn.hutool.core.lang.Assert;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPicVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.BeautyItemPicWithActivityRecommendOpt;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BeautyItemPicWithActivityRecommendOptTest {

    private ActivityCxt activityCxt;

    private ItemPicVP.Param param;

    private BeautyItemPicWithActivityRecommendOpt.Config config;

    @Before
    public void setUp() {
        activityCxt = Mockito.mock(ActivityCxt.class);
        param = Mockito.mock(ItemPicVP.Param.class);
        config = new BeautyItemPicWithActivityRecommendOpt.Config();
    }
    @Test
    public void test1() {
        ProductM productM = new ProductM();
        productM.setAttr("attr_topDisplayProduct","true");
        productM.setPicUrl("http://p0.meituan.net/beauty/72a7047.png");
        when(param.getProductM()).thenReturn(productM);
        when(activityCxt.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn("iphone");
        config.setShopRecommendPic("http://p0.meituan.net/beauty/72a7047216a856b4c9da330d280246c013449.png");
        BeautyItemPicWithActivityRecommendOpt beautyItemPicWithActivityRecommendOpt = new BeautyItemPicWithActivityRecommendOpt();
        PicAreaVO compute = beautyItemPicWithActivityRecommendOpt.compute(activityCxt, param, config);
        Assert.notNull(compute);
    }
}
