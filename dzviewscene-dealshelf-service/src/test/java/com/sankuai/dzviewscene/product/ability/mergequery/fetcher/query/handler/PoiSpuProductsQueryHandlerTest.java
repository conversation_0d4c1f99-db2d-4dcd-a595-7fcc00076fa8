package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.mpproduct.general.trade.api.request.PoiSpuIdQueryRequest;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.query.QueryConstants;
import com.sankuai.dzviewscene.product.utils.ParamsUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PoiSpuProductsQueryHandlerTest {

    @InjectMocks
    private PoiSpuProductsQueryHandler poiSpuProductsQueryHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    /**
     * Test normal scenario.
     */
    @Test
    public void testQueryNormal() throws Throwable {
        // Arrange
        ActivityCxt ctx = new ActivityCxt();
        String groupName = "testGroupName";
        Map<String, Object> params = new HashMap<>();
        params.put(QueryConstants.Params.secondCategoryId, 1L);
        when(compositeAtomService.querySpuIdsByPoi(any())).thenReturn(CompletableFuture.completedFuture(Arrays.asList(1L, 2L)));
        // Act
        CompletableFuture<ProductGroupM> result = poiSpuProductsQueryHandler.query(ctx, groupName, params);
        // Assert
        assertNotNull(result);
        assertEquals(2, result.get().getTotal());
        assertEquals(2, result.get().getProducts().size());
    }

    /**
     * Test exception scenario.
     */
    @Test(expected = RuntimeException.class)
    public void testQueryException() throws Throwable {
        // Arrange
        ActivityCxt ctx = new ActivityCxt();
        String groupName = "testGroupName";
        Map<String, Object> params = new HashMap<>();
        params.put(QueryConstants.Params.secondCategoryId, 1L);
        when(compositeAtomService.querySpuIdsByPoi(any())).thenThrow(new RuntimeException("Simulated service exception"));
        // Act
        poiSpuProductsQueryHandler.query(ctx, groupName, params);
        // The test will pass if a RuntimeException is thrown
    }
}
