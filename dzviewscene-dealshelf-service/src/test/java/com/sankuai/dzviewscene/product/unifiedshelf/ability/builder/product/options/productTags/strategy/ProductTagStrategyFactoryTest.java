package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.productTags.strategy;

import org.apache.commons.collections.MapUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import shaded.org.elasticsearch.common.collect.Map;

import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductTagStrategyFactoryTest {

    @Mock
    private ApplicationContext applicationContext;
    @Mock
    private ProductTagStrategy productTagStrategy;
    
    private ProductTagStrategyFactory productTagStrategyFactory;

    @Before
    public void setUp() throws Exception {
        productTagStrategyFactory = new ProductTagStrategyFactory();
        productTagStrategyFactory.setApplicationContext(applicationContext);
    }

    /**
     * 测试正常情况下获取策略实例
     */
    @Test
    public void testGetProductTagStrategyNormalCase() throws Throwable {
        // arrange
        when(applicationContext.getBeansOfType(ProductTagStrategy.class))
                .thenReturn(Map.of("testStrategy", productTagStrategy));
        when(productTagStrategy.getName()).thenReturn("testStrategy");
        productTagStrategyFactory.afterPropertiesSet();

        // act
        ProductTagStrategy result = productTagStrategyFactory.getProductTagStrategy("testStrategy");

        // assert
        assertNotNull(result);
        assertEquals(productTagStrategy, result);
    }

    /**
     * 测试当策略不存在时
     */
    @Test
    public void testGetProductTagStrategyWhenStrategyDoesNotExist() throws Throwable {
        // arrange
        when(applicationContext.getBeansOfType(ProductTagStrategy.class))
                .thenReturn(Map.of());
        productTagStrategyFactory.afterPropertiesSet();

        // act
        ProductTagStrategy result = productTagStrategyFactory.getProductTagStrategy("nonExistingStrategy");

        // assert
        assertNull(result);
    }

    /**
     * 测试当Spring容器中没有ProductTagStrategy类型的Bean时
     */
    @Test
    public void testGetProductTagStrategyWhenNoStrategiesInContext() throws Throwable {
        // arrange
        when(applicationContext.getBeansOfType(ProductTagStrategy.class))
                .thenReturn(Map.of());
        productTagStrategyFactory.afterPropertiesSet();

        // act
        ProductTagStrategy result = productTagStrategyFactory.getProductTagStrategy("testStrategy");

        // assert
        assertNull(result);
    }
}
