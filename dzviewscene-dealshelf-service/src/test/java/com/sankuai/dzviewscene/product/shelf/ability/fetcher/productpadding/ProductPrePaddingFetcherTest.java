package com.sankuai.dzviewscene.product.shelf.ability.fetcher.productpadding;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ProductPrePaddingFetcherTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private ProductPrePaddingFetcher.Request request;

    @Mock
    private ProductPrePaddingFetcher.Config config;

    @InjectMocks
    private ProductPrePaddingFetcher fetcher = new ProductPrePaddingFetcher();

    private static final String SHOP_PRODUCT_QUERY_FETCHER_CODE = "ShopProductQueryFetcherCode";

    @Test
    public void testBuildMultiGroupParamsIsEmpty() throws Throwable {
        when(config.getMultiGroupParams()).thenReturn(new HashMap<>());
        CompletableFuture<Map<String, ProductGroupM>> result = fetcher.build(ctx, request, config);
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildProductGroupsCompletableFutureIsEmpty() throws Throwable {
        Map<String, List<Map<String, Object>>> multiGroupParams = new HashMap<>();
        multiGroupParams.put("test", new ArrayList<>());
        when(config.getMultiGroupParams()).thenReturn(multiGroupParams);
        CompletableFuture<Map<String, ProductGroupM>> result = fetcher.build(ctx, request, config);
        assertTrue(result.isDone());
        assertTrue(result.get().isEmpty());
    }
}
