package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop.ShopGuaranteeTagPaddingHandler;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;

public class ShopGuaranteeTagPaddingHandler_BuildRequestClfTest {

    @InjectMocks
    private ShopGuaranteeTagPaddingHandler shopGuaranteeTagPaddingHandler;

    @Mock
    private ActivityCxt ctx;

    @Mock
    private CompositeAtomService compositeAtomService;

    // Instance initializer block for initializing mocks
    {
        MockitoAnnotations.initMocks(this);
    }

    private void setUpCommonMocks() {
        when(compositeAtomService.getMtByDpPoiIdL(anyLong())).thenReturn(CompletableFuture.completedFuture(123L));
    }

    @Test(expected = NullPointerException.class)
    public void testBuildRequestClfNullActivityCxt() throws Throwable {
        ActivityCxt nullCtx = null;
        shopGuaranteeTagPaddingHandler.buildRequestClf(nullCtx);
    }

    @Test
    public void testBuildRequestClfNullUserId() throws Throwable {
        setUpCommonMocks();
        Map<String, Object> parameters = new HashMap<>();
        when(ctx.getParameters()).thenReturn(parameters);
        CompletableFuture<BatchQueryGuaranteeTagRequest> result = shopGuaranteeTagPaddingHandler.buildRequestClf(ctx);
        assertNotNull(result);
    }

    @Test
    public void testBuildRequestClfNullMtShopId() throws Throwable {
        setUpCommonMocks();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("dpUserId", 123L);
        parameters.put("mtUserId", 456L);
        when(ctx.getParameters()).thenReturn(parameters);
        CompletableFuture<BatchQueryGuaranteeTagRequest> result = shopGuaranteeTagPaddingHandler.buildRequestClf(ctx);
        assertNotNull(result);
    }

    @Test
    public void testBuildRequestClfNonNullMtShopId() throws Throwable {
        setUpCommonMocks();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("dpUserId", 123L);
        parameters.put("mtUserId", 456L);
        when(ctx.getParameters()).thenReturn(parameters);
        CompletableFuture<BatchQueryGuaranteeTagRequest> result = shopGuaranteeTagPaddingHandler.buildRequestClf(ctx);
        assertNotNull(result);
    }
}
