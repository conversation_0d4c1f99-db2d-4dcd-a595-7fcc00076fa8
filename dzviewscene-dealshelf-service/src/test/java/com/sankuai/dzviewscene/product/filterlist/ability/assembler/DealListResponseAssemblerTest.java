package com.sankuai.dzviewscene.product.filterlist.ability.assembler;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.assembler.DealListResponseAssembler.Config;
import com.sankuai.dzviewscene.product.filterlist.model.DealFilterListM;
import com.sankuai.dzviewscene.product.filterlist.vo.DzFilterProductListVO;
import java.util.concurrent.CompletableFuture;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.athena.viewscene.framework.pmf.annotations.AbilityRequest;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealListResponseAssemblerTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DealListResponseAssembler.Request request;

    @Mock
    private Config config;

    @Test
    public void testBuildDealFilterListMIsNull() throws Throwable {
        // arrange
        DealListResponseAssembler assembler = new DealListResponseAssembler();
        when(ctx.getSource(anyString())).thenReturn(null);
        // act
        CompletableFuture<DzFilterProductListVO> result = assembler.build(ctx, request, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertNotNull(result.get());
    }

    @Test
    public void testBuildDealFilterListMIsNotNull() throws Throwable {
        // arrange
        DealListResponseAssembler assembler = new DealListResponseAssembler();
        DealFilterListM dealFilterListM = mock(DealFilterListM.class);
        // Assuming DealFilterListM contains a list of filters or products that need to be mocked
        // Mock the expected List return for ctx.getSource based on the actual usage in DealListResponseAssembler
        // Corrected to match the actual expected code
        // If DealListResponseAssembler expects a List from ctx.getSource for filters or products, mock those as well
        // Example: when(ctx.getSource(DealFilterBuilder.CODE)).thenReturn(mock(List.class));
        // Example: when(ctx.getSource(DealListBuilder.CODE)).thenReturn(mock(List.class));
        // act
        CompletableFuture<DzFilterProductListVO> result = assembler.build(ctx, request, config);
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertNotNull(result.get());
    }
}
