package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuPriceVP;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.math.BigDecimal;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultSkuPriceOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuPriceVP.Param param;

    @Mock
    private DefaultSkuPriceOpt.Config config;

    @Test
    public void testComputePriceIsNull() {
        when(param.getPrice()).thenReturn(null);
        DefaultSkuPriceOpt defaultSkuPriceOpt = new DefaultSkuPriceOpt();
        String result = defaultSkuPriceOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeFormatIsNoShowPriceFlag() {
        when(param.getPrice()).thenReturn(new BigDecimal("100"));
        when(config.getFormat()).thenReturn("不展示价格标识");
        DefaultSkuPriceOpt defaultSkuPriceOpt = new DefaultSkuPriceOpt();
        String result = defaultSkuPriceOpt.compute(context, param, config);
        assertNull(result);
    }

    @Test
    public void testComputeFormatIsEmpty() {
        when(param.getPrice()).thenReturn(new BigDecimal("100"));
        when(config.getFormat()).thenReturn("");
        DefaultSkuPriceOpt defaultSkuPriceOpt = new DefaultSkuPriceOpt();
        String result = defaultSkuPriceOpt.compute(context, param, config);
        assertEquals("100元", result);
    }

    @Test
    public void testComputeFormatIsNotEmpty() {
        when(param.getPrice()).thenReturn(new BigDecimal("100"));
        when(config.getFormat()).thenReturn("%s元");
        DefaultSkuPriceOpt defaultSkuPriceOpt = new DefaultSkuPriceOpt();
        String result = defaultSkuPriceOpt.compute(context, param, config);
        assertEquals("100元", result);
    }
}
