package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.testng.annotations.Test;

public class DiscountPromoTagsOptTest {

    @Test
    public void getSinglePrice() {
        ProductM productM = new ProductM();
        productM.setAttr("sys_multi_sale_number", "3");

        DiscountPromoTagsOpt opt = new DiscountPromoTagsOpt();
        String price = opt.getSinglePrice(productM, "$300");

        Assert.assertNull(price);
    }

}