package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DiscountItemPromoTagsOpt_AddTimesDealPromoTagIfNecessaryTest {

    @InjectMocks
    private DiscountItemPromoTagsOpt discountItemPromoTagsOpt;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ItemPromoTagsVP.Param param;

    @Mock
    private ProductM productM;

    /**
     * 测试 addTimesDealPromoTagIfNecessary 方法，timesDealPromoTag 列表为空
     */
    @Test
    public void testAddTimesDealPromoTagIfNecessaryWithEmptyTimesDealPromoTag() throws Throwable {
        // arrange
        ItemPromoTagsVP.Param param = Mockito.mock(ItemPromoTagsVP.Param.class);
        Mockito.when(param.getProductM()).thenReturn(new ProductM());
        DiscountItemPromoTagsOpt.Config config = Mockito.mock(DiscountItemPromoTagsOpt.Config.class);
        List<DzPromoVO> result = new ArrayList<>();
        DiscountItemPromoTagsOpt opt = new DiscountItemPromoTagsOpt();
        // act
        opt.addTimesDealPromoTagIfNecessary(param, config, result);
        // assert
        Assert.assertTrue(result.isEmpty());
    }

    /**
     * 测试 addTimesDealPromoTagIfNecessary 方法，timesDealPromoTag 列表不为空
     */
    @Test
    public void testAddTimesDealPromoTagIfNecessaryWithNonEmptyTimesDealPromoTag() throws Throwable {
        // arrange
        ItemPromoTagsVP.Param param = Mockito.mock(ItemPromoTagsVP.Param.class);
        DiscountItemPromoTagsOpt.Config config = Mockito.mock(DiscountItemPromoTagsOpt.Config.class);
        List<DzPromoVO> timesDealPromoTag = new ArrayList<>();
        timesDealPromoTag.add(new DzPromoVO());
        List<DzPromoVO> result = new ArrayList<>();
        DiscountItemPromoTagsOpt opt = Mockito.spy(new DiscountItemPromoTagsOpt());
        // Ensure the config is correctly set up to meet the conditions for adding items
        Mockito.when(config.getTimesDealTagPrefix()).thenReturn("单次¥%s");
        // Mock the behavior of getTimesDealPromoTag to return the timesDealPromoTag list
        Mockito.doReturn(timesDealPromoTag).when(opt).getTimesDealPromoTag(Mockito.any(ItemPromoTagsVP.Param.class), Mockito.anyString());
        // act
        opt.addTimesDealPromoTagIfNecessary(param, config, result);
        // assert
        Assert.assertFalse(result.isEmpty());
        Assert.assertEquals(timesDealPromoTag.size(), result.size());
        Assert.assertEquals(timesDealPromoTag.get(0), result.get(0));
    }

    /**
     * Test case to verify basic initialization of result list
     */
    @Test
    public void testCompute_BasicResultList() throws Throwable {
        // arrange
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getCategoryId()).thenReturn(100);
        // act
        List<DzPromoVO> result = discountItemPromoTagsOpt.compute(activityCxt, param, config);
        // assert
        assertNotNull("Result list should not be null", result);
    }

    /**
     * Test case to verify Lion configuration retrieval
     */
    @Test
    public void testCompute_LionConfig() throws Throwable {
        // arrange
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        List<Integer> categoryIds = Lists.newArrayList(100, 200);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList(eq("com.sankuai.dzviewscene.dealshelf"), eq("com.sankuai.dzviewscene.dealshelf.needShowTimesDealAndDiscountPromoTagCategoryIds"), eq(Integer.class), any())).thenReturn(categoryIds);
            when(param.getProductM()).thenReturn(productM);
            when(productM.getCategoryId()).thenReturn(100);
            // act
            List<DzPromoVO> result = discountItemPromoTagsOpt.compute(activityCxt, param, config);
            // assert
            assertNotNull("Result should not be null", result);
            verify(productM).getCategoryId();
        }
    }
}
