package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import java.util.Map;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GlassesV1ProductTagsOpt_ComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ItemProductTagsVP.Param param;

    @Mock
    private GlassesV1ProductTagsOpt.Config config;

    @Mock
    private ProductM productM;

    @Mock
    private Map<String, Map<String, Map<String, String>>> tagConfig;

    @Mock
    private Map<String, Map<String, String>> keyConfig;

    private GlassesV1ProductTagsOpt glassesV1ProductTagsOpt;

    @Before
    public void setUp() {
        glassesV1ProductTagsOpt = new GlassesV1ProductTagsOpt();
        // Ensure param.getProductM() does not return null
        when(param.getProductM()).thenReturn(productM);
    }

    @Test
    public void testComputeParamIsNull() throws Throwable {
        // Adjusted to simulate getProductM() returning null
        when(param.getProductM()).thenReturn(null);
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeProductCategoryIdNot406() throws Throwable {
        when(productM.getCategoryId()).thenReturn(405);
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeServiceTypeIsNull() throws Throwable {
        when(productM.getCategoryId()).thenReturn(406);
        when(productM.getAttr(anyString())).thenReturn(null);
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeTagConfigIsNull() throws Throwable {
        when(productM.getCategoryId()).thenReturn(406);
        when(productM.getAttr(anyString())).thenReturn("service_type");
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }

    @Test
    public void testComputeServiceTypeIsAdultGlasses() throws Throwable {
        when(productM.getCategoryId()).thenReturn(406);
        when(productM.getAttr(anyString())).thenReturn("成人配镜");
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        // Assert the expected result here
        assertTrue(CollectionUtils.isEmpty(result));
    }

    // 其他服务类型的测试用例类似，这里省略...
    @Test
    public void testComputeServiceTypeIsChildGlasses() throws Throwable {
        when(productM.getCategoryId()).thenReturn(406);
        when(productM.getAttr(anyString())).thenReturn("儿童配镜");
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        // 断言结果
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testComputeServiceTypeIsOnlyOptometry() throws Throwable {
        when(productM.getCategoryId()).thenReturn(406);
        when(productM.getAttr(anyString())).thenReturn("仅验光");
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        // 断言结果
        assertTrue(CollectionUtils.isEmpty(result));
    }

    // Additional test cases for other service types would follow a similar pattern
    // Ensure to mock the necessary dependencies and assert the expected outcomes
    @Test
    public void testComputeServiceTypeIsRandomGlasses() throws Throwable {
        when(productM.getCategoryId()).thenReturn(406);
        when(productM.getAttr(anyString())).thenReturn("任选配镜");
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        // 断言结果
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testComputeServiceTypeIsOther() throws Throwable {
        when(productM.getCategoryId()).thenReturn(406);
        when(productM.getAttr(anyString())).thenReturn("其他服务类型");
        List<String> result = glassesV1ProductTagsOpt.compute(context, param, config);
        assertEquals(0, result.size());
    }
}
