package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP.Param;
import org.junit.Before;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Map;
import java.util.HashMap;
import java.util.HashSet;

import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DiscountItemPromoTagsOptTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private ProductM productM;
    private DiscountItemPromoTagsOpt discountItemPromoTagsOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        discountItemPromoTagsOpt = new DiscountItemPromoTagsOpt();
    }

    /**
     * 测试compute方法，当商品类型在过滤列表中时，应返回空列表
     */
    @Test
    public void testCompute_WithFilteredProductType_ReturnsEmptyList() {
        // arrange
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        HashSet<Integer> filterByProductTypes = new HashSet<>();
        filterByProductTypes.add(1); // 假设1是需要过滤的商品类型
        config.setFilterByProductTypes(filterByProductTypes);

        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductType()).thenReturn(1); // 设置商品类型为1

        // act
        java.util.List<DzPromoVO> result = discountItemPromoTagsOpt.compute(activityCxt, param, config);

        // assert
        assertTrue("当商品类型在过滤列表中时，应返回空列表", result.isEmpty());
    }

    /**
     * 测试 getTimesDealPromoTag 方法，当 getSinglePrice 返回 null 时，预期结果是返回 null
     */
    @Test
    public void testGetTimesDealPromoTagWhenGetSinglePriceReturnNull() throws Throwable {
        DiscountItemPromoTagsOpt discountItemPromoTagsOpt = mock(DiscountItemPromoTagsOpt.class);
        //when(discountItemPromoTagsOpt.getSinglePrice(any(ProductM.class), anyString())).thenReturn(null);

        DiscountItemPromoTagsOpt.Config config = mock(DiscountItemPromoTagsOpt.Config.class);

        DiscountItemPromoTagsOpt opt = new DiscountItemPromoTagsOpt();
        List<DzPromoVO> result = opt.getTimesDealPromoTag(ItemPromoTagsVP.Param.builder().productM(new ProductM()).build(), config.getTimesDealTagPrefix());
        assertNull(result);
    }

    /**
     * 测试 getTimesDealPromoTag 方法，当 getSinglePrice 返回非 null 值时，预期结果是返回包含一个 DzPromoVO 对象的列表
     */
    @Test
    public void testGetTimesDealPromoTagWhenGetSinglePriceReturnNonNull() throws Throwable {
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        config.setTimesDealTagPrefix("单次¥%s");

        ProductM productM = new ProductM();
        productM.setAttr("sys_multi_sale_number", "3");
        productM.setTradeType(19);


        DiscountItemPromoTagsOpt opt = new DiscountItemPromoTagsOpt();
        List<DzPromoVO> result = opt.getTimesDealPromoTag(ItemPromoTagsVP.Param.builder().productM(productM).salePrice("30").build(), config.getTimesDealTagPrefix());

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("单次¥10", result.get(0).getName());
        assertEquals(1, result.get(0).getShowType());
        assertEquals(1, result.get(0).getPosition());
    }

    @Test
    public void testGetSinglePriceSalePriceIsNull() {
        // arrange
        DiscountItemPromoTagsOpt discountItemPromoTagsOpt = new DiscountItemPromoTagsOpt();
        ProductM productM = mock(ProductM.class);
        //when(productM.getAttr(anyString())).thenReturn("1");

        // act
        String result = discountItemPromoTagsOpt.getSinglePrice(productM, null);

        // assert
        assertNull(result);
    }

    @Test
    public void testGetSinglePriceAttrIsNull() {
        // arrange
        DiscountItemPromoTagsOpt discountItemPromoTagsOpt = new DiscountItemPromoTagsOpt();
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr(anyString())).thenReturn(null);

        // act
        String result = discountItemPromoTagsOpt.getSinglePrice(productM, "10");

        // assert
        assertNull(result);
    }

    @Test
    public void testGetSinglePriceAttrIsNotNumber() {
        // arrange
        DiscountItemPromoTagsOpt discountItemPromoTagsOpt = new DiscountItemPromoTagsOpt();
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr(anyString())).thenReturn("not a number");

        // act
        String result = discountItemPromoTagsOpt.getSinglePrice(productM, "10");

        // assert
        assertNull(result);
    }

    @Test
    public void testGetSinglePriceNormal() {
        // arrange
        DiscountItemPromoTagsOpt discountItemPromoTagsOpt = new DiscountItemPromoTagsOpt();
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr(anyString())).thenReturn("2");

        // act
        String result = discountItemPromoTagsOpt.getSinglePrice(productM, "10");

        // assert
        assertEquals("5", result);
    }

    @Test
    public void testAddTimesDealPromoTagIfNecessary() {
        ItemPromoTagsVP.Param param = ItemPromoTagsVP.Param.builder().build();
        ProductM productM = new ProductM();
        productM.setAttr("sys_multi_sale_number", "3");
        param.setProductM(productM);
        param.setSalePrice("100");

        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        config.setTimesDealTagPrefix("单次¥%s");

        List<DzPromoVO> result = Lists.newArrayList();

        DiscountItemPromoTagsOpt opt = new DiscountItemPromoTagsOpt();
        opt.addTimesDealPromoTagIfNecessary(param, config, result);

        String test = null;
        Assert.assertNull(test);
    }

    /**
     * 测试场景：当filterByProductAttr返回true时，compute方法应返回空列表
     */
    @Test
    public void testCompute_WhenFilterByProductAttrReturnsTrue() {
        // Given
        when(param.getProductM()).thenReturn(productM);

        // Mock filterByAttr返回值
        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("color", Lists.newArrayList("red")); // 过滤条件
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        config.setFilterByAttr(filterMap);
        config.setExpId2EnableConfigMap(new HashMap<>());
        when(productM.getAttr("color")).thenReturn("red");

        // When
        List<DzPromoVO> result = discountItemPromoTagsOpt.compute(activityCxt, param, config);

        // Then
        assertTrue("当filterByProductAttr返回true时，应返回空列表", result.isEmpty());
    }

    /**
     * 测试场景：当enableFilterByProductAttr返回true时，但属性值不匹配过滤情况时，compute方法应继续执行
     */
    @Test
    public void testCompute_FilterByProductAttrReturnsFalse() {
        // Given
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn("100");
        when(productM.getProductType()).thenReturn(0); // 非次卡类型
        when(productM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(productM.getMarketPrice()).thenReturn("200");

        // Mock filterByAttr返回值
        Map<String, List<String>> filterMap = new HashMap<>();
        filterMap.put("color", Lists.newArrayList("red")); // 过滤条件
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        config.setFilterByAttr(filterMap);

        // Mock enableFilterByProductAttr返回值
        Map<String, DiscountItemPromoTagsOpt.EnableConfig> expId2EnableConfigMap = new HashMap<>();
        DiscountItemPromoTagsOpt.EnableConfig enableConfig = new DiscountItemPromoTagsOpt.EnableConfig();
        enableConfig.setEnableFilterByAttr(true); // 启用过滤
        expId2EnableConfigMap.put("testExpId", enableConfig);
        config.setExpId2EnableConfigMap(expId2EnableConfigMap);
        // When
        List<DzPromoVO> result = discountItemPromoTagsOpt.compute(activityCxt, param, config);

        // Then
        assertNotNull("Result should not be null when enableFilterByProductAttr returns false", result);
    }


    /**
     * 测试场景：当enableFilterByProductAttr返回false时，不启动过滤，继续执行
     */
    @Test
    public void testCompute_WhenEnableFilterByProductAttrReturnsFalse() {
        // Given
        when(param.getProductM()).thenReturn(productM);
        when(param.getSalePrice()).thenReturn("100");
        when(productM.getProductType()).thenReturn(0); // 非次卡类型
        when(productM.isTimesDeal()).thenReturn(false); // 非团购次卡
        when(productM.getMarketPrice()).thenReturn("200");

        // Mock enableFilterByProductAttr返回值
        DiscountItemPromoTagsOpt.Config config = new DiscountItemPromoTagsOpt.Config();
        Map<String, DiscountItemPromoTagsOpt.EnableConfig> expId2EnableConfigMap = new HashMap<>();
        DiscountItemPromoTagsOpt.EnableConfig enableConfig = new DiscountItemPromoTagsOpt.EnableConfig();
        enableConfig.setEnableFilterByAttr(false); // 禁用过滤
        expId2EnableConfigMap.put("testExpId", enableConfig);
        config.setExpId2EnableConfigMap(expId2EnableConfigMap);

        // When
        List<DzPromoVO> result = discountItemPromoTagsOpt.compute(activityCxt, param, config);

        // Then
        assertNotNull("Result should not be null when enableFilterByProductAttr returns false", result);
    }
}
