package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.product.filterlist.utils.SkuItemUtils;
import com.sankuai.dzviewscene.product.shelf.utils.DealStructUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class KtvSimilarListOpt_FilterPeriodTest {

    private KtvSimilarListOpt ktvSimilarListOpt;

    private ProductM productM;

    @Before
    public void setUp() {
        ktvSimilarListOpt = new KtvSimilarListOpt();
        productM = Mockito.mock(ProductM.class);
    }

    /**
     * 测试filterPeriod方法，当serviceType不等于SING_AND_WINE_SERVICE_TYPE或SING_AND_BEVERAGE_SERVICE_TYPE时，应返回true
     */
    @Test
    public void testFilterPeriodWithNoServiceType() throws Throwable {
        // arrange
        Mockito.when(productM.getExtAttrs()).thenReturn(Arrays.asList(new com.sankuai.dzviewscene.shelf.platform.common.model.AttrM("service_type", "other")));
        // act
        boolean result = ktvSimilarListOpt.filterPeriod(Arrays.asList("period"), productM);
        // assert
        Assert.assertTrue(result);
    }
}
