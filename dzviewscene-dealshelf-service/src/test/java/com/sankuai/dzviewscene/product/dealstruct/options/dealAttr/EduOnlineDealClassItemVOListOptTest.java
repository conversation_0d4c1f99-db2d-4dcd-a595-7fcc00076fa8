package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.beautycontent.creator.application.edu.technicianVideo.dto.EduTechnicianVideoDTO;
import com.sankuai.dzviewscene.product.ability.options.LoadDealTeacherAndTrialClassOpt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.EduOnlineDealClassItemVOListOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.jetbrains.annotations.NotNull;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class EduOnlineDealClassItemVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @Mock
    private Config config;

    private void addTeacherClassValue(ActivityCxt context, int status) {
        List<EduTechnicianVideoDTO> technicianVideoDTOList = new ArrayList<>();
        EduTechnicianVideoDTO videoDTO = new EduTechnicianVideoDTO();
        videoDTO.setStatus(status);
        technicianVideoDTOList.add(videoDTO);
        when(context.getParam(LoadDealTeacherAndTrialClassOpt.CODE)).thenReturn(technicianVideoDTOList);
    }

    @NotNull
    private List<EduOnlineDealClassItemVOListOpt.CoursePlan> getCoursePlans() {
        List<EduOnlineDealClassItemVOListOpt.CoursePlan> coursePlanList = new ArrayList<>();
        EduOnlineDealClassItemVOListOpt.CoursePlan plan1 = new EduOnlineDealClassItemVOListOpt.CoursePlan();
        plan1.setCourseModule("测试1");
        plan1.setCourseTimeNum(new BigDecimal(10));
        coursePlanList.add(plan1);
        EduOnlineDealClassItemVOListOpt.CoursePlan plan2 = new EduOnlineDealClassItemVOListOpt.CoursePlan();
        plan2.setCourseModule("测试2");
        plan2.setCourseTimeNum(new BigDecimal(0.5));
        coursePlanList.add(plan2);
        return coursePlanList;
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsEmpty() {
        // arrange
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, new EduOnlineDealClassItemVOListOpt.Config());
        // assert
        assertNull(result);
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表不为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForDP() {
        // arrange
        List<EduOnlineDealClassItemVOListOpt.CoursePlan> coursePlanList = getCoursePlans();
        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));
        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");
        addTeacherClassValue(context, 0);
        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealId()).thenReturn(101010);
        when(context.getParam(ProductDetailActivityConstants.Params.platform)).thenReturn(100);
        when(context.getParam(ProductDetailActivityConstants.Params.dpPoiIdL)).thenReturn(100L);
        when(context.getParam(ProductDetailActivityConstants.Params.mtPoiIdL)).thenReturn(200L);
        when(context.getParam(ProductDetailActivityConstants.Params.shopUuid)).thenReturn("shopUuid");
        EduOnlineDealClassItemVOListOpt.Config config = new EduOnlineDealClassItemVOListOpt.Config();
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().size() == 2);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName().equals("测试1"));
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(1).getAttrName().equals("测试2"));
        assertTrue(result.get(0).getGroupName().equals("method"));
        assertTrue(result.get(0).getGroupSubtitle().equals("10.5课时"));
        assertTrue(result.get(0).getJumpUrl() != null && result.get(0).getJumpUrl().getUrl().equals("dianping://mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=101010&shopid=100&shopuuid=shopUuid"));
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表不为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForMT() {
        // arrange
        List<EduOnlineDealClassItemVOListOpt.CoursePlan> coursePlanList = getCoursePlans();
        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));
        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");
        addTeacherClassValue(context, 0);
        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealId()).thenReturn(101010);
        when(context.getParam(ProductDetailActivityConstants.Params.platform)).thenReturn(201);
        when(context.getParam(ProductDetailActivityConstants.Params.dpPoiIdL)).thenReturn(100L);
        when(context.getParam(ProductDetailActivityConstants.Params.mtPoiIdL)).thenReturn(200L);
        when(context.getParam(ProductDetailActivityConstants.Params.shopUuid)).thenReturn("shopUuid");
        EduOnlineDealClassItemVOListOpt.Config config = new EduOnlineDealClassItemVOListOpt.Config();
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getJumpUrl() != null && result.get(0).getJumpUrl().getUrl().equals("imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=mrn-le-edutrialcourse&mrn_component=mrn-le-edutrialcourse&dealid=101010&shopid=200"));
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表不为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForXCX() {
        // arrange
        List<EduOnlineDealClassItemVOListOpt.CoursePlan> coursePlanList = getCoursePlans();
        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));
        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");
        addTeacherClassValue(context, 0);
        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(context.getParam(ProductDetailActivityConstants.Params.mpSource)).thenReturn("xcx");
        EduOnlineDealClassItemVOListOpt.Config config = new EduOnlineDealClassItemVOListOpt.Config();
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getJumpUrl() == null);
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表不为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmptyForMT_noJumpUrl() {
        // arrange
        List<EduOnlineDealClassItemVOListOpt.CoursePlan> coursePlanList = getCoursePlans();
        AttrM courseAttr = new AttrM();
        courseAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_PLAN);
        courseAttr.setValue(JsonCodec.encodeWithUTF8(coursePlanList));
        AttrM methodAttr = new AttrM();
        methodAttr.setName(EduOnlineDealClassItemVOListOpt.ATTR_COURSE_METHOD);
        methodAttr.setValue("method");
        addTeacherClassValue(context, 1);
        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(courseAttr, methodAttr));
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        EduOnlineDealClassItemVOListOpt.Config config = new EduOnlineDealClassItemVOListOpt.Config();
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getJumpUrl() == null);
    }

    @Test
    public void testComputeWhenDealAttrsIsNull() {
        // arrange
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        when(param.getDealAttrs()).thenReturn(null);
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenCoursePlanListIsNull() {
        // arrange
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("course_plan", null)));
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenDealDetailStructAttrModuleVOSIsNull() {
        // arrange
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("course_plan", "[]")));
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenTotalClassHoursIsNull() {
        // arrange
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("course_plan", "[]")));
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeWhenIsHasOnlineClassIsFalse() {
        // arrange
        EduOnlineDealClassItemVOListOpt opt = new EduOnlineDealClassItemVOListOpt();
        when(param.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("course_plan", "[]")));
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }
}
