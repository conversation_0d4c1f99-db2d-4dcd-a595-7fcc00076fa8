package com.sankuai.dzviewscene.product.ability.mergequery.fetcher.padding.handler.shop;

import com.dianping.degrade.util.JsonCodec;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionConfigHelper;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.BNPLExposureDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.ExposureEnum;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.testng.AssertJUnit.assertFalse;
import static org.testng.AssertJUnit.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class UserAfterPayPaddingHandlerTest {

    @InjectMocks
    private UserAfterPayPaddingHandler userAfterPayPaddingHandler;

    @Mock
    private CompositeAtomService compositeAtomService;

    private ActivityCxt ctx;
    private ContextHandlerResult contextHandlerResult;
    private Map<String, Object> params;

    @Before
    public void setUp() {
        ctx = new ActivityCxt();
        contextHandlerResult = new ContextHandlerResult();
        params = new HashMap<>();
    }

    /**
     * 测试params为空的情况
     */
    @Test
    public void testPaddingWithEmptyParams() {
        CompletableFuture<ContextHandlerResult> result = userAfterPayPaddingHandler.padding(ctx, contextHandlerResult, params);
        assert result.join().equals(contextHandlerResult);
    }

    /**
     * 测试params不包含clientType的情况
     */
    @Test
    public void testPaddingWithParamsNotContainClientType() {
        try (MockedStatic<LionConfigHelper> mockedStatic = Mockito.mockStatic(LionConfigHelper.class)) {
            mockedStatic.when(() -> LionConfigHelper.timesDealCreditPaySwitch()).thenReturn(true);
            params.put("201", "美团xcx");
            ctx.getParameters().put("userAgent", "200");
            CompletableFuture<ContextHandlerResult> result = userAfterPayPaddingHandler.padding(ctx, contextHandlerResult, params);
            assert result.join().equals(contextHandlerResult);
        }
    }

    /**
     * 测试查询结果为null的情况
     */
    @Test
    public void testPaddingWithNullQueryResult() {
        try (MockedStatic<LionConfigHelper> mockedStatic = Mockito.mockStatic(LionConfigHelper.class)) {
            mockedStatic.when(() -> LionConfigHelper.timesDealCreditPaySwitch()).thenReturn(false);
            CompletableFuture<ContextHandlerResult> result = userAfterPayPaddingHandler.padding(ctx, contextHandlerResult, params);
            assert result.join().equals(contextHandlerResult);
        }
    }

    /**
     * 测试查询结果失败的情况
     */
    @Test
    public void testPaddingWithQueryResultFail() {
        try (MockedStatic<LionConfigHelper> mockedStatic = Mockito.mockStatic(LionConfigHelper.class)) {
            mockedStatic.when(() -> LionConfigHelper.timesDealCreditPaySwitch()).thenReturn(true);
            params.put("200", "美团app");
            ctx.getParameters().put("userAgent", "200");
            BNPLExposureResponse response = new BNPLExposureResponse();
            response.setStatus("SUCCESS");
            CompletableFuture<ContextHandlerResult> result = userAfterPayPaddingHandler.padding(ctx, contextHandlerResult, params);
            assert result.join().equals(contextHandlerResult);
        }

    }


    /**
     * 测试查询结果成功且data不为null的情况
     */
    @Test
    public void testPaddingWithQueryResultSuccessAndDataNotNull() {
        try (MockedStatic<LionConfigHelper> mockedStatic = Mockito.mockStatic(LionConfigHelper.class)) {
            mockedStatic.when(() -> LionConfigHelper.timesDealCreditPaySwitch()).thenReturn(true);
            params.put("200", "美团app");
            ctx.getParameters().put("userAgent", "200");
            ctx.getParameters().put("deviceId", "0000000121312312");
            ctx.getParameters().put("clientType", "android");
            ctx.getParameters().put("lat", "121");
            ctx.getParameters().put("lng", "31");
            ctx.getParameters().put("platform", "2");
            ctx.getParameters().put("mtCityId", "10");
            ctx.getParameters().put("userId", "123");
            BNPLExposureResponse response = new BNPLExposureResponse();
            response.setStatus("SUCCESS");
            BNPLExposureDTO data = new BNPLExposureDTO();
            data.setExposure("exposureData");
            response.setData(data);
            CompletableFuture<ContextHandlerResult> result = userAfterPayPaddingHandler.padding(ctx, contextHandlerResult, params);
            assert result.join()!=null;
        }

    }


    /**
     * 测试getUserExposure方法，当contextHandlerResult不为null且Exposure为EXPOSED时应返回true
     */
    @Test
    public void testGetUserExposureWhenExposed() {
        try (MockedStatic<LionConfigHelper> mockedStatic = Mockito.mockStatic(LionConfigHelper.class)) {
            mockedStatic.when(() -> LionConfigHelper.timesDealCreditPaySwitch()).thenReturn(true);
            ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
            contextHandlerResult.setExposure(ExposureEnum.EXPOSED.getCode());
            Map<String, Object> sourceMap = Maps.newHashMap();
            sourceMap.put("ContextHandlerAbility", contextHandlerResult);
            ctx.setSourceMap(sourceMap);
            boolean result = UserAfterPayPaddingHandler.getUserExposure(ctx);

            assertTrue("当contextHandlerResult不为null且Exposure为EXPOSED时，应返回true", result);
        }

    }

    /**
     * 测试getUserExposure方法，当contextHandlerResult不为null且Exposure不为EXPOSED时应返回false
     */
    @Test
    public void testGetUserExposureWhenNotExposed() {
        try (MockedStatic<LionConfigHelper> mockedStatic = Mockito.mockStatic(LionConfigHelper.class)) {
            mockedStatic.when(() -> LionConfigHelper.timesDealCreditPaySwitch()).thenReturn(true);
            ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
            contextHandlerResult.setExposure(ExposureEnum.UNEXPOSED.getCode());
            Map<String, Object> sourceMap = Maps.newHashMap();
            sourceMap.put("ContextHandlerAbility", contextHandlerResult);
            ctx.setSourceMap(sourceMap);
            boolean result = UserAfterPayPaddingHandler.getUserExposure(ctx);

            assertFalse("当contextHandlerResult不为null且Exposure不为EXPOSED时，应返回false", result);
        }

    }

    @Test
    public void test() {
        Map<String, String> params = Maps.newHashMap();
        params.put("bizId", "121319");
        params.put("planId", "121319");
        params.put("signIphPayMerchantNo", "*****************");
        String encode = JsonCodec.encode(params);
        System.out.println("encode = " + encode);
    }

}
