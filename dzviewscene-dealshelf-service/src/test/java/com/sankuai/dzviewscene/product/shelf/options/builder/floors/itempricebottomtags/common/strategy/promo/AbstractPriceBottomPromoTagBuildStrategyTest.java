package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.strategy.promo;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.common.PriceBottomTagBuildReq;
import com.sankuai.dzviewscene.product.shelf.utils.PromoSimplifyUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractPriceBottomPromoTagBuildStrategyTest {

    private AbstractPriceBottomPromoTagBuildStrategy strategy;

    private PriceBottomTagBuildReq req;

    private MockedStatic<PromoSimplifyUtils> mockedStatic;

    @Before
    public void setUp() {
        strategy = Mockito.mock(AbstractPriceBottomPromoTagBuildStrategy.class, Mockito.CALLS_REAL_METHODS);
        req = new PriceBottomTagBuildReq();
        mockedStatic = Mockito.mockStatic(PromoSimplifyUtils.class);
    }

    @After
    public void tearDown() {
        mockedStatic.close();
    }

    @Test
    public void testGetPicRadioMTWithMtRadioGreaterThanZero() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(true);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
            cfg.setMtRadio(3.25);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testGetPicRadioMTWithMtRadioZero() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(true);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
            cfg.setMtRadio(0);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testGetPicRadioMTWithoutCfg() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(true);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testGetPicRadioDPWithDpRadioGreaterThanZero() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(false);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
            cfg.setDpRadio(3.25);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testGetPicRadioDPWithDpRadioZero() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(false);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            PriceBottomTagBuildCfg cfg = new PriceBottomTagBuildCfg();
            cfg.setDpRadio(0);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, cfg, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testGetPicRadioDPWithoutCfg() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(false);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testGetPicRadioMTWithoutCfgAndDpRadio() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(true);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testGetPicRadioDPWithoutCfgAndMtRadio() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(1)).thenReturn(false);
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            double defaultMTRadio = 3.25;
            double defaultDPRadio = 3.25;
            when(strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio)).thenReturn(3.25);
            double result = strategy.getPicRadio(1, null, defaultMTRadio, defaultDPRadio);
            assertEquals(3.25, result, 0.01);
        }
    }

    @Test
    public void testBuildWhenReqIsNull() {
        assertNull(strategy.build(null));
    }

    @Test
    public void testBuildWhenProductMIsNull() {
        assertNull(strategy.build(req));
    }

    @Test
    public void testBuildWhenPromoPricesIsEmpty() {
        req.setProductM(new ProductM());
        assertNull(strategy.build(req));
    }

    @Test
    public void testBuildWhenBuildTagReturnsNull() {
        req.setProductM(new ProductM());
        req.getProductM().setPromoPrices(Collections.singletonList(new ProductPromoPriceM()));
        when(strategy.buildTag(req)).thenReturn(null);
        mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplify(Mockito.any())).thenReturn(true);
        assertNull(strategy.build(req));
    }

    @Test
    public void testBuildWhenBuildTagReturnsNonNull() {
        req.setProductM(new ProductM());
        req.getProductM().setPromoPrices(Collections.singletonList(new ProductPromoPriceM()));
        DzTagVO dzTagVO = new DzTagVO();
        when(strategy.buildTag(req)).thenReturn(dzTagVO);
        mockedStatic.when(() -> PromoSimplifyUtils.hitPromoSimplify(Mockito.any())).thenReturn(true);
        assertNotNull(strategy.build(req));
    }

    /**
     * 测试平台是美团时，返回预设的 DzPictureComponentVO 对象
     */
    @Test
    public void testBuildPromoAfterPicIsMT() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            // arrange
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            int platform = 1;
            mockedStatic.when(() -> PlatformUtil.isMT(platform)).thenReturn(true);
            DzPictureComponentVO expectedVO = new DzPictureComponentVO("https://p0.meituan.net/travelcube/ac0b1b7e016f85fcf9b8784d34d1bc14439.png", 1);
            when(strategy.buildPromoAfterPic(platform)).thenReturn(expectedVO);
            // act
            DzPictureComponentVO result = strategy.buildPromoAfterPic(platform);
            // assert
            assertEquals(expectedVO.getPicUrl(), result.getPicUrl());
            assertEquals(expectedVO.getAspectRadio(), result.getAspectRadio(), 0.001);
        }
    }

    /**
     * 测试平台不是美团时，返回预设的 DzPictureComponentVO 对象
     */
    @Test
    public void testBuildPromoAfterPicNotMT() throws Throwable {
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            // arrange
            AbstractPriceBottomPromoTagBuildStrategy strategy = mock(AbstractPriceBottomPromoTagBuildStrategy.class);
            int platform = 2;
            mockedStatic.when(() -> PlatformUtil.isMT(platform)).thenReturn(false);
            DzPictureComponentVO expectedVO = new DzPictureComponentVO("https://p1.meituan.net/travelcube/4b5a342d1d0c2ae89ea51d8a2a6f7d75459.png", 1);
            when(strategy.buildPromoAfterPic(platform)).thenReturn(expectedVO);
            // act
            DzPictureComponentVO result = strategy.buildPromoAfterPic(platform);
            // assert
            assertEquals(expectedVO.getPicUrl(), result.getPicUrl());
            assertEquals(expectedVO.getAspectRadio(), result.getAspectRadio(), 0.001);
        }
    }

}
