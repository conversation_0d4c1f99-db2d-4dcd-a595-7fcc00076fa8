package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ZdcTagIdFetcherOptTest {

    @Mock
    private CompositeAtomService compositeAtomService;
    @Mock
    private ActivityCxt context;
    private ZdcTagIdFetcherOpt.Config config;

    @InjectMocks
    private ZdcTagIdFetcherOpt zdcTagIdFetcherOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        config = new ZdcTagIdFetcherOpt.Config();
    }

    /**
     * 测试场景：ShopM为null时，应返回空列表
     */
    @Test
    public void testComputeShopMIsNull() throws Throwable {
        // arrange
        when(context.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(null);

        // act
        CompletableFuture<Object> result = zdcTagIdFetcherOpt.compute(context, null, config);

        // assert
        assertTrue(result.isDone());
        assertTrue(((List) result.get()).isEmpty());
    }

    /**
     * 测试场景：mockTagMap包含shopM的LongShopId时，应返回对应的标签列表
     */
    @Test
    public void testComputeMockTagMapContainsShopId() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        shopM.setLongShopId(1L);
        List<Long> expectedTags = Arrays.asList(1L, 2L);
        HashMap<Long, List<Long>> mockTagMap = new HashMap<>();
        mockTagMap.put(1L, expectedTags);
        config.setMockTagMap(mockTagMap);
        when(context.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);

        // act
        CompletableFuture<Object> result = zdcTagIdFetcherOpt.compute(context, null, config);

        // assert
        assertTrue(result.isDone());
        assertEquals(expectedTags, result.get());
    }

    /**
     * 测试场景：mockTagMap不包含shopM的LongShopId时，应调用compositeAtomService.findZdcTagByDpShopId
     */
    @Test
    public void testComputeMockTagMapDoesNotContainShopId() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        shopM.setLongShopId(2L);
        List<Long> expectedTags = Arrays.asList(3L, 4L);
        CompletableFuture<List<Long>> future = CompletableFuture.completedFuture(expectedTags);
        when(context.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);
        when(compositeAtomService.findZdcTagByDpShopId(2L, "")).thenReturn(future);

        // act
        CompletableFuture<Object> result = zdcTagIdFetcherOpt.compute(context, null, config);

        // assert
        assertTrue(result.isDone());
        assertEquals(expectedTags, result.get());
        verify(compositeAtomService, times(1)).findZdcTagByDpShopId(2L, "");
    }
}
