package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itempricebottomtag.strategy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;

@RunWith(MockitoJUnitRunner.class)
public class PriceBottomTagStrategyFactoryTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private PriceBottomTagStrategy priceBottomTagStrategy;

    private PriceBottomTagStrategyFactory priceBottomTagStrategyFactory;

    @Before
    public void setUp() {
        priceBottomTagStrategyFactory = new PriceBottomTagStrategyFactory();
        // Stubbing getName method to return a non-null value
        when(priceBottomTagStrategy.getName()).thenReturn("testStrategy");
    }

    /**
     * Tests the afterPropertiesSet method when ApplicationContext contains PriceBottomTagStrategy type Bean.
     */
    @Test
    public void testAfterPropertiesSet_ExistBean() throws Throwable {
        // Arrange
        Map<String, PriceBottomTagStrategy> beanMap = new HashMap<>();
        beanMap.put("test", priceBottomTagStrategy);
        when(applicationContext.getBeansOfType(PriceBottomTagStrategy.class)).thenReturn(beanMap);
        priceBottomTagStrategyFactory.setApplicationContext(applicationContext);
        // Act
        priceBottomTagStrategyFactory.afterPropertiesSet();
        // Assert
        verify(applicationContext, times(1)).getBeansOfType(PriceBottomTagStrategy.class);
    }

    /**
     * Tests the afterPropertiesSet method when ApplicationContext does not contain PriceBottomTagStrategy type Bean.
     */
    @Test
    public void testAfterPropertiesSet_NotExistBean() throws Throwable {
        // Arrange
        when(applicationContext.getBeansOfType(PriceBottomTagStrategy.class)).thenReturn(new HashMap<>());
        priceBottomTagStrategyFactory.setApplicationContext(applicationContext);
        // Act
        priceBottomTagStrategyFactory.afterPropertiesSet();
        // Assert
        verify(applicationContext, times(1)).getBeansOfType(PriceBottomTagStrategy.class);
    }
}
