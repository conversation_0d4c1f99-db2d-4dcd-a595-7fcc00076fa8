package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.serviceprocess.DealStandardServiceProcessBuilder;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.StandardServiceProcessVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class StandardServiceProcessStrategyTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    private StandardServiceProcessStrategy strategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        strategy = new StandardServiceProcessStrategy();
    }

    /**
     * 测试 buildModelVO 方法，当 standardServiceProcessVOS 为空时
     */
    @Test
    public void testBuildModelVO_WhenStandardServiceProcessVOSIsEmpty() {
        // arrange
        when(mockActivityCxt.getSource(DealStandardServiceProcessBuilder.CODE)).thenReturn(Collections.emptyList());

        // act
        DealDetailModuleVO result = strategy.buildModelVO(mockActivityCxt, null, "");

        // assert
        assertNull("当 standardServiceProcessVOS 为空时，应返回 null", result);
    }

    /**
     * 测试 buildModelVO 方法，当 standardServiceProcessVOS 不为空时
     */
    @Test
    public void testBuildModelVO_WhenStandardServiceProcessVOSIsNotEmpty() {
        // arrange
        StandardServiceProcessVO vo = new StandardServiceProcessVO();
        vo.setArrowIcon("arrowIcon");
        vo.setStepIcon("stepIcon");
        vo.setStepName("stepName");
        when(mockActivityCxt.getSource(DealStandardServiceProcessBuilder.CODE)).thenReturn(Collections.singletonList(vo));

        // act
        DealDetailModuleVO result = strategy.buildModelVO(mockActivityCxt, null, "");

        // assert
        assertNotNull("当 standardServiceProcessVOS 不为空时，应返回非 null", result);
    }
}
