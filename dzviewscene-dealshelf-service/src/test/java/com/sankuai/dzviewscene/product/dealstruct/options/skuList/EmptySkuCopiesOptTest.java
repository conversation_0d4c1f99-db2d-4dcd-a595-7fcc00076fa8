package com.sankuai.dzviewscene.product.dealstruct.options.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuCopiesVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EmptySkuCopiesOptTest {

    /**
     * Tests whether the compute method always returns null.
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        EmptySkuCopiesOpt emptySkuCopiesOpt = new EmptySkuCopiesOpt();
        ActivityCxt context = new ActivityCxt();
        int copies = 1;
        SkuItemDto skuItemDto = new SkuItemDto();
        List<AttrM> dealAttrs = new ArrayList<>();
        // Using builder pattern to create Param instance
        SkuCopiesVP.Param param = SkuCopiesVP.Param.builder().copies(copies).skuItemDto(skuItemDto).dealAttrs(dealAttrs).build();
        EmptySkuCopiesOpt.Config config = new EmptySkuCopiesOpt.Config();
        // act
        String result = emptySkuCopiesOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }
}
