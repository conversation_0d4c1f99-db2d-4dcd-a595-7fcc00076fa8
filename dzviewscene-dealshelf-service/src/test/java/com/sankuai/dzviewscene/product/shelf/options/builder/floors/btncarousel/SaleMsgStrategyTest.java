package com.sankuai.dzviewscene.product.shelf.options.builder.floors.btncarousel;

import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonCarouselMsgVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemButtonCarouselMsgVP.Param;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class SaleMsgStrategyTest {

    private SaleMsgStrategy saleMsgStrategy;
    private ItemButtonCarouselMsgVP.Param param;
    private CarouselBuilderContext context;
    private ProductM productM;
    private ProductSaleM productSaleM;

    @Before
    public void setUp() {
        saleMsgStrategy = new SaleMsgStrategy();
        param = Mockito.mock(Param.class);
        context = Mockito.mock(CarouselBuilderContext.class);
        productM = Mockito.mock(ProductM.class);
        productSaleM = Mockito.mock(ProductSaleM.class);
    }

//    /**
//     * 测试参数为null时的情况
//     */
//    @Test
//    public void testBuildParamIsNull() {
//        RichLabelVO result = saleMsgStrategy.build(null);
//        assertNull(result);
//    }

    /**
     * 测试ProductM为null时的情况
     */
    @Test
    public void testBuildProductMIsNull() {
        Mockito.when(context.getProductM()).thenReturn(null);
        RichLabelVO result = saleMsgStrategy.build(context);
        assertNull(result);
    }

    /**
     * 测试ProductSaleM为null时的情况
     */
    @Test
    public void testBuildProductSaleMIsNull() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getSale()).thenReturn(null);
        RichLabelVO result = saleMsgStrategy.build(context);
        assertNull(result);
    }

    /**
     * 测试saleTag为空字符串时的情况
     */
    @Test
    public void testBuildSaleTagIsEmpty() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getSale()).thenReturn(productSaleM);
        Mockito.when(productSaleM.getSaleTag()).thenReturn("");
        RichLabelVO result = saleMsgStrategy.build(context);
        assertNull(result);
    }

    /**
     * 测试saleTag为非空字符串时的情况
     */
    @Test
    public void testBuildSaleTagIsNotEmpty() {
        Mockito.when(context.getProductM()).thenReturn(productM);
        Mockito.when(productM.getSale()).thenReturn(productSaleM);
        Mockito.when(productSaleM.getSaleTag()).thenReturn("SaleTag");
        RichLabelVO result = saleMsgStrategy.build(context);

        assertEquals("SaleTag", result.getText());
        assertEquals(10, result.getTextSize());
        assertEquals("#999999", result.getTextColor());
    }
}
