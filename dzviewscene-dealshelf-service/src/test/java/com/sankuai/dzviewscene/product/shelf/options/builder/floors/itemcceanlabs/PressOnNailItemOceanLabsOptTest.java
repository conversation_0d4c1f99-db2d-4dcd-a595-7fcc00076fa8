package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemcceanlabs;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemOceanLabsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PressOnNailUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.google.common.collect.Lists;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailItemOceanLabsOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;
    @Mock
    private ItemOceanLabsVP.Param mockParam;
    @Mock
    private ProductM mockProductM;

    private PressOnNailItemOceanLabsOpt pressOnNailItemOceanLabsOpt;

    private MockedStatic<PressOnNailUtils> pressOnNailUtilsMockedStatic;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailItemOceanLabsOpt = new PressOnNailItemOceanLabsOpt();
        pressOnNailUtilsMockedStatic = Mockito.mockStatic(PressOnNailUtils.class);
    }

    @After
    public void tearDown(){
        pressOnNailUtilsMockedStatic.close();
    }

    /**
     * 测试compute方法，当传入的参数符合预期时，应返回正确的Json字符串。
     */
    @Test
    public void testComputeReturnsCorrectJsonString() {
        // arrange
        when(mockParam.getProductM()).thenReturn(mockProductM);
        pressOnNailUtilsMockedStatic.when(()->PressOnNailUtils.buildItemOceanMap(any(), any())).thenReturn(new HashMap<>());

        // act
        String result = pressOnNailItemOceanLabsOpt.compute(mockActivityCxt, mockParam, null);

        // assert
        assertNotNull(result);
    }
}
