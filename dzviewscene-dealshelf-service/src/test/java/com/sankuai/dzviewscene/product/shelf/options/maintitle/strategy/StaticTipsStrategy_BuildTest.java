package com.sankuai.dzviewscene.product.shelf.options.maintitle.strategy;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.maintitle.strategy.TipsStrategyConfig;
import com.sankuai.dzviewscene.productshelf.vu.vo.IconRichLabelVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class StaticTipsStrategy_BuildTest {

    private StaticTipsStrategy staticTipsStrategy;

    private ActivityCxt mockActivityCxt;

    private TipsStrategyConfig mockTipsStrategyConfig;

    @Before
    public void setUp() {
        staticTipsStrategy = new StaticTipsStrategy();
        mockActivityCxt = Mockito.mock(ActivityCxt.class);
        mockTipsStrategyConfig = Mockito.mock(TipsStrategyConfig.class);
    }

    /**
     * 测试build方法，当hasDataTipsStrategies为null时
     */
    @Test
    public void testBuildWithNullHasDataTipsStrategies() {
        // arrange
        when(mockTipsStrategyConfig.getText()).thenReturn("测试文本");
        when(mockTipsStrategyConfig.getTextColor()).thenReturn("#777777");
        when(mockTipsStrategyConfig.getIcon()).thenReturn("测试图标");
        // act
        List<IconRichLabelVO> result = staticTipsStrategy.build(mockActivityCxt, mockTipsStrategyConfig, null);
        // assert
        assertEquals("测试文本", result.get(0).getText().getText());
        assertEquals("#777777", result.get(0).getText().getTextColor());
        assertEquals("测试图标", result.get(0).getIcon());
    }

    /**
     * 测试build方法，当hasDataTipsStrategies为非空列表时
     */
    @Test
    public void testBuildWithNonNullHasDataTipsStrategies() {
        // arrange
        when(mockTipsStrategyConfig.getText()).thenReturn("测试文本");
        when(mockTipsStrategyConfig.getTextColor()).thenReturn("#777777");
        when(mockTipsStrategyConfig.getIcon()).thenReturn("测试图标");
        // act
        List<IconRichLabelVO> result = staticTipsStrategy.build(mockActivityCxt, mockTipsStrategyConfig, Arrays.asList("测试策略1", "测试策略2"));
        // assert
        assertEquals("测试文本", result.get(0).getText().getText());
        assertEquals("#777777", result.get(0).getText().getTextColor());
        assertEquals("测试图标", result.get(0).getIcon());
    }
}
