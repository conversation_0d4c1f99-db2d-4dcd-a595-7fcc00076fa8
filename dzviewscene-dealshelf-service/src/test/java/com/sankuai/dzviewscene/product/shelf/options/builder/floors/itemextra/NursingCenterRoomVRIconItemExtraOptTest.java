package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemextra;

import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.utils.PoiIdUtil;
import com.sankuai.dzviewscene.product.ability.options.VRInterestFetcherOpt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemExtraVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.utils.ParamsUtil;
import org.apache.commons.collections.MapUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.*;

import java.util.Map;

import static graphql.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

public class NursingCenterRoomVRIconItemExtraOptTest {
    private NursingCenterRoomVRIconItemExtraOpt.Config config;
    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private Param param;
    @Mock
    private ProductM productM;
    @InjectMocks
    private NursingCenterRoomVRIconItemExtraOpt opt;
    private MockedStatic<ParamsUtil> paramsUtilMockedStatic;
    private MockedStatic<PoiIdUtil> poiIdUtilMockedStatic;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(param.getProductM()).thenReturn(productM);
        config = new NursingCenterRoomVRIconItemExtraOpt.Config();
        paramsUtilMockedStatic= Mockito.mockStatic(ParamsUtil.class);
        poiIdUtilMockedStatic=Mockito.mockStatic(PoiIdUtil.class);
    }

    @After
    public void tearDown(){
        paramsUtilMockedStatic.close();
        poiIdUtilMockedStatic.close();
    }

    /**
     * 测试buildExtraMap方法，美团平台正常场景
     */
    @Test
    public void testBuildExtraMapNormalCaseInMt() throws Throwable {
        // arrange
        config.setVrPicUrl("testPicUrl");
        when(activityCxt.getParam(VRInterestFetcherOpt.CODE)).thenReturn(true);
        when(productM.getAttr(NursingCenterRoomVRIconItemExtraOpt.VR_URL_ATTR)).thenReturn("testVrJumpUrl");
        paramsUtilMockedStatic.when(()->ParamsUtil.getIntSafely(activityCxt, ShelfActivityConstants.Params.platform)).thenReturn(VCPlatformEnum.MT.getType());
        poiIdUtilMockedStatic.when(()->PoiIdUtil.getMtPoiIdL(activityCxt,ShelfActivityConstants.Params.mtPoiIdL, ShelfActivityConstants.Params.mtPoiId)).thenReturn(123456L);

        // act
        Map<String, Object> result = opt.buildExtraMap(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertTrue(result.containsKey("vrIcon"));
        NursingCenterRoomVRIconItemExtraOpt.VRIcon vrIcon = (NursingCenterRoomVRIconItemExtraOpt.VRIcon) result.get("vrIcon");
        assertTrue(vrIcon.getJumpUrl().contains("mtShopId=123456"));
    }

    /**
     * 测试buildExtraMap方法，点评平台正常场景
     */
    @Test
    public void testBuildExtraMapNormalCaseInDp() throws Throwable {
        // arrange
        config.setVrPicUrl("testPicUrl");
        when(activityCxt.getParam(VRInterestFetcherOpt.CODE)).thenReturn(true);
        when(productM.getAttr(NursingCenterRoomVRIconItemExtraOpt.VR_URL_ATTR)).thenReturn("testVrJumpUrl");
        paramsUtilMockedStatic.when(()->ParamsUtil.getIntSafely(activityCxt, ShelfActivityConstants.Params.platform)).thenReturn(VCPlatformEnum.DP.getType());
        poiIdUtilMockedStatic.when(()->PoiIdUtil.getDpPoiIdL(activityCxt,ShelfActivityConstants.Params.dpPoiIdL, ShelfActivityConstants.Params.dpPoiId)).thenReturn(123456L);

        // act
        Map<String, Object> result = opt.buildExtraMap(activityCxt, param, config);

        // assert
        assertNotNull(result);
        assertTrue(result.containsKey("vrIcon"));
        NursingCenterRoomVRIconItemExtraOpt.VRIcon vrIcon = (NursingCenterRoomVRIconItemExtraOpt.VRIcon) result.get("vrIcon");
        assertTrue(vrIcon.getJumpUrl().contains("dpShopId=123456"));
    }

    /**
     * 测试buildExtraMap方法，VR图片URL为空
     */
    @Test
    public void testBuildExtraMapVrPicUrlEmpty() throws Throwable {
        // arrange
        config.setVrPicUrl("");
        when(activityCxt.getParam(VRInterestFetcherOpt.CODE)).thenReturn(true);

        // act
        Map<String, Object> result = opt.buildExtraMap(activityCxt, param, config);

        // assert
        assertTrue(MapUtils.isEmpty(result));
    }

    /**
     * 测试buildExtraMap方法，店铺没有VR权益
     */
    @Test
    public void testBuildExtraMapNoVRInterest() throws Throwable {
        // arrange
        config.setVrPicUrl("testPicUrl");
        when(activityCxt.getParam(VRInterestFetcherOpt.CODE)).thenReturn(false);

        // act
        Map<String, Object> result = opt.buildExtraMap(activityCxt, param, config);

        // assert
        assertTrue(MapUtils.isEmpty(result));
    }

    /**
     * 测试buildExtraMap方法，产品模型中不包含VR跳转URL
     */
    @Test
    public void testBuildExtraMapNoVrJumpUrl() throws Throwable {
        // arrange
        config.setVrPicUrl("testPicUrl");
        when(activityCxt.getParam(VRInterestFetcherOpt.CODE)).thenReturn(true);
        when(productM.getAttr(NursingCenterRoomVRIconItemExtraOpt.VR_URL_ATTR)).thenReturn("");

        // act
        Map<String, Object> result = opt.buildExtraMap(activityCxt, param, config);

        // assert
        assertTrue(MapUtils.isEmpty(result));
    }

}
