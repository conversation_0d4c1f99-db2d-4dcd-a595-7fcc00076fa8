package com.sankuai.dzviewscene.product.filterlist.option.builder.product.title;

import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.dianping.zebra.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.common.helper.LionKeys;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductRichTitleVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.douhu.ShelfDouHuFetcher;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/25 11:21
 */
@RunWith(MockitoJUnitRunner.class)
public class TimesDealRichTitleOptTest {

    @InjectMocks
    private TimesDealRichTitleOpt timesDealRichTitleOpt;

    /**
     * 测试正常的情况下
     */
    @Test
    public void testCompute() {
        try (MockedStatic<Lion> lion = Mockito.mockStatic(Lion.class)) {
            lion.when(() -> Lion.getList(anyString(), anyString(), any()))
                    .thenReturn(Lists.newArrayList("EXP2024122400001_c", "EXP2024122400002_c"));
            lion.when(() -> Lion.getBoolean(LionKeys.APP_KEY, LionKeys.TIMES_DEAL_OPTIMIZE_SWITCH, false))
                    .thenReturn(true);
            ProductM productM = JacksonUtils.deserialize(productMJson, ProductM.class);
            List<DouHuM> douHuMList = JacksonUtils.deserialize(douhuDataJson, List.class);
            ActivityCxt context = Mockito.mock(ActivityCxt.class);
            when(context.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);

            TimesDealRichTitleOpt.Config config = Mockito.mock(TimesDealRichTitleOpt.Config.class);
            when(config.getUnit()).thenReturn("/次");
            ProductRichTitleVP.Param param = Mockito.mock(ProductRichTitleVP.Param.class);
            when(param.getProductM()).thenReturn(productM);
            String result = timesDealRichTitleOpt.compute(context, param, config);
            assert StringUtils.isNotBlank(result);
        }
    }

    /**
     * 测试非团购次卡召回
     */
    @Test
    public void testComputeAndIsTimesDealQueryFLagFalse() {
        try (MockedStatic<Lion> lion = Mockito.mockStatic(Lion.class)) {
            lion.when(() -> Lion.getList(anyString(), anyString(), any()))
                    .thenReturn(Lists.newArrayList("EXP2024122400001_c", "EXP2024122400002_c"));
            lion.when(() -> Lion.getBoolean(LionKeys.APP_KEY, LionKeys.TIMES_DEAL_OPTIMIZE_SWITCH, false))
                    .thenReturn(true);
            ProductM productM = JacksonUtils.deserialize(productMJson, ProductM.class);
            productM.setTimesDealQueryFlag(false);
            List<DouHuM> douHuMList = JacksonUtils.deserialize(douhuDataJson, List.class);
            ActivityCxt context = Mockito.mock(ActivityCxt.class);
            // when(context.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);

            TimesDealRichTitleOpt.Config config = Mockito.mock(TimesDealRichTitleOpt.Config.class);
            // when(config.getUnit()).thenReturn("/次");
            ProductRichTitleVP.Param param = Mockito.mock(ProductRichTitleVP.Param.class);
            when(param.getProductM()).thenReturn(productM);
            String result = timesDealRichTitleOpt.compute(context, param, config);
            assert StringUtils.isBlank(result);
        }
    }

    /**
     * 测试非团购次卡
     */
    @Test
    public void testComputeAndIsTimesDealFalse() {
        try (MockedStatic<Lion> lion = Mockito.mockStatic(Lion.class)) {
            lion.when(() -> Lion.getList(anyString(), anyString(), any()))
                    .thenReturn(Lists.newArrayList("EXP2024122400001_c", "EXP2024122400002_c"));
            lion.when(() -> Lion.getBoolean(LionKeys.APP_KEY, LionKeys.TIMES_DEAL_OPTIMIZE_SWITCH, false))
                    .thenReturn(true);
            ProductM productM = JacksonUtils.deserialize(productMJson, ProductM.class);
            productM.setTradeType(3);
            List<DouHuM> douHuMList = JacksonUtils.deserialize(douhuDataJson, List.class);
            ActivityCxt context = Mockito.mock(ActivityCxt.class);
            when(context.getSource(ShelfDouHuFetcher.CODE)).thenReturn(douHuMList);

            TimesDealRichTitleOpt.Config config = Mockito.mock(TimesDealRichTitleOpt.Config.class);
            // when(config.getUnit()).thenReturn("/次");
            ProductRichTitleVP.Param param = Mockito.mock(ProductRichTitleVP.Param.class);
            when(param.getProductM()).thenReturn(productM);
            String result = timesDealRichTitleOpt.compute(context, param, config);
            // {"symbol":"¥","salePrice":"1400","title":"单独购买"}
            assert result.contains("\"单次服务\"");
        }
    }

    private static final String productMJson = "{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductM\",\"groupName\":null,\"productType\":0,\"productId\":1035747853,\"id\":null,\"categoryId\":0,\"categoryName\":null,\"spuType\":0,\"title\":\"10次卡--\",\"picUrl\":null,\"jumpUrl\":\"imeituan://www.meituan.com/gc/deal/detail?did=1035747853&poiid=436822180&shelf&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUKl3xCcPkuXeB9-u17IquUDx_DQ3rkYqThHf4e9-gMg9MoOtxj9eMV0upaMuj7kO12lOiBtX9-ed6Fz8VDbvf5V3JcP8A92RybScpUBcLtA0lKTGwhtXpN9pvOQBXHyPAKpO-G1CVde4lKQZusmR5NttY2y4AdsduvBCw9Fe1CF8wkjJvxn9ajTitmtUGeBBhHZeM0BrRFl30Up1JUrkW_GOB3wnuZWYuDAmv3-ISc3LFRNtnvRLqupd7k2AbJB1XUlKQ3sRJvypMckLu84KPiQP3D2Xp3oiJt6OC7jbWaeU4KmRumEl-ABxy4nCbTOXsBoMtmN_pLcgReVR8PQ0r2xAdZVzIlaVZwBOQcZMw5H5DXNZpiBDSWGcVa3PJlHXTZRPgcFnIkUm70kYrkjiVQmj7VljsHY09Xorq0PFQx3Te--ytXE0JWX_2gy3gZNn6fT4GPj9YDBtO5MzKHblm9ymOOe5lrpxbhmeWqRzvENg\",\"orderUrl\":null,\"jumpText\":null,\"productDesc\":null,\"available\":null,\"productItemMList\":null,\"dealPinItemMList\":null,\"pinPools\":null,\"spuM\":null,\"spuMList\":null,\"saleTag\":null,\"sale\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM\",\"sale\":0,\"saleTag\":\"已售0\"},\"stock\":null,\"basePriceTag\":null,\"basePriceDesc\":null,\"basePrice\":null,\"marketPrice\":null,\"vipPrice\":null,\"promoTag\":null,\"promoPrices\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM\",\"promoType\":0,\"promoPrice\":[\"java.math.BigDecimal\",1400],\"promoTag\":\"特惠促销共省¥480\",\"promoTagPrefix\":null,\"promoPriceTag\":\"1400\",\"marketPrice\":null,\"discount\":[\"java.math.BigDecimal\",0.75],\"discountTag\":null,\"availableTime\":null,\"userHasCard\":false,\"totalPromoPrice\":[\"java.math.BigDecimal\",480],\"totalPromoPriceTag\":\"-¥480\",\"promoItemList\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM\",\"promoId\":1035747853,\"promoTypeCode\":11,\"promoType\":\"团购优惠\",\"desc\":\"\",\"promoTag\":\"-¥480\",\"promoPrice\":[\"java.math.BigDecimal\",480],\"canAssign\":false,\"sourceType\":1,\"promoIdentity\":null,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"endTime\":0,\"remainStock\":0,\"effectiveEndTime\":0,\"couponGroupId\":null,\"couponId\":null,\"amount\":[\"java.math.BigDecimal\",480],\"minConsumptionAmount\":null,\"promotionExplanatoryTags\":null,\"promotionOtherInfoMap\":null,\"promoItemText\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemTextM\",\"atmosphereBarIcon\":null,\"atmosphereBarText\":null,\"atmosphereBarButtonText\":null,\"atmosphereBarButtonUrl\":null,\"promoStatusText\":null,\"title\":\"团购优惠\",\"subTitle\":\"\",\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"promoDivideType\":\"DEAL_PROMO\",\"promoDivideTypeDesc\":null},\"newUser\":false}]],\"coupons\":null,\"startTime\":0,\"endTime\":0,\"promoQuantityLimit\":0,\"icon\":\"\",\"iconText\":null,\"promoTagType\":10,\"singlePrice\":[\"java.math.BigDecimal\",140],\"pricePromoInfoMap\":{\"@class\":\"java.util.HashMap\"},\"extendDisplayInfo\":{\"@class\":\"java.util.HashMap\"}}]],\"bestPromoPrice\":null,\"pinPrice\":null,\"cardPrice\":null,\"purchase\":null,\"couponM\":null,\"coupons\":null,\"activities\":[\"java.util.ArrayList\",[]],\"shopIds\":null,\"shopLongIds\":null,\"shopNum\":0,\"brandName\":null,\"productTags\":null,\"extAttrs\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStructContent\",\"value\":\"{\\\"dealId\\\":1035747853,\\\"stract\\\":\\\"{\\\\\\\"dealGroupId\\\\\\\":1035747853,\\\\\\\"title\\\\\\\":\\\\\\\"\\\\团\\\\购\\\\详\\\\情\\\\\\\",\\\\\\\"skuUniStructuredDto\\\\\\\":{\\\\\\\"salePrice\\\\\\\":\\\\\\\"1400.0\\\\\\\",\\\\\\\"marketPrice\\\\\\\":\\\\\\\"188.0\\\\\\\",\\\\\\\"mustGroups\\\\\\\":[{\\\\\\\"skuItems\\\\\\\":[{\\\\\\\"skuId\\\\\\\":0,\\\\\\\"productCategory\\\\\\\":2104542,\\\\\\\"name\\\\\\\":\\\\\\\"\\\\足\\\\疗\\\\\\\",\\\\\\\"copies\\\\\\\":1,\\\\\\\"marketPrice\\\\\\\":null,\\\\\\\"status\\\\\\\":10,\\\\\\\"addTime\\\\\\\":null,\\\\\\\"updateTime\\\\\\\":null,\\\\\\\"attrItems\\\\\\\":[{\\\\\\\"metaAttrId\\\\\\\":3022,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceProcessArrayNew\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\流\\\\程\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\泡\\\\脚\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"10\\\\\\\\\\\\\\\"},{\\\\\\\\\\\\\\\"servicemethod\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\足\\\\部\\\\按\\\\摩\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"stepTime\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"50\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":300,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2441,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceDurationInt\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\时\\\\长\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"60\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":401,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3015,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceMaterialAndTool\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\选\\\\择\\\\服\\\\务\\\\工\\\\具/\\\\材\\\\料\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3018,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathBucket\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\桶\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\木\\\\桶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3019,\\\\\\\"attrName\\\\\\\":\\\\\\\"footbathMaterial\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\泡\\\\脚\\\\包\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\草\\\\本\\\\包\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3030,\\\\\\\"attrName\\\\\\\":\\\\\\\"freeFood\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\免\\\\费\\\\餐\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\茶\\\\点\\\\水\\\\果\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":3109,\\\\\\\"attrName\\\\\\\":\\\\\\\"serviceBodyRange\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\具\\\\体\\\\服\\\\务\\\\部\\\\位\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\足\\\\部\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404171,\\\\\\\"attrName\\\\\\\":\\\\\\\"Fruit\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\水\\\\果\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404173,\\\\\\\"attrName\\\\\\\":\\\\\\\"TeaWater\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\茶\\\\水\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\养\\\\生\\\\茶\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":404176,\\\\\\\"attrName\\\\\\\":\\\\\\\"Snack\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"\\\\零\\\\食\\\\拼\\\\盘\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":500,\\\\\\\"sequence\\\\\\\":0},{\\\\\\\"metaAttrId\\\\\\\":2730,\\\\\\\"attrName\\\\\\\":\\\\\\\"skuCateId\\\\\\\",\\\\\\\"chnName\\\\\\\":\\\\\\\"\\\\项\\\\目\\\\分\\\\类\\\\\\\",\\\\\\\"attrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"rawAttrValue\\\\\\\":\\\\\\\"2104542\\\\\\\",\\\\\\\"unit\\\\\\\":null,\\\\\\\"valueType\\\\\\\":402,\\\\\\\"sequence\\\\\\\":0}]}]}],\\\\\\\"optionalGroups\\\\\\\":[]},\\\\\\\"structType\\\\\\\":\\\\\\\"uniform-structure-table\\\\\\\"}\\\",\\\"structOrigin\\\":{\\\"dealGroupId\\\":1035747853,\\\"title\\\":\\\"团购详情\\\",\\\"skuUniStructuredDto\\\":{\\\"salePrice\\\":\\\"1400.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skuItems\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"status\\\":10,\\\"addTime\\\":null,\\\"updateTime\\\":null,\\\"attrItems\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"rawAttrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\",\\\"unit\\\":null,\\\"valueType\\\":300,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\",\\\"rawAttrValue\\\":\\\"60\\\",\\\"unit\\\":null,\\\"valueType\\\":401,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\",\\\"rawAttrValue\\\":\\\"选择服务工具/材料\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\",\\\"rawAttrValue\\\":\\\"木桶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\",\\\"rawAttrValue\\\":\\\"草本包\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\",\\\"rawAttrValue\\\":\\\"茶点水果\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\",\\\"rawAttrValue\\\":\\\"足部\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\",\\\"rawAttrValue\\\":\\\"水果拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\",\\\"rawAttrValue\\\":\\\"养生茶\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\",\\\"rawAttrValue\\\":\\\"零食拼盘\\\",\\\"unit\\\":null,\\\"valueType\\\":500,\\\"sequence\\\":0},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\",\\\"rawAttrValue\\\":\\\"2104542\\\",\\\"unit\\\":null,\\\"valueType\\\":402,\\\"sequence\\\":0}]}]}],\\\"optionalGroups\\\":[]},\\\"structType\\\":\\\"uniform-structure-table\\\"},\\\"productCategories\\\":[{\\\"productCategoryId\\\":2104542,\\\"cnName\\\":\\\"足疗\\\"}],\\\"contentDetailList\\\":[],\\\"dealDetailStructuredData\\\":{\\\"dealGroupId\\\":1035747853,\\\"title\\\":\\\"团购详情\\\",\\\"dealDetailSkuUniStructuredModel\\\":{\\\"salePrice\\\":\\\"1400.0\\\",\\\"marketPrice\\\":\\\"188.0\\\",\\\"mustGroups\\\":[{\\\"skus\\\":[{\\\"skuId\\\":0,\\\"productCategory\\\":2104542,\\\"name\\\":\\\"足疗\\\",\\\"copies\\\":1,\\\"marketPrice\\\":null,\\\"skuAttrs\\\":[{\\\"metaAttrId\\\":3022,\\\"attrName\\\":\\\"serviceProcessArrayNew\\\",\\\"chnName\\\":\\\"服务流程\\\",\\\"attrValue\\\":\\\"[{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"泡脚\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"10\\\\\\\"},{\\\\\\\"servicemethod\\\\\\\":\\\\\\\"足部按摩\\\\\\\",\\\\\\\"stepTime\\\\\\\":\\\\\\\"50\\\\\\\"}]\\\"},{\\\"metaAttrId\\\":2441,\\\"attrName\\\":\\\"serviceDurationInt\\\",\\\"chnName\\\":\\\"服务时长\\\",\\\"attrValue\\\":\\\"60\\\"},{\\\"metaAttrId\\\":3015,\\\"attrName\\\":\\\"serviceMaterialAndTool\\\",\\\"chnName\\\":\\\"服务工具/材料\\\",\\\"attrValue\\\":\\\"选择服务工具/材料\\\"},{\\\"metaAttrId\\\":3018,\\\"attrName\\\":\\\"footbathBucket\\\",\\\"chnName\\\":\\\"泡脚桶\\\",\\\"attrValue\\\":\\\"木桶\\\"},{\\\"metaAttrId\\\":3019,\\\"attrName\\\":\\\"footbathMaterial\\\",\\\"chnName\\\":\\\"泡脚包\\\",\\\"attrValue\\\":\\\"草本包\\\"},{\\\"metaAttrId\\\":3030,\\\"attrName\\\":\\\"freeFood\\\",\\\"chnName\\\":\\\"免费餐食\\\",\\\"attrValue\\\":\\\"茶点水果\\\"},{\\\"metaAttrId\\\":3109,\\\"attrName\\\":\\\"serviceBodyRange\\\",\\\"chnName\\\":\\\"具体服务部位\\\",\\\"attrValue\\\":\\\"足部\\\"},{\\\"metaAttrId\\\":404171,\\\"attrName\\\":\\\"Fruit\\\",\\\"chnName\\\":\\\"水果\\\",\\\"attrValue\\\":\\\"水果拼盘\\\"},{\\\"metaAttrId\\\":404173,\\\"attrName\\\":\\\"TeaWater\\\",\\\"chnName\\\":\\\"茶水\\\",\\\"attrValue\\\":\\\"养生茶\\\"},{\\\"metaAttrId\\\":404176,\\\"attrName\\\":\\\"Snack\\\",\\\"chnName\\\":\\\"零食\\\",\\\"attrValue\\\":\\\"零食拼盘\\\"},{\\\"metaAttrId\\\":2730,\\\"attrName\\\":\\\"skuCateId\\\",\\\"chnName\\\":\\\"项目分类\\\",\\\"attrValue\\\":\\\"2104542\\\"}]}]}],\\\"optionalGroups\\\":null}},\\\"dealResourceInfoList\\\":null}\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"attr_search_hidden_status\",\"value\":\"false\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"dealStatusAttr\",\"value\":\"true\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"productType\",\"value\":\"deal\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"service_type\",\"value\":\"足疗\"},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.AttrM\",\"name\":\"sys_multi_sale_number\",\"value\":\"10\"}]],\"extObjAttrs\":null,\"userSubscribe\":false,\"review\":null,\"resourceRank\":{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.ResourceRankM\",\"rankName\":\"\",\"rankIcon\":null,\"rankType\":null,\"rankLinkUrl\":\"\"},\"userReview\":null,\"compositeScore\":0,\"applyShopsDesc\":null,\"nearestShopDesc\":null,\"nearestShopDistance\":0,\"shopMs\":[\"java.util.ArrayList\",[]],\"productTagList\":[\"java.util.ArrayList\",[]],\"beginDate\":0,\"endDate\":0,\"orderUsers\":[\"java.util.ArrayList\",[]],\"extendImages\":[\"java.util.ArrayList\",[]],\"tradeType\":19,\"useRuleM\":null,\"salePrice\":[\"java.math.BigDecimal\",1400],\"timesDealQueryFlag\":true,\"sptSpuId\":null,\"skuIdList\":null,\"itemUnitList\":null,\"materialList\":[\"java.util.Collections$EmptyList\",[]],\"additionalProjectList\":[\"java.util.ArrayList\",[]],\"childProducts\":null,\"prePadded\":true,\"expressOptimize\":false,\"promoTagType\":2,\"actProductId\":1035747853,\"actProductType\":0,\"additionalDeal\":false,\"timesDeal\":true,\"dealSpuId\":0,\"top\":false,\"unifyProduct\":false}";

    private static final String douhuDataJson = "[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM\",\"abtest\":\"{}\",\"code\":\"-9\",\"msg\":null,\"expId\":\"exp003218\",\"sk\":null,\"abQueryId\":\"43b3bff8-39ac-4352-ad57-1337d9f94c7e\",\"bucket\":null,\"others\":{\"@class\":\"java.util.HashMap\"}},{\"@class\":\"com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM\",\"abtest\":\"{\\\"query_id\\\":\\\"9ac44df3-caea-413b-869c-e1800e1f0992\\\",\\\"ab_id\\\":\\\"EXP2024122400001_c\\\"}\",\"code\":\"200\",\"msg\":null,\"expId\":\"EXP2024122400001\",\"sk\":\"EXP2024122400001_c\",\"abQueryId\":\"9ac44df3-caea-413b-869c-e1800e1f0992\",\"bucket\":\"-2\",\"others\":{\"@class\":\"java.util.HashMap\",\"{\\\"@class\\\":\\\"java.lang.String\\\",\\\"value\\\":\\\"expVersion\\\"}\":\"v3\"}}]]";

}