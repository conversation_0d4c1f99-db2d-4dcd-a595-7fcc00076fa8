package com.sankuai.dzviewscene.product.shelf.ability.builder.shelfshowtype;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ShelfShowTypeVPTest {

    @Test
    public void testGetShowTypeByExpDefaultEmptyWhenDouHuSk2ShowTypeIsNull() throws Throwable {
        int result = ShelfShowTypeVP.getShowTypeByExpDefaultEmpty(null, new ArrayList<>());
        assertEquals(0, result);
    }

    @Test
    public void testGetShowTypeByExpDefaultEmptyWhenDouHuMListIsNull() throws Throwable {
        Map<String, Integer> douHuSk2ShowType = new HashMap<>();
        douHuSk2ShowType.put("sk", 1);
        int result = ShelfShowTypeVP.getShowTypeByExpDefaultEmpty(douHuSk2ShowType, null);
        assertEquals(0, result);
    }

    @Test
    public void testGetShowTypeByExpDefaultEmptyWhenDouHuMSkListIsEmpty() throws Throwable {
        Map<String, Integer> douHuSk2ShowType = new HashMap<>();
        douHuSk2ShowType.put("sk", 1);
        List<DouHuM> douHuMList = new ArrayList<>();
        int result = ShelfShowTypeVP.getShowTypeByExpDefaultEmpty(douHuSk2ShowType, douHuMList);
        assertEquals(0, result);
    }

    @Test
    public void testGetShowTypeByExpDefaultEmptyWhenDouHuMSkListIsNotEmptyButDouHuSk2ShowTypeDoesNotContainSk() throws Throwable {
        Map<String, Integer> douHuSk2ShowType = new HashMap<>();
        douHuSk2ShowType.put("sk", 1);
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("otherSk");
        douHuMList.add(douHuM);
        int result = ShelfShowTypeVP.getShowTypeByExpDefaultEmpty(douHuSk2ShowType, douHuMList);
        assertEquals(0, result);
    }

    @Test
    public void testGetShowTypeByExpDefaultEmptyWhenDouHuMSkListIsNotEmptyAndDouHuSk2ShowTypeContainsSkButShowTypeIsNotPositive() throws Throwable {
        Map<String, Integer> douHuSk2ShowType = new HashMap<>();
        douHuSk2ShowType.put("sk", 0);
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk");
        douHuMList.add(douHuM);
        int result = ShelfShowTypeVP.getShowTypeByExpDefaultEmpty(douHuSk2ShowType, douHuMList);
        assertEquals(0, result);
    }

    @Test
    public void testGetShowTypeByExpDefaultEmptyWhenDouHuMSkListIsNotEmptyAndDouHuSk2ShowTypeContainsSkAndShowTypeIsPositive() throws Throwable {
        Map<String, Integer> douHuSk2ShowType = new HashMap<>();
        douHuSk2ShowType.put("sk", 1);
        List<DouHuM> douHuMList = new ArrayList<>();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk");
        douHuMList.add(douHuM);
        int result = ShelfShowTypeVP.getShowTypeByExpDefaultEmpty(douHuSk2ShowType, douHuMList);
        assertEquals(1, result);
    }
}
