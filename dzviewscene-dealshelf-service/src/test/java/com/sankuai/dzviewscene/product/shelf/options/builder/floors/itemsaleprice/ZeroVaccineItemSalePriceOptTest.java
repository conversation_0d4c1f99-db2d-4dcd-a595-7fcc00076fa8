package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsaleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.mockito.Mockito;
import java.util.Collections;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.shelf.platform.shelf.ability.builder.floors.FloorsBuilderExtAdapter;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ZeroVaccineItemSalePriceOptTest {

    private ZeroVaccineItemSalePriceOpt zeroVaccineItemSalePriceOpt;

    private ActivityCxt context;

    private ZeroVaccineItemSalePriceOpt.Param param;

    // Corrected testCompute_ProductMIsNull to expect NullPointerException
    @Test(expected = NullPointerException.class)
    public void testCompute_ProductMIsNull() throws Throwable {
        ZeroVaccineItemSalePriceOpt zeroVaccineItemSalePriceOpt = new ZeroVaccineItemSalePriceOpt();
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        ZeroVaccineItemSalePriceOpt.Param param = Mockito.mock(ZeroVaccineItemSalePriceOpt.Param.class);
        when(param.getProductM()).thenReturn(null);
        zeroVaccineItemSalePriceOpt.compute(context, param, null);
    }

    // Corrected testCompute_RetailPriceStyleIsNull to properly mock and assert
    @Test
    public void testCompute_RetailPriceStyleIsNull() throws Throwable {
        ZeroVaccineItemSalePriceOpt zeroVaccineItemSalePriceOpt = new ZeroVaccineItemSalePriceOpt();
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        ZeroVaccineItemSalePriceOpt.Param param = Mockito.mock(ZeroVaccineItemSalePriceOpt.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        // Assuming empty list means null style
        when(productM.getExtAttrs()).thenReturn(Collections.emptyList());
        // Assuming default behavior
        when(productM.getBasePriceTag()).thenReturn("100");
        String result = zeroVaccineItemSalePriceOpt.compute(context, param, null);
        assertEquals("100", result);
    }

    // Corrected testCompute_RetailPriceStyleIs1
    @Test
    public void testCompute_RetailPriceStyleIs1() throws Throwable {
        ZeroVaccineItemSalePriceOpt zeroVaccineItemSalePriceOpt = new ZeroVaccineItemSalePriceOpt();
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        ZeroVaccineItemSalePriceOpt.Param param = Mockito.mock(ZeroVaccineItemSalePriceOpt.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(new AttrM("retailPriceStyle", "1")));
        when(productM.getBasePriceTag()).thenReturn("100");
        String result = zeroVaccineItemSalePriceOpt.compute(context, param, null);
        assertEquals("100", result);
    }

    // Corrected testCompute_RetailPriceStyleIs3
    @Test
    public void testCompute_RetailPriceStyleIs3() throws Throwable {
        ZeroVaccineItemSalePriceOpt zeroVaccineItemSalePriceOpt = new ZeroVaccineItemSalePriceOpt();
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        ZeroVaccineItemSalePriceOpt.Param param = Mockito.mock(ZeroVaccineItemSalePriceOpt.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(new AttrM("retailPriceStyle", "3")));
        when(productM.getBasePriceTag()).thenReturn("100");
        String result = zeroVaccineItemSalePriceOpt.compute(context, param, null);
        assertEquals("100", result);
    }

    // Corrected testCompute_RetailPriceStyleIsOther
    @Test
    public void testCompute_RetailPriceStyleIsOther() throws Throwable {
        ZeroVaccineItemSalePriceOpt zeroVaccineItemSalePriceOpt = new ZeroVaccineItemSalePriceOpt();
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        ZeroVaccineItemSalePriceOpt.Param param = Mockito.mock(ZeroVaccineItemSalePriceOpt.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        // Assuming "2" is an "other" style
        when(productM.getExtAttrs()).thenReturn(Collections.singletonList(new AttrM("retailPriceStyle", "2")));
        // Assuming EMPTY_VALUE is the correct return for "other" styles
        when(productM.getBasePriceTag()).thenReturn(FloorsBuilderExtAdapter.EMPTY_VALUE);
        String result = zeroVaccineItemSalePriceOpt.compute(context, param, null);
        assertEquals(FloorsBuilderExtAdapter.EMPTY_VALUE, result);
    }
}
