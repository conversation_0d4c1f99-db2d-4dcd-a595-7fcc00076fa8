package com.sankuai.dzviewscene.product.dealstruct.ability.desc;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDescModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dzviewscene.product.dealstruct.ability.desc.vpoints.DealDetailDescVP;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailDescBuilderTest {

    @Mock
    private ActivityCxt activityCxt;

    @InjectMocks
    private DealDetailDescBuilder dealDetailDescBuilder;

    private void commonSetup() {
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(new DealDetailInfoModel()));
        // Removed the incorrect mocking of findVPoint method
    }

    @Test
    public void testBuildWhenDealDetailInfoModelListIsEmpty() throws Throwable {
        commonSetup();
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        CompletableFuture<List<DealDetailDescModel>> result = dealDetailDescBuilder.build(activityCxt, new DealDetailDescParam(), new DealDetailDescCfg());
        assertTrue(result.get().isEmpty());
    }
}
