package com.sankuai.dzviewscene.product.utils;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PromoItemM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;
import org.testng.collections.Lists;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MerchantMemberPromoUtilsTest {

    @Test
    public void getMerchantMemberPromoTest(){
        ProductM productM = buildProduct();
        MerchantMemberProductPromoData merchantMemberPromoData = MerchantMemberPromoUtils.getMerchantMemberPromo(productM);
        Assert.assertTrue(merchantMemberPromoData.getMemberDiscountType().equals(2));
        Assert.assertEquals(merchantMemberPromoData.getProductPromoPrice().getPromoType(),PromoTypeEnum.MERCHANT_MEMBER.getType());

    }

    private ProductM buildProduct(){
        ProductM productM = new ProductM();
       List<ProductPromoPriceM> promoPrices = Lists.newArrayList();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoType(PromoTypeEnum.MERCHANT_MEMBER.getType());
        promoPrice1.setPromoTagType(PromoTagTypeEnum.Merchant_Member.getCode());
        promoPrice1.setPromoPrice(new BigDecimal(66));
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoTypeCode(PromoTypeEnum.MERCHANT_MEMBER.getType());
        promoPrice1.setPromoItemList(Lists.newArrayList(promoItemM));
        promoPrices.add(promoPrice1);

        ProductPromoPriceM promoPrice2 = new ProductPromoPriceM();
        promoPrice2.setPromoType(PromoTypeEnum.NORMAL_PROMO.getType());
        promoPrice2.setPromoTagType(PromoTagTypeEnum.Default.getCode());
        promoPrices.add(promoPrice2);
        promoPrice2.setPromoPrice(new BigDecimal(100));
        productM.setPromoPrices(promoPrices);

        productM.setAttr("MERCHANT_MEMBER_DEAL","{\"memberDiscountType\":2,\"merchantMember\":{\"isMember\":true,\"isNewMember\":false}}");

        return productM;
    }


    @Test
    public void testBuildMemberExclusiveTag_DpPrePic() throws Throwable {
        ProductM productM = mock(ProductM.class);
        ProductPromoPriceM promoPriceM = mock(ProductPromoPriceM.class);
        when(productM.getMarketPrice()).thenReturn("166");
        int platform = 1;
        String salePrice = "100";
        int popType = 1;
        when(DzPromoUtils.buildPromoDetail(promoPriceM,popType)).thenReturn(null);
        DzTagVO result = ProductMPromoInfoUtils.buildMemberExclusiveTag(productM, platform, salePrice, promoPriceM, popType);
        Assert.assertNotNull(result);
        DzPictureComponentVO prePic = result.getPrePic();
        Assert.assertNotNull(prePic);
        Assert.assertEquals("https://p0.meituan.net/travelcube/df6c671e8baecc8d1b2ae41df518ff827159.png",prePic.getPicUrl());

    }

    @Test
    public void testBuildMemberExclusiveTag_MtPrePic() throws Throwable {
        ProductM productM = mock(ProductM.class);
        ProductPromoPriceM promoPriceM = mock(ProductPromoPriceM.class);
        when(productM.getMarketPrice()).thenReturn("166");
        String salePrice = "100";
        int popType = 1;
        when(DzPromoUtils.buildPromoDetail(promoPriceM,popType)).thenReturn(null);

        int platform1 = 2;
        DzTagVO result2 = ProductMPromoInfoUtils.buildMemberExclusiveTag(productM, platform1, salePrice, promoPriceM, popType);
        Assert.assertNotNull(result2);
        DzPictureComponentVO prePic1 = result2.getPrePic();
        Assert.assertNotNull(prePic1);
        Assert.assertEquals("https://p0.meituan.net/travelcube/dab2626832fe4afdc3da732eab1b66398113.png",prePic1.getPicUrl());
    }


}
