package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import static org.mockito.Mockito.when;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.BeautyShelItemProductRichTagsOpt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.BeautyShelItemProductRichTagsOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.dzviewscene.product.enums.ProductTypeEnum;
import static org.junit.Assert.*;
import org.junit.*;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductRichTagsVP;

@RunWith(MockitoJUnitRunner.class)
public class BeautyShelItemProductRichTagsOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ItemProductRichTagsVP.Param param;

    @Mock
    private Config config;

    @Mock
    private ProductM productM;

    @Test
    public void testComputeNormalCase() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeIsTimesCard() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductType()).thenReturn(ProductTypeEnum.TIME_CARD.getType());
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeIsPreSaleDeal() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeIsHotSpu() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeNoProductTags() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(null);
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeHasProductTags() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        when(productM.getProductTags()).thenReturn(java.util.Arrays.asList("tag1", "tag2"));
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeHasRepurchaseTag() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }

    @Test
    public void testComputeHasMeiJiaKuanShiRenXuanTag() throws Throwable {
        BeautyShelItemProductRichTagsOpt opt = new BeautyShelItemProductRichTagsOpt();
        when(param.getProductM()).thenReturn(productM);
        List<RichLabelVO> result = opt.compute(context, param, config);
        assertNotNull(result);
    }
}
