package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP;
import com.sankuai.dzviewscene.product.constants.PmfConstants;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Map;

import static com.sankuai.it.iam.common_base.exception.BizAssert.assertEquals;
import static com.sankuai.it.iam.common_base.exception.BizAssert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class LocationFallbackOptTest {
    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private LocationFallbackOpt locationFallbackOpt;

    private ActivityCxt activityCxt;
    private PreSyncHandlerVP.Param param;
    private LocationFallbackOpt.Config config;

    @Test
    public void testAllLocate() {
        activityCxt = new ActivityCxt();
        activityCxt.addParam(PmfConstants.Params.lat, 0.1);
        activityCxt.addParam(PmfConstants.Params.lng, 0.1);
        activityCxt.addParam(PmfConstants.Params.dpCityId, 1);
        param = PreSyncHandlerVP.Param.builder()
                .build();
        config = new LocationFallbackOpt.Config();

        Map<String, Object> result = locationFallbackOpt.compute(activityCxt, param, config);

        assertTrue(result.isEmpty());
    }
    @Test
    public void testLngLocate() {
        activityCxt = new ActivityCxt();
        activityCxt.addParam(PmfConstants.Params.lat, 0.0);
        activityCxt.addParam(PmfConstants.Params.lng, 0.1);
        activityCxt.addParam(PmfConstants.Params.dpCityId, 0);
        param = PreSyncHandlerVP.Param.builder()
                .build();
        config = new LocationFallbackOpt.Config();

        Map<String, Object> result = locationFallbackOpt.compute(activityCxt, param, config);

        assertTrue(result.isEmpty());
    }
    @Test
    public void testLatLocate() {
        activityCxt = new ActivityCxt();
        activityCxt.addParam(PmfConstants.Params.lat, 0.1);
        activityCxt.addParam(PmfConstants.Params.lng, 0.0);
        activityCxt.addParam(PmfConstants.Params.dpCityId, 0);
        param = PreSyncHandlerVP.Param.builder()
                .build();
        config = new LocationFallbackOpt.Config();

        Map<String, Object> result = locationFallbackOpt.compute(activityCxt, param, config);

        assertTrue(result.isEmpty());
    }
    @Test
    public void testCityLocate() {
        activityCxt = new ActivityCxt();
        activityCxt.addParam(PmfConstants.Params.lat, 0.0);
        activityCxt.addParam(PmfConstants.Params.lng, 0.0);
        activityCxt.addParam(PmfConstants.Params.dpCityId, 1);
        activityCxt.addParam(PmfConstants.Params.shopDpCityId, 1);
        activityCxt.addParam(PmfConstants.Params.shopMtCityId, 2);
        ShopM shopM = new ShopM();
        shopM.setCityId(3);
        shopM.setLat(4.1);
        shopM.setLng(5.1);
        activityCxt.addParam(ShelfActivityConstants.Ctx.ctxShop, shopM);
        param = PreSyncHandlerVP.Param.builder()
                .build();
        config = new LocationFallbackOpt.Config();

        Map<String, Object> result = locationFallbackOpt.compute(activityCxt, param, config);

        assertEquals(result.get(PmfConstants.Params.dpCityId), 1);
        assertEquals(result.get(PmfConstants.Params.mtCityId), 2);
        assertEquals(result.get(PmfConstants.Params.lat), 4.1);
        assertEquals(result.get(PmfConstants.Params.lng), 5.1);
    }
    @Test
    public void testNoShop() {
        activityCxt = new ActivityCxt();
        activityCxt.addParam(PmfConstants.Params.lat, 0.0);
        activityCxt.addParam(PmfConstants.Params.lng, 0.0);
        activityCxt.addParam(PmfConstants.Params.dpCityId, 0);
        param = PreSyncHandlerVP.Param.builder()
                .build();
        config = new LocationFallbackOpt.Config();

        Map<String, Object> result = locationFallbackOpt.compute(activityCxt, param, config);

        assertTrue(result.isEmpty());
    }
    @Test
    public void testFallback() {
        activityCxt = new ActivityCxt();
        activityCxt.addParam(PmfConstants.Params.lat, 0.0);
        activityCxt.addParam(PmfConstants.Params.lng, 0.0);
        activityCxt.addParam(PmfConstants.Params.dpCityId, 0);
        activityCxt.addParam(PmfConstants.Params.shopDpCityId, 1);
        activityCxt.addParam(PmfConstants.Params.shopMtCityId, 2);
        ShopM shopM = new ShopM();
        shopM.setCityId(3);
        shopM.setLat(4.1);
        shopM.setLng(5.1);
        activityCxt.addParam(ShelfActivityConstants.Ctx.ctxShop, shopM);
        param = PreSyncHandlerVP.Param.builder()
                .build();
        config = new LocationFallbackOpt.Config();

        Map<String, Object> result = locationFallbackOpt.compute(activityCxt, param, config);

        assertEquals(result.get(PmfConstants.Params.dpCityId), 1);
        assertEquals(result.get(PmfConstants.Params.mtCityId), 2);
        assertEquals(result.get(PmfConstants.Params.lat), 4.1);
        assertEquals(result.get(PmfConstants.Params.lng), 5.1);
    }
}
