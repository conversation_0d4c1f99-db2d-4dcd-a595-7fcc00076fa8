package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuSetModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuUniModel;
import com.sankuai.dzviewscene.product.dealstruct.model.SkuItemModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuGroup.DealDetailSkuGroupsBuilder;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailSkuUniModelList2DealSkuGroupModuleVOListStrategyTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assambleParam;

    private DealDetailSkuUniModelList2DealSkuGroupModuleVOListStrategy strategy;

    public DealDetailSkuUniModelList2DealSkuGroupModuleVOListStrategyTest() {
        strategy = new DealDetailSkuUniModelList2DealSkuGroupModuleVOListStrategy();
    }

    @Test
    public void testBuildModelVO_NoDealDetailSkuUniModelList() throws Throwable {
        when(activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE)).thenReturn(null);
        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull("Expected null when no DealDetailSkuUniModelList is provided", result.getSkuGroupsModel1());
    }

    @Test
    public void testBuildModelVO_EmptyDealDetailSkuUniModelList() throws Throwable {
        when(activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE)).thenReturn(new ArrayList<>());
        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull("Expected null when an empty DealDetailSkuUniModelList is provided", result.getSkuGroupsModel1());
    }

    @Test
    public void testBuildModelVO_DealDetailSkuUniModelWithEmptyMustGroupsAndOptionGroups() throws Throwable {
        DealDetailSkuUniModel dealDetailSkuUniModel = new DealDetailSkuUniModel();
        when(activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE)).thenReturn(Arrays.asList(dealDetailSkuUniModel));
        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNull("Expected null when DealDetailSkuUniModel has empty must and option groups", result.getSkuGroupsModel1());
    }

    @Test
    public void testBuildModelVO_DealDetailSkuUniModelWithNonEmptyMustGroupsAndOptionGroupsButEmptyDealDetailSkuSetModelOrSkuItems() throws Throwable {
        DealDetailSkuUniModel dealDetailSkuUniModel = new DealDetailSkuUniModel();
        dealDetailSkuUniModel.setMustGroups(Arrays.asList(new DealDetailSkuGroupModel()));
        dealDetailSkuUniModel.setOptionGroups(Arrays.asList(new DealDetailSkuGroupModel()));
        when(activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE)).thenReturn(Arrays.asList(dealDetailSkuUniModel));
        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assambleParam, "config");
        assertTrue("Expected empty list when must and option groups are non-empty but DealDetailSkuSetModel or SkuItems are empty", result.getSkuGroupsModel1().isEmpty());
    }

    @Test
    public void testBuildModelVO_DealDetailSkuUniModelWithNonEmptyMustGroupsAndOptionGroupsAndNonEmptyDealDetailSkuSetModelAndSkuItems() throws Throwable {
        DealDetailSkuUniModel dealDetailSkuUniModel = new DealDetailSkuUniModel();
        DealDetailSkuGroupModel dealDetailSkuGroupModel = new DealDetailSkuGroupModel();
        DealDetailSkuSetModel dealDetailSkuSetModel = new DealDetailSkuSetModel();
        dealDetailSkuSetModel.setSkuItems(Arrays.asList(new SkuItemModel()));
        dealDetailSkuGroupModel.setSkuSetModels(Arrays.asList(dealDetailSkuSetModel));
        dealDetailSkuUniModel.setMustGroups(Arrays.asList(dealDetailSkuGroupModel));
        dealDetailSkuUniModel.setOptionGroups(Arrays.asList(dealDetailSkuGroupModel));
        when(activityCxt.getSource(DealDetailSkuGroupsBuilder.CODE)).thenReturn(Arrays.asList(dealDetailSkuUniModel));
        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assambleParam, "config");
        assertNotNull("Expected non-null when must and option groups are non-empty and have non-empty DealDetailSkuSetModel and SkuItems", result);
        assertNotNull("Expected non-null skuGroupsModel1", result.getSkuGroupsModel1());
        assertTrue("Expected non-empty skuGroupsModel1", !result.getSkuGroupsModel1().isEmpty());
    }
}
