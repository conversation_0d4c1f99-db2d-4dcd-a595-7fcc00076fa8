package com.sankuai.dzviewscene.product.filterlist.option.builder.product.saleprice;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductSalePriceVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.utils.CardPromoUtils;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.MerchantMemberProductPromoData;
import com.sankuai.dzviewscene.product.utils.MerchantMemberPromoUtils;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SimilarShelfProductMerchantMemberSalePriceOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private ProductM productM;

    @Mock
    private CardM cardM;

    @Mock
    private ProductPromoPriceM cardPromo;

    @Mock
    private ProductPromoPriceM perfectActivityPrice;

    @Mock
    private ProductPromoPriceM merchantMemberPromoPriceM;

    @Mock
    private ProductPromoPriceM productPromoPriceM;

    private SimilarShelfProductMerchantMemberSalePriceOpt similarShelfProductMerchantMemberSalePriceOpt;

    @Before
    public void setUp() {
        similarShelfProductMerchantMemberSalePriceOpt = new SimilarShelfProductMerchantMemberSalePriceOpt();
    }

    @Test
    public void testComputeProductPromoPriceNotNull() throws Throwable {
        List<ProductPromoPriceM> promos = new ArrayList<>();
        promos.add(cardPromo);
        when(activityCxt.getSource(anyString())).thenReturn(cardM);
        when(productM.getPromoPrices()).thenReturn(promos);
        when(productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType())).thenReturn(productPromoPriceM);
        when(productPromoPriceM.getPromoPriceTag()).thenReturn("70");
        String result = similarShelfProductMerchantMemberSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals("70", result);
    }

    @Test
    public void testComputeGroupPrice() throws Throwable {
        List<ProductPromoPriceM> promos = new ArrayList<>();
        promos.add(cardPromo);
        when(activityCxt.getSource(anyString())).thenReturn(cardM);
        when(productM.getPromoPrices()).thenReturn(promos);
        when(productM.getBasePriceTag()).thenReturn("50");
        String result = similarShelfProductMerchantMemberSalePriceOpt.compute(activityCxt, ProductSalePriceVP.Param.builder().productM(productM).build(), null);
        assertEquals("50", result);
    }
}
