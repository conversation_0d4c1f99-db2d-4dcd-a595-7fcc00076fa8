package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.shelf.business.detail.medical.model.ProductSkuCategoryModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Constructor;
import java.util.Collections;
import java.util.List;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicalBeautyAbroadAttrVOListOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private MedicalBeautyAbroadAttrVOListOpt.Config // Mocking the Config inner class
    config;

    @Test
    public void testComputeParamIsNull() throws Throwable {
        // arrange
        MedicalBeautyAbroadAttrVOListOpt opt = new MedicalBeautyAbroadAttrVOListOpt();
        // act
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, null, null);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeParamIsNotNull() throws Throwable {
        // arrange
        MedicalBeautyAbroadAttrVOListOpt opt = new MedicalBeautyAbroadAttrVOListOpt();
        when(activityCxt.getParam("serviceType")).thenReturn("test");
        // Setting up mock behavior
        when(config.getConfigServiceTypeList()).thenReturn(Collections.singletonList("test"));
        List<AttrM> dealAttrs = Collections.emptyList();
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        List<ProductSkuCategoryModel> productCategories = Collections.emptyList();
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        // Using reflection to create an instance of DealAttrVOListVP.Param
        Class<?> paramClass = Class.forName("com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP$Param");
        Constructor<?> constructor = paramClass.getDeclaredConstructors()[0];
        constructor.setAccessible(true);
        DealAttrVOListVP.Param param = DealAttrVOListVP.Param.builder()
                .dealAttrs(dealAttrs)
                .dealDetailDtoModel(dealDetailDtoModel)
                .productCategories(productCategories)
                .dealDetailInfoModel(dealDetailInfoModel).build();
        // act
        // Pass the mocked config
        List<DealDetailStructAttrModuleGroupModel> result = opt.compute(activityCxt, param, config);
        // assert
        assertNotNull(result);
    }
}
