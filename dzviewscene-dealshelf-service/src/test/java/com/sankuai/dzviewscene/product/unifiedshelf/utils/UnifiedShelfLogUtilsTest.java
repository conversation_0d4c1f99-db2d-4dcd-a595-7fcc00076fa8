package com.sankuai.dzviewscene.product.unifiedshelf.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.FileUtil;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfFilterProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfItemVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfProductAreaVO;
import com.sankuai.dzviewscene.dealshelf.shelfvo.UnifiedShelfResponse;
import com.sankuai.dzviewscene.product.shelf.utils.UnifiedShelfLogUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.List;

public class UnifiedShelfLogUtilsTest {

    private MockedStatic<Lion> lionMockedStatic;

    @Before
    public void setUp() {
        lionMockedStatic = Mockito.mockStatic(Lion.class);
    }

    @After
    public void tearDown() throws Exception {
        lionMockedStatic.close();
    }


    @Test
    public void test_cleanDegradeData() {

        lionMockedStatic.when(() -> Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.empty.key.log.flow", 0)).thenReturn(1);

        String string = FileUtil.file2str("unified_shelf.json");
        UnifiedShelfResponse response = JSON.parseObject(string, UnifiedShelfResponse.class);
        List<ShelfFilterProductAreaVO> filterIdAndProductAreas = response.getFilterIdAndProductAreas();
        UnifiedShelfLogUtils.logMetric(filterIdAndProductAreas, "beauty", 1, 1);
        Object object = new Object();
        Assert.assertNotNull(object);
    }


    @Test
    public void test_cleanDegradeData2() {
        lionMockedStatic.when(() -> Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.empty.key.log.flow", 0)).thenReturn(1);
        List<ShelfFilterProductAreaVO> filterIdAndProductAreas = Lists.newArrayList();
        ShelfFilterProductAreaVO shelfFilterProductAreaVO = new ShelfFilterProductAreaVO();
        filterIdAndProductAreas.add(shelfFilterProductAreaVO);


        ShelfProductAreaVO shelfProductAreaVO = new ShelfProductAreaVO();
        ShelfItemVO shelfItemVO = new ShelfItemVO();

        shelfProductAreaVO.setItems(Lists.newArrayList(shelfItemVO));
        shelfFilterProductAreaVO.setProductAreas(Lists.newArrayList(shelfProductAreaVO));
        UnifiedShelfLogUtils.logMetric(filterIdAndProductAreas, "beauty", 1, 1);
        Object object = new Object();
        Assert.assertNotNull(object);
    }

    @Test
    public void test_logMetric() {
        lionMockedStatic.when(() -> Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.empty.key.log.flow", 0)).thenReturn(1);
        UnifiedShelfLogUtils.logMetric(new ShelfItemVO(), "beauty", 1, 1);
        Object object = new Object();
        Assert.assertNotNull(object);
    }

    @Test
    public void test_logMetricForTitle() {
        lionMockedStatic.when(() -> Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.empty.key.log.flow", 0)).thenReturn(1);
        UnifiedShelfLogUtils.logMetricForTitle(new ShelfItemVO(), "beauty", 1, 1);
        Object object = new Object();
        Assert.assertNotNull(object);
    }

    @Test
    public void test_logMetricForPic() {
        lionMockedStatic.when(() -> Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.empty.key.log.flow", 0)).thenReturn(1);
        UnifiedShelfLogUtils.logMetricForPic(new ShelfItemVO(), "beauty", 1, 1);
        Object object = new Object();
        Assert.assertNotNull(object);
    }

    @Test
    public void test_logMetricForSalePrice() {
        lionMockedStatic.when(() -> Lion.getInt(Environment.getAppName(), "com.sankuai.dzviewscene.dealshelf.empty.key.log.flow", 0)).thenReturn(1);
        UnifiedShelfLogUtils.logMetricForSalePrice(new ShelfItemVO(), "beauty", 1, 1);
        Object object = new Object();
        Assert.assertNotNull(object);
    }

}
