package com.sankuai.dzviewscene.product.shelf.options.builder.floors.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.gm.marketing.member.card.api.dto.membercard.CardTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ItemPriceTagUtilTest {

    @Mock
    private ProductM productM;

    @Mock
    private CardM cardM;

    @Mock
    private ProductPromoPriceM productPromoPriceM;

    /**
     * Test case for null productM
     */
    @Test
    public void testIsShowIdlePromoWithNullProduct() {
        // arrange
        ProductM productM = null;
        // act
        boolean result = ItemPriceTagUtil.isShowIdlePromo(productM);
        // assert
        assertFalse(result);
    }

    @Test
    public void testHashPromoTagWithMember_PreSaleTag() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        when(productPromoPriceM.getPromoTagType()).thenReturn(PromoTagTypeEnum.PreSale.getCode());
        assertTrue(ItemPriceTagUtil.hashPromoTagWithMember(productM, cardM));
    }

    @Test
    public void testHashPromoTagWithMember_RainbowSecKillTag() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        when(productPromoPriceM.getPromoTagType()).thenReturn(PromoTagTypeEnum.PreSale.getCode());
        assertTrue(ItemPriceTagUtil.hashPromoTagWithMember(productM, cardM));
    }

    @Test
    public void testHashPromoTagWithMember_NoMemberPromo() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.singletonList(productPromoPriceM));
        when(productPromoPriceM.getPromoTagType()).thenReturn(PromoTagTypeEnum.PreSale.getCode());
        assertTrue(ItemPriceTagUtil.hashPromoTagWithMember(productM, cardM));
    }

    @Test
    public void testHashPromoTagWithMember_MemberPromo() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Arrays.asList(productPromoPriceM));
        when(productPromoPriceM.getPromoTagType()).thenReturn(PromoTagTypeEnum.Member.getCode());
        when(productPromoPriceM.getPromoType()).thenReturn(PromoTypeEnum.DISCOUNT_CARD.getType());
        when(cardM.getUserCardList()).thenReturn(Arrays.asList(CardTypeEnum.DISCOUNT_CARD.getCode()));
        assertTrue(ItemPriceTagUtil.hashPromoTagWithMember(productM, cardM));
    }

    @Test
    public void testHashPromoTagWithMember_NoTag() throws Throwable {
        when(productM.getPromoPrices()).thenReturn(Collections.emptyList());
        assertFalse(ItemPriceTagUtil.hashPromoTagWithMember(productM, cardM));
    }
}
