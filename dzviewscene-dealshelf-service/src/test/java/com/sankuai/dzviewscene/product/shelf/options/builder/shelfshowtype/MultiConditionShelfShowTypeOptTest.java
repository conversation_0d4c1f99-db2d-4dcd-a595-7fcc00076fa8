package com.sankuai.dzviewscene.product.shelf.options.builder.shelfshowtype;

import com.alibaba.fastjson.JSON;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.InstitutionNatureEnum;
import com.sankuai.dzviewscene.product.shelf.ability.builder.shelfshowtype.ShelfShowTypeVP;
import com.sankuai.dzviewscene.product.shelf.utils.InstitutionNatureUtil;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.vividsolutions.jts.util.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import org.testng.collections.Maps;

import java.util.Map;
import java.util.Objects;

/**
 * @Author: zhouzhaoming02
 * @Create: 2024/6/26 19:55
 * @Description:
 */
@RunWith(MockitoJUnitRunner.class)
public class MultiConditionShelfShowTypeOptTest {


    @Test
    public void test_private_shop_low_version() {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.dpPoiIdL,11L);
        context.addParam(ShelfActivityConstants.Params.customInfo,mockHospitalType(InstitutionNatureEnum.PRIVATE));
        MultiConditionShelfShowTypeOpt.Config config = mockConfig();
        MultiConditionShelfShowTypeOpt opt = new MultiConditionShelfShowTypeOpt();
        Integer showType = opt.compute(context, ShelfShowTypeVP.Param.builder().shelfVersion(50).build(),config);
        Assert.isTrue(Objects.equals(showType,31));
    }

    private String mockHospitalType(InstitutionNatureEnum nature) {
        Map<String,String> map = Maps.newHashMap();
        map.put("institutionNature",nature.getKey());
        return JSON.toJSONString(map);
    }

    private MultiConditionShelfShowTypeOpt.Config mockConfig() {
        String jsonValue = "{\n" +
                "    \"conditionConfigs\": [\n" +
                "        {\n" +
                "            \"hitShowType\": 31,\n" +
                "            \"priority\": 1,\n" +
                "            \"condition\": {\n" +
                "                \"shelfLessVersion\": 100\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"hitShowType\": 20109,\n" +
                "            \"priority\": 2,\n" +
                "            \"condition\": {\n" +
                "                \"institutionNature\": \"public\"\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"hitShowType\": 20109,\n" +
                "            \"priority\": 3,\n" +
                "            \"condition\": {\n" +
                "                \"category\": 34188\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"defaultShowType\": 20107\n" +
                "}";
        return JSON.parseObject(jsonValue,MultiConditionShelfShowTypeOpt.Config.class);
    }

    @Test
    public void test_private_shop_high_version() {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.dpPoiIdL,11L);
        context.addParam(ShelfActivityConstants.Params.customInfo,mockHospitalType(InstitutionNatureEnum.PRIVATE));
        MultiConditionShelfShowTypeOpt.Config config = mockConfig();
        MultiConditionShelfShowTypeOpt opt = new MultiConditionShelfShowTypeOpt();
        Integer showType = opt.compute(context, ShelfShowTypeVP.Param.builder().shelfVersion(200).build(),config);
        Assert.isTrue(Objects.equals(showType,20107));
    }


    @Test
    public void test_public_shop_high_version() {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.dpPoiIdL,11L);
        context.addParam(ShelfActivityConstants.Params.customInfo,mockHospitalType(InstitutionNatureEnum.PUBLIC));
        MultiConditionShelfShowTypeOpt.Config config = mockConfig();
        MultiConditionShelfShowTypeOpt opt = new MultiConditionShelfShowTypeOpt();
        Integer showType = opt.compute(context, ShelfShowTypeVP.Param.builder().shelfVersion(200).build(),config);
        Assert.isTrue(Objects.equals(showType,20109));
    }

    @Test
    public void test_category() {
        ActivityCxt context = new ActivityCxt();
        context.addParam(ShelfActivityConstants.Params.dpPoiIdL,11L);
        MultiConditionShelfShowTypeOpt.Config config = mockConfig();
        MultiConditionShelfShowTypeOpt opt = new MultiConditionShelfShowTypeOpt();
        Integer showType = opt.compute(context, ShelfShowTypeVP.Param.builder().shelfVersion(200).category(34188).build(),config);
        Assert.isTrue(Objects.equals(showType,20109));
    }











}
