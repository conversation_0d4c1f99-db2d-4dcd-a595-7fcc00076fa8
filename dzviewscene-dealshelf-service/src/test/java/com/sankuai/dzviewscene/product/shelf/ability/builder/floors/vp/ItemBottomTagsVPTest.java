package com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemBottomTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.business.shelf.life.builder.LifeDealFloorsBuilderExt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ItemBottomTagsVPTest {

    @Mock
    private ItemBottomTagsVP itemBottomTagsVP;

    @Mock
    private ProductM productM;

    @Mock
    private ProductPriceM cardPrice;

    @Mock
    private ProductPriceM pinPrice;

    @Before
    public void setUp() {
        // Resetting mocks to ensure clean state for each test
        reset(itemBottomTagsVP, productM, cardPrice, pinPrice);
        // Common setup for ProductM mock
        when(productM.getCardPrice()).thenReturn(cardPrice);
        when(productM.getPinPrice()).thenReturn(pinPrice);
    }

    @Test
    public void testGetCardAndPinTags_BothPricesPresent() throws Throwable {
        when(cardPrice.getPriceTag()).thenReturn("Card Price Tag");
        when(pinPrice.getPriceTag()).thenReturn("Pin Price Tag");
        when(itemBottomTagsVP.getCardAndPinTags(productM, false, 1)).thenCallRealMethod();
        List<RichLabelVO> result = itemBottomTagsVP.getCardAndPinTags(productM, false, 1);
        assertEquals("Expected two labels in the result list", 2, result.size());
    }

    @Test
    public void testGetCardAndPinTags_OnlyCardPricePresent() throws Throwable {
        when(cardPrice.getPriceTag()).thenReturn("Card Price Tag");
        when(productM.getPinPrice()).thenReturn(null);
        when(itemBottomTagsVP.getCardAndPinTags(productM, false, 1)).thenCallRealMethod();
        List<RichLabelVO> result = itemBottomTagsVP.getCardAndPinTags(productM, false, 1);
        assertEquals("Expected one label in the result list", 1, result.size());
    }

    @Test
    public void testGetCardAndPinTags_OnlyPinPricePresent() throws Throwable {
        when(productM.getCardPrice()).thenReturn(null);
        when(pinPrice.getPriceTag()).thenReturn("Pin Price Tag");
        when(itemBottomTagsVP.getCardAndPinTags(productM, false, 1)).thenCallRealMethod();
        List<RichLabelVO> result = itemBottomTagsVP.getCardAndPinTags(productM, false, 1);
        assertEquals("Expected one label in the result list", 1, result.size());
    }

    @Test
    public void testGetCardAndPinTags_NoPricesPresent() throws Throwable {
        when(productM.getCardPrice()).thenReturn(null);
        when(productM.getPinPrice()).thenReturn(null);
        when(itemBottomTagsVP.getCardAndPinTags(productM, false, 1)).thenCallRealMethod();
        List<RichLabelVO> result = itemBottomTagsVP.getCardAndPinTags(productM, false, 1);
        assertEquals("Expected no labels in the result list", 0, result.size());
    }

    @Test
    public void testGetCardAndPinTags_ShowDiscountByNumFalse_CardPriceNull_PinPriceNull() throws Throwable {
        when(productM.getCardPrice()).thenReturn(null);
        when(productM.getPinPrice()).thenReturn(null);
        when(itemBottomTagsVP.getCardAndPinTags(productM, false, 1)).thenCallRealMethod();
        List<RichLabelVO> result = itemBottomTagsVP.getCardAndPinTags(productM, false, 1);
        assertEquals("Expected no labels in the result list", 0, result.size());
    }

    /**
     * Test filterEmptyTags when input list is null
     */
    @Test
    public void testFilterEmptyTags_WhenInputNull() throws Throwable {
        // arrange
        ItemBottomTagsVP<Object> itemBottomTagsVP = new ItemBottomTagsVP<Object>() {

            @Override
            public List<RichLabelVO> compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemBottomTagsVP.Param param, Object object) {
                throw new UnsupportedOperationException();
            }
        };
        List<RichLabelVO> beforeTags = null;
        // act
        List<RichLabelVO> result = itemBottomTagsVP.filterEmptyTags(beforeTags);
        // assert
        assertNull("Should return null when input list is null", result);
    }

    /**
     * Test filterEmptyTags when input list is empty
     */
    @Test
    public void testFilterEmptyTags_WhenInputEmpty() throws Throwable {
        // arrange
        ItemBottomTagsVP<Object> itemBottomTagsVP = new ItemBottomTagsVP<Object>() {

            @Override
            public List<RichLabelVO> compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemBottomTagsVP.Param param, Object object) {
                throw new UnsupportedOperationException();
            }
        };
        List<RichLabelVO> beforeTags = new ArrayList<>();
        // act
        List<RichLabelVO> result = itemBottomTagsVP.filterEmptyTags(beforeTags);
        // assert
        assertNull("Should return null when input list is empty", result);
    }

    /**
     * Test filterEmptyTags when all elements are null
     */
    @Test
    public void testFilterEmptyTags_WhenAllElementsNull() throws Throwable {
        // arrange
        ItemBottomTagsVP<Object> itemBottomTagsVP = new ItemBottomTagsVP<Object>() {

            @Override
            public List<RichLabelVO> compute(com.sankuai.athena.viewscene.framework.ActivityCxt activityCxt, com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemBottomTagsVP.Param param, Object object) {
                throw new UnsupportedOperationException();
            }
        };
        List<RichLabelVO> beforeTags = new ArrayList<>();
        beforeTags.add(null);
        beforeTags.add(null);
        // act
        List<RichLabelVO> result = itemBottomTagsVP.filterEmptyTags(beforeTags);
        // assert
        assertNull("Should return null when all elements are null", result);
    }

    /**
     * Helper class to test abstract class
     */
    private static class TestItemBottomTagsVP extends ItemBottomTagsVP<Object> {

        @Override
        public List<RichLabelVO> compute(ActivityCxt activityCxt, Param request, Object config) {
            // Mock implementation for testing purposes
            return Collections.emptyList();
        }
    }

    /**
     * Test case when priceTag is empty
     */
    @Test
    public void testBuildPromoDescRichLabel_WhenPriceTagEmpty() throws Throwable {
        // arrange
        TestItemBottomTagsVP itemBottomTagsVP = new TestItemBottomTagsVP();
        String promoPreDesc = "Test Promo";
        String priceTag = "";
        // act
        RichLabelVO result = itemBottomTagsVP.buildPromoDescRichLabel(promoPreDesc, priceTag);
        // assert
        assertNull(result);
    }

    /**
     * Test case when both parameters are valid
     */
    @Test
    public void testBuildPromoDescRichLabel_WhenValidParams() throws Throwable {
        // arrange
        TestItemBottomTagsVP itemBottomTagsVP = new TestItemBottomTagsVP();
        String promoPreDesc = "Test Promo";
        String priceTag = "¥99";
        // act
        RichLabelVO result = itemBottomTagsVP.buildPromoDescRichLabel(promoPreDesc, priceTag);
        // assert
        assertNotNull(result);
        String expectedJson = JsonCodec.encode(Lists.newArrayList(new LifeDealFloorsBuilderExt.RichLabel(promoPreDesc, "#777777", 11), new LifeDealFloorsBuilderExt.RichLabel(priceTag, "#FF6633", 11)));
        assertEquals(expectedJson, result.getText());
    }

    /**
     * Test case when both parameters are valid (duplicate test case for demonstration purposes)
     */
    @Test
    public void testBuildPromoDescRichLabel_WhenValidParamsDuplicate() throws Throwable {
        // arrange
        TestItemBottomTagsVP itemBottomTagsVP = new TestItemBottomTagsVP();
        String promoPreDesc = "Test Promo";
        String priceTag = "¥99";
        // act
        RichLabelVO result = itemBottomTagsVP.buildPromoDescRichLabel(promoPreDesc, priceTag);
        // assert
        assertNotNull(result);
        String expectedJson = JsonCodec.encode(Lists.newArrayList(new LifeDealFloorsBuilderExt.RichLabel(promoPreDesc, "#777777", 11), new LifeDealFloorsBuilderExt.RichLabel(priceTag, "#FF6633", 11)));
        assertEquals(expectedJson, result.getText());
    }

    /**
     * Test case when both parameters are valid (duplicate test case for demonstration purposes)
     */
    @Test
    public void testBuildoDescRichLabel_WhenValidParams() throws Throwable {
        // arrange
        TestItemBottomTagsVP itemBottomTagsVP = new TestItemBottomTagsVP();
        String promoPreDesc = "Test Promo";
        String priceTag = "¥99";
        // act
        RichLabelVO result = itemBottomTagsVP.buildPromoDescRichLabel(promoPreDesc, priceTag);
        // assert
        assertNotNull(result);
        String expectedJson = JsonCodec.encode(Lists.newArrayList(new LifeDealFloorsBuilderExt.RichLabel(promoPreDesc, "#777777", 11), new LifeDealFloorsBuilderExt.RichLabel(priceTag, "#FF6633", 11)));
        assertEquals(expectedJson, result.getText());
    }
}
