package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import org.junit.Assert;
import org.junit.Test;

public class DealSkuListModuleUtils_BuildDealSkuItemVOTest {

    /**
     * 测试 buildDealSkuItemVO 方法，当 itemName 为 null 时
     */
    @Test
    public void testBuildDealSkuItemVONull() {
        // arrange
        String itemName = null;
        // act
        DealSkuItemVO result = DealSkuListModuleUtils.buildDealSkuItemVO(itemName);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 buildDealSkuItemVO 方法，当 itemName 为 空字符串 时
     */
    @Test
    public void testBuildDealSkuItemVOEmpty() {
        // arrange
        String itemName = "";
        // act
        DealSkuItemVO result = DealSkuListModuleUtils.buildDealSkuItemVO(itemName);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 buildDealSkuItemVO 方法，当 itemName 只包含空格 时
     */
    @Test
    public void testBuildDealSkuItemVOSpace() {
        // arrange
        String itemName = " ";
        // act
        DealSkuItemVO result = DealSkuListModuleUtils.buildDealSkuItemVO(itemName);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 buildDealSkuItemVO 方法，当 itemName 是非空字符串 时
     */
    @Test
    public void testBuildDealSkuItemVONonEmpty() {
        // arrange
        String itemName = "test";
        // act
        DealSkuItemVO result = DealSkuListModuleUtils.buildDealSkuItemVO(itemName);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(itemName, result.getName());
    }
}
