package com.sankuai.dzviewscene.product.shelf.ability.list.adapter;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.SaleVP;
import com.sankuai.dzviewscene.product.shelf.utils.ActivityCtxtUtils;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class ProductListBuilderExtAdapter_ItemComponentSaleTest {

    @InjectMocks
    private ProductListBuilderExtAdapter productListBuilderExtAdapter;

    @Mock
    private PmfExecutionHelper pmfExecutionHelper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Tests the itemComponentSale method under normal conditions.
     */
    @Test
    public void testItemComponentSaleNormal() throws Throwable {
        // Arrange
        ActivityContext activityContext = mock(ActivityContext.class);
        String groupName = "testGroup";
        ProductM productM = mock(ProductM.class);
        SaleVP saleVP = mock(SaleVP.class);
        String expected = "testResult";
        when(pmfExecutionHelper.findVPoint(any(ActivityCxt.class), anyString(), anyString())).thenReturn(saleVP);
        when(saleVP.execute(any(ActivityCxt.class), any())).thenReturn(expected);
        // Act
        String result = productListBuilderExtAdapter.itemComponentSale(activityContext, groupName, productM);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * Tests the itemComponentSale method under exceptional conditions.
     */
    @Test(expected = IllegalArgumentException.class)
    public void testItemComponentSaleException() throws Throwable {
        // Arrange
        ActivityContext activityContext = null;
        String groupName = null;
        ProductM productM = null;
        // Act
        productListBuilderExtAdapter.itemComponentSale(activityContext, groupName, productM);
        // Assert
        // Expect an IllegalArgumentException to be thrown
    }
}
