package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.product.shelf.utils.PreSaleUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Constructor;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ItemPromoTagsSupportPreSaleOptTest {

    @InjectMocks
    private ItemPromoTagsSupportPreSaleOpt itemPromoTagsSupportPreSaleOpt;

    @Mock
    private ProductM productM;

    @Mock
    private CardM cardM;

    private List<DouHuM> douHuList = new ArrayList<>();

    private ItemPromoTagsVP.Param param;

    @Before
    public void setUp() throws Exception {
        // Use reflection to access the constructor
        Constructor<ItemPromoTagsVP.Param> constructor = ItemPromoTagsVP.Param.class.getDeclaredConstructor(ProductM.class, CardM.class, String.class, String.class, List.class, int.class, String.class, String.class, Map.class, String.class);
        constructor.setAccessible(true);
        param = constructor.newInstance(productM, cardM, "subScene", "salePrice", douHuList, 1, "extra", "a", Maps.newHashMap(), "团购");
    }

    @Test
    public void testComputePreSaleEnabled() throws Throwable {
        // This test now focuses on the behavior when pre-sale is enabled, without directly stubbing the isPreSaleDeal method.
        ItemPromoTagsSupportPreSaleOpt.Config config = new ItemPromoTagsSupportPreSaleOpt.Config();
        config.setEnablePreSale(true);
        // Assuming the ProductM object's state is such that isPreSaleDeal would return true, we proceed with the test.
        // The actual behavior of isPreSaleDeal should be verified in a separate test or through integration testing.
        List<DzPromoVO> result = itemPromoTagsSupportPreSaleOpt.compute(null, param, config);
        assertNotNull(result);
        // Additional assertions can be made based on the expected behavior of the compute method when pre-sale is enabled.
    }

    @Test
    public void testComputePreSaleDisabled() throws Throwable {
        ItemPromoTagsSupportPreSaleOpt.Config config = new ItemPromoTagsSupportPreSaleOpt.Config();
        config.setEnablePreSale(false);
        List<DzPromoVO> result = itemPromoTagsSupportPreSaleOpt.compute(null, param, config);
        assertNotNull(result);
        // Additional assertions can be made based on the expected behavior of the compute method when pre-sale is disabled.
    }
}
