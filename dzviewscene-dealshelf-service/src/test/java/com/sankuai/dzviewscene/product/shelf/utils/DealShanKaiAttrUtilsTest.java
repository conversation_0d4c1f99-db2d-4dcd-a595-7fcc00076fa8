package com.sankuai.dzviewscene.product.shelf.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * DealShanKaiAttrUtils单元测试
 */
public class DealShanKaiAttrUtilsTest {

    private ProductM productM;
    private Map<String, Object> extraMap;

    @Before
    public void setUp() {
        productM = mock(ProductM.class);
        extraMap = new HashMap<>();
    }

    /**
     * 测试processShanKai方法，当productM中没有dealPageLayout属性时
     */
    @Test
    public void testProcessShanKai_NoDealPageLayoutAttr() {
        when(productM.getExtAttrs()).thenReturn(new ArrayList<>());

        DealShanKaiAttrUtils.processShanKai(productM, extraMap);

        assertTrue(extraMap.isEmpty());
    }

    /**
     * 测试processShanKai方法，当dealPageLayout属性的config中render为true时
     */
    @Test
    public void testProcessShanKai_RenderTrue() {
        List<AttrM> attrList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("config", new JSONObject().fluentPut("render", true));
        jsonObject.put("components", new JSONArray().fluentAdd("component1"));
        attrList.add(new AttrM("dealPageLayout", jsonObject.toJSONString()));

        when(productM.getExtAttrs()).thenReturn(attrList);

        DealShanKaiAttrUtils.processShanKai(productM, extraMap);

        assertFalse(extraMap.isEmpty());
        assertNotNull(extraMap.get("dealPageLayout"));
    }

    /**
     * 测试processShanKai方法，当dealPageLayout属性的config中render为false时
     */
    @Test
    public void testProcessShanKai_RenderFalse() {
        List<AttrM> attrList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("config", new JSONObject().fluentPut("render", false));
        attrList.add(new AttrM("dealPageLayout", jsonObject.toJSONString()));

        when(productM.getExtAttrs()).thenReturn(attrList);

        DealShanKaiAttrUtils.processShanKai(productM, extraMap);

        assertTrue(extraMap.isEmpty());
    }

    /**
     * 测试processShanKai方法，当dealPageLayout属性的JSON格式不正确时
     */
    @Test
    public void testProcessShanKai_InvalidJson() {
        List<AttrM> attrList = new ArrayList<>();
        attrList.add(new AttrM("dealPageLayout", "invalid json"));

        when(productM.getExtAttrs()).thenReturn(attrList);

        DealShanKaiAttrUtils.processShanKai(productM, extraMap);

        assertTrue(extraMap.isEmpty());
    }

    /**
     * 测试processShanKai方法，当dealPageLayout属性的config缺失时
     */
    @Test
    public void testProcessShanKai_MissingConfig() {
        List<AttrM> attrList = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        attrList.add(new AttrM("dealPageLayout", jsonObject.toJSONString()));

        when(productM.getExtAttrs()).thenReturn(attrList);

        DealShanKaiAttrUtils.processShanKai(productM, extraMap);

        assertTrue(extraMap.isEmpty());
    }

    /**
     * 测试processShanKai方法，当dealPageLayout属性的config缺失时
     */
    @Test
    public void testProductM_MissingConfig() {
        DealShanKaiAttrUtils.processShanKai(null, extraMap);

        assertTrue(extraMap.isEmpty());
    }

}
