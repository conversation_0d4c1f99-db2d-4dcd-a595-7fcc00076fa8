package com.sankuai.dzviewscene.product.utils;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum;
import com.sankuai.dzcard.navigation.api.enums.CardTypeEnum;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.biz.utils.DzPromoUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPromoPriceM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import java.math.BigDecimal;

@RunWith(MockitoJUnitRunner.class)
public class ProductMPromoInfoUtilsTest {
    @Test
    public void getPreSalePromoPriceM(){
        ProductPromoPriceM productPromoPriceM1 = new ProductPromoPriceM();
        productPromoPriceM1.setPromoTagType(PromoTagTypeEnum.PreSale.getCode());
        ProductPromoPriceM productPromoPriceM2 = new ProductPromoPriceM();
        productPromoPriceM2.setPromoTagType(PromoTagTypeEnum.PreSale_NewUser.getCode());
        ProductPromoPriceM productPromoPriceM3 = new ProductPromoPriceM();
        productPromoPriceM3.setPromoTagType(PromoTagTypeEnum.PreSale_Member.getCode());

        ProductM productM1 = new ProductM();
        productM1.setPromoPrices(Lists.newArrayList(productPromoPriceM1,productPromoPriceM2));
        Assert.assertNotNull(ProductMPromoInfoUtils.getPreSalePromoPriceM(productM1));

        ProductM productM2 = new ProductM();
        productM2.setPromoPrices(Lists.newArrayList(productPromoPriceM3));
        Assert.assertNull(ProductMPromoInfoUtils.getPreSalePromoPriceM(productM2));
    }

    @Test
    public void getSecKillPromoPriceM(){
        ProductPromoPriceM productPromoPriceM1 = new ProductPromoPriceM();
        productPromoPriceM1.setPromoTagType(PromoTagTypeEnum.NewUser.getCode());
        DzTagVO dzTagVO1 = ProductMPromoInfoUtils.getSecKillPromoPriceM(Lists.newArrayList(productPromoPriceM1), "100", "80", 1, 2);

        Assert.assertTrue(dzTagVO1 != null && dzTagVO1.getName().startsWith("新客共省¥20"));

        ProductPromoPriceM productPromoPriceM2 = new ProductPromoPriceM();
        productPromoPriceM2.setPromoTagType(PromoTagTypeEnum.PreSale_Member.getCode());
        DzTagVO dzTagVO2 = ProductMPromoInfoUtils.getSecKillPromoPriceM(Lists.newArrayList(productPromoPriceM2), "100", "80", 1, 2);

        Assert.assertTrue(dzTagVO2 != null && dzTagVO2.getName().startsWith("共省¥20"));
    }

    @Test
    public void getSalePrice(){
        Assert.assertEquals(ProductMPromoInfoUtils.getSalePrice("100"), new BigDecimal("100"));
        Assert.assertEquals(ProductMPromoInfoUtils.getSalePrice(""), new BigDecimal("0"));
    }

    @Test
    public void getMemberPromoPriceM(){
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoTagType(PromoTagTypeEnum.Member.getCode());
        promoPrice1.setPromoType(PromoTypeEnum.DISCOUNT_CARD.getType());
        productM.setPromoPrices(Lists.newArrayList(promoPrice1));
        CardM cardM = new CardM();
        cardM.setUserCardList(Lists.newArrayList(CardTypeEnum.DISCOUNT_CARD.getCode()));

        Assert.assertNotNull(ProductMPromoInfoUtils.getMemberPromoPriceM(productM, cardM));
    }
    @Test
    public void getPlatformPromoPromoPriceM(){
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoTagType(PromoTagTypeEnum.Official_Subsidies_NewUser.getCode());
        productM.setPromoPrices(Lists.newArrayList(promoPrice1));
        Assert.assertNotNull(ProductMPromoInfoUtils.getPlatformPromoPromoPriceM(productM));
    }
    @Test
    public void getNewUserPromoPriceM(){
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoTagType(PromoTagTypeEnum.NewUser.getCode());
        productM.setPromoPrices(Lists.newArrayList(promoPrice1));
        Assert.assertNotNull(ProductMPromoInfoUtils.getNewUserPromoPriceM(productM));
    }
    @Test
    public void getOtherPromoPriceM(){
        ProductM productM = new ProductM();
        ProductPromoPriceM promoPrice1 = new ProductPromoPriceM();
        promoPrice1.setPromoTagType(PromoTagTypeEnum.Other.getCode());
        productM.setPromoPrices(Lists.newArrayList(promoPrice1));
        Assert.assertNotNull(ProductMPromoInfoUtils.getOtherPromoPriceM(productM));
    }


    @Test
    public void testBuildMemberExclusiveTag_DpPrePic() throws Throwable {
        ProductM productM = mock(ProductM.class);
        ProductPromoPriceM promoPriceM = mock(ProductPromoPriceM.class);
        when(productM.getMarketPrice()).thenReturn("166");
        int platform = 1;
        String salePrice = "100";
        int popType = 1;
        when(DzPromoUtils.buildPromoDetail(promoPriceM,popType)).thenReturn(null);
        DzTagVO result = ProductMPromoInfoUtils.buildMemberExclusiveTag(productM, platform, salePrice, promoPriceM, popType);
        Assert.assertNotNull(result);
        DzPictureComponentVO prePic = result.getPrePic();
        Assert.assertNotNull(prePic);
        Assert.assertEquals("https://p0.meituan.net/travelcube/df6c671e8baecc8d1b2ae41df518ff827159.png",prePic.getPicUrl());

    }

    @Test
    public void testBuildMemberExclusiveTag_MtPrePic() throws Throwable {
        ProductM productM = mock(ProductM.class);
        ProductPromoPriceM promoPriceM = mock(ProductPromoPriceM.class);
        when(productM.getMarketPrice()).thenReturn("166");
        String salePrice = "100";
        int popType = 1;
        when(DzPromoUtils.buildPromoDetail(promoPriceM,popType)).thenReturn(null);

        int platform1 = 2;
        DzTagVO result2 = ProductMPromoInfoUtils.buildMemberExclusiveTag(productM, platform1, salePrice, promoPriceM, popType);
        Assert.assertNotNull(result2);
        DzPictureComponentVO prePic1 = result2.getPrePic();
        Assert.assertNotNull(prePic1);
        Assert.assertEquals("https://p0.meituan.net/travelcube/dab2626832fe4afdc3da732eab1b66398113.png",prePic1.getPicUrl());
    }

    /**
     * 测试buildMerchantMemberPromoTagNew方法，当memberDzTagVO为null时
     */
    @Test
    public void testBuildMerchantMemberPromoTagNew() {
        // arrange
        ProductM productM = mock(ProductM.class);
        ProductPromoPriceM promoPriceM = mock(ProductPromoPriceM.class);
        MerchantMemberPromoUtils.MerchantMemberDealModel memberProductPromo =  mock(MerchantMemberPromoUtils.MerchantMemberDealModel.class);
        memberProductPromo.setChargeType(1);
        memberProductPromo.setMemberDiscountType(2);
        MerchantMemberPromoUtils.MerchantMemberModel merchantMemberModel = new MerchantMemberPromoUtils.MerchantMemberModel();
        merchantMemberModel.setIsNewMember(false);
        merchantMemberModel.setIsMember(true);
        memberProductPromo.setMerchantMember(merchantMemberModel);
        when(promoPriceM.getIcon()).thenReturn(null);
        when(productM.getMarketPrice()).thenReturn("100");
        when(promoPriceM.getPromoPrice()).thenReturn(new BigDecimal("80"));
        // act
        DzTagVO result = ProductMPromoInfoUtils.buildMerchantMemberPromoTagNew(productM, 1, promoPriceM, memberProductPromo, 1);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试buildMerchantMemberPromoTagNew方法，正常情况
     */
    @Test
    public void testBuildMerchantMemberPromoTagNew_Normal() {
        // arrange
        ProductM productM = mock(ProductM.class);
        ProductPromoPriceM promoPriceM = mock(ProductPromoPriceM.class);
        MerchantMemberPromoUtils.MerchantMemberDealModel memberProductPromo =  mock(MerchantMemberPromoUtils.MerchantMemberDealModel.class);
        memberProductPromo.setChargeType(1);
        memberProductPromo.setMemberDiscountType(1);
        MerchantMemberPromoUtils.MerchantMemberModel merchantMemberModel = new MerchantMemberPromoUtils.MerchantMemberModel();
        merchantMemberModel.setIsNewMember(true);
        merchantMemberModel.setIsMember(true);
        memberProductPromo.setMerchantMember(merchantMemberModel);
        when(productM.getMarketPrice()).thenReturn("100");
        when(promoPriceM.getPromoPrice()).thenReturn(new BigDecimal("80"));
        when(promoPriceM.getIcon()).thenReturn("iconUrl");

        // act
        DzTagVO result = ProductMPromoInfoUtils.buildMerchantMemberPromoTagNew(productM, 2, promoPriceM, memberProductPromo, 1);

        // assert
        assertNotNull(result);
        assertEquals("共省¥20", result.getName());
    }

    /**
     * 测试buildMerchantMemberPromoTagNew方法，当productM.marketPrice为null时
     */
    @Test
    public void testBuildMerchantMemberPromoTagNewWithNullMarketPrice() {
        // arrange
        ProductM productM = mock(ProductM.class);
        ProductPromoPriceM promoPriceM = mock(ProductPromoPriceM.class);
        MerchantMemberPromoUtils.MerchantMemberDealModel memberProductPromo =  mock(MerchantMemberPromoUtils.MerchantMemberDealModel.class);
        memberProductPromo.setChargeType(1);
        memberProductPromo.setMemberDiscountType(2);
        MerchantMemberPromoUtils.MerchantMemberModel merchantMemberModel = new MerchantMemberPromoUtils.MerchantMemberModel();
        merchantMemberModel.setIsNewMember(false);
        merchantMemberModel.setIsMember(true);
        memberProductPromo.setMerchantMember(merchantMemberModel);
        when(promoPriceM.getIcon()).thenReturn(null);
        when(productM.getMarketPrice()).thenReturn(null);
        // act
        DzTagVO result = ProductMPromoInfoUtils.buildMerchantMemberPromoTagNew(productM, 1, promoPriceM, memberProductPromo, 1);

        // assert
        assertNull(result);
    }

}
