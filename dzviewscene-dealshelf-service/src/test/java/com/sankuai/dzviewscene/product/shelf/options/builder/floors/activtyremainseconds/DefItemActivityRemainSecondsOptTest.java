package com.sankuai.dzviewscene.product.shelf.options.builder.floors.activtyremainseconds;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemActivityRemainSecondsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import org.junit.runner.RunWith;
import java.util.Arrays;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefItemActivityRemainSecondsOptTest {

    @Test
    public void testComputeProductIsNull() throws Throwable {
        // arrange
        DefItemActivityRemainSecondsOpt opt = new DefItemActivityRemainSecondsOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        // Corrected the way to mock the Param class
        ItemActivityRemainSecondsVP.Param param = mock(ItemActivityRemainSecondsVP.Param.class);
        when(param.getProductM()).thenReturn(null);
        // act
        Long result = opt.compute(activityCxt, param, null);
        // assert
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testComputeActivitiesIsEmpty() throws Throwable {
        // arrange
        DefItemActivityRemainSecondsOpt opt = new DefItemActivityRemainSecondsOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        // Corrected the way to mock the Param class
        ItemActivityRemainSecondsVP.Param param = mock(ItemActivityRemainSecondsVP.Param.class);
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getActivities()).thenReturn(null);
        // act
        Long result = opt.compute(activityCxt, param, null);
        // assert
        assertEquals(Long.valueOf(0L), result);
    }

    @Test
    public void testComputeHasPerfectActivity() throws Throwable {
        // arrange
        DefItemActivityRemainSecondsOpt opt = new DefItemActivityRemainSecondsOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        // Corrected the way to mock the Param class
        ItemActivityRemainSecondsVP.Param param = mock(ItemActivityRemainSecondsVP.Param.class);
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        ProductActivityM activityM = mock(ProductActivityM.class);
        when(productM.getActivities()).thenReturn(Arrays.asList(activityM));
        when(activityM.getRemainingTime()).thenReturn(1000L);
        // act
        Long result = opt.compute(activityCxt, param, null);
        // assert
        assertEquals(Long.valueOf(1000L), result);
    }

    @Test
    public void testComputeHasPreheatPerfectActivity() throws Throwable {
        // arrange
        DefItemActivityRemainSecondsOpt opt = new DefItemActivityRemainSecondsOpt();
        ActivityCxt activityCxt = mock(ActivityCxt.class);
        // Corrected the way to mock the Param class
        ItemActivityRemainSecondsVP.Param param = mock(ItemActivityRemainSecondsVP.Param.class);
        ProductM productM = mock(ProductM.class);
        when(param.getProductM()).thenReturn(productM);
        ProductActivityM activityM = mock(ProductActivityM.class);
        when(productM.getActivities()).thenReturn(Arrays.asList(activityM));
        when(activityM.getRemainingTime()).thenReturn(1000L);
        // act
        Long result = opt.compute(activityCxt, param, null);
        // assert
        assertEquals(Long.valueOf(1000L), result);
    }
}
