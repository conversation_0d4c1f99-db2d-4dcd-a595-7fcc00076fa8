package com.sankuai.dzviewscene.product.filterlist.option.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.product.filterlist.option.factory.impl.SpaStrategyImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

/**
 * @author: created by hang.yu on 2024/4/12 10:57
 */
@RunWith(MockitoJUnitRunner.class)
public class SpaStrategyImplTest {

    @InjectMocks
    private SpaStrategyImpl spaStrategy;

    @Test
    public void getFilterListTitle() {
        SkuItemDto skuItemDto = new SkuItemDto();

        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("serviceDurationInt");
        attrItemDto.setAttrValue("10");

        SkuAttrItemDto attrItemDto1 = new SkuAttrItemDto();
        attrItemDto1.setAttrName("serviceTechnique");
        attrItemDto1.setAttrValue("手法");

        SkuAttrItemDto attrItemDto2 = new SkuAttrItemDto();
        attrItemDto2.setAttrName("bodyRegion");
        attrItemDto2.setAttrValue("全身");

        skuItemDto.setAttrItems(Lists.newArrayList(attrItemDto, attrItemDto1));
        String filterListTitle = spaStrategy.getFilterListTitle(skuItemDto, "spa");
        Assert.assertNotNull(filterListTitle);
    }

    @Test
    public void getProductCategorys() {
        List<Long> productCategorys = spaStrategy.getProductCategorys();
        Assert.assertNotNull(productCategorys);
    }
}