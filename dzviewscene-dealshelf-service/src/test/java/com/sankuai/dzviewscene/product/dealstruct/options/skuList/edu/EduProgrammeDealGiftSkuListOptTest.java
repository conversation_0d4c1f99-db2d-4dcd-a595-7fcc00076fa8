package com.sankuai.dzviewscene.product.dealstruct.options.skuList.edu;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.jetbrains.annotations.NotNull;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class EduProgrammeDealGiftSkuListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @InjectMocks
    private EduProgrammeDealGiftSkuListOpt opt;

    private AttrM buildAttr(String attrName, String value) {
        AttrM attrM = new AttrM();
        attrM.setName(attrName);
        attrM.setValue(value);
        return attrM;
    }

    @NotNull
    private List<EduProgrammeDealGiftSkuListOpt.MaterialInfo> getMaterialInfo() {
        List<EduProgrammeDealGiftSkuListOpt.MaterialInfo> list = new ArrayList<>();
        EduProgrammeDealGiftSkuListOpt.MaterialInfo gift = new EduProgrammeDealGiftSkuListOpt.MaterialInfo();
        gift.setMaterialName("测试1");
        gift.setMaterialType("纸质资料");
        list.add(gift);
        EduProgrammeDealGiftSkuListOpt.MaterialInfo gift2 = new EduProgrammeDealGiftSkuListOpt.MaterialInfo();
        gift2.setMaterialName("测试2");
        gift.setMaterialType("电子资料");
        list.add(gift2);
        return list;
    }

    private List<EduProgrammeDealGiftSkuListOpt.CoursePlan> getCoursePlans() {
        List<EduProgrammeDealGiftSkuListOpt.CoursePlan> coursePlanList = new ArrayList<>();
        EduProgrammeDealGiftSkuListOpt.CoursePlan plan1 = new EduProgrammeDealGiftSkuListOpt.CoursePlan();
        plan1.setCourseModule("测试1");
        plan1.setCourseTimeNum(new BigDecimal(10));
        coursePlanList.add(plan1);
        EduProgrammeDealGiftSkuListOpt.CoursePlan plan2 = new EduProgrammeDealGiftSkuListOpt.CoursePlan();
        plan2.setCourseModule("测试2");
        plan2.setCourseTimeNum(new BigDecimal(0.5));
        coursePlanList.add(plan2);
        return coursePlanList;
    }

    /**
     * 测试compute方法，当入参没有值时
     * 期望返回null
     */
    @Test
    public void testParamIsEmpty() {
        // arrange
        EduProgrammeDealGiftSkuListOpt opt = new EduProgrammeDealGiftSkuListOpt();
        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, new EduProgrammeDealGiftSkuListOpt.Config());
        // assert
        assertNull(result);
    }

    /**
     * 测试compute方法，当入参有值时
     * 期望返回完整的skuModule
     */
    @Test
    public void testHasAttrValue() {
        // arrange
        AttrM provideServiceTypeAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_PROVIDE_SERVICE_TYPE, JsonCodec.encodeWithUTF8(Lists.newArrayList("免费资料", "报考规划", "免费课程", "水平测试")));
        AttrM physicalGiftsAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_MATERIAL_LIST, JsonCodec.encodeWithUTF8(getMaterialInfo()));
        AttrM materialTypeAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_MATERIAL_TYPE, "资料类型1");
        AttrM planContentAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_PLAN_CONTENT, "规划内容1");
        AttrM planMethodAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_EXAM_PLAN_METHOD, "规划方式1");
        AttrM levelTestContentAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_LEVEL_TEST_CONTENT, "水平测试内容1");
        AttrM levelTestMethodAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_LEVEL_TEST_METHOD, "规划方式1");
        AttrM courseMethodAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_COURSE_METHOD, "授课方式1");
        AttrM coursePlanAttr = buildAttr(EduProgrammeDealGiftSkuListOpt.ATTR_COURSE_PLAN, JsonCodec.encodeWithUTF8(getCoursePlans()));
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        when(dealDetailInfoModel.getDealAttrs()).thenReturn(Lists.newArrayList(provideServiceTypeAttr, materialTypeAttr, physicalGiftsAttr, planContentAttr, planMethodAttr, levelTestContentAttr, levelTestMethodAttr, courseMethodAttr, coursePlanAttr));
        EduProgrammeDealGiftSkuListOpt opt = new EduProgrammeDealGiftSkuListOpt();
        EduProgrammeDealGiftSkuListOpt.Config config = new EduProgrammeDealGiftSkuListOpt.Config();
        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        // assert
        assertTrue(result.size() == 1 && result.get(0).getDealSkuGroupModuleVOS().size() == 1 && result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList().size() == 4);
        List<DealSkuVO> dealSkuVOList = result.get(0).getDealSkuGroupModuleVOS().get(0).getDealSkuList();
        assertTrue("免费资料".equals(dealSkuVOList.get(0).getTitle()));
        assertTrue(dealSkuVOList.get(0).getItems().size() == 2);
        assertTrue("资料内容".equals(dealSkuVOList.get(0).getItems().get(0).getName()));
        assertTrue("资料类型".equals(dealSkuVOList.get(0).getItems().get(1).getName()));
        assertTrue("报考规划".equals(dealSkuVOList.get(1).getTitle()));
        assertTrue(dealSkuVOList.get(1).getItems().size() == 2);
        assertTrue("规划内容".equals(dealSkuVOList.get(1).getItems().get(0).getName()));
        assertTrue("规划方式".equals(dealSkuVOList.get(1).getItems().get(1).getName()));
        assertTrue("免费课程".equals(dealSkuVOList.get(2).getTitle()));
        assertTrue(dealSkuVOList.get(2).getItems().size() == 2);
        assertTrue("免费课程".equals(dealSkuVOList.get(2).getItems().get(0).getName()));
        assertTrue("授课方式".equals(dealSkuVOList.get(2).getItems().get(1).getName()));
        assertTrue("水平测试".equals(dealSkuVOList.get(3).getTitle()));
        assertTrue(dealSkuVOList.get(3).getItems().size() == 2);
        assertTrue("测试内容".equals(dealSkuVOList.get(3).getItems().get(0).getName()));
        assertTrue("测试方式".equals(dealSkuVOList.get(3).getItems().get(1).getName()));
    }

    @Test
    public void testCompute_WhenSkuVOListEmpty_ShouldReturnNull() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        EduProgrammeDealGiftSkuListOpt.Param param = EduProgrammeDealGiftSkuListOpt.Param.builder().dealDetailInfoModel(dealDetailInfoModel).build();
        EduProgrammeDealGiftSkuListOpt.Config config = new EduProgrammeDealGiftSkuListOpt.Config();
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        assertNull("Result should be null when skuVOList is empty", result);
    }

    @Test
    public void testCompute_WhenSkuVOListNull_ShouldReturnNull() throws Throwable {
        DealDetailInfoModel dealDetailInfoModel = new DealDetailInfoModel();
        EduProgrammeDealGiftSkuListOpt.Param param = EduProgrammeDealGiftSkuListOpt.Param.builder().dealDetailInfoModel(dealDetailInfoModel).build();
        EduProgrammeDealGiftSkuListOpt.Config config = new EduProgrammeDealGiftSkuListOpt.Config();
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(context, param, config);
        assertNull("Result should be null when skuVOList is null", result);
    }
}
