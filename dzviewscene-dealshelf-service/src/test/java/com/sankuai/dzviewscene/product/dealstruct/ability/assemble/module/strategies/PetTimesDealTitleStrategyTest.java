package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.productdetail.util.TimesDealUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;
/**
 * <AUTHOR>
 * @ClassName PetTimesDealTitleStrategyTest.java
 * @createTime 2024/04/22 16:02
 */

@RunWith(MockitoJUnitRunner.class)
public class PetTimesDealTitleStrategyTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private DealDetailAssembleParam assembleParam;
    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    private PetTimesDealTitleStrategy petTimesDealTitleStrategy;

    private MockedStatic<TimesDealUtil> timesDealUtilMockedStatic;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        petTimesDealTitleStrategy = new PetTimesDealTitleStrategy();
        timesDealUtilMockedStatic = Mockito.mockStatic(TimesDealUtil.class);
    }

    @After
    public void tearDown() {
        timesDealUtilMockedStatic.close();
    }


    @Test
    public void testBuildModelVO() {
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());
        DealDetailModuleVO result = petTimesDealTitleStrategy.buildModelVO(activityCxt, assembleParam, "");
        assertNull(result);
    }

    @Test
    public void testBuildModelVO2() {

        when(activityCxt.getSource(Mockito.any())).thenReturn(Collections.singletonList(dealDetailInfoModel));

        when(TimesDealUtil.isTimesDeal(any())).thenReturn(true);
        when(dealDetailInfoModel.getDealTitle()).thenReturn("Test Title");
        when(TimesDealUtil.getTimesTitle(any())).thenReturn("Test Name");

        DealDetailModuleVO result = petTimesDealTitleStrategy.buildModelVO(activityCxt, assembleParam, "");

        assertNotNull(result);
    }
}
