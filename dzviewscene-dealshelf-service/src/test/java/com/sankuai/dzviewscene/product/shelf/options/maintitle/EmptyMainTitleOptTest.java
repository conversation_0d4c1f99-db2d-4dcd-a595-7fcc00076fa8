package com.sankuai.dzviewscene.product.shelf.options.maintitle;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.maintitle.vp.DealMainTitleVP;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class EmptyMainTitleOptTest {

    private EmptyDealMainTitleOpt target;

    @Before
    public void setUp() {
        target = new EmptyDealMainTitleOpt();
    }

    /**
     * 测试 compute 方法，当 config 为 null 时
     */
    @Test
    public void test_empty_maintitle_not_null() {
        EmptyDealMainTitleOpt.Param param = DealMainTitleVP.Param.builder()
                .build();
        assertNotNull(target.compute(new ActivityCxt(), param, null));
    }
    
}
