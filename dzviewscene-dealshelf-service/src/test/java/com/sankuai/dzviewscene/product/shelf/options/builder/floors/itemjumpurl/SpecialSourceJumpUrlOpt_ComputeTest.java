package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemjumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductActivityM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.PpEffectiveTagInfo;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import org.mockito.Mockito;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import static org.mockito.Mockito.when;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class SpecialSourceJumpUrlOpt_ComputeTest {

    private SpecialSourceJumpUrlOpt specialSourceJumpUrlOpt;

    private ActivityCxt activityCxt;

    private ItemJumpUrlVP.Param param;

    private ProductM productM;

    private ShopM shopM;

    @Test
    public void testComputeIsPpEffectiveActivity() throws Throwable {
        SpecialSourceJumpUrlOpt specialSourceJumpUrlOpt = new SpecialSourceJumpUrlOpt();
        ActivityCxt activityCxt = Mockito.mock(ActivityCxt.class);
        ItemJumpUrlVP.Param param = Mockito.mock(ItemJumpUrlVP.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        ProductActivityM activityM = Mockito.mock(ProductActivityM.class);
        PpEffectiveTagInfo ppEffectiveTagInfo = Mockito.mock(PpEffectiveTagInfo.class);
        when(ppEffectiveTagInfo.isPpEffective()).thenReturn(true);
        when(activityM.getPpEffectiveTagInfo()).thenReturn(ppEffectiveTagInfo);
        when(productM.getActivities()).thenReturn(Collections.singletonList(activityM));
        when(productM.getJumpUrl()).thenReturn("http://www.example.com");
        when(param.getProductM()).thenReturn(productM);
        String result = specialSourceJumpUrlOpt.compute(activityCxt, param, null);
        assertEquals("http://www.example.com&source=cost_effective", result);
    }

    @Test
    public void testComputeCategoryForAddDealShelfSourceSetContainsCategory() throws Throwable {
        SpecialSourceJumpUrlOpt specialSourceJumpUrlOpt = new SpecialSourceJumpUrlOpt();
        ActivityCxt activityCxt = Mockito.mock(ActivityCxt.class);
        ItemJumpUrlVP.Param param = Mockito.mock(ItemJumpUrlVP.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        ShopM shopM = Mockito.mock(ShopM.class);
        Set<Integer> categorySet = new HashSet<>();
        categorySet.add(1);
        Field categorySetField = SpecialSourceJumpUrlOpt.class.getDeclaredField("categoryForAddDealShelfSourceSet");
        categorySetField.setAccessible(true);
        categorySetField.set(specialSourceJumpUrlOpt, categorySet);
        when(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);
        when(shopM.getCategory()).thenReturn(1);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://www.example.com");
        String result = specialSourceJumpUrlOpt.compute(activityCxt, param, null);
        assertEquals("http://www.example.com&source=poi_page", result);
    }

    @Test
    public void testComputeNotPpEffectiveActivityAndCategoryNotContains() throws Throwable {
        SpecialSourceJumpUrlOpt specialSourceJumpUrlOpt = new SpecialSourceJumpUrlOpt();
        ActivityCxt activityCxt = Mockito.mock(ActivityCxt.class);
        ItemJumpUrlVP.Param param = Mockito.mock(ItemJumpUrlVP.Param.class);
        ProductM productM = Mockito.mock(ProductM.class);
        ShopM shopM = Mockito.mock(ShopM.class);
        Set<Integer> categorySet = new HashSet<>();
        categorySet.add(2);
        Field categorySetField = SpecialSourceJumpUrlOpt.class.getDeclaredField("categoryForAddDealShelfSourceSet");
        categorySetField.setAccessible(true);
        categorySetField.set(specialSourceJumpUrlOpt, categorySet);
        when(activityCxt.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);
        when(shopM.getCategory()).thenReturn(1);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://www.example.com");
        String result = specialSourceJumpUrlOpt.compute(activityCxt, param, null);
        assertEquals("http://www.example.com", result);
    }
}
