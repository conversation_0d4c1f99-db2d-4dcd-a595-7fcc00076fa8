package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailPriceModuleVO;
import com.sankuai.dzviewscene.shelf.platform.utils.CollectUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PriceStrategyTest {

    @InjectMocks
    private PriceStrategy priceStrategy;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assembleParam;

    @Test
    public void testBuildModelVOWhenPriceModelsIsEmpty() {
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(Collections.emptyList());
        // act
        DealDetailModuleVO result = priceStrategy.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildModelVOWhenPriceModelsIsNotEmpty() {
        // arrange
        DealDetailPriceModel priceModel = new DealDetailPriceModel();
        priceModel.setSalePrice("salePrice");
        priceModel.setSalePriceTitle("salePriceTitle");
        priceModel.setOriginalPrice("originalPrice");
        priceModel.setOriginalPriceTitle("originalPriceTitle");
        when(activityCxt.getSource(anyString())).thenReturn(Arrays.asList(priceModel));
        // act
        DealDetailModuleVO result = priceStrategy.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNotNull(result);
        DealDetailPriceModuleVO priceModuleVO = result.getPriceModel();
        assertEquals("salePrice", priceModuleVO.getSalePrice());
        assertEquals("salePriceTitle", priceModuleVO.getSalePriceTitle());
        assertEquals("originalPrice", priceModuleVO.getOriginalPrice());
        assertEquals("originalPriceTitle", priceModuleVO.getOriginalPriceTitle());
    }
}
