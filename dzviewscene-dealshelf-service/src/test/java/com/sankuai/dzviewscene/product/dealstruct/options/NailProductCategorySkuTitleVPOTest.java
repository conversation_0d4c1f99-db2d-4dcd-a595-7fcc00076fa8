package com.sankuai.dzviewscene.product.dealstruct.options;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.sku.vpoints.SkuTitleVP;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.lang.reflect.Field;
import java.util.Collections;

import static org.junit.Assert.*;
import org.junit.*;

import static org.mockito.Mockito.*;

public class NailProductCategorySkuTitleVPOTest {
    private NailProductCategorySkuTitleVPO vpo;
    @Mock
    private ActivityCxt context;
    @Mock
    private SkuTitleVP.Param param;
    @Mock
    private NailProductCategorySkuTitleVPO.Config config;
    private static final long COLOR_CATEGORY_ID = getPrivateStaticLong(NailProductCategorySkuTitleVPO.class, "COLOR_CATEGORY_ID");
    private static final long REMOVE_NAIL_CATEGORY_ID = getPrivateStaticLong(NailProductCategorySkuTitleVPO.class, "REMOVE_NAIL_CATEGORY_ID");
    private static final String SINGLE_COLOR_NAIL = getPrivateStaticString(NailProductCategorySkuTitleVPO.class, "SINGLE_COLOR_NAIL");
    private static final String STYLE_NAIL_SECOND_CATEGORY = getPrivateStaticString(NailProductCategorySkuTitleVPO.class, "STYLE_NAIL_SECOND_CATEGORY");
    private static final String DIff_COLOR_NAIL = getPrivateStaticString(NailProductCategorySkuTitleVPO.class, "DIff_COLOR_NAIL");
    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        vpo = new NailProductCategorySkuTitleVPO();
    }
    @Test
    public void testComputeWhenSkuItemDtoIsNull() {
        when(param.getSkuItemDto()).thenReturn(null);
        String result = vpo.compute(context, param, config);
        assertNull(result);
    }
    @Test
    public void testComputeWhenConfigIsNull() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4043L);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        String result = vpo.compute(context, param, null);
        assertNull(result);
    }
    @Test
    public void testComputeWhenSkuCategoryIdWithCustomzedTitleIsEmpty() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4043L);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.emptyList());
        String result = vpo.compute(context, param, config);
        assertNull(result);
    }
    @Test
    public void testComputeWhenProductCategoryNotInConfig() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4043L);
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.singletonList(9999L));
        String result = vpo.compute(context, param, config);
        assertNull(result);
    }
    @Test
    public void testComputeWhenAttrItemsIsEmpty() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(4043L);
        skuItemDto.setAttrItems(Collections.emptyList());
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.singletonList(4043L));
        String result = vpo.compute(context, param, config);
        assertNull(result);
    }
    @Test
    public void testComputeWhenProductCategoryIsColorCategoryId() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(COLOR_CATEGORY_ID);
        skuItemDto.setAttrItems(Collections.singletonList(new SkuAttrItemDto()));
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.singletonList(COLOR_CATEGORY_ID));
        String result = vpo.compute(context, param, config);
        assertEquals(SINGLE_COLOR_NAIL, result);
    }
    @Test
    public void testComputeWhenProductCategoryIsRemoveNailCategoryId() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(REMOVE_NAIL_CATEGORY_ID);
        skuItemDto.setAttrItems(Collections.singletonList(new SkuAttrItemDto()));
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.singletonList(REMOVE_NAIL_CATEGORY_ID));
        String result = vpo.compute(context, param, config);
        assertNull(result);
    }
    @Test
    public void testComputeWhenProductCategoryIsOther() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(9999L);
        skuItemDto.setAttrItems(Collections.singletonList(new SkuAttrItemDto()));
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.singletonList(9999L));
        String result = vpo.compute(context, param, config);
        assertNull(result);
    }
    @Test
    public void testComputeWhenAttrItemsContainStyleNail() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(COLOR_CATEGORY_ID);
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("category2");
        attrItemDto.setAttrValue(STYLE_NAIL_SECOND_CATEGORY);
        skuItemDto.setAttrItems(Collections.singletonList(attrItemDto));
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.singletonList(COLOR_CATEGORY_ID));
        String result = vpo.compute(context, param, config);
        assertEquals(STYLE_NAIL_SECOND_CATEGORY, result);
    }
    @Test
    public void testComputeWhenAttrItemsContainDiffColorServiceContent() {
        SkuItemDto skuItemDto = new SkuItemDto();
        skuItemDto.setProductCategory(COLOR_CATEGORY_ID);
        SkuAttrItemDto attrItemDto = new SkuAttrItemDto();
        attrItemDto.setAttrName("content");
        attrItemDto.setAttrValue("含跳色");
        skuItemDto.setAttrItems(Collections.singletonList(attrItemDto));
        when(param.getSkuItemDto()).thenReturn(skuItemDto);
        when(config.getSkuCategoryIdWithCustomzedTitle()).thenReturn(Collections.singletonList(COLOR_CATEGORY_ID));
        String result = vpo.compute(context, param, config);
        assertEquals(DIff_COLOR_NAIL, result);
    }
    private static long getPrivateStaticLong(Class<?> clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.getLong(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    private static String getPrivateStaticString(Class<?> clazz, String fieldName) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            return (String) field.get(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}