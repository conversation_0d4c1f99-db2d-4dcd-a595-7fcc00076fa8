package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class EduOnlineDealAddServiceVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsEmpty() {
        // arrange
        EduOnlineDealAddServiceVOListOpt eduOnlineDealAddServiceVOListOpt = new EduOnlineDealAddServiceVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = eduOnlineDealAddServiceVOListOpt.compute(context, param, new EduOnlineDealAddServiceVOListOpt.Config());

        // assert
        assertNull(result);
    }

    /**
     * 测试compute方法，当buildDealDetailStructAttrModuleVO返回的列表不为空时
     */
    @Test
    public void testComputeWhenBuildDealDetailStructAttrModuleVOListIsNotEmpty() {
        // arrange
        AttrM attrM = new AttrM();
        attrM.setName(EduOnlineDealAddServiceVOListOpt.ATTR_ADDITIONAL_SERVICE);
        attrM.setValue(JsonCodec.encodeWithUTF8(Lists.newArrayList("测试1", "测试2")));
        when(param.getDealAttrs()).thenReturn(Lists.newArrayList(attrM));

        EduOnlineDealAddServiceVOListOpt.Config config = new EduOnlineDealAddServiceVOListOpt.Config();
        EduOnlineDealAddServiceVOListOpt eduOnlineDealAddServiceVOListOpt = new EduOnlineDealAddServiceVOListOpt();

        // act
        List<DealDetailStructAttrModuleGroupModel> result = eduOnlineDealAddServiceVOListOpt.compute(context, param, config);

        // assert
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().size() == 2);
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(0).getAttrName().equals("测试1"));
        assertTrue(result.get(0).getDealDetailStructAttrModuleVOS().get(1).getAttrName().equals("测试2"));
        assertTrue(result.get(0).getGroupName().equals(config.getTitle()));
    }
}
