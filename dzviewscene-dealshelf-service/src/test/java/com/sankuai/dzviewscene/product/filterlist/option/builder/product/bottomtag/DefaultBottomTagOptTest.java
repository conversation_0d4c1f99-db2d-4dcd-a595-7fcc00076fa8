package com.sankuai.dzviewscene.product.filterlist.option.builder.product.bottomtag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductBottomTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class DefaultBottomTagOptTest {

    /**
     * 测试 compute 方法是否总是返回 null
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        DefaultBottomTagOpt defaultBottomTagOpt = new DefaultBottomTagOpt();
        ActivityCxt context = new ActivityCxt();
        // Assuming ProductM has a no-argument constructor for simplicity
        ProductM productM = new ProductM();
        String salePrice = "100";
        // Using the builder pattern to create Param instance
        ProductBottomTagsVP.Param param = ProductBottomTagsVP.Param.builder().productM(productM).salePrice(salePrice).build();
        // act
        List<RichLabelVO> result = defaultBottomTagOpt.compute(context, param, null);
        // assert
        assertNull(result);
    }
}
