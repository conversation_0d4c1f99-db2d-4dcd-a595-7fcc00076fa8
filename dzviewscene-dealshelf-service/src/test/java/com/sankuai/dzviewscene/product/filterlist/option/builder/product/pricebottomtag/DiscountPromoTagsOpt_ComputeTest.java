package com.sankuai.dzviewscene.product.filterlist.option.builder.product.pricebottomtag;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceBottomTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DiscountPromoTagsOpt_ComputeTest {

    @Mock
    private ProductM productM;

    @Test
    public void testComputeNotTimesDeal() throws Throwable {
        // arrange
        DiscountPromoTagsOpt discountPromoTagsOpt = new DiscountPromoTagsOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        DiscountPromoTagsOpt.Config config = mock(DiscountPromoTagsOpt.Config.class);
        ProductPriceBottomTagVP.Param param = mock(ProductPriceBottomTagVP.Param.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.isTimesDeal()).thenReturn(false);
        // act
        List<DzTagVO> result = discountPromoTagsOpt.compute(context, param, config);
        // assert
        assertTrue(result == null || result.isEmpty());
    }
}
