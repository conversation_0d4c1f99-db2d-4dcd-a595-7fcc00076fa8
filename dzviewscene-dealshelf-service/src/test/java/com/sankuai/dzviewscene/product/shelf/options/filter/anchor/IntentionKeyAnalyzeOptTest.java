package com.sankuai.dzviewscene.product.shelf.options.filter.anchor;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.dianping.product.shelf.common.enums.NavRouterTypeEnum;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ProductAnchorVP;
import com.sankuai.dzviewscene.product.shelf.common.ProductAnchorInfo;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class IntentionKeyAnalyzeOptTest {

    @Mock
    private ActivityCxt context;

    private IntentionKeyAnalyzeOpt intentionKeyAnalyzeOpt;

    @Before
    public void setUp() {
        intentionKeyAnalyzeOpt = new IntentionKeyAnalyzeOpt();
    }

    @Test
    public void testComputeKeywordIsNullAndConfigIsNull() {
        ProductAnchorInfo result = intentionKeyAnalyzeOpt.compute(context, null, null);
        assertEquals(NavRouterTypeEnum.DEAL_FIRST_NAV_TYPE.getType(), result.getRouterType());
    }

    @Test
    public void testComputeKeywordIsNullAndConfigIsNotNullButRouterTypeIsZero() {
        ProductAnchorInfo result = intentionKeyAnalyzeOpt.compute(context, null, new IntentionKeyAnalyzeOpt.Config());
        assertEquals(NavRouterTypeEnum.DEAL_FIRST_NAV_TYPE.getType(), result.getRouterType());
    }

    @Test
    public void testComputeKeywordIsNullAndConfigIsNotNullAndRouterTypeIsGreaterThanZero() {
        IntentionKeyAnalyzeOpt.Config config = new IntentionKeyAnalyzeOpt.Config();
        config.setRouterType(1);
        ProductAnchorInfo result = intentionKeyAnalyzeOpt.compute(context, null, config);
        assertEquals(1, result.getRouterType());
    }

    @Test
    public void testComputeKeywordIsNotNullAndConfigIsNull() {
        when(context.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("keyword");
        ProductAnchorInfo result = intentionKeyAnalyzeOpt.compute(context, null, null);
        assertEquals(NavRouterTypeEnum.DEAL_FIRST_NAV_TYPE.getType(), result.getRouterType());
    }

    @Test
    public void testComputeKeywordIsNotNullAndConfigIsNotNullButRouterTypeIsZero() {
        when(context.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("keyword");
        ProductAnchorInfo result = intentionKeyAnalyzeOpt.compute(context, null, new IntentionKeyAnalyzeOpt.Config());
        assertEquals(NavRouterTypeEnum.DEAL_FIRST_NAV_TYPE.getType(), result.getRouterType());
    }

    @Test
    public void testComputeKeywordIsNotNullAndConfigIsNotNullAndRouterTypeIsGreaterThanZero() {
        when(context.getParam(ShelfActivityConstants.Params.keyword)).thenReturn("keyword");
        IntentionKeyAnalyzeOpt.Config config = new IntentionKeyAnalyzeOpt.Config();
        config.setRouterType(1);
        ProductAnchorInfo result = intentionKeyAnalyzeOpt.compute(context, null, config);
        assertEquals(1, result.getRouterType());
    }
}
