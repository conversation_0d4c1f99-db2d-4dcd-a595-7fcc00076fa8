package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.util.Collections;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CarDailyMaintainSkuModuleBuilder_BuildTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailAssembleParam assembleParam;

    @Test
    public void testBuildModelVOWhenDealDetailSkuListModuleGroupModelsIsEmpty() {
        // arrange
        CarDailyMaintainSkuModuleBuilder builder = new CarDailyMaintainSkuModuleBuilder();
        when(activityCxt.getSource(CarDailyMaintainSkuModuleBuilder.CODE)).thenReturn(Collections.emptyList());
        // act
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildModelVOWhenGroupModelIsNull() {
        // arrange
        CarDailyMaintainSkuModuleBuilder builder = new CarDailyMaintainSkuModuleBuilder();
        when(activityCxt.getSource(CarDailyMaintainSkuModuleBuilder.CODE)).thenReturn(Collections.singletonList(null));
        // act
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildModelVOWhenDealSkuGroupModuleVOSIsEmpty() {
        // arrange
        CarDailyMaintainSkuModuleBuilder builder = new CarDailyMaintainSkuModuleBuilder();
        when(activityCxt.getSource(CarDailyMaintainSkuModuleBuilder.CODE)).thenReturn(Collections.singletonList(new DealDetailSkuListModuleGroupModel()));
        // act
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildModelVOWhenAllNotNull() {
        // arrange
        CarDailyMaintainSkuModuleBuilder builder = new CarDailyMaintainSkuModuleBuilder();
        DealDetailSkuListModuleGroupModel groupModel = new DealDetailSkuListModuleGroupModel();
        groupModel.setDealSkuGroupModuleVOS(Collections.singletonList(new DealSkuGroupModuleVO()));
        groupModel.setGroupName("groupName");
        when(activityCxt.getSource(CarDailyMaintainSkuModuleBuilder.CODE)).thenReturn(Collections.singletonList(groupModel));
        // act
        DealDetailModuleVO result = builder.buildModelVO(activityCxt, assembleParam, "config");
        // assert
        assertNotNull(result);
        assertEquals("groupName", result.getName());
        assertEquals(1, result.getSkuGroupsModel1().size());
    }
}
