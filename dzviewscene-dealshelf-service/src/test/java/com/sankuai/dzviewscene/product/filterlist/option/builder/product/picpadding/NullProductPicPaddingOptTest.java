package com.sankuai.dzviewscene.product.filterlist.option.builder.product.picpadding;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPicPaddingVP;
import com.sankuai.dzviewscene.product.filterlist.vo.DzProductVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class NullProductPicPaddingOptTest {

    /**
     * 测试 compute 方法，期望返回 null
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        ActivityCxt context = new ActivityCxt();
        DzProductVO dzProductVO = new DzProductVO();
        ProductM productM = new ProductM();
        // Using the builder pattern to create Param instance as direct instantiation is not allowed
        ProductPicPaddingVP.Param param = ProductPicPaddingVP.Param.builder().dzProductVO(dzProductVO).productM(productM).build();
        // act
        Void result = new NullProductPicPaddingOpt().compute(context, param, null);
        // assert
        assertNull(result);
    }
}
