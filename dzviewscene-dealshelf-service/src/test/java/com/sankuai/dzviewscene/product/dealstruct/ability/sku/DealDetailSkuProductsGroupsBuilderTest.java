package com.sankuai.dzviewscene.product.dealstruct.ability.sku;

import com.alibaba.fastjson.JSON;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.DefaultNoStructDealSkuGroupBuildOpt;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试DealDetailSkuProductsGroupsBuilder的build方法
 */
@RunWith(MockitoJUnitRunner.class)
public class DealDetailSkuProductsGroupsBuilderTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private DealDetailSkuProductsGroupsParam skuProductsGroupsParam;
    @Mock
    private DealDetailSkuProductsGroupsCfg skuProductsGroupsCfg;
    @Mock
    private DealDetailFetcher dealDetailFetcher;

    @Mock
    private PmfExecutionHelper pmfExecutionHelper;
    @InjectMocks
    private DealDetailSkuProductsGroupsBuilder builder;

    private MockedStatic<DealDetailUtils> dealDetailUtilsMockedStatic;

    @Before
    public void setUp() {
        dealDetailUtilsMockedStatic = Mockito.mockStatic(DealDetailUtils.class);
        when(pmfExecutionHelper.findVPoint(any(), eq(DealDetailSkuProductsGroupsBuilder.CODE) , eq("NoStructDealSkuGroupBuildVP"))).thenReturn(new DefaultNoStructDealSkuGroupBuildOpt());
    }

    @After
    public void tearDown(){
        dealDetailUtilsMockedStatic.close();
    }

    /**
     * 测试dealDetailInfoModels为空时的场景
     */
    @Test
    public void testBuildWithEmptyDealDetailInfoModels() throws Throwable {
        // arrange
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());

        // act
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);

        // assert
        assertTrue("结果应该是空的列表", result.get().isEmpty());
    }

    /**
     * 测试dealDetailInfoModels不为空但无有效DealDetail的场景
     */
    @Test
    public void testBuildWithInvalidDealDetailInfoModels() throws Throwable {
        // arrange
        DealDetailInfoModel detailInfoModel = mock(DealDetailInfoModel.class);
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(detailInfoModel));
        dealDetailUtilsMockedStatic.when(()-> DealDetailUtils.isWearableNail(anyList())).thenReturn(false);
//        when(builder.isValidDealDetail(detailInfoModel)).thenReturn(false);

        // act
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);

        // assert
        assertNotNull("结果不应该为null", result.get());
        assertTrue("结果应该是空的列表", result.get().isEmpty());
    }

    /**
     * 测试dealDetailInfoModels不为空且有有效DealDetail但enableAdaptNoStructDeal为false的场景
     */
    @Test
    public void testBuildWithValidDealDetailInfoModelsAndAdaptNoStructDealDisabled() throws Throwable {
        // arrange
        DealDetailInfoModel detailInfoModel = mock(DealDetailInfoModel.class);
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(detailInfoModel));
        dealDetailUtilsMockedStatic.when(()-> DealDetailUtils.isWearableNail(anyList())).thenReturn(true);
        when(skuProductsGroupsCfg.isEnableAdaptNoStructDeal()).thenReturn(false);
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        dealDetailDtoModel.setSkuUniStructuredDto(dealDetailSkuUniStructuredDto);
        when(detailInfoModel.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);
        // act
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);

        // assert
        assertNotNull("结果不应该为null", result.get());
        assertTrue("结果应该是空的列表", result.get().isEmpty());
    }

    /**
     * 测试dealDetailInfoModels不为空且有有效DealDetail且enableAdaptNoStructDeal为true的场景
     */
    @Test
    public void testBuildWithValidDealDetailInfoModelsAndAdaptNoStructDealEnabled() throws Throwable {
        // arrange
        DealDetailInfoModel detailInfoModel = mock(DealDetailInfoModel.class);
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(detailInfoModel));
        dealDetailUtilsMockedStatic.when(()-> DealDetailUtils.isWearableNail(anyList())).thenReturn(true);
        when(skuProductsGroupsCfg.isEnableAdaptNoStructDeal()).thenReturn(true);
        DealDetailDtoModel dealDetailDtoModel = new DealDetailDtoModel();
        DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto = new DealDetailSkuUniStructuredDto();
        dealDetailDtoModel.setSkuUniStructuredDto(dealDetailSkuUniStructuredDto);
        when(detailInfoModel.getDealDetailDtoModel()).thenReturn(dealDetailDtoModel);

        // act
        CompletableFuture<List<List<DealSkuGroupModuleVO>>> result = builder.build(activityCxt, skuProductsGroupsParam, skuProductsGroupsCfg);

        // assert
        assertNotNull("结果不应该为null", result.get());
        assertTrue("结果不应该是空的列表", result.get().isEmpty());
    }

    /**
     * 测试convertWearableNailSkuItemDto方法，正常情况
     */
    @Test
    public void testConvertWearableNailSkuItemDto_Normal() {
        DealDetailSkuProductsGroupsCfg.SkuAttrGroup group = new DealDetailSkuProductsGroupsCfg.SkuAttrGroup();
        group.setItemName("TestItem");
        DealDetailSkuProductsGroupsCfg.SkuAttrDTO attrDTO = new DealDetailSkuProductsGroupsCfg.SkuAttrDTO();
        attrDTO.setAttrName("Color");
        attrDTO.setCnName("颜色");
        attrDTO.setSeparator(",");
        attrDTO.setDesc("%d种颜色");
        group.setIncludeAttrName(Lists.newArrayList(attrDTO));

        List<AttrM> dealAttrs = Lists.newArrayList(new AttrM("Color", JSON.toJSONString(Arrays.asList("Red", "Blue"))));

        dealDetailUtilsMockedStatic.when(() -> DealDetailUtils.getAttrSingleValueByAttrName(any(), any())).thenReturn("[\"Red\",\"Blue\"]");
        SkuItemDto result = builder.convertWearableNailSkuItemDto(group, dealAttrs);

        assertNotNull(result);
        assertEquals("TestItem", result.getName());
        assertEquals(1, result.getAttrItems().size());
        SkuAttrItemDto attrItemDto = result.getAttrItems().get(0);
        assertEquals("Color", attrItemDto.getAttrName());
        assertEquals("颜色", attrItemDto.getChnName());
        assertEquals("Red,Blue 2种颜色", attrItemDto.getAttrValue());
    }
}
