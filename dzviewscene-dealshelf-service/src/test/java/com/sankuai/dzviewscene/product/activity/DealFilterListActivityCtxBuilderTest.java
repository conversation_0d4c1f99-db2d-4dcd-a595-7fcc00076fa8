package com.sankuai.dzviewscene.product.activity;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.gson.Gson;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.ActivityRequest;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivity;
import com.sankuai.dzviewscene.product.filterlist.acitivity.DealFilterListActivityCtxBuilder;
import com.sankuai.dzviewscene.productshelf.nr.atom.AtomFacadeService;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.mtstore.aggregate.thrift.dto.resp.GetJumpUrlRespDTO;
import com.sankuai.mtstore.aggregate.thrift.dto.resp.GetStoreMainRecommendConfigRespDTO;
import net.sf.oval.constraint.AssertNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DealFilterListActivityCtxBuilderTest {

    @InjectMocks
    private DealFilterListActivityCtxBuilder dealFilterListActivityCtxBuilder;

    @Mock
    private ActivityRequest activityRequest;

    @Mock
    private JsonCodec jsonCodec;

    @Mock
    private CompositeAtomService compositeAtomService;

    private ActivityCxt activityContext;
    @Mock
    private ActivityCxt mockActivityContext;

    @Before
    public void setUp() {
        activityContext = new ActivityCxt();
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testBuildParamsIsNull() throws Throwable {
        when(activityRequest.getParams()).thenReturn(new HashMap<>());
        ActivityCxt result = dealFilterListActivityCtxBuilder.build(activityRequest);
        assertNotNull("ActivityCxt should not be null when params are empty", result);
    }

    @Test
    public void testBuildSpaceKeyInSpecialSpaceKeys() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("spaceKey", "SHOP_DEAL_SHELF_LANDING");
        when(activityRequest.getParams()).thenReturn(params);
        ActivityCxt result = dealFilterListActivityCtxBuilder.build(activityRequest);
        assertNotNull("ActivityCxt should not be null when spaceKey is in special space keys", result);
    }

    @Test
    public void testBuildSpaceKeyIsFlagshipStoreShelf() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("spaceKey", "FLAGSHIP_STORE_SHELF");
        when(activityRequest.getParams()).thenReturn(params);
        ActivityCxt result = dealFilterListActivityCtxBuilder.build(activityRequest);
        assertNotNull("ActivityCxt should not be null when spaceKey is FLAGSHIP_STORE_SHELF", result);
    }

    @Test
    public void testBuildNeedLongPoiProcess() throws Throwable {
        Map<String, Object> params = new HashMap<>();
        params.put("spaceKey", "SHOP_DEAL_SHELF_LANDING");
        when(activityRequest.getParams()).thenReturn(params);
        ActivityCxt result = dealFilterListActivityCtxBuilder.build(activityRequest);
        assertNotNull("ActivityCxt should not be null when needing long POI process", result);
    }

    @Test
    public void testBuildNotNeedLongPoiProcess() throws Throwable {
        int platform = 202;
        int shopId = 82312294;
        com.sankuai.athena.viewscene.framework.ActivityRequest activityRequest = new com.sankuai.athena.viewscene.framework.ActivityRequest();
        activityRequest.addParam(ShelfActivityConstants.Params.spaceKey,
                DealFilterListActivity.SpaceKey.DEFAULT_DEAL_FILTER_LIST);
        activityRequest.setActivityCode(DealFilterListActivity.CODE);
        activityRequest.addParam(ShelfActivityConstants.Params.userAgent, platform);
        activityRequest.addParam(ShelfActivityConstants.Params.appVersion, "12.35.6");
        activityRequest.addParam(ShelfActivityConstants.Params.unionId,
                "3e9f3d51f9f14b39b39fa0328f097ba3a155600291719487416");
        activityRequest.addParam(ShelfActivityConstants.Params.platform, VCClientTypeEnum.isMtClientTypeByCode(platform)
                ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            activityRequest.addParam(ShelfActivityConstants.Params.mtCityId, 8000);
            activityRequest.addParam(ShelfActivityConstants.Params.mtPoiId, shopId);
        } else {
            activityRequest.addParam(ShelfActivityConstants.Params.dpCityId, 1);
            activityRequest.addParam(ShelfActivityConstants.Params.dpPoiId, shopId);
        }
        activityRequest.addParam(ShelfActivityConstants.Params.extra,
                "{\"uuid\":\"18f7f3592f2c8-0338da36bb4492-5b164600-505c8-18f7f3592f3c8\",\"ip\":\"\",\"app\":-1,\"partner\":28,\"platform\":13,\"dfpid\":\"uu039259267u5wvw1032uvvw4v4008v881u2u64580997958w7z92829\",\"mobile\":\"\",\"versionNum\":2,\"h5Fingerprint\":\"H5dfp_2.4.0_tttt_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\",\"appid\":\"wxde8ac0a21135c07d\",\"openid\":\"oJVP50EWHZz9RzRb9ySCgdmzDZRI\",\"location\":\"{\\\"latitude\\\":31.27368337539473,\\\"longitude\\\":121.5244786971069}\"}\");\n");
        activityRequest.addParam(ShelfActivityConstants.Params.mtgsig,
                "%7B%22a1%22%3A%221.1%22%2C%22a2%22%3A1716435654567%2C%22a3%22%3A%22uu039259267u5wvw1032uvvw4v4008v881u2u64580997958w7z92829%22%2C%22a5%22%3A%22uRz6QkhzR7dYocRjFsx5dE8jYcKbyewkjW%3D%3D%22%2C%22a6%22%3A%22hs1.4a4gsvX1s4RLQYqBR3sFhAZPY1ahJ5SRcFN4zTsfoMiLmrX74U1%2B%2FBfFL42xjadKuPsOmBxT2Ka3OKee9NaWdFj36Y7Ccry2%2B1g98SieXKpI%3D%22%2C%22x0%22%3A4%2C%22d1%22%3A%22eed60cc8d2254619801b91533cf34adc%22%7D");
        activityRequest.addParam(ShelfActivityConstants.Params.clientType, "ios");
        activityRequest.addParam(ShelfActivityConstants.Params.pageSource, "source%253Dnewyouhuima");
        activityRequest.addParam(ShelfActivityConstants.Params.sceneCode, "activity_beauty_medical_coupon_shelf");
        activityRequest.addParam(ShelfActivityConstants.Params.channel,
                ShelfActivityConstants.ChannelType.dealFilterList);

        ActivityCxt activityContext = new ActivityCxt();
        activityContext.setParameters(activityRequest.getParams());
        activityContext.setActivityCode(activityRequest.getActivityCode());
        dealFilterListActivityCtxBuilder.addRiskParam(activityContext);
        assertNotNull("ActivityCxt should not be null when not needing long POI process", activityContext.getParam(ShelfActivityConstants.Params.riskParam));
    }

    /**
     * 测试addPlatformParam方法，当平台为美团且CompletableFuture执行异常时
     */
    @Test(expected = Exception.class)
    public void testAddPlatformParam_WhenPlatformIsMeituanAndFutureThrowsException() throws Throwable {
        // arrange
        ActivityCxt activityContext = new ActivityCxt();
        HashMap<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.platform, 1); // 美团平台
        params.put(ShelfActivityConstants.Params.mtPoiId, 789);
        params.put(ShelfActivityConstants.Params.mtCityId, 101112);
        activityContext.setParameters(params);
        DealFilterListActivityCtxBuilder ctxBuilder = new DealFilterListActivityCtxBuilder();
        CompositeAtomService compositeAtomService = Mockito.mock(CompositeAtomService.class);
        ReflectionTestUtils.setField(ctxBuilder, "compositeAtomService", compositeAtomService);
        CompletableFuture<List<Integer>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Test exception"));
        //when(compositeAtomService.batchGetDpByMtId(anyInt())).thenReturn(failedFuture);

        // act
        ReflectionTestUtils.invokeMethod(ctxBuilder, "addPlatformParam", activityContext);
    }



    /**
     * 测试 build 方法当 params 为空且场景代码不是优惠码场景时
     */
    @Test
    public void testBuildWithNonCouponSceneCode() throws Throwable {
        // arrange
        HashMap<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.sceneCode, "non_coupon_scene");
        when(activityRequest.getParams()).thenReturn(params);

        // act
        ActivityCxt result = dealFilterListActivityCtxBuilder.build(activityRequest);

        // assert
        assertNotNull("ActivityCxt should not be null with non-coupon scene code", result);
    }

    /**
     * 测试addRiskParam方法，当extra参数为空时，不应添加riskParam
     */
    @Test
    public void testAddRiskParamWithEmptyExtra() {
        // arrange
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.extra, "");

        // act
        dealFilterListActivityCtxBuilder.addRiskParam(activityContext);

        // assert
        assertNull("riskParam should not be added when extra is empty", activityContext.getParam(ShelfActivityConstants.Params.riskParam));
    }

    /**
     * 测试addRiskParam方法，当extra参数非空且mtgsig参数非空时，应正确添加riskParam
     */
    @Test
    public void testAddRiskParamWithNonEmptyExtraAndMtgsig() {
        // arrange
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.extra, "{\"key\":\"value\"}");
        activityContext.addParam(ShelfActivityConstants.Params.mtgsig, "mtgsigValue");

        // act
        dealFilterListActivityCtxBuilder.addRiskParam(activityContext);

        // assert
        assertNotNull("riskParam should be added when extra and mtgsig are non-empty", activityContext.getParam(ShelfActivityConstants.Params.riskParam));
    }

    /**
     * 测试addRiskParam方法，当extra参数非空但格式错误时，不应添加riskParam
     */
    @Test
    public void testAddRiskParamWithInvalidExtraFormat() {
        // arrange
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.extra, "invalidJson");

        // act
        dealFilterListActivityCtxBuilder.addRiskParam(activityContext);

        // assert
        assertNull("riskParam should not be added when extra format is invalid", activityContext.getParam(ShelfActivityConstants.Params.riskParam));
    }

    /**
     * 测试addRiskParam方法，当extra参数非空且格式正确但mtgsig参数为空时，应正确添加riskParam但不包含mtgsig
     */
    @Test
    public void testAddRiskParamWithNonEmptyExtraAndEmptyMtgsig() {
        // arrange
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.extra, "{\"key\":\"value\"}");

        // act
        dealFilterListActivityCtxBuilder.addRiskParam(activityContext);

        // assert
        assertNotNull("riskParam should be added when extra is non-empty and mtgsig is empty", activityContext.getParam(ShelfActivityConstants.Params.riskParam));
    }

    /**
     * 测试当extra参数为空时，不进行任何操作
     */
    @Test
    public void testAddPromoShelfExtraParams_WithEmptyExtra() {
        // arrange
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn("");

        // act
        dealFilterListActivityCtxBuilder.addPromoShelfExtraParams(mockActivityContext);

        // assert
        verify(mockActivityContext, never()).addParam(anyString(), any());
    }

    /**
     * 测试当extra参数解析后为空Map时，不进行任何操作
     */
    @Test
    public void testAddPromoShelfExtraParams_WithEmptyMapExtra() {
        // arrange
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn("%7B%7D"); // URL编码的"{}"

        // act
        dealFilterListActivityCtxBuilder.addPromoShelfExtraParams(mockActivityContext);

        // assert
        verify(mockActivityContext, never()).addParam(anyString(), any());
    }

    /**
     * 测试当extra参数包含source和codeKey时，只设置source
     */
    @Test
    public void testAddPromoShelfExtraParams_WithSourceAndCodeKey() {
        // arrange
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("source", 1);
        extraMap.put("codeKey", "someCodeKey");
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);

        // act
        dealFilterListActivityCtxBuilder.addPromoShelfExtraParams(mockActivityContext);

        // assert
        verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), anyMap());

    }

    /**
     * 测试当extra参数不包含source但包含codeKey时，设置pass_param
     */
    @Test
    public void testAddPromoShelfExtraParams_WithCodeKeyOnly() {
        // arrange
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("codeKey", "someCodeKey");
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);

        // act
        dealFilterListActivityCtxBuilder.addPromoShelfExtraParams(mockActivityContext);

        // assert
        verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), anyMap());
    }

    /**
     * 测试当extra参数包含mmcinflate时，设置mmcinflate
     */
    @Test
    public void testAddPromoShelfExtraParams_WithMmcinflate() {
        // arrange
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("mmcinflate", true);
        String extraJson = new Gson().toJson(extraMap);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.extra)).thenReturn(extraJson);
        when(mockActivityContext.getParam(ShelfActivityConstants.Params.userAgent)).thenReturn(100);

        // act
        dealFilterListActivityCtxBuilder.addPromoShelfExtraParams(mockActivityContext);

        // assert
        verify(mockActivityContext).addParam(eq(ShelfActivityConstants.Params.promoCodeExtraInfo), anyMap());
    }


    @Mock
    private AtomFacadeService facadeService;

    @Test
    public void testAddFlagshipStoreShelfLaunchParamNull() {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.entityId, "123");
        activityContext.addParam(ShelfActivityConstants.Params.platform, 1);
        when(facadeService.getStoreMainRecommendConfig(any())).thenReturn(CompletableFuture.completedFuture(new GetStoreMainRecommendConfigRespDTO()));
        when(facadeService.getJumpUrlSuffixByComponents(any())).thenReturn(CompletableFuture.completedFuture(null));
        dealFilterListActivityCtxBuilder.addFlagshipStoreShelfLaunchParam(activityContext);
        Assert.assertNull(activityContext.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl));
    }

    @Test
    public void testAddFlagshipStoreShelfLaunchParam() {
        ActivityCxt activityContext = new ActivityCxt();
        activityContext.addParam(ShelfActivityConstants.Params.entityId, "123");
        activityContext.addParam(ShelfActivityConstants.Params.platform, 1);
        when(facadeService.getStoreMainRecommendConfig(any())).thenReturn(CompletableFuture.completedFuture(new GetStoreMainRecommendConfigRespDTO()));
        GetJumpUrlRespDTO getJumpUrlRespDTO = new GetJumpUrlRespDTO();
        getJumpUrlRespDTO.setJumpUrl("url");
        when(facadeService.getJumpUrlSuffixByComponents(any())).thenReturn(CompletableFuture.completedFuture(getJumpUrlRespDTO));
        dealFilterListActivityCtxBuilder.addFlagshipStoreShelfLaunchParam(activityContext);
        Assert.assertEquals(activityContext.getParam(ShelfActivityConstants.Ctx.ctxFlagshipStoreUrl), "url");
    }
}