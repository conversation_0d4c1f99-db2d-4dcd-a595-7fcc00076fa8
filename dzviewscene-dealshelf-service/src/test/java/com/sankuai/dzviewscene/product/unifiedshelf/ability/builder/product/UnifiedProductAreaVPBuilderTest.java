package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.itemmarketprice.DefaultMarketPriceOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.jumpurl.UnifiedShelfItemDefaultJumpUrlOpt;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.vp.*;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Test;

public class UnifiedProductAreaVPBuilderTest {

    @Test
    public void test() {

        ProductAreaMoreJumpUrlVP.Param build = ProductAreaMoreJumpUrlVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test1() {

        UnifiedShelfItemActivityVP.Param build = UnifiedShelfItemActivityVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test2() {

        UnifiedShelfItemAvailableVP.Param build = UnifiedShelfItemAvailableVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test3() {

        UnifiedShelfItemButtonVP.Param build = UnifiedShelfItemButtonVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test4() {

        UnifiedShelfItemExtraVP.Param build = UnifiedShelfItemExtraVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test5() {

        UnifiedShelfItemJumpUrlVP.Param build = UnifiedShelfItemJumpUrlVP.Param.builder().build();
        build.setProductM(new ProductM());
        UnifiedShelfItemJumpUrlVP vp = new UnifiedShelfItemDefaultJumpUrlOpt();
        vp.compute(new ActivityCxt(), build, null);
        Assert.assertNotNull(build);
    }

    @Test
    public void test6() {
        UnifiedShelfItemMarketPriceVP vp = new DefaultMarketPriceOpt();
        System.out.println(vp.getVPointOptionCode());
        UnifiedShelfItemMarketPriceVP.Param build = UnifiedShelfItemMarketPriceVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test7() {
        UnifiedShelfItemOceanLabsVP.Param build = UnifiedShelfItemOceanLabsVP.Param.builder()
                .productM(null).build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test8() {

        UnifiedShelfItemPicFloatTagVP.Param build = UnifiedShelfItemPicFloatTagVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test9() {

        UnifiedShelfItemPicVP.Param build = UnifiedShelfItemPicVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test10() {

        UnifiedShelfItemPriceBottomTagVP.Param build = UnifiedShelfItemPriceBottomTagVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test11() {

        UnifiedShelfItemPromoTagVP.Param build = UnifiedShelfItemPromoTagVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test12() {

        UnifiedShelfItemSalePriceVP.Param build = UnifiedShelfItemSalePriceVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test13() {

        UnifiedShelfItemSubTitleVP.Param build = UnifiedShelfItemSubTitleVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test14() {

        UnifiedShelfItemTitleVP.Param build = UnifiedShelfItemTitleVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test15() {
        UnifiedShelfSalePricePrefixVP vp = new UnifiedShelfSalePricePrefixVP() {
            @Override
            public Object compute(ActivityCxt context, Object o, Object o2) {
                return null;
            }
        };
        UnifiedShelfSalePricePrefixVP.Param build = UnifiedShelfSalePricePrefixVP.Param.builder()
                .productM(null).build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test16() {

        UnifiedShelfSalePriceSuffixVP.Param build = UnifiedShelfSalePriceSuffixVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test17() {

        ProductAreaDefaultShowNumVP.Param build = ProductAreaDefaultShowNumVP.Param.builder().build();
        Assert.assertNotNull(build);
    }

    @Test
    public void test18() {

        UnifiedShelfItemSpecialTagVP.Param build = UnifiedShelfItemSpecialTagVP.Param.builder().build();
        Assert.assertNotNull(build);
    }
}
