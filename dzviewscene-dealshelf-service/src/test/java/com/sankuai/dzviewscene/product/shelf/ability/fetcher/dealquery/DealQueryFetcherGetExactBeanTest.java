package com.sankuai.dzviewscene.product.shelf.ability.fetcher.dealquery;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.lang.reflect.Field;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;

@RunWith(MockitoJUnitRunner.class)
public class DealQueryFetcherGetExactBeanTest {

    @Mock
    private DefaultListableBeanFactory beanFactory;

    @Test(expected = NullPointerException.class)
    public void testGetExactBeanWhenBeanFactoryIsNull() throws Throwable {
        DealQueryFetcher.getExactBean(null, String.class);
    }

    @Test(expected = NullPointerException.class)
    public void testGetExactBeanWhenRequiredTypeIsNull() throws Throwable {
        DealQueryFetcher.getExactBean(beanFactory, null);
    }

    @Test(expected = NoSuchBeanDefinitionException.class)
    public void testGetExactBeanWhenNoMatchingBean() throws Throwable {
        when(beanFactory.getBeanNamesForType(String.class)).thenReturn(new String[0]);
        DealQueryFetcher.getExactBean(beanFactory, String.class);
    }

    @Test
    public void testGetExactBeanWhenMatchingBeanExists() throws Throwable {
        when(beanFactory.getBeanNamesForType(String.class)).thenReturn(new String[] { "bean1", "bean2" });
        doReturn(String.class).when(beanFactory).getType("bean1");
        when(beanFactory.getBean("bean1", String.class)).thenReturn("test");
        Object result = DealQueryFetcher.getExactBean(beanFactory, String.class);
        assertEquals("test", result);
    }
}
