package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembottomtags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemBottomTagsVP.Param;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembottomtags.ItemBottomTagsWithPromoOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductPriceM;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class ItemBottomTagsWithPromoOpt_ComputeTest {

    private ItemBottomTagsWithPromoOpt itemBottomTagsWithPromoOpt;

    private ActivityCxt mockActivityCxt;

    private Param mockParam;

    private Config mockConfig;

    private ProductM mockProductM;

    @Test
    public void testComputeConfigIsNull() throws Throwable {
        ItemBottomTagsWithPromoOpt itemBottomTagsWithPromoOpt = new ItemBottomTagsWithPromoOpt();
        ActivityCxt mockActivityCxt = mock(ActivityCxt.class);
        Param mockParam = mock(Param.class);
        List<RichLabelVO> result = itemBottomTagsWithPromoOpt.compute(mockActivityCxt, mockParam, null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
