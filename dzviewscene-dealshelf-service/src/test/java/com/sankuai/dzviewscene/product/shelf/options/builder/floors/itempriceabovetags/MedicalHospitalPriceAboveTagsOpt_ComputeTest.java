package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MedicalHospitalPriceAboveTagsOpt_ComputeTest {

    @InjectMocks
    private MedicalHospitalPriceAboveTagsOpt opt;

    @Mock
    private ActivityCxt context;

    /**
     * 测试 ProductM 为 null 的情况
     */
    @Test
    public void testComputeProductMIsNull() throws Throwable {
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(null).build();
        MedicalHospitalPriceAboveTagsOpt.Config config = new MedicalHospitalPriceAboveTagsOpt.Config();
        List<DzTagVO> result = opt.compute(context, param, config);
        Assert.assertNull(result);
    }

    /**
     * 测试 DouHuM 为 null 的情况
     */
    @Test
    public void testComputeDouHuMIsNull() throws Throwable {
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(new ProductM()).build();
        MedicalHospitalPriceAboveTagsOpt.Config config = new MedicalHospitalPriceAboveTagsOpt.Config();
        List<DzTagVO> result = opt.compute(context, param, config);
        Assert.assertNull(result);
    }

    /**
     * 测试 DouHuM 的 sk 包含 "a" 的情况
     */
    @Test
    public void testComputeDouHuMSkContainsA() throws Throwable {
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(new ProductM()).build();
        MedicalHospitalPriceAboveTagsOpt.Config config = new MedicalHospitalPriceAboveTagsOpt.Config();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("a");
        douHuM.setExpId("exp003239");
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        when(context.getSource(any())).thenReturn(douHuList);
        List<DzTagVO> result = opt.compute(context, param, config);
        Assert.assertNull(result);
    }

    /**
     * 测试 DouHuM 的 sk 包含 "b" 的情况
     */
    @Test
    public void testComputeDouHuMSkContainsB() throws Throwable {
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(new ProductM()).build();
        MedicalHospitalPriceAboveTagsOpt.Config config = new MedicalHospitalPriceAboveTagsOpt.Config();
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("b");
        douHuM.setExpId("exp003239");
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        when(context.getSource(any())).thenReturn(douHuList);
        List<DzTagVO> result = opt.compute(context, param, config);
        Assert.assertNotNull(result);
    }

    /**
     * 测试 serviceType 不在 config.getExamServiceType() 和 config.getHealthyServiceType() 中的情况
     */
    @Test
    public void testComputeServiceTypeNotInExamServiceTypeAndHealthyServiceType() throws Throwable {
        ProductM productM = new ProductM();
        productM.setExtAttrs(Arrays.asList(new AttrM("service_type", "其他服务")));
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(productM).build();
        MedicalHospitalPriceAboveTagsOpt.Config config = new MedicalHospitalPriceAboveTagsOpt.Config();
        config.setExamServiceType("入职体检");
        config.setHealthyServiceType("健康证检查");
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("b");
        douHuM.setExpId("exp003239");
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        when(context.getSource(any())).thenReturn(douHuList);
        List<DzTagVO> result = opt.compute(context, param, config);
        Assert.assertNotNull(result);
    }

    /**
     * 测试 serviceType 等于 config.getExamServiceType() 的情况
     */
    @Test
    public void testComputeServiceTypeEqualsExamServiceType() throws Throwable {
        ProductM productM = new ProductM();
        productM.setExtAttrs(Arrays.asList(new AttrM("service_type", "入职体检")));
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(productM).build();
        MedicalHospitalPriceAboveTagsOpt.Config config = new MedicalHospitalPriceAboveTagsOpt.Config();
        config.setExamServiceType("入职体检");
        config.setHealthyServiceType("健康证检查");
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("b");
        douHuM.setExpId("exp003239");
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        when(context.getSource(any())).thenReturn(douHuList);
        List<DzTagVO> result = opt.compute(context, param, config);
        Assert.assertNotNull(result);
    }

    /**
     * 测试 serviceType 等于 config.getHealthyServiceType() 的情况
     */
    @Test
    public void testComputeServiceTypeEqualsHealthyServiceType() throws Throwable {
        ProductM productM = new ProductM();
        productM.setExtAttrs(Arrays.asList(new AttrM("service_type", "健康证检查")));
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(productM).build();
        MedicalHospitalPriceAboveTagsOpt.Config config = new MedicalHospitalPriceAboveTagsOpt.Config();
        config.setExamServiceType("入职体检");
        config.setHealthyServiceType("健康证检查");
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("b");
        douHuM.setExpId("exp003239");
        List<DouHuM> douHuList = new ArrayList<>();
        douHuList.add(douHuM);
        when(context.getSource(any())).thenReturn(douHuList);
        List<DzTagVO> result = opt.compute(context, param, config);
        Assert.assertNotNull(result);
    }
}
