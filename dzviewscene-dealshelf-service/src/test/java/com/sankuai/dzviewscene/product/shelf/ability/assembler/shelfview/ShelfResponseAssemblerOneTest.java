package com.sankuai.dzviewscene.product.shelf.ability.assembler.shelfview;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.execution.PmfExecutionHelper;
import com.sankuai.dzviewscene.product.shelf.ability.builder.shelfshowtype.FilterTypeTypeVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.shelfshowtype.ShelfVersionShelfFilterTypeOpt;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ShelfResponseAssemblerOneTest {

    @InjectMocks
    private ShelfResponseAssembler shelfResponseAssembler;

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private ShelfResponseAssembler.Request request;
    @Mock
    private ShelfResponseAssembler.Config config;
    @Mock
    private FilterTypeTypeVP filterTypeTypeVP;

    @Mock
    private PmfExecutionHelper pmfExecutionHelper;

    /**
     * 测试getFilterType方法，当findVPoint返回非null但VPointOptionCode为null时
     */
    @Test
    public void testGetFilterTypeWhenVPointOptionCodeIsNull() {
        when(pmfExecutionHelper.findVPoint(activityCxt, ShelfResponseAssembler.CODE,FilterTypeTypeVP.CODE)).thenReturn(filterTypeTypeVP);
        when(filterTypeTypeVP.getVPointOptionCode()).thenReturn(null);
        when(config.getFilterType()).thenReturn(2);

        int result = shelfResponseAssembler.getFilterType(activityCxt, request, config);

        assertEquals(2, result);
    }

    /**
     * 测试getFilterType方法，当execute返回非null时
     */
    @Test
    public void testGetFilterTypeWhenExecuteReturnsNonNull() {
        when(pmfExecutionHelper.findVPoint(activityCxt, ShelfResponseAssembler.CODE,FilterTypeTypeVP.CODE)).thenReturn(new ShelfVersionShelfFilterTypeOpt());

        int result = shelfResponseAssembler.getFilterType(activityCxt, request, config);

        assertEquals(0, result);
    }

}
