package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.specialTags.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzviewscene.dealshelf.shelfvo.RichLabelModel;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfTagVO;
import com.sankuai.dzviewscene.product.unifiedshelf.enums.RichLabelStyleEnum;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class SpecialTagsUtilsTest {

    /**
     * 测试 buildShelfTagVO 方法，正常情况
     */
    @Test
    public void testBuildShelfTagVONormal() {
        // arrange
        String tagName = "测试标签";
        String textColor = "#FFFFFF";
        // act
        ShelfTagVO result = SpecialTagsUtils.buildShelfTagVO(tagName, textColor);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(tagName, result.getText().getText());
        Assert.assertEquals(Lists.newArrayList(tagName), result.getText().getMultiText());
        Assert.assertEquals(textColor, result.getText().getTextColor());
        Assert.assertEquals(RichLabelStyleEnum.BUBBLE.getType(), result.getText().getStyle());
    }

    /**
     * 测试buildShelfTagVO方法，使用空字符串参数
     */
    @Test
    public void testBuildShelfTagVOEmptyString() {
        // arrange
        String tagName = "";
        String background = "";
        String textColor = "";
        // 假设这是一个有效的样式代码
        int style = 1;
        // act
        ShelfTagVO result = SpecialTagsUtils.buildShelfTagVO(tagName, background, textColor, style);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(tagName, result.getText().getText());
        Assert.assertEquals(Lists.newArrayList(tagName), result.getText().getMultiText());
        Assert.assertEquals(textColor, result.getText().getTextColor());
        Assert.assertEquals(background, result.getText().getBackgroundColor());
        Assert.assertEquals(style, result.getText().getStyle());
    }
}
