package com.sankuai.dzviewscene.product.productdetail.ability.bottomBar;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.productdetail.ability.bottomBar.DetailBottomBarCfg;
import com.sankuai.dzviewscene.product.productdetail.ability.bottomBar.vpoints.BottomBarSingleButtonVP;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.DetailModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.BottomBarModuleVO;
import com.sankuai.dzviewscene.product.productdetail.gateways.vo.common.ButtonVO;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DetailBottomBarBuilderBuildTest {

    @Mock
    private ActivityCxt ctx;

    @Mock
    private DetailBottomBarCfg cfg;

    private DetailBottomBarBuilder builder = new DetailBottomBarBuilder();

    /**
     * Test when ctx is null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildCtxNull() throws Throwable {
        // Act
        builder.build(null, null, cfg);
    }

    /**
     * Test when cfg is null.
     */
    @Test(expected = NullPointerException.class)
    public void testBuildCfgNull() throws Throwable {
        // Act
        builder.build(ctx, null, null);
    }
}
