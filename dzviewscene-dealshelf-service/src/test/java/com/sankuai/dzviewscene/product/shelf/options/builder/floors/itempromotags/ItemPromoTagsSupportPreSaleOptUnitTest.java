package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPromoTagsVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.util.List;
import java.util.Objects;

/**
 * @auther: liweilong06
 * @date: 2023/3/16 下午4:04
 */
public class ItemPromoTagsSupportPreSaleOptUnitTest {

    private ItemPromoTagsSupportPreSaleOpt opt;
    private ItemPromoTagsSupportPreSaleOpt.Config config;
    private ProductM productM;

    @Before
    public void init() {
        productM = buildProductM();
        opt = new ItemPromoTagsSupportPreSaleOpt();
        config = new ItemPromoTagsSupportPreSaleOpt.Config();
    }

    //@Test
    public void test_give_null_return_null() {
        productM.setPromoPrices(null);
        Assert.assertTrue(CollectionUtils.isEmpty(opt.compute(null, ItemPromoTagsVP.Param.builder().productM(productM).build(), config)));
    }

    //@Test
    public void test_give_normalPromo_return_promo() {
        productM.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals("preSaleTag"))
                .forEach(attrM -> attrM.setValue("false"));
        List<DzPromoVO> promoVOList = opt.compute(null, ItemPromoTagsVP.Param.builder().productM(productM).build(), config);
        Assert.assertTrue(CollectionUtils.size(promoVOList) == 1);
        DzPromoVO dzPromoVO = promoVOList.get(0);
        Assert.assertTrue(dzPromoVO.getDetail() != null && dzPromoVO.getPrePic() == null && dzPromoVO.getDetail().getPopType() == 1);
        Assert.assertTrue("提前买共省￥218".equals(dzPromoVO.getName()));
    }

    //@Test
    public void test_give_preSalePromo_return_promo() {
        List<DzPromoVO> promoVOList = opt.compute(null, ItemPromoTagsVP.Param.builder().productM(productM).build(), config);
        Assert.assertTrue(CollectionUtils.size(promoVOList) == 1);
        DzPromoVO dzPromoVO = promoVOList.get(0);
        Assert.assertTrue(dzPromoVO.getDetail() == null && dzPromoVO.getPrePic() != null);
        Assert.assertTrue("提前买共省￥218".equals(dzPromoVO.getName()));

        config.setPreSaleHasDetail(true);
        promoVOList = opt.compute(null, ItemPromoTagsVP.Param.builder().productM(productM).build(), config);
        Assert.assertTrue(CollectionUtils.size(promoVOList) == 1);
        dzPromoVO = promoVOList.get(0);
        Assert.assertTrue("提前买共省￥218".equals(dzPromoVO.getName()));
        Assert.assertTrue(dzPromoVO.getDetail() != null && dzPromoVO.getPrePic() != null && dzPromoVO.getDetail().getPopType() == 3);
    }

    private ProductM buildProductM() {
        String json = "{\"productId\":*********,\"title\":\"【测试玉树】两大一小·所有日期通用·全日票\",\"picUrl\":\"https://p0.meituan.net/dpmerchantpic/1ff4a407f604d9f76ba7451a0b9a66d7361120.jpg\",\"jumpUrl\":\"dianping://tuandeal?id=*********&shopid=11600886" +
                "10&shopuuid=k4J7xWvBRtRGc7BU&isgoodshop=0/1\",\"sale\":{\"sale\":24,\"saleTag\":\"已预售24\"},\"stock\":{\"totalStock\":2000000,\"remainStock\":2000000,\"soldOut\":false},\"basePriceTag\":\"150\",\"basePrice\":150,\"marketPrice\":\"368\",\"pro" +
                "moPrices\":[{\"promoPrice\":150,\"promoTag\":\"提前买共省￥218\",\"promoPriceTag\":\"150\",\"marketPrice\":\"368\",\"userHasCard\":false,\"totalPromoPrice\":218,\"totalPromoPriceTag\":\"-¥218\",\"promoItemList\":[{\"promoId\":*********,\"promo" +
                "TypeCode\":12,\"promoType\":\"预售优惠\",\"desc\":\"\",\"promoTag\":\"-¥218\",\"promoPrice\":218,\"canAssign\":false,\"sourceType\":1,\"icon\":\"https://p0.meituan.net/travelcube/1a7a0f263848c0490651d375fe869d114478.png\",\"newUser\":fals" +
                "e}]}],\"productTags\":[\"游玩时长：不限时\"],\"extAttrs\":[{\"name\":\"preSaleStartDate\",\"value\":\"2023.06.30\"},{\"name\":\"attr_shopRecommend\",\"value\":\"false\"},{\"name\":\"preSaleTag\",\"value\":\"true\"},{\"name\":\"service_" +
                "type\",\"value\":\"门票\"},{\"name\":\"reservation_is_needed_or_not\",\"value\":\"否\"}]}\n";
        ProductM productM = JsonCodec.decode(json, ProductM.class);
        return productM;
    }

}
