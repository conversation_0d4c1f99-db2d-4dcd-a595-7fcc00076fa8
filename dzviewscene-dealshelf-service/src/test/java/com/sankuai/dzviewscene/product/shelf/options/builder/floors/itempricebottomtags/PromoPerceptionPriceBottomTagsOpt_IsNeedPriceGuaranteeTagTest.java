package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempricebottomtags.PromoPerceptionPriceBottomTagsOpt.Config;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class PromoPerceptionPriceBottomTagsOpt_IsNeedPriceGuaranteeTagTest {

    private PromoPerceptionPriceBottomTagsOpt promoPerceptionPriceBottomTagsOpt;

    private ActivityCxt context;

    private Config config;

    @Before
    public void setUp() {
        promoPerceptionPriceBottomTagsOpt = new PromoPerceptionPriceBottomTagsOpt();
        context = Mockito.mock(ActivityCxt.class);
        config = Mockito.mock(Config.class);
    }

    /**
     * 测试总开关关闭的情况
     */
    @Test
    public void testIsNeedPriceGuaranteeTagWhenNeedPriceGuaranteeTagIsFalse() throws Throwable {
        when(config.isNeedPriceGuaranteeTag()).thenReturn(false);
        assertFalse(promoPerceptionPriceBottomTagsOpt.isNeedPriceGuaranteeTag(context, config));
    }

    /**
     * 测试总开关打开，未配置实验的情况
     */
    @Test
    public void testIsNeedPriceGuaranteeTagWhenNeedPriceGuaranteeTagIsTrueAndNeedPriceGuaranteeTagBySkListIsEmpty() throws Throwable {
        when(config.isNeedPriceGuaranteeTag()).thenReturn(true);
        when(config.getNeedPriceGuaranteeTagBySkList()).thenReturn(null);
        assertTrue(promoPerceptionPriceBottomTagsOpt.isNeedPriceGuaranteeTag(context, config));
    }

    /**
     * 测试总开关打开，配置了实验，实验不包含买贵必赔的情况
     */
    @Test
    public void testIsNeedPriceGuaranteeTagWhenNeedPriceGuaranteeTagIsTrueAndNeedPriceGuaranteeTagBySkListIsNotEmptyAndDouHuMListIsNotEmptyAndIntersectionIsEmpty() throws Throwable {
        when(config.isNeedPriceGuaranteeTag()).thenReturn(true);
        when(config.getNeedPriceGuaranteeTagBySkList()).thenReturn(Arrays.asList("sk1", "sk2"));
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk3");
        when(context.getSource("SHELF_DOU_HU")).thenReturn(Arrays.asList(douHuM));
        assertTrue(promoPerceptionPriceBottomTagsOpt.isNeedPriceGuaranteeTag(context, config));
    }

    /**
     * 测试总开关打开，配置了实验，实验包含买贵必赔的情况
     */
    @Test
    public void testIsNeedPriceGuaranteeTagWhenNeedPriceGuaranteeTagIsTrueAndNeedPriceGuaranteeTagBySkListIsNotEmptyAndDouHuMListIsNotEmptyAndIntersectionIsNotEmpty() throws Throwable {
        when(config.isNeedPriceGuaranteeTag()).thenReturn(true);
        when(config.getNeedPriceGuaranteeTagBySkList()).thenReturn(Arrays.asList("sk1", "sk2"));
        DouHuM douHuM = new DouHuM();
        douHuM.setSk("sk1");
        when(context.getSource("SHELF_DOU_HU")).thenReturn(Arrays.asList(douHuM));
        assertTrue(promoPerceptionPriceBottomTagsOpt.isNeedPriceGuaranteeTag(context, config));
    }
}
