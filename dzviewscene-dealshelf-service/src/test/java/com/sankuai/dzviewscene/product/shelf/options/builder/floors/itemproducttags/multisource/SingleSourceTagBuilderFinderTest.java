package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags.multisource;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class SingleSourceTagBuilderFinderTest {

    @Mock
    private ApplicationContext appCtx;

    private SingleSourceTagBuilderFinder singleSourceTagBuilderFinder;

    @Before
    public void setUp() {
        singleSourceTagBuilderFinder = new SingleSourceTagBuilderFinder();
        singleSourceTagBuilderFinder.setApplicationContext(appCtx);
    }

    /**
     * 测试afterPropertiesSet方法，当ApplicationContext中没有SingleSourceTagBuilder类型的Bean时
     */
    @Test
    public void testAfterPropertiesSetNoBean() throws Exception {
        // arrange
        when(appCtx.getBeansOfType(SingleSourceTagBuilder.class)).thenReturn(new HashMap<>());
        // act
        singleSourceTagBuilderFinder.afterPropertiesSet();
        // assert
        verify(appCtx, times(1)).getBeansOfType(SingleSourceTagBuilder.class);
    }

    /**
     * 测试afterPropertiesSet方法，当ApplicationContext中有SingleSourceTagBuilder类型的Bean时
     */
    @Test
    public void testAfterPropertiesSetWithBean() throws Exception {
        // arrange
        Map<String, SingleSourceTagBuilder> beanMap = new HashMap<>();
        SingleSourceTagBuilder builder = mock(SingleSourceTagBuilder.class);
        when(builder.getName()).thenReturn("test");
        beanMap.put("test", builder);
        when(appCtx.getBeansOfType(SingleSourceTagBuilder.class)).thenReturn(beanMap);
        // act
        singleSourceTagBuilderFinder.afterPropertiesSet();
        // assert
        verify(appCtx, times(1)).getBeansOfType(SingleSourceTagBuilder.class);
        verify(builder, times(1)).getName();
    }
}
