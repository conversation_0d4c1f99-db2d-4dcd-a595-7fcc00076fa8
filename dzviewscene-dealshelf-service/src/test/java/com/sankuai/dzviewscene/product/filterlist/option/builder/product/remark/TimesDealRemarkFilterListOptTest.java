package com.sankuai.dzviewscene.product.filterlist.option.builder.product.remark;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class TimesDealRemarkFilterListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductM productM;

    private TimesDealRemarkFilterListOpt.Param param(ProductM productM) {
        return TimesDealRemarkFilterListOpt.Param.builder().productM(productM).build();
    }

    private TimesDealRemarkFilterListOpt.Config config(String timesKey, String remarkTemplate) {
        TimesDealRemarkFilterListOpt.Config config = new TimesDealRemarkFilterListOpt.Config();
        config.setTimesKey(timesKey);
        config.setRemarkTemplate(remarkTemplate);
        return config;
    }

    @Test
    public void testComputeNotTimesDeal() throws Throwable {
        // arrange
        TimesDealRemarkFilterListOpt opt = new TimesDealRemarkFilterListOpt();
        when(productM.isTimesDeal()).thenReturn(false);
        // act
        String result = opt.compute(context, param(productM), config("", ""));
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeBlankTimesKeyOrRemarkTemplate() throws Throwable {
        // arrange
        TimesDealRemarkFilterListOpt opt = new TimesDealRemarkFilterListOpt();
        when(productM.isTimesDeal()).thenReturn(true);
        // act
        String result = opt.compute(context, param(productM), config("", ""));
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeBlankAttrOrNotDigits() throws Throwable {
        // arrange
        TimesDealRemarkFilterListOpt opt = new TimesDealRemarkFilterListOpt();
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(anyString())).thenReturn("");
        // act
        String result = opt.compute(context, param(productM), config("sys_multi_sale_number", "/%s次"));
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        TimesDealRemarkFilterListOpt opt = new TimesDealRemarkFilterListOpt();
        when(productM.isTimesDeal()).thenReturn(true);
        when(productM.getAttr(anyString())).thenReturn("10");
        // act
        String result = opt.compute(context, param(productM), config("sys_multi_sale_number", "/%s次"));
        // assert
        assertEquals("/10次", result);
    }
}
