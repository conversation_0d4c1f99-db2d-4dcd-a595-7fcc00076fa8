package com.sankuai.dzviewscene.product.dealstruct.ability.assemble.module.strategies;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.assemble.DealDetailAssembleParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.fetcher.DealDetailFetcher;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * 测试 GynecologyStructAttrsGroupStrategy 的 buildModelVO 方法
 */
public class GynecologyStructAttrsGroupStrategyTest {

    @Mock
    private ActivityCxt activityCxt;
    @Mock
    private DealDetailAssembleParam assembleParam;

    private GynecologyStructAttrsGroupStrategy strategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        strategy = new GynecologyStructAttrsGroupStrategy();
    }

    /**
     * 测试 dealDetailInfoModels 为空的情况
     */
    @Test
    public void testBuildModelVOWithEmptyDealDetailInfoModels() {
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.emptyList());

        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assembleParam, "anyConfig");

        assertNull(result);
    }

    /**
     * 测试找不到匹配 config 的属性的情况
     */
    @Test
    public void testBuildModelVOWithNoMatchingConfig() {
        DealDetailInfoModel model = mock(DealDetailInfoModel.class);
        when(model.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("otherConfig", "value")));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(model));

        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assembleParam, "nonExistingConfig");

        assertNull(result);
    }

    /**
     * 测试属性值为空的情况
     */
    @Test
    public void testBuildModelVOWithEmptyAttrValue() {
        DealDetailInfoModel model = mock(DealDetailInfoModel.class);
        when(model.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("config", "")));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(model));

        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assembleParam, "config");

        assertNull(result);
    }

    /**
     * 测试正常情况，config 不是 INSPECTION_INSTRUCTIONS
     */
    @Test
    public void testBuildModelVONormalCaseNotInspectionInstructions() {
        DealDetailInfoModel model = mock(DealDetailInfoModel.class);
        when(model.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("config", "value1,value2")));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(model));

        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assembleParam, "config");

        assertNotNull(result);
        assertEquals("value1\nvalue2", result.getDescModel());
    }

    // 更多测试用例可以根据实际情况添加，例如处理 INSPECTION_INSTRUCTIONS 配置的情况
    @Test
    public void testBuildModelVONormalCaseInspectionInstructions() {
        DealDetailInfoModel model = mock(DealDetailInfoModel.class);
        when(model.getDealAttrs()).thenReturn(Collections.singletonList(new AttrM("inspectionInstructionsArray", "[{\"content\": \"showContent\"}]")));
        when(activityCxt.getSource(DealDetailFetcher.CODE)).thenReturn(Collections.singletonList(model));

        DealDetailModuleVO result = strategy.buildModelVO(activityCxt, assembleParam, "inspectionInstructionsArray");

        assertNotNull(result);
        assertEquals("showContent", result.getDescModel());
    }
}
