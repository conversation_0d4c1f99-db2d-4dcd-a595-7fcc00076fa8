package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Comparator;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractTimesDealListOpt_SortSaleTest {

    private AbstractTimesDealListOpt abstractTimesDealListOpt = new AbstractTimesDealListOpt() {

        @Override
        protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
            return null;
        }
    };

    /**
     * 测试销量不同的情况
     */
    @Test
    public void testSortSaleDifferentSale() throws Throwable {
        // arrange
        ProductM product1 = new ProductM();
        ProductSaleM sale1 = new ProductSaleM();
        sale1.setSale(10);
        product1.setSale(sale1);
        ProductM product2 = new ProductM();
        ProductSaleM sale2 = new ProductSaleM();
        sale2.setSale(20);
        product2.setSale(sale2);
        // act
        Comparator<ProductM> comparator = abstractTimesDealListOpt.sortSale();
        int result = comparator.compare(product1, product2);
        // assert
        assertTrue("The product with lower sale should come after the one with higher sale in descending order", result > 0);
    }

    /**
     * 测试销量相同的情况
     */
    @Test
    public void testSortSaleSameSale() throws Throwable {
        // arrange
        ProductM product1 = new ProductM();
        ProductSaleM sale1 = new ProductSaleM();
        sale1.setSale(10);
        product1.setSale(sale1);
        ProductM product2 = new ProductM();
        ProductSaleM sale2 = new ProductSaleM();
        sale2.setSale(10);
        product2.setSale(sale2);
        // act
        Comparator<ProductM> comparator = abstractTimesDealListOpt.sortSale();
        int result = comparator.compare(product1, product2);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试对象为 null 的情况
     */
    @Test(expected = NullPointerException.class)
    public void testSortSaleNull() throws Throwable {
        // arrange
        ProductM product1 = null;
        ProductM product2 = new ProductM();
        ProductSaleM sale2 = new ProductSaleM();
        sale2.setSale(10);
        product2.setSale(sale2);
        // act
        Comparator<ProductM> comparator = abstractTimesDealListOpt.sortSale();
        comparator.compare(product1, product2);
    }
}
