package com.sankuai.dzviewscene.product.dealstruct.ability.price;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.athena.viewscene.framework.pmf.core.PmfAbility;
import com.sankuai.dzviewscene.product.dealstruct.ability.price.vpoints.DealDetailPriceVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailPriceModel;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailPriceBuilderBuildTest {

    @InjectMocks
    private DealDetailPriceBuilder builder;

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailPriceVP<?> dealDetailPriceVP;

    private DealDetailPriceParam priceParam = new DealDetailPriceParam();

    private DealDetailPriceCfg priceCfg = new DealDetailPriceCfg();

    @Test
    public void testBuildWhenDetailModelIsNull() throws Throwable {
        List<DealDetailInfoModel> models = new ArrayList<>();
        models.add(null);
        when(activityCxt.getSource(anyString())).thenReturn(models);
        CompletableFuture<List<DealDetailPriceModel>> result = builder.build(activityCxt, priceParam, priceCfg);
        assertNotNull(result);
        assertTrue(result.get().isEmpty());
    }
}
