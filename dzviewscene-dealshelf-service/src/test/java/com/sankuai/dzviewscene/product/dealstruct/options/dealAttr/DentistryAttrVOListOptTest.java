package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import java.util.Collections;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import org.mockito.InjectMocks;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DentistryAttrVOListOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param param;

    @Mock
    private DentistryAttrVOListOpt.StepConfig config;

    @Test
    public void testComputeWhenProcessStepAttrModulesIsNull() throws Throwable {
        DentistryAttrVOListOpt dentistryAttrVOListOpt = new DentistryAttrVOListOpt();
        when(config.getTitle()).thenReturn("title");
        List<DealDetailStructAttrModuleGroupModel> result = dentistryAttrVOListOpt.compute(context, param, config);
        assertNull(result);
    }
}
