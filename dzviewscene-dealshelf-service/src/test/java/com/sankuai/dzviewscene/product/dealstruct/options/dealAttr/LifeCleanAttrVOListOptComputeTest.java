package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.deal.struct.query.api.entity.dto.MustSkuItemsGroupDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealAttr.vpoints.DealAttrVOListVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailStructAttrModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.options.dealAttr.LifeCleanAttrVOListOpt.ActivityTags;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealDetailStructAttrModuleVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class LifeCleanAttrVOListOptComputeTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private DealAttrVOListVP.Param // Corrected Param type
    param;

    @Mock
    private LifeCleanAttrVOListOpt.Config config;

    @Mock
    private DealDetailDtoModel dealDetailDtoModel;

    @Mock
    private MustSkuItemsGroupDto mustSkuItemsGroupDto;

    @Mock
    private SkuItemDto skuItemDto;

    @Mock
    private SkuAttrItemDto skuAttrItemDto;

    @Spy
    private LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt;

    /**
     * Test compute method when config is null
     */
    @Test
    public void testComputeWhenConfigIsNull() throws Throwable {
        LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt = new LifeCleanAttrVOListOpt();
        // Corrected to use the mocked Param
        // Corrected to use the mocked Param
        List<DealDetailStructAttrModuleGroupModel> result = lifeCleanAttrVOListOpt.compute(context, param, null);
        assertNull("Result should be null when config is null", result);
    }

    /**
     * Test compute method when param is null
     */
    @Test
    public void testComputeWhenParamIsNull() throws Throwable {
        // arrange
        LifeCleanAttrVOListOpt lifeCleanAttrVOListOpt = new LifeCleanAttrVOListOpt();
        ActivityCxt context = mock(ActivityCxt.class);
        // act
        List<DealDetailStructAttrModuleGroupModel> result = lifeCleanAttrVOListOpt.compute(context, null, mock(LifeCleanAttrVOListOpt.Config.class));
        // assert
        assertNull("Result should be null when param is null", result);
    }

    @Test
    public void testBuildDealDetailStructAttrModuleVOList_WhenSelfSupportMismatch() throws Throwable {
        LifeCleanAttrVOListOpt.AttrListGroupModel attrListGroupModel = new LifeCleanAttrVOListOpt.AttrListGroupModel();
        attrListGroupModel.setSelfSupport(false);
        List<AttrM> attrs = new ArrayList<>();
        boolean isSelfSupport = true;
        DealDetailStructAttrModuleGroupModel result = lifeCleanAttrVOListOpt.buildDealDetailStructAttrModuleVOList(attrListGroupModel, attrs, isSelfSupport);
        assertNull(result);
    }
}
