package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean;

import com.alibaba.fastjson.JSON;
import com.sankuai.FileUtil;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.dealshelf.shelfvo.ShelfOceanVO;
import com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.ocean.bean.UnifiedShelfOceanConfig;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterM;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.ShelfGroupM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RunWith(MockitoJUnitRunner.class)
public class UnifiedShelfOceanBuilderTest {

    @Mock
    private UnifiedShelfOceanConfig unifiedShelfOceanConfig;

    @Test
    public void test_unifiedProductAreaBuilder() throws NoSuchFieldException, IllegalAccessException {
        ShelfGroupM shelfGroupM = initShelfGroupM();
        UnifiedShelfOceanBuilder.Request request = initReq();
        ActivityCxt cxt = new ActivityCxt();
        cxt.getSourceMap().put("ShelfMainDataAssembler", shelfGroupM);

        UnifiedShelfOceanBuilder builder = new UnifiedShelfOceanBuilder();
        Field field = builder.getClass().getDeclaredField("unifiedShelfOceanConfig");
        field.setAccessible(true);
        field.set(builder, unifiedShelfOceanConfig);

        ShelfOceanVO shelfOceanVO = JSON.parseObject(FileUtil.file2str("ocean.json"), ShelfOceanVO.class);

        Mockito.when(unifiedShelfOceanConfig.getOcean(Mockito.any(), Mockito.anyInt())).thenReturn(shelfOceanVO);

        CompletableFuture<ShelfOceanVO> build = builder.build(cxt, request, new UnifiedShelfOceanBuilder.Config());
        ShelfOceanVO join = build.join();
        System.out.println(join);
        Assert.assertNotNull(join);
    }


    public static ShelfGroupM initShelfGroupM() {

        ProductGroupM productGroupM = JSON.parseObject(FileUtil.file2str("product_group.json"), ProductGroupM.class);
        FilterM filterM = JSON.parseObject(FileUtil.file2str("filter.json"), FilterM.class);
        List<DouHuM> douHuMS = JSON.parseArray(FileUtil.file2str("douhu.json"), DouHuM.class);

        ShelfGroupM shelfGroupM = new ShelfGroupM();

        shelfGroupM.setProductGroupMs(new HashMap<>());
        shelfGroupM.setFilterMs(new HashMap<>());

        shelfGroupM.getDouHus().addAll(douHuMS);
        shelfGroupM.getProductGroupMs().put("团购", productGroupM);
        shelfGroupM.getFilterMs().put("团购", filterM);
        // System.out.println(JSON.toJSONString(shelfGroupM));
        return shelfGroupM;
    }


    public static UnifiedShelfOceanBuilder.Request initReq() {

        String shop = "{\"shopId\":132234798,\"longShopId\":132234798,\"shopName\":\"镁连社·美联社\",\"shopType\":50,\"useType\":1,\"category\":157,\"lat\":31.274683,\"lng\":121.519067,\"cityId\":1,\"shopUuid\":\"H1fQdj8AYzUea3Of\"}\n";
        UnifiedShelfOceanBuilder.Request deserialize = new UnifiedShelfOceanBuilder.Request();
        deserialize.setCtxShop(JSON.parseObject(shop, ShopM.class));
        deserialize.setDpPoiIdL(132234798);
        deserialize.setMtPoiIdL(195223233);
        deserialize.setPlatform(2);
        // System.out.println(JSON.toJSONString(deserialize));
        return deserialize;
    }
}
