package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproductrichtags;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductRichTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.fetcher.shoppadding.ContextHandlerAbility;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.productrichtags.IntentionAnalyzeProductRichTagsOpt;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ContextHandlerResult;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.ExposureEnum;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class IntentionAnalyzeProductRichTagsOptTest {

    @InjectMocks
    private IntentionAnalyzeProductRichTagsOpt intentionAnalyzeProductRichTagsOpt;

    @Mock
    private IntentionAnalyzeProductRichTagsOpt.Config config;

    @Mock
    private ItemProductRichTagsVP.Param param;

    @Test
    public void test_productTags(){
        when(param.getProductTags()).thenReturn(Lists.newArrayList("肩颈按摩", "头部按摩"));
        List<RichLabelVO> compute = intentionAnalyzeProductRichTagsOpt.compute(buildContext(), param, config);
        Assert.assertNotNull(compute);
    }

    @Test
    public void test_productTags_isNull(){
        when(param.getProductTags()).thenReturn(Lists.newArrayList());
        List<RichLabelVO> compute = intentionAnalyzeProductRichTagsOpt.buildHighLight(buildContext(), param, null);
        Assert.assertTrue(compute.isEmpty());
    }

    @Test
    public void test_richLabelVOS(){
        List<RichLabelVO> richLabelVOS = Lists.newArrayList();
        RichLabelVO richLabelVO = new RichLabelVO();
        richLabelVO.setText("肩颈按摩");
        richLabelVO.setTextColor("#FF0000");
        richLabelVOS.add(richLabelVO);
        List<RichLabelVO> compute = intentionAnalyzeProductRichTagsOpt.buildHighLight(buildContext(), param, richLabelVOS);
        Assert.assertNotNull(compute);
    }

    private ActivityCxt buildContext() {
        ActivityCxt ctx = new ActivityCxt();
        ctx.addParam(ShelfActivityConstants.Params.platform, 2);
        ctx.addParam(ShelfActivityConstants.Params.searchKeyword, "肩颈按摩");
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setRecognizeList(Lists.newArrayList("肩颈","按摩"));
        ctx.attachSource(ContextHandlerAbility.CODE,contextHandlerResult);
        return ctx;
    }

    @Test
    public void test_compute() {
        ContextHandlerResult contextHandlerResult = new ContextHandlerResult();
        contextHandlerResult.setExposure(ExposureEnum.EXPOSED.getCode());
        Map<String, Object> sourceMap = Maps.newHashMap();
        sourceMap.put("ContextHandlerAbility", contextHandlerResult);
        ActivityCxt ctx = buildContext();
        ctx.setSourceMap(sourceMap);
        ProductM productM = new ProductM();
        productM.setProductId(1);
        productM.setTradeType(19);
        productM.setAttr("pay_method", "true");
        ItemProductRichTagsVP.Param param = ItemProductRichTagsVP.Param.builder()
                .productTags(Lists.newArrayList("肩颈按摩", "头部按摩")).productM(productM).build();
        IntentionAnalyzeProductRichTagsOpt.Config config = new IntentionAnalyzeProductRichTagsOpt.Config();
        config.setEnableAfterPay(true);
        config.setPreAfterTag("先用后付");
        List<RichLabelVO> compute = intentionAnalyzeProductRichTagsOpt.compute(ctx, param, config);
        Assert.assertNotNull(compute);
    }
}
