package com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductActivityTagsVP;
import com.sankuai.dzviewscene.product.filterlist.option.builder.product.activity.DealListSelectTagItemPicWithPriceTagOpt.Config;
import com.sankuai.dzviewscene.product.filterlist.vo.DzActivityTagVO;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategy;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildStrategyFactory;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealListSelectTagItemPicWithPriceTagOptTest {

    // Note: The test case that directly mocks the private method getMarketingFloatTag has been removed.
    @InjectMocks
    private DealListSelectTagItemPicWithPriceTagOpt dealListSelectTagItemPicWithPriceTagOpt;

    @Mock
    private FloatTagBuildStrategyFactory tagBuildStrategyFactory;

    @Mock
    private FloatTagBuildStrategy tagBuildStrategy;

    @Mock
    private ProductActivityTagsVP.Param param;

    @Mock
    private DealListSelectTagItemPicWithPriceTagOpt.Config config;

    @Mock
    private ProductM productM;

    /**
     * Test compute method when PicUrl is null.
     * @throws Throwable
     */
    @Test
    public void testComputePicUrlIsNull() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn(null);
        List<DzActivityTagVO> result = dealListSelectTagItemPicWithPriceTagOpt.compute(context, param, config);
        assertNull(result);
    }

    /**
     * Test compute method when MarketingFloatTagList is empty.
     * @throws Throwable
     */
    @Test
    public void testComputeMarketingFloatTagListIsEmpty() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("somePicUrl");
        List<DzActivityTagVO> result = dealListSelectTagItemPicWithPriceTagOpt.compute(context, param, config);
        assertTrue(result.isEmpty());
    }

    /**
     * Test compute method when PriceDisplayTag is null.
     * @throws Throwable
     */
    @Test
    public void testComputePriceDisplayTagIsNull() throws Throwable {
        ActivityCxt context = mock(ActivityCxt.class);
        when(param.getProductM()).thenReturn(productM);
        when(productM.getPicUrl()).thenReturn("somePicUrl");
        List<DzActivityTagVO> result = dealListSelectTagItemPicWithPriceTagOpt.compute(context, param, config);
        assertTrue(result.isEmpty());
    }
}
