package com.sankuai.dzviewscene.product.shelf.ability.builder.floors;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.productshelf.vu.vo.*;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class FloorsBuilderUnitTest {

    private FloorsBuilder floorsBuilder;
    private ActivityCxt mockActivityCxt;
    private List<ProductAreaComponentVO> productAreaList;
    private Method getPriceCipherLogMap = null;

    @Before
    public void setUp() throws NoSuchMethodException {
        floorsBuilder = new FloorsBuilder();
        mockActivityCxt = Mockito.mock(ActivityCxt.class);
        productAreaList = new ArrayList<>();
        getPriceCipherLogMap = floorsBuilder.getClass().getDeclaredMethod("getPriceCipherLogMap", ActivityCxt.class, List.class);
        getPriceCipherLogMap.setAccessible(true);
    }


    @Test
    public void testItemComponentPriceBottomTagsForBar() {
        FloorsBuilder floorsBuilder = new FloorsBuilder();
        List<DzTagVO> dzTagVOS = floorsBuilder.fillAttr(Lists.newArrayList(new DzPromoVO()));
        Assert.assertNotNull(dzTagVOS);

        List<DzTagVO> dzTagVOS2 = floorsBuilder.fillAttr(null);
        Assert.assertNull(dzTagVOS2);
    }

    /**
     * 测试场景：当productAreaList为空时
     */
    @Test
    public void testGetPriceCipherLogMap_EmptyProductAreaList() throws InvocationTargetException, IllegalAccessException {
        Mockito.when(mockActivityCxt.getSceneCode()).thenReturn("testSceneCode");

        Map<String, String> result = (Map<String, String>)getPriceCipherLogMap.invoke(floorsBuilder, mockActivityCxt, productAreaList);

        assertFalse("anyOneNoJumpUrl应为false", Boolean.parseBoolean(result.get("anyOneNoJumpUrl")));
        assertFalse("anyOneNoPriceCipher应为false", Boolean.parseBoolean(result.get("anyOneNoPriceCipher")));
        assertEquals("场景代码应为testSceneCode", "testSceneCode", result.get("sceneCode"));
    }

    /**
     * 测试场景：当productAreaList中的ProductAreaComponentVO的itemArea为null时
     */
    @Test
    public void testGetPriceCipherLogMap_NullItemArea() throws InvocationTargetException, IllegalAccessException {
        ProductAreaComponentVO mockProductAreaComponentVO = Mockito.mock(ProductAreaComponentVO.class);
        productAreaList.add(mockProductAreaComponentVO);
        Mockito.when(mockActivityCxt.getSceneCode()).thenReturn("testSceneCode");

        Map<String, String> result = (Map<String, String>)getPriceCipherLogMap.invoke(floorsBuilder, mockActivityCxt, productAreaList);

        assertFalse("anyOneNoJumpUrl应为false", Boolean.parseBoolean(result.get("anyOneNoJumpUrl")));
        assertFalse("anyOneNoPriceCipher应为false", Boolean.parseBoolean(result.get("anyOneNoPriceCipher")));
        assertEquals("场景代码应为testSceneCode", "testSceneCode", result.get("sceneCode"));
    }

    /**
     * 测试场景：当productAreaList中的ProductAreaComponentVO的itemArea不为null，但productItems为空时
     */
    @Test
    public void testGetPriceCipherLogMap_EmptyProductItems() throws InvocationTargetException, IllegalAccessException {
        ProductAreaComponentVO mockProductAreaComponentVO = Mockito.mock(ProductAreaComponentVO.class);
        Mockito.when(mockProductAreaComponentVO.getItemArea()).thenReturn(new DzItemAreaComponentVO());
        productAreaList.add(mockProductAreaComponentVO);
        Mockito.when(mockActivityCxt.getSceneCode()).thenReturn("testSceneCode");

        Map<String, String> result = (Map<String, String>)getPriceCipherLogMap.invoke(floorsBuilder, mockActivityCxt, productAreaList);

        assertFalse("anyOneNoJumpUrl应为false", Boolean.parseBoolean(result.get("anyOneNoJumpUrl")));
        assertFalse("anyOneNoPriceCipher应为false", Boolean.parseBoolean(result.get("anyOneNoPriceCipher")));
        assertEquals("场景代码应为testSceneCode", "testSceneCode", result.get("sceneCode"));
    }

    /**
     * 测试场景：当productAreaList中的ProductAreaComponentVO的itemArea不为null，productItems不为空，但jumpUrl为空时
     */
    @Test
    public void testGetPriceCipherLogMap_EmptyJumpUrl() throws InvocationTargetException, IllegalAccessException {
        ProductAreaComponentVO mockProductAreaComponentVO = Mockito.mock(ProductAreaComponentVO.class);
        DzItemAreaComponentVO mockItemAreaComponentVO = Mockito.mock(DzItemAreaComponentVO.class);
        DzItemVO mockDzItemVO = Mockito.mock(DzItemVO.class);

        List<DzItemVO> dzItemVOList = new ArrayList<>();
        dzItemVOList.add(mockDzItemVO);

        Mockito.when(mockProductAreaComponentVO.getItemArea()).thenReturn(mockItemAreaComponentVO);
        Mockito.when(mockItemAreaComponentVO.getProductItems()).thenReturn(dzItemVOList);
        Mockito.when(mockDzItemVO.getJumpUrl()).thenReturn("");
        productAreaList.add(mockProductAreaComponentVO);
        Mockito.when(mockActivityCxt.getSceneCode()).thenReturn("testSceneCode");

        Map<String, String> result = (Map<String, String>)getPriceCipherLogMap.invoke(floorsBuilder, mockActivityCxt, productAreaList);

        assertTrue("anyOneNoJumpUrl应为true", Boolean.parseBoolean(result.get("anyOneNoJumpUrl")));
        assertTrue("anyOneNoPriceCipher应为true", Boolean.parseBoolean(result.get("anyOneNoPriceCipher")));
        assertEquals("场景代码应为testSceneCode", "testSceneCode", result.get("sceneCode"));
    }

    /**
     * 测试场景：当productAreaList中的ProductAreaComponentVO的itemArea不为null，productItems不为空，jumpUrl不为空但不包含pricecipher时
     */
    @Test
    public void testGetPriceCipherLogMap_JumpUrlWithoutPriceCipher() throws InvocationTargetException, IllegalAccessException {
        ProductAreaComponentVO mockProductAreaComponentVO = Mockito.mock(ProductAreaComponentVO.class);
        DzItemAreaComponentVO mockItemAreaComponentVO = Mockito.mock(DzItemAreaComponentVO.class);
        DzItemVO mockDzItemVO = Mockito.mock(DzItemVO.class);

        List<DzItemVO> dzItemVOList = new ArrayList<>();
        dzItemVOList.add(mockDzItemVO);

        Mockito.when(mockProductAreaComponentVO.getItemArea()).thenReturn(mockItemAreaComponentVO);
        Mockito.when(mockItemAreaComponentVO.getProductItems()).thenReturn(dzItemVOList);
        Mockito.when(mockDzItemVO.getJumpUrl()).thenReturn("dianping://tuandeal?id=775281677&shopid=615055061&shopuuid=H16rzSQerWEVjWql&nailexhibitid=0");
        productAreaList.add(mockProductAreaComponentVO);
        Mockito.when(mockActivityCxt.getSceneCode()).thenReturn("testSceneCode");

        Map<String, String> result = (Map<String, String>)getPriceCipherLogMap.invoke(floorsBuilder, mockActivityCxt, productAreaList);

        assertFalse("anyOneNoJumpUrl应为false", Boolean.parseBoolean(result.get("anyOneNoJumpUrl")));
        assertTrue("anyOneNoPriceCipher应为true", Boolean.parseBoolean(result.get("anyOneNoPriceCipher")));
        assertEquals("场景代码应为testSceneCode", "testSceneCode", result.get("sceneCode"));
    }

    /**
     * 测试场景：当productAreaList中的ProductAreaComponentVO的itemArea不为null，productItems不为空，jumpUrl不为空且包含pricecipher时
     */
    @Test
    public void testGetPriceCipherLogMap_JumpUrlWithPriceCipher() throws InvocationTargetException, IllegalAccessException {
        ProductAreaComponentVO mockProductAreaComponentVO = Mockito.mock(ProductAreaComponentVO.class);
        DzItemAreaComponentVO mockItemAreaComponentVO = Mockito.mock(DzItemAreaComponentVO.class);
        DzItemVO mockDzItemVO = Mockito.mock(DzItemVO.class);

        List<DzItemVO> dzItemVOList = new ArrayList<>();
        dzItemVOList.add(mockDzItemVO);

        Mockito.when(mockProductAreaComponentVO.getItemArea()).thenReturn(mockItemAreaComponentVO);
        Mockito.when(mockItemAreaComponentVO.getProductItems()).thenReturn(dzItemVOList);
        Mockito.when(mockDzItemVO.getJumpUrl()).thenReturn("dianping://tuandeal?id=775281677&shopid=615055061&shopuuid=H16rzSQerWEVjWql&nailexhibitid=0&pricecipher=vH4THqgHbPMHcB_0K6VVVionFo5BRsk9elX3jodyFoUGE5ylRMsuAdmZ8PctW2IiPj57uHc0qHDeF0COYpQdrsl9ciErfpUBxz3lgbJopxV8_ku9JsMSWg-0o46eJQGi8EiDy0_vwM9_Ios4ZQ06hZkiBjtfShfEiq-sNkOmMJ6bWfIKNvxcbdviXqk9kARAEbmoOJ91HrIUtTvocooy-WxHhTAprFYFFogR1JfiMiQc1btJBxbAWP2SoUJHoNBRdc1lEtvD_bRLo1aaAx_zGkE63lZuUFWjQijC-kKgIQHlZChn6iWSJ-yKF8E0fv-V-48YkygWy1rnnwWseATbyZFhuaC3loT6op5X8xeastcELdrtC0plxA60uIwnGZB1GAL7pNWRvEIRrL6EGyI_4g9JYwcfXgMbw4vlONMzqY2WcJGepje76MfwMTLJ-rsdjnWOIFO0QwgjqzBueRtd4-YzJr-Pd_roy4BRzx_qCMCC7Ubhcvu9fk6obsFMVWRFJMIz7qVDnfraS_kZYZScJaNykIDyUEj72-7ddOZGZdu2vAt5QX2kadYiwKsHCHgjHpy2uDVYyhdWc0NvDIhmIg");
        productAreaList.add(mockProductAreaComponentVO);
        Mockito.when(mockActivityCxt.getSceneCode()).thenReturn("testSceneCode");

        Map<String, String> result = (Map<String, String>)getPriceCipherLogMap.invoke(floorsBuilder, mockActivityCxt, productAreaList);

        assertFalse("anyOneNoJumpUrl应为false", Boolean.parseBoolean(result.get("anyOneNoJumpUrl")));
        assertFalse("anyOneNoPriceCipher应为false", Boolean.parseBoolean(result.get("anyOneNoPriceCipher")));
        assertEquals("场景代码应为testSceneCode", "testSceneCode", result.get("sceneCode"));
    }

}