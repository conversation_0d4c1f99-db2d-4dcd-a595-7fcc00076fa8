package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempriceabovetags;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPriceAboveTagsVP.Param;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.CardM;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DefaultItemPriceAboveTagsOptTest {

    /**
     * Tests whether the compute method returns null.
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // Arrange
        DefaultItemPriceAboveTagsOpt defaultItemPriceAboveTagsOpt = new DefaultItemPriceAboveTagsOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = new ProductM();
        int platform = 1;
        List<DouHuM> douHuList = new ArrayList<>();
        CardM cardM = new CardM();
        List<DzTagVO> priceBottomTags = new ArrayList<>();
        // Using the builder pattern to create Param instance
        ItemPriceAboveTagsVP.Param param = ItemPriceAboveTagsVP.Param.builder().productM(productM).platform(platform).douHuList(douHuList).cardM(cardM).priceBottomTags(priceBottomTags).build();
        // Act
        List<DzTagVO> result = defaultItemPriceAboveTagsOpt.compute(context, param, null);
        // Assert
        assertNull(result);
    }
}
