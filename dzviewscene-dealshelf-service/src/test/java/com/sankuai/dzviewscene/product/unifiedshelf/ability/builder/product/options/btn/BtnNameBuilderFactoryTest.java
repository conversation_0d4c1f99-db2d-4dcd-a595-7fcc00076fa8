package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.product.options.btn;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BtnNameBuilderFactoryTest {

    @Mock
    private ActivityCxt mockContext;

    @Mock
    private ProductM mockProductM;

    /**
     * Test when button name config contains invalid builder key
     */
    @Test
    public void testGetButtonName_InvalidBuilderKey() throws Throwable {
        // arrange
        Map<String, String> btnNameCfg = new HashMap<>();
        btnNameCfg.put("INVALID_KEY", "config");
        // act
        String result = BtnNameBuilderFactory.getButtonName(mockContext, mockProductM, btnNameCfg);
        // assert
        assertNull(result);
    }

    @Test(expected = NullPointerException.class)
    public void testHaveCouponsProductMIsNull() throws Throwable {
        BtnNameBuilderFactory.haveCoupons(null);
    }

    @Test
    public void testHaveCouponsBothCouponAttributesAreNull() throws Throwable {
        ProductM productM = mock(ProductM.class);
        boolean result = BtnNameBuilderFactory.haveCoupons(productM);
        assertFalse(result);
    }

    @Test
    public void testHaveCouponsCommonConsumerCouponAssignIsNull() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr("common_consumer_coupon_assign")).thenReturn(null);
        when(productM.getAttr("government_consumer_coupon_assign")).thenReturn("false");
        boolean result = BtnNameBuilderFactory.haveCoupons(productM);
        assertTrue(result);
    }

    @Test
    public void testHaveCouponsCommonConsumerCouponAssignIsNotNull() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr("common_consumer_coupon_assign")).thenReturn("false");
        boolean result = BtnNameBuilderFactory.haveCoupons(productM);
        assertTrue(result);
    }

    @Test
    public void testHaveCouponsBothCouponAttributesAreNotNull() throws Throwable {
        ProductM productM = mock(ProductM.class);
        when(productM.getAttr("common_consumer_coupon_assign")).thenReturn("false");
        boolean result = BtnNameBuilderFactory.haveCoupons(productM);
        assertTrue(result);
    }
}
