package com.sankuai.dzviewscene.product.dealstruct.ability.skuList;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkusModuleBuilder;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkuListsParam;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.DealDetailSkusModuleCfg;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailDtoModel;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.InjectMocks;
import com.dianping.deal.struct.query.api.entity.dto.DealDetailSkuUniStructuredDto;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.model.DouhuResultModel;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealDetailSkusModuleBuilder_BuildTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealDetailSkuListsParam dealDetailSkuListsParam;

    @Mock
    private DealDetailSkusModuleCfg dealDetailSkusModuleCfg;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    @Mock
    private DealDetailDtoModel dealDetailDtoModel;

    @Mock
    private DealDetailSkuUniStructuredDto dealDetailSkuUniStructuredDto;

    @Test
    public void testBuildNoDealDetailInfo() throws Throwable {
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(null);
        DealDetailSkusModuleBuilder builder = new DealDetailSkusModuleBuilder();
        // act
        CompletableFuture<List<DealDetailSkuListModuleGroupModel>> result = builder.build(activityCxt, dealDetailSkuListsParam, dealDetailSkusModuleCfg);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildInvalidDealDetail() throws Throwable {
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(Collections.singletonList(dealDetailInfoModel));
        when(dealDetailInfoModel.getDealDetailDtoModel()).thenReturn(null);
        DealDetailSkusModuleBuilder builder = new DealDetailSkusModuleBuilder();
        // act
        CompletableFuture<List<DealDetailSkuListModuleGroupModel>> result = builder.build(activityCxt, dealDetailSkuListsParam, dealDetailSkusModuleCfg);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildNoVPoints() throws Throwable {
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(Collections.singletonList(dealDetailInfoModel));
        when(dealDetailSkusModuleCfg.isNonNeedDealDetail()).thenReturn(false);
        when(activityCxt.getSource(anyString())).thenReturn(null);
        DealDetailSkusModuleBuilder builder = new DealDetailSkusModuleBuilder();
        // act
        CompletableFuture<List<DealDetailSkuListModuleGroupModel>> result = builder.build(activityCxt, dealDetailSkuListsParam, dealDetailSkusModuleCfg);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildNoDouhuResultModels() throws Throwable {
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(Collections.singletonList(dealDetailInfoModel));
        when(dealDetailSkusModuleCfg.isNonNeedDealDetail()).thenReturn(false);
        when(activityCxt.getSource(anyString())).thenReturn(null);
        DealDetailSkusModuleBuilder builder = new DealDetailSkusModuleBuilder();
        // act
        CompletableFuture<List<DealDetailSkuListModuleGroupModel>> result = builder.build(activityCxt, dealDetailSkuListsParam, dealDetailSkusModuleCfg);
        // assert
        assertTrue(result.get().isEmpty());
    }

    @Test
    public void testBuildEmptyDealDetailSkuListModuleGroupModels() throws Throwable {
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(Collections.singletonList(dealDetailInfoModel));
        when(dealDetailSkusModuleCfg.isNonNeedDealDetail()).thenReturn(false);
        when(activityCxt.getSource(anyString())).thenReturn(Collections.emptyList());
        DealDetailSkusModuleBuilder builder = new DealDetailSkusModuleBuilder();
        // act
        CompletableFuture<List<DealDetailSkuListModuleGroupModel>> result = builder.build(activityCxt, dealDetailSkuListsParam, dealDetailSkusModuleCfg);
        // assert
        assertTrue(result.get().isEmpty());
    }
}
