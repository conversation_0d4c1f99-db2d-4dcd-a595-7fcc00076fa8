package com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.skuList.vpoints.SkuListModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailInfoModel;
import com.sankuai.dzviewscene.product.dealstruct.options.skuList.footmessage.FootDealDetailSkuListModuleOpt;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.List;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class FootDealDetailSkuListModuleOptTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private SkuListModuleVP.Param param;

    @Mock
    private FootDealDetailSkuListModuleOpt.Config config;

    @Mock
    private DealDetailInfoModel dealDetailInfoModel;

    /**
     * 测试 compute 方法，正常情况
     */
    @Test
    public void testComputeNormal() throws Throwable {
        // arrange
        FootDealDetailSkuListModuleOpt opt = new FootDealDetailSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(dealDetailInfoModel);
        // act
        List<DealDetailSkuListModuleGroupModel> result = opt.compute(activityCxt, param, config);
        // assert
        assertNotNull(result);
        verify(param, times(1)).getDealDetailInfoModel();
    }

    /**
     * 测试 compute 方法，异常情况
     */
    @Test(expected = NullPointerException.class)
    public void testComputeException() throws Throwable {
        // arrange
        FootDealDetailSkuListModuleOpt opt = new FootDealDetailSkuListModuleOpt();
        when(param.getDealDetailInfoModel()).thenReturn(null);
        // act
        opt.compute(activityCxt, param, config);
        // assert
        // expect an exception to be thrown
    }
}
