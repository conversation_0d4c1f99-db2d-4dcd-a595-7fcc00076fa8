package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemproducttags;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.degrade.util.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemProductTagsVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.TagM;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PhysicalExaminationProductTagsOptTest {

    @Mock
    private ActivityCxt context;

    @Test
    public void testImplantTag() {
        ProductM productM = new ProductM();
        List<TagM> productTagList = new ArrayList<>();
        TagM tagM = new TagM();
        tagM.setId("100208251");
        tagM.setName("xxxxx");
        productTagList.add(tagM);
        productM.setProductTagList(productTagList);
        List<String> list = Lists.newArrayList("dsa", "dsasdsa", "eddsa");
        List<AttrM> extAttrs = new ArrayList<>();
        AttrM attrM = new AttrM();
        attrM.setName("dentistryPlantCheckTag");
        attrM.setValue(JsonCodec.encode(list));
        extAttrs.add(attrM);
        productM.setExtAttrs(extAttrs);
        PhysicalExaminationProductTagsOpt.Config config = new PhysicalExaminationProductTagsOpt.Config();
        config.setFront11(11);
        config.setColor777777("#77777");
        config.setColorFF6633("FF6633");
        PhysicalExaminationProductTagsOpt opt = new PhysicalExaminationProductTagsOpt();
        List<String> compute = opt.compute(null, ItemProductTagsVP.Param.builder().productM(productM).build(), config);
        Assert.assertNotNull(compute);
    }

    @Test
    public void testFilter() {
        List<String> products = Lists.newArrayList("妇科3项目", "妇科0项目");
        PhysicalExaminationProductTagsOpt opt = new PhysicalExaminationProductTagsOpt();
        List<String> result = opt.filter(products);
        Assert.assertTrue(result != null && result.size() == 1);
    }

    /**
     * Test compute method when bookInfo is blank and product has no tags
     */
    @Test
    public void testComputeWhenBookInfoBlankNoTags() throws Throwable {
        // arrange
        PhysicalExaminationProductTagsOpt opt = spy(new PhysicalExaminationProductTagsOpt());
        ProductM productM = new ProductM();
        // Ensure productTags is not null
        productM.setProductTags(new ArrayList<>());
        ItemProductTagsVP.Param param = ItemProductTagsVP.Param.builder().productM(productM).build();
        // act
        List<String> result = opt.compute(context, param, null);
        // assert
        assertEquals(0, result.size());
    }
}
