package com.sankuai.dzviewscene.product.filterlist.option.builder.product.button;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.degrade.util.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.enums.WarmUpStageEnum;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductButtonVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class // 其他测试用例...
ShoppingMallMembershipWarmUpButtonOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private ProductButtonVP.Param param;

    @Mock
    private ProductM productM;

    @InjectMocks
    private ShoppingMallMembershipWarmUpButtonOpt buttonOpt;

    @Test
    public void testComputeInvalidWarmUpStage() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        // Assuming necessary mocks for productM if needed based on actual usage.
        // Mock static methods or internals of compute method as needed based on actual code structure.
        DzSimpleButtonVO result = buttonOpt.compute(context, param, null);
        assertNotNull(result);
    }

    @Test
    public void testComputeValidWarmUpStage() throws Throwable {
        when(param.getProductM()).thenReturn(productM);
        // Assuming necessary mocks for productM if needed based on actual usage.
        // Mock static methods or internals of compute method as needed based on actual code structure.
        DzSimpleButtonVO result = buttonOpt.compute(context, param, null);
        assertNotNull(result);
    }

    /**
     * Test case for ONLY_WARM_UP_PRESALE stage
     */
    @Test
    public void testComputeWhenOnlyWarmUpPresale() throws Throwable {
        // arrange
        ShoppingMallMembershipWarmUpButtonOpt opt = new ShoppingMallMembershipWarmUpButtonOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.getJumpUrl()).thenReturn("test_jump_url");
        // Set warm up start time attribute
        // 1 second ago
        when(productM.getAttr(WarmUpStageEnum.ATTRIBUTE_KEY_WARM_UP_START_TIME)).thenReturn(String.valueOf(System.currentTimeMillis() - 1000));
        // 100 seconds in future
        when(productM.getBeginDate()).thenReturn(System.currentTimeMillis() + 100000);
        ShoppingMallMembershipWarmUpButtonOpt.Param param = ShoppingMallMembershipWarmUpButtonOpt.Param.builder().productM(productM).build();
        // act
        DzSimpleButtonVO buttonVO = opt.compute(context, param, null);
        // assert
        assertEquals("即将开抢", buttonVO.getName());
        assertEquals("test_jump_url", buttonVO.getJumpUrl());
    }

    /**
     * Test case for ONLY_WARM_UP_SALE stage
     */
    @Test
    public void testComputeWhenOnlyWarmUpSale() throws Throwable {
        // arrange
        ShoppingMallMembershipWarmUpButtonOpt opt = new ShoppingMallMembershipWarmUpButtonOpt();
        ActivityCxt context = new ActivityCxt();
        ProductM productM = Mockito.mock(ProductM.class);
        when(productM.getOrderUrl()).thenReturn("test_order_url");
        // Set warm up start time attribute
        // 100 seconds ago
        when(productM.getAttr(WarmUpStageEnum.ATTRIBUTE_KEY_WARM_UP_START_TIME)).thenReturn(String.valueOf(System.currentTimeMillis() - 100000));
        // 1 second ago
        when(productM.getBeginDate()).thenReturn(System.currentTimeMillis() - 1000);
        ShoppingMallMembershipWarmUpButtonOpt.Param param = ShoppingMallMembershipWarmUpButtonOpt.Param.builder().productM(productM).build();
        // act
        DzSimpleButtonVO buttonVO = opt.compute(context, param, null);
        // assert
        assertEquals("立即抢", buttonVO.getName());
        assertEquals("test_order_url", buttonVO.getJumpUrl());
    }
}
