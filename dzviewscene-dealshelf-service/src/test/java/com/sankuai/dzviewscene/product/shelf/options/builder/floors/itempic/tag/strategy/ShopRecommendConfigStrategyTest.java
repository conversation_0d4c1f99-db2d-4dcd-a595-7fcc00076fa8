package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import org.junit.runner.RunWith;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ShopRecommendConfigStrategyTest {

    private ShopRecommendConfigStrategy shopRecommendConfigStrategy = new ShopRecommendConfigStrategy();

    /**
     * 测试 buildTag 方法，当 picAspectRadio 大于 0 时
     */
    @Test
    public void testBuildTagWhenPicAspectRadioIsGreaterThanZero() throws Throwable {
        // arrange
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        config.setPicAspectRadio(1.0);
        config.setPicUrl("http://test.com/test.jpg");
        // act
        FloatTagVO result = shopRecommendConfigStrategy.buildTag(param, config);
        // assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals("http://test.com/test.jpg", result.getIcon().getPicUrl());
        assertEquals(1.0, result.getIcon().getAspectRadio(), 0.00001);
    }

    /**
     * 测试 buildTag 方法，当 picAspectRadio 不大于 0 时
     */
    @Test
    public void testBuildTagWhenPicAspectRadioIsNotGreaterThanZero() throws Throwable {
        // arrange
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        config.setPicAspectRadio(0.0);
        config.setPicUrl("http://test.com/test.jpg");
        // act
        FloatTagVO result = shopRecommendConfigStrategy.buildTag(param, config);
        // assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals("http://test.com/test.jpg", result.getIcon().getPicUrl());
        assertEquals(2.77778, result.getIcon().getAspectRadio(), 0.00001);
    }

    /**
     * 测试 buildTag 方法，当 picUrl 为 null 时
     */
    @Test
    public void testBuildTagWhenPicUrlIsNull() throws Throwable {
        // arrange
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        config.setPicAspectRadio(1.0);
        config.setPicUrl(null);
        // act
        FloatTagVO result = shopRecommendConfigStrategy.buildTag(param, config);
        // assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals(null, result.getIcon().getPicUrl());
        assertEquals(1.0, result.getIcon().getAspectRadio(), 0.00001);
    }

    /**
     * 测试 buildTag 方法，当 picUrl 不为 null 时
     */
    @Test
    public void testBuildTagWhenPicUrlIsNotNull() throws Throwable {
        // arrange
        FloatTagBuildReq param = new FloatTagBuildReq();
        FloatTagBuildCfg config = new FloatTagBuildCfg();
        config.setPicAspectRadio(1.0);
        config.setPicUrl("http://test.com/test.jpg");
        // act
        FloatTagVO result = shopRecommendConfigStrategy.buildTag(param, config);
        // assert
        assertNotNull(result);
        assertNotNull(result.getIcon());
        assertEquals("http://test.com/test.jpg", result.getIcon().getPicUrl());
        assertEquals(1.0, result.getIcon().getAspectRadio(), 0.00001);
    }
}
