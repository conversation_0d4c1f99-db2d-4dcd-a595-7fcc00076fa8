package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemjumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemJumpUrlVP.Param;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PressOnNailJumpUrlOptTest {

    @Mock
    private ActivityCxt context;
    @Mock
    private Param param;
    @Mock
    private PressOnNailJumpUrlOpt.Config config;
    @Mock
    private ProductM productM;

    private PressOnNailJumpUrlOpt pressOnNailJumpUrlOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        pressOnNailJumpUrlOpt = new PressOnNailJumpUrlOpt();
    }

    /**
     * 测试产品非空，跳转链接非空，SKU列表非空
     */
    @Test
    public void testCompute_ProductAndJumpUrlAndSkuListNotNull() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(Arrays.asList("SKU123"));

        // act
        String result = pressOnNailJumpUrlOpt.compute(context, param, config);

        // assert
        assertEquals("http://example.com&skuinitindex=SKU123", result);
    }

    /**
     * 测试产品非空，跳转链接非空，SKU列表为空
     */
    @Test
    public void testCompute_ProductAndJumpUrlNotNullSkuListNull() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("http://example.com");
        when(productM.getSkuIdList()).thenReturn(null);

        // act
        String result = pressOnNailJumpUrlOpt.compute(context, param, config);

        // assert
        assertEquals("http://example.com", result);
    }

    /**
     * 测试产品非空，跳转链接为空
     */
    @Test
    public void testCompute_ProductNotNullJumpUrlNull() {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getJumpUrl()).thenReturn("");

        // act
        String result = pressOnNailJumpUrlOpt.compute(context, param, config);

        // assert
        assertEquals("", result);
    }

    /**
     * 测试产品为空
     */
    @Test
    public void testCompute_ProductNull() {
        // arrange
        when(param.getProductM()).thenReturn(null);

        // act
        String result = pressOnNailJumpUrlOpt.compute(context, param, config);

        // assert
        assertEquals("", result);
    }
}
