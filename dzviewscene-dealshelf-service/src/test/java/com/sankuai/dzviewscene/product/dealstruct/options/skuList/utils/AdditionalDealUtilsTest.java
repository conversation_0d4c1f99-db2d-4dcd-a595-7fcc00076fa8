package com.sankuai.dzviewscene.product.dealstruct.options.skuList.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

import com.sankuai.dzviewscene.product.dealstruct.model.DealDetailSkuListModuleGroupModel;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuGroupModuleVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuItemVO;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealSkuVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DealAdditionalProjectM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.*;

public class AdditionalDealUtilsTest {

    private List<DealAdditionalProjectM> additionalProjectList;
    private Map<String, String> tagFormatMap;
    private List<String> titleFormatList;
    private List<ProductM> products;

    @Before
    public void setUp() {
        // 初始化测试数据
        additionalProjectList = new ArrayList<>();
        DealAdditionalProjectM projectM = mock(DealAdditionalProjectM.class);
        when(projectM.getClassification()).thenReturn("分类1");
        when(projectM.getItemName()).thenReturn("项目1");
        when(projectM.getSalePrice()).thenReturn(new BigDecimal("100"));
        when(projectM.getSalesCnt()).thenReturn(10);
        when(projectM.getProductTagMap()).thenReturn(Collections.singletonMap("标签1", "值1"));
        additionalProjectList.add(projectM);

        tagFormatMap = Collections.singletonMap("标签1", "格式化后的值：%s");
        titleFormatList = Collections.singletonList("去除的字符");

        products = new ArrayList<>();
        ProductM productM = mock(ProductM.class);
        when(productM.isAdditionalDeal()).thenReturn(true);
        products.add(productM);
    }

    @Test
    public void testParseAdditionalProject() {
        DealDetailSkuListModuleGroupModel result = AdditionalDealUtils.parseAdditionalProject(additionalProjectList, tagFormatMap, titleFormatList);
        assertNotNull(result);
        assertEquals("自选服务模块", result.getGroupName());
        assertEquals("下单时可自选加购", result.getGroupSubtitle());
        assertFalse(result.getDealSkuGroupModuleVOS().isEmpty());
        DealSkuGroupModuleVO moduleVO = result.getDealSkuGroupModuleVOS().get(0);
        assertEquals("分类1", moduleVO.getTitle());
        assertFalse(moduleVO.getDealSkuList().isEmpty());
        DealSkuVO skuVO = moduleVO.getDealSkuList().get(0);
        assertEquals("项目1", skuVO.getTitle());
        assertEquals("￥100", skuVO.getPrice());
        assertFalse(skuVO.getItems().isEmpty());
        DealSkuItemVO itemVO = skuVO.getItems().get(0);
        assertEquals("格式化后的值：值1", itemVO.getValue());
    }

    @Test
    public void testContainsAdditionalDeal() {
        assertTrue(AdditionalDealUtils.containsAdditionalDeal(products));
        products.clear();
        assertFalse(AdditionalDealUtils.containsAdditionalDeal(products));
    }
}
