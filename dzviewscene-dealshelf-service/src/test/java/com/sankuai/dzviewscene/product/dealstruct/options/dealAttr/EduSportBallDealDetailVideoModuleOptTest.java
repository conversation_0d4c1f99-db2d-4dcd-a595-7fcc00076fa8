package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.utils.DealDetailUtils;
import com.sankuai.dzviewscene.product.dealstruct.ability.videoModule.vpoints.DealDetailVideoModuleVP;
import com.sankuai.dzviewscene.product.dealstruct.vo.VideoModuleVO;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.Mock;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class EduSportBallDealDetailVideoModuleOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private EduSportBallDealDetailVideoModuleOpt.Config config;

    @Mock
    private DealDetailVideoModuleVP.Param param;

    private MockedStatic<DealDetailUtils> dealDetailUtils;

    @Before
    public void setUp() {
        dealDetailUtils = Mockito.mockStatic(DealDetailUtils.class);
    }

    @After
    public void tearDown() {
        dealDetailUtils.close();
    }

    @Test
    public void testComputeCourseContentsIsEmpty() throws Throwable {
        EduSportBallDealDetailVideoModuleOpt opt = new EduSportBallDealDetailVideoModuleOpt();
        dealDetailUtils.when(() ->DealDetailUtils.getSkuAttrValueBySkuAttrName(any(), any())).thenReturn("");
        dealDetailUtils.when(() ->DealDetailUtils.getFirstMustGroupFirstSkuAttrList(any())).thenReturn(null);
        dealDetailUtils.when(() ->DealDetailUtils.getFirstMustGroupFirstSkuAttrList(any())).thenReturn(null);
        when(config.getVideoUrlModels()).thenReturn(Lists.newArrayList(new EduSportBallDealDetailVideoModuleOpt.VideoUrlModel()));
        VideoModuleVO videoModuleVO = opt.compute(context, param, config);
        assertNull(videoModuleVO.getUrl());
    }

    @Test
    public void testComputeCourseContentsIsCustom() throws Throwable {
        EduSportBallDealDetailVideoModuleOpt opt = new EduSportBallDealDetailVideoModuleOpt();
        dealDetailUtils.when(() ->DealDetailUtils.getSkuAttrValueBySkuAttrName(any(), any())).thenReturn("自定义课程目标");
        dealDetailUtils.when(() ->DealDetailUtils.getFirstMustGroupFirstSkuAttrList(any())).thenReturn(null);
        dealDetailUtils.when(() ->DealDetailUtils.getFirstMustGroupFirstSkuAttrList(any())).thenReturn(null);
        when(config.getVideoUrlModels()).thenReturn(Lists.newArrayList(new EduSportBallDealDetailVideoModuleOpt.VideoUrlModel()));
        VideoModuleVO videoModuleVO = opt.compute(context, param, config);
        assertNull(videoModuleVO.getUrl());
    }
}
