package com.sankuai.dzviewscene.product.shelf.options.list.perfect;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.PicAreaVP;
import com.sankuai.dzviewscene.product.shelf.options.list.perfect.PerfectPicAreaOpt.Config;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PerfectPicAreaOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private PicAreaVP.Param param;

    @Mock
    private Config config;

    @Test
    public void testComputePicUrlIsEmpty() throws Throwable {
        // arrange
        PerfectPicAreaOpt perfectPicAreaOpt = new PerfectPicAreaOpt();
        ProductM productM = new ProductM();
        productM.setPicUrl("");
        when(param.getProductM()).thenReturn(productM);
        // act
        PicAreaVO result = perfectPicAreaOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeNotShowPerfectActivity() throws Throwable {
        // arrange
        PerfectPicAreaOpt perfectPicAreaOpt = new PerfectPicAreaOpt();
        ProductM productM = new ProductM();
        productM.setPicUrl("http://example.com/pic.jpg");
        when(param.getProductM()).thenReturn(productM);
        when(config.getHeaderPicAspectRadio()).thenReturn(1.77778);
        when(config.getPicWidth()).thenReturn(160);
        when(config.getPicHeight()).thenReturn(90);
        // act
        PicAreaVO result = perfectPicAreaOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        assertNull(result.getFloatTags());
    }

    @Test
    public void testComputeShowPerfectActivity() throws Throwable {
        try (MockedStatic<PerfectActivityBuildUtils> mockedStatic = mockStatic(PerfectActivityBuildUtils.class)) {
            // arrange
            PerfectPicAreaOpt perfectPicAreaOpt = new PerfectPicAreaOpt();
            ProductM productM = new ProductM();
            productM.setPicUrl("http://example.com/pic.jpg");
            when(param.getProductM()).thenReturn(productM);
            when(config.getHeaderPicAspectRadio()).thenReturn(1.77778);
            when(config.getPicWidth()).thenReturn(160);
            when(config.getPicHeight()).thenReturn(90);
            mockedStatic.when(() -> PerfectActivityBuildUtils.isShowPerfectActivity(productM)).thenReturn(true);
            // act
            PicAreaVO result = perfectPicAreaOpt.compute(context, param, config);
            // assert
            assertNotNull(result);
            assertNotNull(result.getFloatTags());
        }
    }
}
