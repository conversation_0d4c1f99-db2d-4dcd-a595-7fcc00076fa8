package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.strategy;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildCfg;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.tag.FloatTagBuildReq;
import com.sankuai.dzviewscene.productshelf.vu.enums.FloatTagPositionEnums;
import com.sankuai.dzviewscene.productshelf.vu.enums.ShelfShowTypeEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.testng.collections.Maps;

public class AdditionalDealStrategyTest {

    @Mock
    private FloatTagBuildReq mockReq;

    @Mock
    private FloatTagBuildCfg mockCfg;

    @Mock
    private ProductM mockProductM;

    private AdditionalDealStrategy strategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        strategy = new AdditionalDealStrategy();
        when(mockReq.getProductM()).thenReturn(mockProductM);
        Map<Integer, String> shelfFloatTagsIcon = Maps.newHashMap();
        shelfFloatTagsIcon.put(1, "aa");
        when(mockCfg.getShelfFloatTagsIcon()).thenReturn(shelfFloatTagsIcon);
        Map<Integer, Double> shelfAspectRadio = Maps.newHashMap();
        shelfAspectRadio.put(1, 1.0D);
        when(mockCfg.getShelfAspectRadio()).thenReturn(shelfAspectRadio);
    }

    @Test
    public void testGetName() {
        assertEquals("AdditionalDealStrategy", strategy.getName());
    }

    @Test
    public void testIsMatchTrue() {
        when(mockProductM.isAdditionalDeal()).thenReturn(true);
        assertTrue(strategy.isMatch(mockReq, mockCfg));
    }

    @Test
    public void testIsMatchFalse() {
        when(mockProductM.isAdditionalDeal()).thenReturn(false);
        assertFalse(strategy.isMatch(mockReq, mockCfg));
    }

    @Test
    public void testBuildTagWithValidData() {
        when(mockReq.getShelfShowType()).thenReturn(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType());
        when(mockProductM.isAdditionalDeal()).thenReturn(true);
        FloatTagVO result = strategy.buildTag(mockReq, mockCfg);
        assertEquals(FloatTagPositionEnums.LEFT_TOP.getPosition(), result.getPosition());
    }

    /**
     * Test when config maps are empty
     */
    @Test
    public void testBuildTagWhenConfigMapsEmpty() {
        // arrange
        AdditionalDealStrategy strategy = new AdditionalDealStrategy();
        FloatTagBuildReq req = new FloatTagBuildReq();
        FloatTagBuildCfg cfg = new FloatTagBuildCfg();
        // act
        FloatTagVO result = strategy.buildTag(req, cfg);
        // assert
        assertNull(result);
    }

    /**
     * Test when url is blank or required values are null
     */
    @Test
    public void testBuildTagWhenRequiredValuesNull() {
        // arrange
        AdditionalDealStrategy strategy = new AdditionalDealStrategy();
        FloatTagBuildReq req = new FloatTagBuildReq();
        req.setShelfShowType(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType());
        FloatTagBuildCfg cfg = new FloatTagBuildCfg();
        Map<Integer, String> iconMap = new HashMap<>();
        iconMap.put(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType(), "");
        Map<Integer, Double> ratioMap = new HashMap<>();
        ratioMap.put(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType(), null);
        cfg.setShelfFloatTagsIcon(iconMap);
        cfg.setShelfAspectRadio(ratioMap);
        // act
        FloatTagVO result = strategy.buildTag(req, cfg);
        // assert
        assertNull(result);
    }

    /**
     * Test successful case where FloatTagVO is properly constructed
     */
    @Test
    public void testBuildTagSuccess() {
        // arrange
        AdditionalDealStrategy strategy = new AdditionalDealStrategy();
        FloatTagBuildReq req = new FloatTagBuildReq();
        req.setShelfShowType(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType());
        FloatTagBuildCfg cfg = new FloatTagBuildCfg();
        Map<Integer, String> iconMap = new HashMap<>();
        iconMap.put(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType(), "test_url");
        Map<Integer, Double> ratioMap = new HashMap<>();
        ratioMap.put(ShelfShowTypeEnum.SINGLE_SMALL_PIC_SHELF.getType(), 1.5);
        cfg.setShelfFloatTagsIcon(iconMap);
        cfg.setShelfAspectRadio(ratioMap);
        // act
        FloatTagVO result = strategy.buildTag(req, cfg);
        // assert
        assertNotNull(result);
        assertEquals(FloatTagPositionEnums.LEFT_TOP.getPosition(), result.getPosition());
        DzPictureComponentVO icon = result.getIcon();
        assertNotNull(icon);
        assertEquals("test_url", icon.getPicUrl());
        assertEquals(1.5, icon.getAspectRadio(), 0.001);
        assertEquals(16, icon.getPicHeight());
    }
}
