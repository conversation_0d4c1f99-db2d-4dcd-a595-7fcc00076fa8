package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempromotags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPromoVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class BeautyShelfItemPromoTagsOptComputeTest {

    @Spy
    @InjectMocks
    private BeautyShelfItemPromoTagsOpt beautyShelfItemPromoTagsOpt;

    @Mock
    private ActivityCxt context;

    @Mock
    private BeautyShelfItemPromoTagsOpt.Param param;

    @Mock
    private BeautyShelfItemPromoTagsOpt.Config config;

    @Mock
    private ProductM // Corrected reference
    productM;

    /**
     * Test case: Both cardTimes and discount tags are present
     * Expected: Both promos should be added to the result list
     */
    @Test
    public void testComputeWithBothPromos() throws Throwable {
        // arrange
        when(param.getProductM()).thenReturn(productM);
        when(productM.getAttr("times_card_times")).thenReturn("10");
        // act
        List<DzPromoVO> result = beautyShelfItemPromoTagsOpt.compute(context, param, config);
        // assert
        assertNotNull(result);
        // Additional assertions can be added based on the expected behavior
    }
}
