package com.sankuai.dzviewscene.product.filterlist.option.builder.product.priceabovetags;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.core.config.Config;
import com.dianping.lion.client.api.annotation.Param;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.ability.builder.product.vp.ProductPriceAboveTagVP;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzTagVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.compress.utils.Lists;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductListConfigPriceAboveTagsOptComputeTest {

    @InjectMocks
    private ProductListConfigPriceAboveTagsOpt opt;

    @Mock
    private ProductM productM;

    @Test
    public void testComputeWithTopDisplayProduct() throws Throwable {
        // Test setup
        List<String> topDisplayAttrKeys = new ArrayList<>();
        topDisplayAttrKeys.add("key1");
        Map<String, ProductListConfigPriceAboveTagsOpt.TagCfg> attrKey2Cfg = new HashMap<>();
        ProductListConfigPriceAboveTagsOpt.TagCfg tagCfg = new ProductListConfigPriceAboveTagsOpt.TagCfg();
        tagCfg.setTextColor("red");
        attrKey2Cfg.put("key1", tagCfg);
        // Create Config object and set fields directly
        ProductListConfigPriceAboveTagsOpt.Config config = new ProductListConfigPriceAboveTagsOpt.Config();
        config.setTopDisplayAttrKeys(topDisplayAttrKeys);
        config.setAttrKey2Cfg(attrKey2Cfg);
        // Mock ProductM to return true for isTopDisplayProduct
        when(productM.getAttr("attr_topDisplayProduct")).thenReturn("true");
        // Mock ProductM to return a non-empty value for the attribute key "key1"
        when(productM.getAttr("key1")).thenReturn("value1");
        // Act
        List<DzTagVO> result = opt.compute(new ActivityCxt(), ProductPriceAboveTagVP.Param.builder().productM(productM).build(), config);
        // Assert
        assertEquals(1, result.size());
    }
}
