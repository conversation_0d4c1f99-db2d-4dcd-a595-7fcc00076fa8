package com.sankuai.dzviewscene.product.ability.options;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.ability.extCtx.vp.PreSyncHandlerVP.Param;
import com.sankuai.dzviewscene.product.ability.options.DpBasicParamsFetcherOpt.Config;
import com.sankuai.dzviewscene.product.enums.ExtContextEnum;
import com.sankuai.dzviewscene.nr.atom.CompositeAtomService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.junit.*;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class DpBasicParamsFetcherOptTest {

    @InjectMocks
    private DpBasicParamsFetcherOpt dpBasicParamsFetcherOpt;

    @Mock
    private ActivityCxt ctx;

    @Mock
    private Param param;

    @Mock
    private Config config;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Before
    public void setUp() {
        setUpCommonMocks();
        // Mocking the return of a ShopM object instead of a Long
        ShopM mockShopM = new ShopM();
        when(ctx.getParam(anyString())).thenReturn(mockShopM);
    }

    private void setUpCommonMocks() {
        when(param.getNeedFields()).thenReturn(Arrays.asList(ExtContextEnum.MT_SHOP_ID.getType(), ExtContextEnum.MT_CITY_ID.getType(), ExtContextEnum.SHOP_INFO.getType(), ExtContextEnum.SHOP_DP_CITY_ID.getType(), ExtContextEnum.SHOP_MT_CITY_ID.getType(), ExtContextEnum.MT_USER_ID.getType()));
        when(param.getPlatform()).thenReturn(1);
        when(compositeAtomService.getMtByDpPoiIdL(anyLong())).thenReturn(CompletableFuture.completedFuture(123L));
        when(compositeAtomService.getMtCityIdByDp(anyInt())).thenReturn(CompletableFuture.completedFuture(1));
    }

    @Test(expected = NullPointerException.class)
    public void testComputeWithNullParams() throws Throwable {
        dpBasicParamsFetcherOpt.compute(null, null, null);
    }

    @Test(expected = RuntimeException.class)
    public void testComputeWithException() throws Throwable {
        when(ctx.getParam(anyString())).thenThrow(new RuntimeException());
        dpBasicParamsFetcherOpt.compute(ctx, param, config);
    }
}
