package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.Arrays;
import java.util.List;

public class BathSimilarListOpt_GetSimilarDealListTest {

    private BathSimilarListOpt bathSimilarListOpt;

    private ActivityCxt context;

    private ProductM currentProduct;

    private List<ProductM> list;

    @Before
    public void setUp() {
        bathSimilarListOpt = new BathSimilarListOpt();
        context = Mockito.mock(ActivityCxt.class);
        currentProduct = Mockito.mock(ProductM.class);
        list = Arrays.asList(Mockito.mock(ProductM.class), Mockito.mock(ProductM.class));
    }

    /**
     * 测试getSimilarDealList方法，当pageSource等于GUESS时
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsGuess() {
        // arrange
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("GUESS");
        // act
        List<ProductM> result = bathSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试getSimilarDealList方法，当pageSource等于SHELF时
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsShelf() {
        // arrange
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("SHELF");
        // act
        List<ProductM> result = bathSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        // assert
        Assert.assertNotNull(result);
    }

    /**
     * 测试getSimilarDealList方法，当pageSource既不等于GUESS也不等于SHELF时
     */
    @Test
    public void testGetSimilarDealListWhenPageSourceIsNotGuessOrShelf() {
        // arrange
        Mockito.when(context.getParam("ShelfActivityConstants.Params.pageSource")).thenReturn("OTHER");
        // act
        List<ProductM> result = bathSimilarListOpt.getSimilarDealList(context, currentProduct, list);
        // assert
        Assert.assertTrue(result.isEmpty());
    }
}
