package com.sankuai.dzviewscene.product.dealstruct.options.dealAttr;

import com.alibaba.fastjson.JSON;
import com.sankuai.dzviewscene.product.filterlist.utils.LifeCleanUtils;
import com.sankuai.dzviewscene.shelf.platform.common.model.AttrM;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;


import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class FirstSkuAttrVOListOptNewTest {



    @Test
    public void testIsLifeClearSelfWhenDealAttrsIsEmpty() {
        List<AttrM> attrs = new ArrayList<>();
        boolean result = LifeCleanUtils.isLifeClearSelf(attrs);
        assertFalse("当 dealAttrs 为空时，应返回 false", result);
    }


    @Test
    public void testIsLifeClearSelfWhenDealAttrsDoesNotContainSelfOwnProduct() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("other_attr", "是"));
        boolean result = LifeCleanUtils.isLifeClearSelf(attrs);
        assertFalse("当 dealAttrs 中不包含 self_own_product 属性时，应返回 false", result);
    }


    @Test
    public void testIsLifeClearSelfWhenSelfOwnProductIsNo() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("self_own_product", "否"));
        boolean result = LifeCleanUtils.isLifeClearSelf(attrs);
        assertFalse("当 dealAttrs 中 self_own_product 属性值为“否”时，应返回 false", result);
    }


    @Test
    public void testIsLifeClearSelfWhenSelfOwnProductIsYes() {
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(new AttrM("self_own_product", "是"));
        boolean result = LifeCleanUtils.isLifeClearSelf(attrs);
        assertTrue("当 dealAttrs 中 self_own_product 属性值为“是”时，应返回 true", result);
    }


    @Test
    public void testIsLifeClearSelfWhenSelfOwnProductIsYes1() {
        String string = "{\n" +
                "  \"attrModelList\": [\n" +
                "    {\n" +
                "      \"name\": \"Size\",\n" +
                "      \"value\": \"Large\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"groupName\": \"Sample Group\",\n" +
                "  \"selfSupport\": true\n" +
                "}\n";
        LifeCleanAttrVOListOpt.AttrListGroupModel attrListGroupModel = JSON.parseObject(string,LifeCleanAttrVOListOpt.AttrListGroupModel.class);
        Assert.assertNotNull("当 dealAttrs 中 self_own_product 属性值为“是”时，应返回 true", attrListGroupModel);
    }

}
