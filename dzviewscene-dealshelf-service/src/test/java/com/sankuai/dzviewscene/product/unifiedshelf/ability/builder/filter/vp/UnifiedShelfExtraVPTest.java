package com.sankuai.dzviewscene.product.unifiedshelf.ability.builder.filter.vp;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Test cases for UnifiedShelfExtraVP.extractMustParameters method.
 */
public class UnifiedShelfExtraVPTest {

    @Mock
    private ActivityCxt context;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test extractMustParameters when there are no required keys.
     */
    @Test
    public void testExtractMustParameters_NoRequiredKeys() throws Throwable {
        // arrange
        JSONObject extra = new JSONObject();
        when(context.getParam("extra")).thenReturn("");
        // act
        new UnifiedShelfExtraVP<String>() {

            @Override
            public String compute(ActivityCxt context, UnifiedShelfExtraVP.Param param, String config) {
                return "";
            }
        }.extractMustParameters(context, extra);
        // assert
        assertTrue(extra.isEmpty());
    }

    /**
     * Test extractMustParameters when required keys are present and parameters are available.
     */
    @Test
    public void testExtractMustParameters_WithRequiredKeysAndParameters() throws Throwable {
        // arrange
        JSONObject extra = new JSONObject();
        Map<String, Object> params = new HashMap<>();
        params.put("ShelfActivityConstants.Params.extra", "{\"key1\":\"value1\",\"key2\":\"value2\"}");
        when(context.getParam(anyString())).thenAnswer(invocation -> params.get(invocation.getArgument(0)));
        // act
        new UnifiedShelfExtraVP<String>() {

            @Override
            public String compute(ActivityCxt context, UnifiedShelfExtraVP.Param param, String config) {
                return "";
            }
        }.extractMustParameters(context, extra);
        // assert
        verify(context, atLeast(1)).getParam(anyString());
    }

    /**
     * Test extractMustParameters when required keys are present but parameters are not available.
     */
    @Test
    public void testExtractMustParameters_WithRequiredKeysNoParameters() throws Throwable {
        // arrange
        JSONObject extra = new JSONObject();
        when(context.getParam("extra")).thenReturn("{}");
        // act
        new UnifiedShelfExtraVP<String>() {

            @Override
            public String compute(ActivityCxt context, UnifiedShelfExtraVP.Param param, String config) {
                return "";
            }
        }.extractMustParameters(context, extra);
        // assert
        assertTrue(extra.isEmpty());
    }

    /**
     * Test extractMustParameters when required keys are present and some parameters are available.
     */
    @Test
    public void testExtractMustParameters_WithRequiredKeysSomeParameters() throws Throwable {
        // arrange
        JSONObject extra = new JSONObject();
        Map<String, Object> params = new HashMap<>();
        params.put("ShelfActivityConstants.Params.extra", "{\"key1\":\"value1\"}");
        when(context.getParam(anyString())).thenAnswer(invocation -> params.get(invocation.getArgument(0)));
        // act
        new UnifiedShelfExtraVP<String>() {

            @Override
            public String compute(ActivityCxt context, UnifiedShelfExtraVP.Param param, String config) {
                return "";
            }
        }.extractMustParameters(context, extra);
        // assert
        verify(context, atLeast(1)).getParam(anyString());
        assertNotNull(extra);
    }
}