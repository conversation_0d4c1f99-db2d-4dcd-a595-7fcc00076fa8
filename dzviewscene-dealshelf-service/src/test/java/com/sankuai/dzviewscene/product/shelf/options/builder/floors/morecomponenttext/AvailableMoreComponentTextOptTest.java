package com.sankuai.dzviewscene.product.shelf.options.builder.floors.morecomponenttext;

import static org.mockito.Mockito.when;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.filterlist.utils.ContextParamBuildUtils;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.MoreComponentTextVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.morecomponenttext.AvailableMoreComponentTextOpt;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AvailableMoreComponentTextOptTest {

    private ActivityCxt activityCxt;

    private AvailableMoreComponentTextOpt.Config config;

    private MoreComponentTextVP.Param param;

    @InjectMocks
    private AvailableMoreComponentTextOpt opt;

    @Before
    public void setUp() {
        activityCxt = new ActivityCxt();
        param = MoreComponentTextVP.Param.builder().build();
        ;
        config = new AvailableMoreComponentTextOpt.Config();
        opt = new AvailableMoreComponentTextOpt();
    }

    @Test
    public void test() {
        param.setItemAreaItemCnt(10);
        param.setDefaultShowNum(1);
        param.setUnavailableItemCnt(10);
        AvailableMoreComponentTextOpt beautyShelfMoreComponentTextOpt = new AvailableMoreComponentTextOpt();
        String compute = beautyShelfMoreComponentTextOpt.compute(activityCxt, param, config);
        Assert.assertEquals("展开全部不可购", compute);
    }

    @Test
    public void test1() {
        param.setItemAreaItemCnt(10);
        param.setDefaultShowNum(3);
        param.setUnavailableItemCnt(7);
        AvailableMoreComponentTextOpt beautyShelfMoreComponentTextOpt = new AvailableMoreComponentTextOpt();
        String compute = beautyShelfMoreComponentTextOpt.compute(activityCxt, param, config);
        Assert.assertEquals("更多7个团购", compute);
    }

    @Test
    public void testComputeCase4() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        AvailableMoreComponentTextOpt.Param param = Mockito.mock(AvailableMoreComponentTextOpt.Param.class);
        AvailableMoreComponentTextOpt.Config config = Mockito.mock(AvailableMoreComponentTextOpt.Config.class);
        when(config.isNeedCurrentFilterAllProductNum()).thenReturn(false);
        when(param.getDefaultShowNum()).thenReturn(3);
        when(param.getItemAreaItemCnt()).thenReturn(3);
        AvailableMoreComponentTextOpt availableMoreComponentTextOpt = new AvailableMoreComponentTextOpt();
        String result = availableMoreComponentTextOpt.compute(context, param, config);
        Assert.assertEquals("", result);
    }

    @Test
    public void testComputeCase5() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        AvailableMoreComponentTextOpt.Param param = Mockito.mock(AvailableMoreComponentTextOpt.Param.class);
        AvailableMoreComponentTextOpt.Config config = Mockito.mock(AvailableMoreComponentTextOpt.Config.class);
        when(config.isNeedCurrentFilterAllProductNum()).thenReturn(false);
        when(param.getUnavailableItemCnt()).thenReturn(7);
        when(param.getItemAreaItemCnt()).thenReturn(7);
        AvailableMoreComponentTextOpt availableMoreComponentTextOpt = new AvailableMoreComponentTextOpt();
        String result = availableMoreComponentTextOpt.compute(context, param, config);
        Assert.assertEquals("展开全部不可购", result);
    }

    @Test
    public void testComputeCase6() throws Throwable {
        ActivityCxt context = Mockito.mock(ActivityCxt.class);
        AvailableMoreComponentTextOpt.Param param = Mockito.mock(AvailableMoreComponentTextOpt.Param.class);
        AvailableMoreComponentTextOpt.Config config = Mockito.mock(AvailableMoreComponentTextOpt.Config.class);
        when(config.isNeedCurrentFilterAllProductNum()).thenReturn(false);
        when(param.getDefaultShowNum()).thenReturn(3);
        when(param.getUnavailableItemCnt()).thenReturn(7);
        when(param.getItemAreaItemCnt()).thenReturn(10);
        AvailableMoreComponentTextOpt availableMoreComponentTextOpt = new AvailableMoreComponentTextOpt();
        String result = availableMoreComponentTextOpt.compute(context, param, config);
        Assert.assertEquals("更多7个团购", result);
    }
}
