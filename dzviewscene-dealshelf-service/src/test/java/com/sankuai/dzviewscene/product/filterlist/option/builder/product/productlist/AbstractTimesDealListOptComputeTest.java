package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import java.lang.reflect.Constructor;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractTimesDealListOptComputeTest {

    @InjectMocks
    private AbstractTimesDealListOpt abstractTimesDealListOpt = new AbstractTimesDealListOpt() {

        @Override
        protected List<ProductM> getSimilarDealList(ActivityCxt context, ProductM currentProduct, List<ProductM> list) {
            return Lists.newArrayList(createProduct(3, false));
        }
    };

    @Mock
    private ActivityCxt context;

    private AbstractTimesDealListOpt.Param createParamInstance() throws Exception {
        Constructor<AbstractTimesDealListOpt.Param> constructor = AbstractTimesDealListOpt.Param.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        return constructor.newInstance();
    }

    private void setupContextWithCurrentDealId(int currentDealId) {
        Map<String, Object> params = new HashMap<>();
        params.put(ShelfActivityConstants.Params.entityId, currentDealId);
        when(context.getParameters()).thenReturn(params);
    }

    private ProductM createProduct(int productId, boolean isTimesDealQueryFlag) {
        ProductM product = new ProductM();
        product.setProductId(productId);
        product.setSalePrice(BigDecimal.TEN);
        product.setAttr("productType", "deal");
        product.setAttr("dealStatusAttr", "true");
        product.setAttr("attr_search_hidden_status", "false");
        product.setSale(new ProductSaleM());
        product.setTimesDealQueryFlag(isTimesDealQueryFlag);
        return product;
    }

    @Test
    public void testComputeWhenCurrentProductExistsWithSimilarDeals() throws Throwable {
        int currentDealId = 1;
        setupContextWithCurrentDealId(currentDealId);
        List<ProductM> productMS = new ArrayList<>();
        ProductM currentProduct = createProduct(currentDealId, true);
        productMS.add(currentProduct);
        productMS.add(createProduct(2, true));
        AbstractTimesDealListOpt.Param param = createParamInstance();
        param.setProductMS(productMS);
        List<ProductM> result = abstractTimesDealListOpt.compute(context, param, null);
        assertEquals(2, result.size());
        assertEquals(currentDealId, result.get(0).getProductId());
    }
}
