package com.sankuai.dzviewscene.product.shelf.options.builder.filter.extra;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.filter.vp.FilterExtraVP;
import com.sankuai.dzviewscene.shelf.platform.shelf.model.FilterBtnM;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.URLEncoder;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class MedicalHospitalFilterExtraOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private FilterExtraVP.Param param;

    @Mock
    private MedicalHospitalFilterExtraOpt.Config config;

    @Mock
    private FilterBtnM // Mocking FilterBtnM
    filterBtnM;

    @Test
    public void testComputeConfigEmpty() throws Throwable {
        // arrange
        MedicalHospitalFilterExtraOpt opt = new MedicalHospitalFilterExtraOpt();
        when(config.getShelfFilterIdDepartmentId()).thenReturn(null);
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeExtraMapEmpty() throws Throwable {
        // arrange
        MedicalHospitalFilterExtraOpt opt = new MedicalHospitalFilterExtraOpt();
        when(config.getShelfFilterIdDepartmentId()).thenReturn(Collections.emptyMap());
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertNull(result);
    }

    @Test
    public void testComputeExtraMapNotEmpty() throws Throwable {
        // arrange
        MedicalHospitalFilterExtraOpt opt = new MedicalHospitalFilterExtraOpt();
        Map<Long, Long> mockMap = Collections.singletonMap(1L, 2L);
        when(config.getShelfFilterIdDepartmentId()).thenReturn(mockMap);
        // Ensure getFilterBtnM() does not return null
        when(param.getFilterBtnM()).thenReturn(filterBtnM);
        // Ensure getFilterId() returns a valid long value
        when(filterBtnM.getFilterId()).thenReturn(1L);
        // act
        String result = opt.compute(context, param, config);
        // assert
        assertNotNull(result);
    }

    @Test
    public void testContainsMustParemeters() throws Throwable {
        MedicalHospitalFilterExtraOpt opt = new MedicalHospitalFilterExtraOpt();
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put("offlinecode", "offlinecode111");
        String extraStr = URLEncoder.encode(JsonCodec.encode(extraMap));
        when(context.getParam("extra")).thenReturn(extraStr);
        String result = opt.compute(context, param, config);
        assertNotNull(result);
    }
}
