package com.sankuai.dzviewscene.product.shelf.options.list.morejumpurl;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.MoreJumpUrlVP;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductGroupM;
import org.junit.runner.RunWith;
import java.lang.reflect.Constructor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ConfigMoreJumpUrlOptTest {

    /**
     * 测试 compute 方法是否返回 null
     */
    @Test
    public void testComputeReturnNull() throws Throwable {
        // arrange
        ConfigMoreJumpUrlOpt configMoreJumpUrlOpt = new ConfigMoreJumpUrlOpt();
        ActivityCxt context = new ActivityCxt();
        ProductGroupM productGroupM = new ProductGroupM(0, null);
        // Using reflection to create an instance of MoreJumpUrlVP.Param
        Class<?> paramClass = Class.forName("com.sankuai.dzviewscene.product.shelf.ability.list.vpoints.MoreJumpUrlVP$Param");
        Constructor<?> constructor = paramClass.getDeclaredConstructor(ProductGroupM.class);
        constructor.setAccessible(true);
        MoreJumpUrlVP.Param param = (MoreJumpUrlVP.Param) constructor.newInstance(productGroupM);
        ConfigMoreJumpUrlOpt.Config config = new ConfigMoreJumpUrlOpt.Config();
        // act
        String result = configMoreJumpUrlOpt.compute(context, param, config);
        // assert
        assertNull(result);
    }
}
