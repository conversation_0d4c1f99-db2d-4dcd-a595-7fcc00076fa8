package com.sankuai.dzviewscene.product.filterlist.option.builder.product.productlist;

import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductSaleM;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

public class BathSimilarListOpt_SortByProjectCategoryAndSaleTest {

    private BathSimilarListOpt bathSimilarListOpt;

    @Before
    public void setUp() {
        bathSimilarListOpt = Mockito.spy(new BathSimilarListOpt());
    }

    @After
    public void tearDown() {
        bathSimilarListOpt = null;
    }

    @Test
    public void testSortByProjectCategoryAndSale_SameCategorySizeDifferentSales() throws Throwable {
        ProductM productM1 = Mockito.mock(ProductM.class);
        ProductM productM2 = Mockito.mock(ProductM.class);
        ProductSaleM sale1 = new ProductSaleM();
        sale1.setSale(10);
        ProductSaleM sale2 = new ProductSaleM();
        sale2.setSale(20);
        Mockito.when(productM1.getSale()).thenReturn(sale1);
        Mockito.when(productM2.getSale()).thenReturn(sale2);
        Mockito.doReturn(new ArrayList<>(Arrays.asList(1L, 2L))).when(bathSimilarListOpt).getProductCategoryList(productM1);
        Mockito.doReturn(new ArrayList<>(Arrays.asList(1L, 2L))).when(bathSimilarListOpt).getProductCategoryList(productM2);
        Comparator<ProductM> comparator = bathSimilarListOpt.sortByProjectCategoryAndSale(new ArrayList<>(Arrays.asList(1L, 2L, 3L)));
        int result = comparator.compare(productM1, productM2);
        Assert.assertTrue("Product with higher sales should come first", result > 0);
    }
}
