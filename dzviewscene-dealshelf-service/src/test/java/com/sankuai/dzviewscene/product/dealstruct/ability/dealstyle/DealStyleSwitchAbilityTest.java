package com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.DealStyleSwitchCfg;
import com.sankuai.dzviewscene.product.dealstruct.ability.dealstyle.router.DealCategoryRouter;
import com.sankuai.dzviewscene.product.dealstruct.vo.DealModuleDetailVO;
import com.sankuai.dzviewscene.shelf.business.detail.beauty.builder.struct.detailSwitch.SwitchModel;
import com.sankuai.dzviewscene.shelf.platform.utils.PlatformUtil;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DealStyleSwitchAbilityTest {

    @Mock
    private ActivityCxt activityCxt;

    @Mock
    private DealStyleSwitchCfg abilityCfg;

    @Mock
    private DealCategoryRouter dealCategoryRouter;

    @InjectMocks
    private DealStyleSwitchAbility ability;

    private void setDealCategoryRouters(DealStyleSwitchAbility ability, DealCategoryRouter dealCategoryRouter) throws Exception {
        Field field = DealStyleSwitchAbility.class.getDeclaredField("dealCategoryRouters");
        field.setAccessible(true);
        field.set(ability, Collections.singletonList(dealCategoryRouter));
    }

    @Test
    public void testBuildComputeReturnsNonNull() throws Throwable {
        setDealCategoryRouters(ability, dealCategoryRouter);
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(Collections.emptyList());
        when(dealCategoryRouter.identify(eq(abilityCfg), anyInt())).thenReturn(true);
        SwitchModel switchModel = new SwitchModel();
        when(dealCategoryRouter.compute(any(), any(), any())).thenReturn(switchModel);
        // act
        CompletableFuture<DealModuleDetailVO> result = ability.build(activityCxt, null, abilityCfg);
        // assert
        assertNotNull(result);
        // Ensure the future is completed
        DealModuleDetailVO detailVO = result.get();
        assertNotNull(detailVO.getSwitchModel());
    }

    @Test
    public void testBuildDealCategoryRoutersIsEmpty() throws Throwable {
        // No need to set routers as we are testing the empty case
        // arrange
        when(activityCxt.getSource(anyString())).thenReturn(Collections.emptyList());
        // act
        CompletableFuture<DealModuleDetailVO> result = ability.build(activityCxt, null, abilityCfg);
        // assert
        assertNull(result.get());
    }

    /**
     * Tests the getModuleKeyByPlatform method when the platform is Meituan.
     */
    @Test
    public void testGetModuleKeyByPlatformIsMT() throws Throwable {
        // arrange
        int platform = 1;
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(platform)).thenReturn(true);
            // act
            String result = DealStyleSwitchAbility.getModuleKeyByPlatform(platform);
            // assert
            assertEquals("dealdetail_gc_packagedetail", result);
        }
    }

    /**
     * Tests the getModuleKeyByPlatform method when the platform is not Meituan.
     */
    @Test
    public void testGetModuleKeyByPlatformNotMT() throws Throwable {
        // arrange
        int platform = 2;
        try (MockedStatic<PlatformUtil> mockedStatic = mockStatic(PlatformUtil.class)) {
            mockedStatic.when(() -> PlatformUtil.isMT(platform)).thenReturn(false);
            // act
            String result = DealStyleSwitchAbility.getModuleKeyByPlatform(platform);
            // assert
            assertEquals("tuandeal_gc_packagedetail", result);
        }
    }
}
