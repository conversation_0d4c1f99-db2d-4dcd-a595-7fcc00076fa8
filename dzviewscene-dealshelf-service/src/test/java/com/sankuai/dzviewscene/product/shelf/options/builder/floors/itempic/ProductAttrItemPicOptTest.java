package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.vc.sdk.dp.pic.PictureURLBuilders;
import com.google.common.collect.Lists;
import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPicVP;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemPicVP.Param;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.ProductAttrItemPicOpt.Config;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.itempic.ProductAttrItemPicOpt.HitConfig;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzPictureComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.PicAreaVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.DouHuM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

@RunWith(MockitoJUnitRunner.class)
public class ProductAttrItemPicOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private Param param;

    @Mock
    private Config config;

    @Mock
    private ProductM productM;

    @Mock
    private HitConfig hitConfig;

    @InjectMocks
    private ProductAttrItemPicOpt productAttrItemPicOpt;

    @Test
    public void testComputeWithInvalidPicUrl() throws Throwable {
        // Configure your mocks to simulate an invalid picture URL state
        when(param.getProductM()).thenReturn(productM);
        // Simulating invalid or no picture URL
        // Simulate other necessary configurations for an invalid state
        PicAreaVO result = productAttrItemPicOpt.compute(context, param, config);
        assertNull("Result should be null when an invalid picture URL is provided", result);
    }

    @Test
    public void test_getPic() {
        ProductM productM = new ProductM();
        productM.setAttr("mtss_ref_spu_id", "1234");
        productM.setAttr("spuSceneType", "17");
        DealProductSpuDTO productSpuM = new DealProductSpuDTO();
        productSpuM.setHeadPic("http://example.com/pic.jpg");
        productM.setSpuM(productSpuM);
        ProductAttrItemPicOpt productAttrItemPicOpt1 = new ProductAttrItemPicOpt();
        String pic = productAttrItemPicOpt1.getPicUrlByProductAttrValue(productM, null);
        Assert.assertEquals("http://example.com/pic.jpg", pic);
    }

    @Test
    public void testGetPicUrlByProductAttrValue_WhenAttrValueExistsInConfigMap() throws Throwable {
        ProductAttrItemPicOpt opt = new ProductAttrItemPicOpt();
        ProductM productM = mock(ProductM.class);
        ProductAttrItemPicOpt.Config config = new ProductAttrItemPicOpt.Config();
        String attrKey = "testAttr";
        String attrValue = "testValue";
        String expectedPicUrl = "expectedPicUrl";
        Map<String, String> attrMap = new HashMap<>();
        attrMap.put(attrValue, expectedPicUrl);
        config.setProductAttr(attrKey);
        config.setProductAttr2HeadPic(attrMap);
        when(productM.getAttr(attrKey)).thenReturn(attrValue);
        String result = opt.getPicUrlByProductAttrValue(productM, config);
        assertEquals(expectedPicUrl, result);
    }

    @Test
    public void testGetPicUrlByProductAttrValue_WhenAttrValueEmpty() throws Throwable {
        ProductAttrItemPicOpt opt = new ProductAttrItemPicOpt();
        ProductM productM = mock(ProductM.class);
        ProductAttrItemPicOpt.Config config = new ProductAttrItemPicOpt.Config();
        String attrKey = "testAttr";
        String defaultPicUrl = "defaultPicUrl";
        Map<String, String> attrMap = new HashMap<>();
        attrMap.put("someValue", "somePicUrl");
        config.setProductAttr(attrKey);
        config.setProductAttr2HeadPic(attrMap);
        when(productM.getAttr(attrKey)).thenReturn("");
        when(productM.getPicUrl()).thenReturn(defaultPicUrl);
        String result = opt.getPicUrlByProductAttrValue(productM, config);
        assertEquals(defaultPicUrl, result);
    }

    @Test
    public void testGetPicUrlByProductAttrValue_WhenAttrValueNotExistsInConfigMap() throws Throwable {
        ProductAttrItemPicOpt opt = new ProductAttrItemPicOpt();
        ProductM productM = mock(ProductM.class);
        ProductAttrItemPicOpt.Config config = new ProductAttrItemPicOpt.Config();
        String attrKey = "testAttr";
        String attrValue = "testValue";
        String defaultPicUrl = "defaultPicUrl";
        Map<String, String> attrMap = new HashMap<>();
        attrMap.put("otherValue", "otherPicUrl");
        config.setProductAttr(attrKey);
        config.setProductAttr2HeadPic(attrMap);
        when(productM.getAttr(attrKey)).thenReturn(attrValue);
        when(productM.getPicUrl()).thenReturn(defaultPicUrl);
        String result = opt.getPicUrlByProductAttrValue(productM, config);
        assertEquals(defaultPicUrl, result);
    }

    @Test
    public void testComputeWithValidPicUrl() throws Throwable {
        ProductM productM = new ProductM();
        ProductAttrItemPicOpt.Param param = ProductAttrItemPicOpt.Param.builder().productM(productM).build();
        ProductAttrItemPicOpt.Config config = new ProductAttrItemPicOpt.Config();
        config.setPicWidth(300);
        config.setPicHeight(300);
        String testPicUrl = "http://test.com/pic.jpg";
        ProductAttrItemPicOpt spyOpt = spy(new ProductAttrItemPicOpt() {

            public String getPicUrlByProductAttrValue(ProductM productM, ProductAttrItemPicOpt.Config config) {
                return testPicUrl;
            }
        });
        PicAreaVO result = spyOpt.compute(context, param, config);
        assertNotNull(result);
        assertNotNull(result.getPic());
        // Adjusted to expect HTTPS
        assertEquals("https://test.com/pic.jpg", result.getPic().getPicUrl());
    }

    @Test
    public void testWithEmptyPicUrl() throws Throwable {
        ProductM productM = new ProductM();
        ProductAttrItemPicOpt.Param param = ProductAttrItemPicOpt.Param.builder().productM(productM).build();
        ProductAttrItemPicOpt.Config config = new ProductAttrItemPicOpt.Config();
        ProductAttrItemPicOpt spyOpt = spy(new ProductAttrItemPicOpt() {

            public String getPicUrlByProductAttrValue(ProductM productM, ProductAttrItemPicOpt.Config config) {
                return "";
            }
        });
        PicAreaVO result = spyOpt.compute(context, param, config);
        assertNull(result);
    }

    /**
     * 测试getHitConfig方法，当config.hitConfig不为空但不包含匹配的DouHuM时，应返回默认HitConfig
     */
    @Test
    public void testGetHitConfigWithNonMatchingDouHuM() {
        // arrange
        Map<String, ProductAttrItemPicOpt.HitConfig> hitConfigMap = new HashMap<>();
        hitConfigMap.put("nonMatchingSk", new ProductAttrItemPicOpt.HitConfig());
        ReflectionTestUtils.setField(config, "hitConfig", hitConfigMap);
        when(param.getDouHuList()).thenReturn(Lists.newArrayList(new DouHuM()));

        // act
        ProductAttrItemPicOpt.HitConfig result = ReflectionTestUtils.invokeMethod(productAttrItemPicOpt, "getHitConfig", param, config);

        // assert
        assertNotNull("Result should not be null when DouHuM does not match", result);
    }
}
