package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itembtn;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.FinalHandleBtnVP;
import com.sankuai.dzviewscene.product.shelf.options.builder.floors.finalhandlebtn.ConfigHandleBtnOpt;
import com.sankuai.dzviewscene.product.utils.ProductMAttrUtils;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzSimpleButtonVO;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import com.sankuai.dzviewscene.shelf.platform.common.model.ShopM;
import com.sankuai.dzviewscene.shelf.platform.shelf.ShelfActivityConstants;
import org.assertj.core.util.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConfigHandleBtnOptTest {

    @Mock
    private ActivityCxt context;

    @Mock
    private FinalHandleBtnVP.Param param;

    @Mock
    private ConfigHandleBtnOpt.Config config;

    @Mock
    private ProductM productM;

    @Test
    public void test_showBookBtn(){
        try(MockedStatic<ProductMAttrUtils> mockedStatic = Mockito.mockStatic(ProductMAttrUtils.class)){
            mockedStatic.when(() -> ProductMAttrUtils.getAttrValue(productM,"medicalBookable")).thenReturn("true");
            ConfigHandleBtnOpt opt = new ConfigHandleBtnOpt();
            productM.setAttr("medicalBookable","true");
            when(param.getProductM()).thenReturn(productM);
            when(config.getCategoryList()).thenReturn(Lists.newArrayList(1));
            when(config.getBookBtnName()).thenReturn("预约");
            DzItemVO dzItemVO = new DzItemVO();
            DzSimpleButtonVO btn = new DzSimpleButtonVO();
            btn.setName("抢购");
            dzItemVO.setBtn(btn);
            when(param.getDzItemVO()).thenReturn(dzItemVO);
            ShopM shopM = new ShopM();
            shopM.setCategory(1);
            when(context.getParam(ShelfActivityConstants.Ctx.ctxShop)).thenReturn(shopM);
            opt.compute(context, param, config);
            Assert.assertEquals("预约", btn.getName());
        }

    }
}
