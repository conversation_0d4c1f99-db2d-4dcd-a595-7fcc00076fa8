package com.sankuai.dzviewscene.product.shelf.options.builder.floors.itemsalepriceprefix;

import com.sankuai.athena.viewscene.framework.ActivityCxt;
import com.sankuai.dzviewscene.product.shelf.ability.builder.floors.vp.ItemSalePricePrefixVP;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertNull;

/**
 * 测试DefaultSalePricePrefixOpt类的compute方法
 */
public class DefaultSalePricePrefixOptTest {

    @Mock
    private ActivityCxt mockActivityCxt;

    private DefaultSalePricePrefixOpt defaultSalePricePrefixOpt;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        defaultSalePricePrefixOpt = new DefaultSalePricePrefixOpt();
    }

    /**
     * 测试compute方法在正常情况下返回null
     */
    @Test
    public void testComputeReturnsNull() {
        // arrange
        ItemSalePricePrefixVP.Param param = ItemSalePricePrefixVP.Param.builder().build();

        // act
        String result = defaultSalePricePrefixOpt.compute(mockActivityCxt, param, null);

        // assert
        assertNull("compute方法应该返回null", result);
    }
}
