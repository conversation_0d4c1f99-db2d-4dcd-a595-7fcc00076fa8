package com.sankuai.dzviewscene.productshelf.filterlist.roleplay;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.sankuai.AppContextConfiguration;
import com.sankuai.dzviewscene.shelf.framework.ActivityEngine;
import com.sankuai.dzviewscene.shelf.framework.ActivityRequest;
import com.sankuai.dzviewscene.shelf.framework.ActivityResponse;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivity;
import com.sankuai.dzviewscene.shelf.platform.detail.ProductDetailActivityConstants;
import com.sankuai.dzviewscene.shelf.platform.filterlist.FilterListActivityConstants;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 剧本杀单元测试--详情页
 */
@Ignore("没有可执行的方法")
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppContextConfiguration.class)
public class ProductDetailApiTest {

    @Resource
    private ActivityEngine activityEngine;

    //@Test
    public void test() {

        Object result = executeByActivityEngine(buildActivityRequest());
        System.out.println(result);

    }

    private ActivityRequest buildActivityRequest() {
        ActivityRequest activityRequest = new ActivityRequest();
        activityRequest.setActivityCode(ProductDetailActivity.ACTIVITY_PRODUCT_DETAIL_CODE);
        paddingParams(activityRequest.getParams());
        return activityRequest;
    }

    private void paddingParams(Map<String, Object> params) {
        int platform = 100;
        String sceneCode1 = "inactive_roleplay_standardproduct_detail"; //剧本标品详情页

        params.put(FilterListActivityConstants.Params.sceneCode, sceneCode1);
        params.put(ProductDetailActivityConstants.Params.productId, 5304586);//资源id
        params.put(FilterListActivityConstants.Params.userAgent, platform);
        params.put(FilterListActivityConstants.Params.lat, 31.230708);
        params.put(FilterListActivityConstants.Params.lng, 121.472916);

        params.put(FilterListActivityConstants.Params.traceMark, "1");
//        params.put(FilterListActivityConstants.Params.extra, "{\"activityId\":138,\"endTime\":1611050400000,\"floorId\":1017988,\"lastScene\":false,\"preScene\":false,\"productCategoryIds\":[304],\"sceneId\":138,\"startTime\":1611021600000}");
       // params.put(FilterListActivityConstants.Params.extra, "{\"activityId\":68,\"endTime\":1611662399000,\"floorId\":1140502,\"lastScene\":false,\"productCategoryIds\":[304],\"sceneId\":450,\"startTime\":1611658800000}");

        //////////////////////平台相关参数//////////////////////
        if (VCClientTypeEnum.isMtClientTypeByCode(platform)) {
            params.put(FilterListActivityConstants.Params.platform, VCPlatformEnum.MT.getType());
            params.put(FilterListActivityConstants.Params.mtCityId, 8000);
            params.put(FilterListActivityConstants.Params.mtUserId, 0L);

        } else {
            params.put(FilterListActivityConstants.Params.platform, VCPlatformEnum.DP.getType());
            //params.put(FilterListActivityConstants.Params.dpCityId, 4432);
            params.put(FilterListActivityConstants.Params.dpCityId, 1);
            params.put(FilterListActivityConstants.Params.dpUserId, 0L);

        }
    }

    private Object executeByActivityEngine(ActivityRequest activityRequest) {
        // 1. 活动框架
        ActivityResponse activityResponse = activityEngine.execute(activityRequest);
        if (activityResponse == null) {
            return null;
        }

        // 3. 返回结果
        if (activityResponse.getResult() == null) {
            return null;
        }
        return activityResponse.getResult().join();
    }
}
