package com.sankuai.dzviewscene.productshelf.operate;

import com.dianping.vc.sdk.shopuuid.ShopUUIdWrapperServiceImpl;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.inf.TestBeanFactory;
import org.junit.Ignore;
import org.junit.runner.RunWith;

/**
 * created by z<PERSON><PERSON><PERSON>04 in 2021/5/19
 */
@Ignore("没有可执行的方法")
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"com.sankuai.dzviewscene.shelf.business.detail.edu.context","com.sankuai.dzviewscene.nr.atom","com.sankuai.dzviewscene.productshelf.nr.atom"})
public class APITest {


    static {
        TestBeanFactory.registerBean("shopUUIdWrapperService", ShopUUIdWrapperServiceImpl.class);
        TestBeanFactory.scanXmlLocations("config/spring/local/appcontext-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/common/appcontext-haima-client.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-core.xml");
        TestBeanFactory.scanXmlLocations("config/spring/content/contenthelper-client.xml");
    }
}
