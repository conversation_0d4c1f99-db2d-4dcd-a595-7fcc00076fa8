package com.sankuai.dzviewscene.productshelf.gateways.utils;

import com.dianping.vc.web.biz.client.api.AppContext;
import com.sankuai.dzviewscene.shelf.gateways.utils.AppContextUtil;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

/**
 * @auther: liweilong06
 * @date: 2020/11/11 5:13 下午
 */
@Ignore("没有可执行的方法")
public class AppContextUtilUnitTest {

    //@Test
    @DisplayName("测试null")
    public void test_readDeviceId_given_null_should_return_empty() {
        Assert.assertTrue("应该是空白对象", "".equals(AppContextUtil.readDeviceId(null, 1, null)));
    }

    //@Test
    @DisplayName("测试空对象")
    public void test_readDeviceId_given_empty_should_return_empty() {
        AppContext appContext = new AppContext();
        Assert.assertTrue("应该是空白对象", "".equals(AppContextUtil.readDeviceId(appContext, 1, null)));
    }

    //@Test
    @DisplayName("测试默认值")
    public void test_readDeviceId_given_nullAndDefault_should_return_default() {
        AppContext appContext = new AppContext();
        Assert.assertTrue("应该是asd", "asd".equals(AppContextUtil.readDeviceId(appContext, 1, "asd")));
    }

    //@Test
    @DisplayName("测试点评")
    public void test_readDeviceId_given_dpValue_should_return_value() {
        AppContext appContext = new AppContext();
        appContext.setDpid("1234");
        appContext.setUnionid("12345");
        Assert.assertTrue("应该是1234", "1234".equals(AppContextUtil.readDeviceId(appContext, 1, "asd")));
    }

    //@Test
    @DisplayName("测试美团")
    public void test_readDeviceId_given_mtValue_should_return_value() {
        AppContext appContext = new AppContext();
        appContext.setDpid("1234");
        appContext.setUnionid("12345");
        Assert.assertTrue("应该是12345", "12345".equals(AppContextUtil.readDeviceId(appContext, 201, "asd")));
    }

}
