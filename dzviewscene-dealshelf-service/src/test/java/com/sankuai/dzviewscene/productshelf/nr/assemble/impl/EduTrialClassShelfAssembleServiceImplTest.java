package com.sankuai.dzviewscene.productshelf.nr.assemble.impl;

import com.dianping.deal.struct.query.api.entity.dto.SkuAttrItemDto;
import com.dianping.deal.struct.query.api.entity.dto.SkuItemDto;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.DealItemData;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TrialClassData;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TrialClassShelfConfig;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TrialClassShelfKeywordResult;
import com.sankuai.dzviewscene.productshelf.vu.biz.enums.TrialClassSearchResultEnum;
import com.sankuai.dzviewscene.productshelf.vu.vo.FloatTagVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.RichLabelVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;


@RunWith(MockitoJUnitRunner.class)
public class EduTrialClassShelfAssembleServiceImplTest {

    @InjectMocks
    EduTrialClassShelfAssembleServiceImpl service;
    @Before
    public void init(){
        try {
            Class serviceClass = service.getClass();
            Field[] fields = serviceClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if ("trialClassShelfConfig".equals(field.getName())) {
                    field.set(service, new TrialClassShelfConfig());
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    @DisplayName("托班/幼儿园类目'班型'和'适用年龄'的解析逻辑测试")
    public void parseTuobanTrialClassData() throws Exception {
        Method parseTuobanTrialClassData = service.getClass().getDeclaredMethod("parseTuobanTrialClassData", DealItemData.class, TrialClassData.class);
        parseTuobanTrialClassData.setAccessible(true);

        DealItemData dzItemVO = new DealItemData();
        dzItemVO.setCategoryName("托班/幼儿园");

        TrialClassData trialClassData = new TrialClassData();

        parseTuobanTrialClassData.invoke(service, dzItemVO, trialClassData);

        assert trialClassData.getSuitableAgeGroup().equals("幼儿");
        assert trialClassData.getSuitableClass().equals("幼儿班");
    }

    @Test
    @DisplayName("早教试听课的科目、适用年龄、适用人群字段解析逻辑测试")
    public void parseZaojiaoTrialClassData() throws Exception {
        Method parseZaojiaoTrialClassData = service.getClass().getDeclaredMethod("parseZaojiaoTrialClassData", DealItemData.class, TrialClassData.class);
        parseZaojiaoTrialClassData.setAccessible(true);

        DealItemData dzItemVO = new DealItemData();
        dzItemVO.setCategoryName("早教");

        TrialClassData trialClassData = new TrialClassData();
        trialClassData.setProductName("【0-36个月】欢动共育课程");


        parseZaojiaoTrialClassData.invoke(service, dzItemVO, trialClassData);

        assert trialClassData.getSuitableAgeGroup().equals("幼儿");
        assert trialClassData.getEduSuitableAge().equals("0-36个月");
        assert trialClassData.getProductName().equals("欢动共育课程");

        trialClassData.setProductName("【0-36个月欢动共育课程");
        parseZaojiaoTrialClassData.invoke(service, dzItemVO, trialClassData);
        assert trialClassData.getProductName() == null;

    }

    @Test
    @DisplayName("试听课模块商品高亮提示文案封装测试")
    public void buildHintTags() throws Exception {
        Method buildHintTags = service.getClass().getDeclaredMethod("buildHintTags", DealItemData.class);
        buildHintTags.setAccessible(true);

        DealItemData dzItemVO = new DealItemData();

        SkuItemDto skuItemDto = new SkuItemDto();
        dzItemVO.setSkuItemDtoList(Arrays.asList(skuItemDto));

        SkuAttrItemDto shopGift = new SkuAttrItemDto();
        shopGift.setAttrName("shopGift");
        shopGift.setAttrValue("有赠礼");
        SkuAttrItemDto courseValue = new SkuAttrItemDto();
        courseValue.setAttrName("CourseValue");
        courseValue.setAttrValue("课程价值");
        skuItemDto.setAttrItems(Arrays.asList(shopGift, courseValue));

        List<RichLabelVO> list = (List<RichLabelVO>) buildHintTags.invoke(service, dzItemVO);
        assert list.size() == 2;
        assert list.get(0).getText().contains("有赠礼");
        assert list.get(1).getText().contains("课程价值");
    }

    @Test
    @DisplayName("试听课模块商品浮层角标封装测试")
    public void buildFloatTags() throws Exception {
        Method buildFloatTags = service.getClass().getDeclaredMethod("buildFloatTags", DealItemData.class);
        buildFloatTags.setAccessible(true);

        DealItemData dzItemVO = new DealItemData();

        SkuItemDto skuItemDto = new SkuItemDto();
        dzItemVO.setSkuItemDtoList(Arrays.asList(skuItemDto));

        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("shopGift");
        skuAttrItemDto.setAttrValue("有赠礼");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));

        List<FloatTagVO> list = (List<FloatTagVO>) buildFloatTags.invoke(service, dzItemVO);
        assert list.size() == 1;
        assert list.get(0).getLabel().getText().equals("有赠礼");
    }

    @Test
    @DisplayName("命中搜索可解释行情况下，试听课模块标题封装测试")
    public void buildSummaryTitleWithKeyword() throws Exception {
        Method buildSummaryTitle = service.getClass().getDeclaredMethod("buildSummaryTitle",
                TrialClassShelfKeywordResult.class, List.class, int.class);
        buildSummaryTitle.setAccessible(true);

        TrialClassShelfKeywordResult keywordResult = new TrialClassShelfKeywordResult();
        keywordResult.setSearchType(TrialClassSearchResultEnum.CLASS);
        keywordResult.setShowValue("足球");
        List<TrialClassData> trialClassDataList = Arrays.asList(buildTrialClassData("篮球"), buildTrialClassData("羽毛球"),
                buildTrialClassData("足球"), buildTrialClassData("排球"));

        List<RichLabelVO> list = (List<RichLabelVO>) buildSummaryTitle.invoke(service, keywordResult, trialClassDataList, 1);
        assert list.size() == 4;
        assert list.get(1).getText().equals("足球/");
    }

    @Test
    @DisplayName("未中搜索可解释行情况下，试听课模块标题封装测试")
    public void buildSummaryTitleNonKeyword() throws Exception {
        Method buildSummaryTitle = service.getClass().getDeclaredMethod("buildSummaryTitle",
                TrialClassShelfKeywordResult.class, List.class, int.class);
        buildSummaryTitle.setAccessible(true);

        List<TrialClassData> trialClassDataList = Arrays.asList(buildTrialClassData("篮球"), buildTrialClassData("羽毛球"),
                buildTrialClassData("足球"), buildTrialClassData("排球"));

        List<RichLabelVO> list = (List<RichLabelVO>) buildSummaryTitle.invoke(service, null, trialClassDataList, 1);
        assert list.size() == 4;
        assert list.get(1).getText().equals("篮球/");
    }

    @Test
    @DisplayName("获取试听课标题")
    public void getTitle() throws Exception {
        Method getTitle = service.getClass().getDeclaredMethod("getTitle",
                DealItemData.class, SkuItemDto.class);
        getTitle.setAccessible(true);

        DealItemData dealItemData = new DealItemData();
        dealItemData.setCategoryName("早教");
        SkuItemDto skuItemDto = new SkuItemDto();
        SkuAttrItemDto skuAttrItemDto = new SkuAttrItemDto();
        skuAttrItemDto.setAttrName("desc");
        skuAttrItemDto.setAttrValue("【0-36个月】欢动共育课程");
        skuItemDto.setAttrItems(Arrays.asList(skuAttrItemDto));
        String name = (String) getTitle.invoke(service, dealItemData, skuItemDto);

        assert name.equals("【0-36个月】欢动共育课程");
    }

    private TrialClassData buildTrialClassData(String productName) {
        TrialClassData trialClassData = new TrialClassData();
        trialClassData.setProductName(productName);
        return trialClassData;
    }
}
