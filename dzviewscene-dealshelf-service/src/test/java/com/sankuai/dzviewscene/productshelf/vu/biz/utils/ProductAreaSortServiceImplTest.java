package com.sankuai.dzviewscene.productshelf.vu.biz.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemAreaComponentVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.DzItemVO;
import com.sankuai.dzviewscene.productshelf.vu.vo.ProductAreaComponentVO;
import com.sankuai.dzviewscene.shelf.framework.ActivityContext;
import com.sankuai.dzviewscene.shelf.platform.common.batchrank.ShelfMultiRankService;
import com.sankuai.dzviewscene.shelf.platform.common.model.ProductM;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductAreaSortServiceImplTest {

    @InjectMocks
    private ProductAreaSortServiceImpl productAreaSortService;

    @Mock
    private ShelfMultiRankService shelfMultiRankService;

    private ActivityContext ctx = new ActivityContext();

    private long filterBtnId = 1L;

    private ProductAreaComponentVO areaComponentVO = mock(ProductAreaComponentVO.class);

    private DzItemAreaComponentVO itemAreaComponentVO = mock(DzItemAreaComponentVO.class);

    @Before
    public void setUp() {
        when(areaComponentVO.getItemArea()).thenReturn(itemAreaComponentVO);
    }

    @Test
    public void testSortAreaComponentVONull() throws Throwable {
        productAreaSortService.sort(ctx, filterBtnId, null);
        verify(shelfMultiRankService, never()).multiRank(any(), any());
    }

    @Test
    public void testSortSortedMapEmpty() throws Throwable {
        when(itemAreaComponentVO.getProductItems()).thenReturn(Arrays.asList());
        productAreaSortService.sort(ctx, filterBtnId, areaComponentVO);
        verify(shelfMultiRankService, never()).multiRank(any(), any());
    }

    @Test
    public void testSortSortedMapNotContainsFilterBtnId() throws Throwable {
        Map<Long, List<ProductM>> sortedMap = new HashMap<>();
        sortedMap.put(2L, Arrays.asList(new ProductM()));
        when(itemAreaComponentVO.getProductItems()).thenReturn(Arrays.asList());
        productAreaSortService.sort(ctx, filterBtnId, areaComponentVO);
        verify(shelfMultiRankService, never()).multiRank(any(), any());
    }

    @Test
    public void testSortSortedMapContainsFilterBtnId() throws Throwable {
        Map<Long, List<ProductM>> sortedMap = new HashMap<>();
        sortedMap.put(filterBtnId, Arrays.asList(new ProductM()));
        when(itemAreaComponentVO.getProductItems()).thenReturn(Arrays.asList());
        productAreaSortService.sort(ctx, filterBtnId, areaComponentVO);
        verify(shelfMultiRankService, never()).multiRank(any(), any());
    }
}
