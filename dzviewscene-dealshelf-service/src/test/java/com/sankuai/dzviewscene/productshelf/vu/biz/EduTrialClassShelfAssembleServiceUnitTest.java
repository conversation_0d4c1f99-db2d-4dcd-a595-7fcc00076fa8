package com.sankuai.dzviewscene.productshelf.vu.biz;

import com.sankuai.dzviewscene.productshelf.nr.assemble.impl.EduTrialClassShelfAssembleServiceImpl;
import com.sankuai.dzviewscene.productshelf.vu.biz.data.TrialClassShelfConfig;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

import java.lang.reflect.Field;

/**
 * @auther: liweilong06
 * @date: 2023/7/17 下午8:26
 */
public class EduTrialClassShelfAssembleServiceUnitTest {

    private EduTrialClassShelfAssembleServiceImpl service = new EduTrialClassShelfAssembleServiceImpl();

    @Before
    public void init() {
        try {
            Class serviceClass = service.getClass();
            Field[] fields = serviceClass.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if ("trialClassShelfConfig".equals(field.getName())) {
                    field.set(service, new TrialClassShelfConfig());
                    break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //@Test
    public void test_getSaleStr() {
        Assert.assertEquals(null, service.getSaleStr(-1));
        Assert.assertEquals(null, service.getSaleStr(0));
        Assert.assertEquals("年报名1", service.getSaleStr(1));
        Assert.assertEquals("年报名49", service.getSaleStr(49));
        Assert.assertEquals("年报名50+", service.getSaleStr(50));
        Assert.assertEquals("年报名50+", service.getSaleStr(51));
        Assert.assertEquals("年报名50+", service.getSaleStr(59));
        Assert.assertEquals("年报名90+", service.getSaleStr(99));
        Assert.assertEquals("年报名100+", service.getSaleStr(100));
        Assert.assertEquals("年报名600+", service.getSaleStr(601));
        Assert.assertEquals("年报名600+", service.getSaleStr(699));
        Assert.assertEquals("年报名900+", service.getSaleStr(999));
        Assert.assertEquals("年报名1000+", service.getSaleStr(1000));
        Assert.assertEquals("年报名1000+", service.getSaleStr(1000));
        Assert.assertEquals("年报名1000+", service.getSaleStr(1001));
        Assert.assertEquals("年报名2000+", service.getSaleStr(2000));
        Assert.assertEquals("年报名2000+", service.getSaleStr(2999));
        Assert.assertEquals("年报名9000+", service.getSaleStr(9999));
        Assert.assertEquals("年报名1万+", service.getSaleStr(10000));
        Assert.assertEquals("年报名1万+", service.getSaleStr(10999));
        Assert.assertEquals("年报名5.9万+", service.getSaleStr(59999));
    }

}
