package com.sankuai.dzviewscene.nr.atom;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.athena.inf.config.ConfigValue;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.athena.test.AthenaRunner;
import com.sankuai.athena.test.annotation.AthenaEnv;
import com.sankuai.athena.test.annotation.ContextConfig;
import com.sankuai.athena.test.annotation.Environment;
import lombok.extern.slf4j.Slf4j;
import org.junit.FixMethodOrder;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;

import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2022/1/7 上午10:23
 */
@Ignore("没有可执行的方法")
@Slf4j
@RunWith(AthenaRunner.class)
@ContextConfig(packages = {"java.lang"})
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class LionIntegTest {

    private static final String CONFIG_GROUP_NAME = "timeout";

//    private static final String SCENE_DEFAULT_TIMEOUT_CONFIG_KEY = com.dianping.lion.Environment.getAppName() + ".timeout.all.scene.default.config";

    private static final String SCENE_DEFAULT_TIMEOUT_CONFIG_KEY = "com.sankuai.dzviewscene.productshelf.timeout.all.scene.default.config";

    private static final String CONTROL_STRATEGIES = "com.sankuai.dzviewscene.productshelf.lifeservice.douhu.control.strategies";

    @ConfigValue(key = "com.sankuai.dzviewscene.productshelf.spu.detailurls.config", defaultValue = "{}")
    private static Map<Long, String> spuTypeToSpuDetailConfig;

    @Environment(AthenaEnv.Product)
    //@Test
    public void test_config() {
        log.info("@Config读取：" + JsonCodec.encodeWithUTF8(spuTypeToSpuDetailConfig));// 可以的
        log.info("lion.getXXValue读取：" + Lion.getStringValue("com.sankuai.dzviewscene.productshelf.dp.m.host"));// 可以的
        log.info("Lion.getConfigRepository读取：" + JsonCodec.encodeWithUTF8(Lion.getConfigRepository(com.dianping.lion.Environment.getAppName(), CONFIG_GROUP_NAME).getIntValue(SCENE_DEFAULT_TIMEOUT_CONFIG_KEY, 0)));// 不可以
        log.info("Lion.getConfigRepository读取(指定app)：" + JsonCodec.encodeWithUTF8(Lion.getConfigRepository("com.sankuai.dzviewscene.productshelf", CONFIG_GROUP_NAME).getIntValue(SCENE_DEFAULT_TIMEOUT_CONFIG_KEY, 0)));// 不可以
        log.info("Lion.getList读取：" + JsonCodec.encodeWithUTF8((Lion.getList(CONTROL_STRATEGIES, String.class, com.google.common.collect.Lists.newArrayList()))));// 可以的
    }


}
