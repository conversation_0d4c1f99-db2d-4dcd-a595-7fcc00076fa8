package com.sankuai.dzviewscene.dealshelf.dto.baidumap;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/3
 */
public class BaiduMapShelfDealDTO implements Serializable {
    /**
     * 团购id
     */
    private long itemId;

    /**
     * 团购标题
     */
    private String title;

    /**
     * 头图
     */
    private String picUrl;

    /**
     * 市场价（有优惠时不返回），单位分
     */
    private int marketPrice;

    /**
     * 团购售价（有优惠时为优惠价），单位分
     */
    private int salePrice;

    /**
     * 基础价格
     */
    private int basePrice;

    /**
     * 优惠标签，如已减20
     */
    private String promoTag;

    /**
     * 销量信息，如半年消费30
     */
    private String sale;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 副标题
     */
    private List<String> productTags;

    /**
     * 排序顺序
     */
    private int index;

    /**
     * 团单二级分类id
     */
    private int categoryId;

    /**
     * 和平台优惠冲突的优惠信息
     */
    private List<DealPromoDetailDTO> dealPromos;

    public long getItemId() {
        return itemId;
    }

    public void setItemId(long itemId) {
        this.itemId = itemId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public int getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(int marketPrice) {
        this.marketPrice = marketPrice;
    }

    public int getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(int salePrice) {
        this.salePrice = salePrice;
    }

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public String getSale() {
        return sale;
    }

    public void setSale(String sale) {
        this.sale = sale;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public List<String> getProductTags() {
        return productTags;
    }

    public void setProductTags(List<String> productTags) {
        this.productTags = productTags;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(int categoryId) {
        this.categoryId = categoryId;
    }

    public int getBasePrice() {
        return basePrice;
    }

    public void setBasePrice(int basePrice) {
        this.basePrice = basePrice;
    }

    public List<DealPromoDetailDTO> getDealPromos() {
        return dealPromos;
    }

    public void setDealPromos(List<DealPromoDetailDTO> dealPromos) {
        this.dealPromos = dealPromos;
    }

    @Override
    public String toString() {
        return "BaiduMapShelfDealDTO{" +
                "itemId=" + itemId +
                ", title='" + title + '\'' +
                ", picUrl='" + picUrl + '\'' +
                ", marketPrice=" + marketPrice +
                ", salePrice=" + salePrice +
                ", promoTag='" + promoTag + '\'' +
                ", sale='" + sale + '\'' +
                ", jumpUrl='" + jumpUrl + '\'' +
                ", productTags=" + productTags +
                ", index=" + index +
                ", categoryId=" + categoryId +
                '}';
    }
}
