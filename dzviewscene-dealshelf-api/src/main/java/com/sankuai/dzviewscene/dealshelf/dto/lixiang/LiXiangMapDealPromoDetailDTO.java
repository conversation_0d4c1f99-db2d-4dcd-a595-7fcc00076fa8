package com.sankuai.dzviewscene.dealshelf.dto.lixiang;

import java.io.Serializable;

/**
 * 团购的优惠详情
 */
public class LiXiangMapDealPromoDetailDTO implements Serializable {

    /**
     * 优惠金额的文案，优惠-¥598元则返回-¥598
     */
    private String promoPrice;

    /**
     * 优惠标题
     */
    private String title;

    /**
     * 优惠描述
     */
    private String desc;

    public String getPromoPrice() {
        return promoPrice;
    }

    public void setPromoPrice(String promoPrice) {
        this.promoPrice = promoPrice;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String toString() {
        return "LiXiangMapDealPromoDetailDTO{" +
                "promoPrice=" + promoPrice +
                ", title='" + title + '\'' +
                ", desc='" + desc + '\'' +
                '}';
    }

}
