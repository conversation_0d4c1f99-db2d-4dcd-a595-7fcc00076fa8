package com.sankuai.dzviewscene.dealshelf.dto.tencentmap;

import java.io.Serializable;
import java.util.List;

/**
 * 团购货架组件
 * <AUTHOR>
 * @date 2022/5/20
 */
public class ShelfComponentDTO implements Serializable {

    /**
     * 首屏查看团购数量阈值
     */
    private int foldThreshold;

    /**
     * 查看更多文案
     */
    private String foldTitle;

    /**
     * 团单总数
     */
    private int dealTotal;

    /**
     * 详细的团单信息
     */
    private List<ShelfDealDTO> dealList;

    public int getFoldThreshold() {
        return foldThreshold;
    }

    public void setFoldThreshold(int foldThreshold) {
        this.foldThreshold = foldThreshold;
    }

    public String getFoldTitle() {
        return foldTitle;
    }

    public void setFoldTitle(String foldTitle) {
        this.foldTitle = foldTitle;
    }

    public int getDealTotal() {
        return dealTotal;
    }

    public void setDealTotal(int dealTotal) {
        this.dealTotal = dealTotal;
    }

    public List<ShelfDealDTO> getDealList() {
        return dealList;
    }

    public void setDealList(List<ShelfDealDTO> dealList) {
        this.dealList = dealList;
    }

    @Override
    public String toString() {
        return "ShelfComponentDTO{" +
                "foldThreshold=" + foldThreshold +
                ", foldTitle='" + foldTitle + '\'' +
                ", dealTotal=" + dealTotal +
                ", dealList=" + dealList +
                '}';
    }
}
