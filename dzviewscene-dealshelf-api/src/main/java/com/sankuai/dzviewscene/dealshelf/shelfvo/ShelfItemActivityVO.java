/*
 * Create Author  : liyan<PERSON>
 * Create Date    : 2024-09-10
 * Project        :
 * File Name      : ShelfItemActivityVO.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzviewscene.dealshelf.shelfvo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> yanmin.li <p>
 * @version 1.0 2024-09-10
 * @since dzviewscene-dealshelf-home 1.0
 */
@Data
public class ShelfItemActivityVO implements Serializable {
    /**
     * 倒计时前缀文案
     */
    @MobileDo.MobileField
    private String preText;

    /**
     * 活动结束时间戳
     */
    @MobileDo.MobileField
    private long activityEndTime;

    /**
     * 倒计时后缀文案
     */
    @MobileDo.MobileField
    private String suffix;

    public ShelfItemActivityVO() {
    }

    public ShelfItemActivityVO(long activityEndTime, String preText) {
        this.preText = preText;
        this.activityEndTime = activityEndTime;
    }

    public ShelfItemActivityVO(long activityEndTime, String preText, String suffix) {
        this.preText = preText;
        this.activityEndTime = activityEndTime;
        this.suffix = suffix;
    }
}
